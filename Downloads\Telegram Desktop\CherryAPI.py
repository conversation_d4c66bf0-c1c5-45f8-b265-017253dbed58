import httpx
import aiomysql
import re

#TOKEN = "6411633937:AAGZJ_6X4_sqTznBCMDuV9mMJxErXPx044k"
TOKEN = "6764548105:AAG5d1SKVV2yERI9q5OMjyWFhcPNT0BsAvg"

URL = f"https://api.telegram.org/bot{TOKEN}/"

async def send_message(chat_id, text, parse_mode=None, reply_markup=None):
    async with httpx.AsyncClient(timeout=20) as client:
        url = URL + "sendMessage"
        payload = {'chat_id': chat_id, 'text': text}
        if parse_mode:
            payload['parse_mode'] = parse_mode
        if reply_markup:
            payload['reply_markup'] = reply_markup
        response = await client.post(url, json=payload)
        return response.json()


def create_inline_keyboard_button(text, url):
    return {"text": text, "url": url}

def create_inline_keyboard_markup(button):
    return {"inline_keyboard": [[button]]}

def pregs(lst):
    return re.findall(r'[0-9]+', lst)

class BinInfoFetcher:
    def __init__(self):
        self.pool = None
        self.cache = {}

    async def connect_db(self):
        if not self.pool:
            try:
                self.pool = await aiomysql.create_pool(
                    host='localhost',
                    user='CherryDatabase',
                    password='Elprogames109;',
                    db='bin_info',
                    autocommit=True
                )
            except Exception as e:
                print(f"Error connecting to the database: {e}")
                self.pool = None

    async def fetch_bin_info(self, bin_number):
        if bin_number in self.cache:
            return self.cache[bin_number]

        if not self.pool:
            await self.connect_db()

        if self.pool:
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("SELECT * FROM bin2 WHERE BIN = %s", (bin_number,))
                    result = await cursor.fetchone()

                    if result:
                        bin_info = {
                            'bin_number': result[0],
                            'bank': result[1],
                            'brand': result[2],
                            'type': result[3],
                            'level': result[4],
                            'country': result[5],
                            'iso1': result[6],
                            'web_bank': result[7],
                            'number_bank': result[8],
                            'issuer': result[9],
                            'code_mogi': result[10]
                        }

                        self.cache[bin_number] = bin_info
                        return bin_info

        bin_info = {
            'bin_number': 'Desconocido',
            'bank': 'Desconocido',
            'brand': 'Desconocido',
            'type': 'Desconocido',
            'level': 'Desconocido',
            'country': 'Desconocido',
            'iso1': 'Desconocido',
            'web_bank': 'Desconocido',
            'number_bank': 'Desconocido',
            'issuer': 'Desconocido',
            'code_mogi': 'Desconocido'
        }
        return bin_info


class DatabaseManager:
    def __init__(self, host, user, password, db):
        self.host = host
        self.user = user
        self.password = password
        self.db = db
        self.pool = None

    async def create_pool(self):
        self.pool = await aiomysql.create_pool(
            host=self.host,
            user=self.user,
            password=self.password,
            db=self.db,
            autocommit=True
        )

    async def card_exists_in_cherry_database(self, ccnum, database):
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(f"SELECT * FROM {database} WHERE ccnum = %s", (ccnum,))
                result = await cursor.fetchone()
                return result is not None

    async def move_record_to_cherry_database(self, ccnum, mes, ano, cvc, database):
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(f"INSERT INTO {database} (ccnum, month, year, cvv) VALUES (%s, %s, %s, %s)", (ccnum, mes, ano, cvc))
            print(f"Registro movido: ccnum: {ccnum}, month: {mes}, year: {ano}, cvv: {cvc}")

    async def delete_record_from_ent_cherry(self, cece, lista, database):
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                try:
                    await cursor.execute(f"DELETE FROM {database} WHERE cece = %s AND lista = %s", (cece, lista))
                    await conn.commit()
                    print(f"Registro eliminado: cece: {cece}, lista: {lista}")
                except Exception as e:
                    print(f"Error al eliminar el registro: cece: {cece}, lista: {lista}")
                    print(f"Error: {e}")

    async def get_row(self, database):
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(f"SELECT cece, lista FROM {database} LIMIT 1")
                row = await cursor.fetchone()
            return row

    async def get_rows(self, database):
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(f"SELECT DISTINCT cece, lista FROM {database} LIMIT 2")
                rows = await cursor.fetchall()
            return rows

    async def delete_record_from_ent_cherry_scam(self, cece, lista, response_info, database):
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                try:
                    await cursor.execute(f"DELETE FROM {database} WHERE cece = %s AND lista = %s AND response_info = %s", (cece, lista, response_info))
                    await conn.commit()
                    print(f"Registro eliminado: cece: {cece}, lista: {lista}, response_info: {response_info}")
                except Exception as e:
                    print(f"Error al eliminar el registro: cece: {cece}, lista: {lista}, response_info: {response_info}")
                    print(f"Error: {e}")

    async def get_row_scam(self, database):
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(f"SELECT cece, lista, response_info FROM {database} LIMIT 1")
                row = await cursor.fetchone()
            return row