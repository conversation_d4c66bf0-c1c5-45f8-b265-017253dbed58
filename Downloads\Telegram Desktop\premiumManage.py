import logging
import re
import time
import random
import os
import json
from datetime import datetime, timedelta
import asyncio
import aiofiles
import httpx
import pyexcel_ods
import importlib
import io
import html
import aiomysql
from aiogram.types import BufferedInputFile

from aiogram import Bo<PERSON>, Dispatcher, types, F
from aiogram.types import (
    InlineKeyboardMarkup, InlineKeyboardButton, 
    InputFile, FSInputFile, CallbackQuery, Message
)
from aiogram.utils.keyboard import InlineKeyboardBuilder
from aiogram.filters import Command, CommandStart
from aiogram.enums import ParseMode
import functools
from aiogram.methods import EditMessageText
from aiogram.fsm.context import FSMContext

from telethon import TelegramClient
from telethon.tl.functions.messages import GetHistoryRequest

from utils import APIValidators as validator
from utils import APICollect as collector
from utils import ConnectDB as ConnectDB
from utils.cmds import Gen, lookup, rand

import capsolver
from aiofiles import open as aio_open
from utils import cap as capap

API_TOKEN = ""

# Configura la conexión a la base de datos MySQL

# Configura el registro
logging.basicConfig(level=logging.INFO)


# Inicializa el bot y el despachador
dp = Dispatcher()

PREFIXES="/!.,"

# Define la función para insertar usuarios en la base de datos

user_insert_semaphore = asyncio.Semaphore(5)
ITEMS_PER_PAGE = 10

user_data = {}
user_risk = {}
user_data_extra = {}
user_data_aextra = {}
user_semaphores = {}
database_data = {}
command_prefixes = ['/', '.', '$', '!', '*']
PCMDS = ['pa','bow','axe']


### MESSAGES ###

async def response_gen_msg(inpu, generateds, user, userid):
    bin = inpu[:6]
    rango = await collector.get_user_info(userid)
    rango = json.loads(rango)['premiums']['status']

    bin_info = await bin_loader(bin)
    text = f"∙ • <b>CCherryGenerator [⚙️]  #GEN{bin}</b> • ∙\n"
    text += "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n"
    text += f"<b>🍒 | Bank : </b> <code>{bin_info['bank']}</code>\n"
    text += f"<b>🍒 | Brand : </b><code>{str(bin_info['brand']).upper()}</code>\n"
    text += f"<b>🍒 | Type : </b><code>{str(bin_info['type']).upper()}</code>\n"
    text += f"<b>🍒 | Level : </b><code>{str(bin_info['level']).upper()}</code>\n"
    text += f"<b>🍒 | Country : </b><code>{str(bin_info['country'])}</code> | {bin_info['code_mogi']}\n"
    text += "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n"
    text += f"{generateds}"
    text += "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n"
    text += f"<b>User : </b><code>@{user}</code> | <b>[{rango}]</b>\n"
    text += f"<b>Bot: @CherryScrapperBot 🌤</b>" 

    return text

### FUNCIONES ###
### FUNCIONES ###
### FUNCIONES ###

# Variable para rastrear si se ha enviado una pregunta
pregunta_enviada = False
resultado_correcto = None
chat_id = None  # Para almacenar el ID del chat donde se envió la pregunta
ganador = False  # Para rastrear si alguien ya ganó
usuarios_respuestas = set()  # Para almacenar los IDs de usuarios que ya respondieron
trivia_id = None  # Inicializa el ID del mensaje como None

# Función para generar una pregunta matemática
def generar_pregunta():
    operaciones = [
        ("+", lambda x, y: x + y),
        ("-", lambda x, y: x - y),
        ("*", lambda x, y: x * y),
        ("/", lambda x, y: x / y if y != 0 else None),  # Evitar división por cero
    ]

    while True:  # Bucle que continuará hasta generar una pregunta válida
        num1 = random.randint(1, 100)
        num2 = random.randint(1, 100)
        num3 = random.randint(1, 100)

        # Seleccionar dos operaciones al azar
        op1, func1 = random.choice(operaciones)
        op2, func2 = random.choice(operaciones)

        # Asegurarse de que no haya divisiones por cero
        if op1 == "/" and num2 == 0:
            num2 = random.randint(1, 100)
        if op2 == "/" and num3 == 0:
            num3 = random.randint(1, 100)

        # Calcular el resultado respetando la precedencia
        pregunta = f"¿Cuánto es {num1} {op1} {num2} {op2} {num3}?"
        if op1 in ["*", "/"] and op2 in ["+", "-"]:
            resultado_correcto = func2(func1(num1, num2), num3)
        else:
            resultado_correcto = func1(num1, num2)
            resultado_correcto = func2(resultado_correcto, num3)

        if resultado_correcto is None or resultado_correcto % 1 != 0:
            continue  # Vuelve a iniciar el bucle si el resultado no es un entero

        # Generar respuestas aleatorias
        respuestas = [resultado_correcto]
        while len(respuestas) < 4:
            respuesta_aleatoria = random.randint(1, 200)
            if respuesta_aleatoria not in respuestas:
                respuestas.append(respuesta_aleatoria)

        random.shuffle(respuestas)  # Mezclar respuestas
        return pregunta, respuestas, resultado_correcto

# Función asíncrona para obtener una pregunta desde Open Trivia DB
async def obtener_pregunta_trivia():
    url = "https://opentdb.com/api.php?amount=1&difficulty=easy&type=multiple"

    async with httpx.AsyncClient() as client:
        response = await client.get(url)

    data = response.json()

    if data['response_code'] == 0:
        # Capturando la pregunta y respuestas en inglés
        pregunta_en_ingles = data['results'][0]['question']
        correcta_en_ingles = data['results'][0]['correct_answer']
        incorrectas_en_ingles = data['results'][0]['incorrect_answers']

        # Separar incorrectas en variables individuales
        incorrecta_1 = incorrectas_en_ingles[0]
        incorrecta_2 = incorrectas_en_ingles[1]
        incorrecta_3 = incorrectas_en_ingles[2]

        # Mezclar respuestas
        respuestas = [correcta_en_ingles, incorrecta_1, incorrecta_2, incorrecta_3]
        random.shuffle(respuestas)  # Mezclar respuestas

        # Establecer el resultado correcto en la respuesta correcta en español
        return pregunta_en_ingles, respuestas, correcta_en_ingles  # Cambiado a devolver la respuesta correcta en español
    else:
        return None, None, None

# Comando para iniciar y enviar la pregunta
@dp.message(Command("trivia"))
async def iniciar_pregunta(message: types.Message, bot: Bot):
    global chat_id, ganador, usuarios_respuestas, pregunta_enviada, resultado_correcto
    chat_id = message.chat.id  # Guardar el ID del chat
    user_id = str(message.from_user.id)
    
    if user_id not in staffs:
        return

    # Restablecer estado para nueva trivia
    ganador = False
    usuarios_respuestas.clear()

    # Elegir aleatoriamente entre pregunta matemática y pregunta de trivia
    if random.choice([True, False]):
        # Generar pregunta matemática
        pregunta, respuestas, resultado_correcto = generar_pregunta()
    else:
        # Obtener pregunta de trivia
        pregunta, respuestas, resultado_correcto = await obtener_pregunta_trivia()

    # Enviar la pregunta
    await enviar_pregunta(chat_id, bot, pregunta, respuestas, resultado_correcto)

# Función para enviar la pregunta
async def enviar_pregunta(chat_id, bot: Bot, pregunta, respuestas, resultado_correcto):
    global pregunta_enviada, trivia_id  # Asegurarnos de usar la variable global

    # Si ya se envió una pregunta, no hacemos nada
    if pregunta_enviada:
        return

    if pregunta is None:  # Si no se pudo generar una pregunta válida
        await bot.send_message(chat_id, "No se pudo generar una pregunta válida. Intenta de nuevo.")
        return

    pregunta_enviada = True  # Marcamos que se ha enviado una pregunta

    # Crear botones para las respuestas
    builder = InlineKeyboardBuilder()
    for respuesta in respuestas:
        builder.row(types.InlineKeyboardButton(text=str(respuesta), callback_data=f"respuesta_{respuesta}"))

    # Enviar la pregunta con los botones
    sent_message = await bot.send_message(
        chat_id=chat_id,
        text=f'▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n*¡CherryVia Ya Está Aquí!*\n_Responde Y Gana Recompensas_\n▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n`{pregunta}`',
        reply_markup=builder.as_markup(),
        parse_mode='Markdown'
    )

    trivia_id = sent_message.message_id  # Guarda el ID del mensaje

# Función para añadir créditos
async def add_credits_trivia(user_id):
    credit_options = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    probabilities = [40, 30, 20, 10, 5, 4, 3, 2, 1, 1]  # Menor probabilidad para 10 créditos

    selected_credits = random.choices(credit_options, weights=probabilities, k=1)[0]

    connection = await ConnectDB.ConnectDB("cherryscrappbot")
    async with connection.cursor() as cursor:
        await cursor.execute("SELECT credits FROM premiums WHERE userid = %s", (user_id,))
        result = await cursor.fetchone()
        if result:
            current_credits = result[0]
            new_credits = current_credits + selected_credits
            await cursor.execute("UPDATE premiums SET credits = %s WHERE userid = %s", (new_credits, user_id))
            await connection.commit()
            return selected_credits, new_credits
        else:
            return None, None  # Usuario no encontrado

# Función para añadir días al plan premium
async def add_days(user_id, days):
    try:
        connection = await ConnectDB.ConnectDB("cherryscrappbot")
        async with connection.cursor() as cursor:
            await cursor.execute("SELECT FIN_fecha FROM users WHERE userid = %s", (user_id,))
            user_result = await cursor.fetchone()

            if user_result:
                current_date_db = user_result[0]
                current_date = datetime.now() if current_date_db is None else current_date_db

                end_date = current_date + timedelta(days=days)
                end_date_str = end_date.strftime('%Y-%m-%d')

                await cursor.execute("UPDATE users SET FIN_fecha = %s WHERE userid = %s", (end_date_str, user_id))
                await connection.commit()
                return end_date_str
            else:
                return None  # Usuario no encontrado
    except Exception as e:
        print(f"Error: {e}")
        return None

# Función para otorgar un descuento
def get_discount():
    discount_options = [5, 10, 15, 20]
    probabilities = [30, 30, 20, 10]  # Menor probabilidad para 20%

    selected_discount = random.choices(discount_options, weights=probabilities, k=1)[0]
    return selected_discount

# Ejemplo de uso
async def reward_user(user_id):
    # Establecer probabilidades para cada tipo de recompensa
    reward_types = ['credits', 'days', 'discount']
    probabilities = [70, 20, 10]  # 70% créditos, 20% días, 10% descuento

    selected_reward_type = random.choices(reward_types, weights=probabilities, k=1)[0]

    if selected_reward_type == 'credits':
        selected_credits, new_credits = await add_credits_trivia(user_id)
        return f"Añadidos {selected_credits} créditos. Total: {new_credits}"
    elif selected_reward_type == 'days':
        days_to_add = random.choice([1, 2])  # Opción de 1 o 2 días
        new_end_date = await add_days(user_id, days_to_add)
        return f"Añadidos {days_to_add} días. Nueva fecha de finalización: {new_end_date}"
    elif selected_reward_type == 'discount':
        selected_discount = get_discount()
        return f"Acreditado un descuento del {selected_discount}%. Fecha: {datetime.now().strftime('%Y-%m-%d')}."

# Modificar el manejador de respuestas para incluir la información de recompensas
@dp.callback_query(F.data.startswith('respuesta_'))
async def handle_response(callback: types.CallbackQuery, bot: Bot):
    global pregunta_enviada, ganador, usuarios_respuestas, trivia_id

    # Verificar si la trivia ya terminó
    if ganador:
        await bot.answer_callback_query(callback.id, text="La CherryVia ya ha terminado.", show_alert=True)
        return

    user_id = callback.from_user.id
    respuesta_elegida = callback.data.split("_")[1]

    # Verificar si el usuario ya ha respondido
    if user_id in usuarios_respuestas:
        await bot.answer_callback_query(callback.id, text="Ya has respondido. No puedes intentarlo de nuevo.", show_alert=True)
        return

    # Agregar el usuario al conjunto de IDs que ya han respondido
    usuarios_respuestas.add(user_id)

    # Comparar respuesta elegida con resultado correcto
    if respuesta_elegida == str(resultado_correcto):  # Convertimos resultado_correcto a cadena
        username = callback.from_user.username or user_id
        recompensa = await reward_user(user_id)  # Obtener la recompensa
        mensaje_felicidad = f"▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n*¡Felicidades @{username}!*\n_Has ganado la CherryVia._\n▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n`{recompensa}`"  # Incluir información de recompensa
        await bot.send_message(chat_id, mensaje_felicidad, parse_mode='Markdown')  # Usar chat_id guardado
        ganador = True  # Marcar que ya hay un ganador
        pregunta_enviada = False  # Restablecer el estado para futuras preguntas
        await bot.delete_message(chat_id, trivia_id)
        trivia_id = None
    else:
        await bot.answer_callback_query(callback.id, text="Incorrecto. Suerte la proxima.", show_alert=True)


def canusecommands(func):
    @functools.wraps(func)
    async def wrapper(message: types.Message, bot: Bot, *args, **kwargs):
        try:
            if message.from_user.id not in user_risk:
                user_risk[message.from_user.id] = { 
                    'last_checkeds': [],
                    'last_lives': [],
                    'risk': False,
                    'last-checked-time': '',
                }

            # Determinar si el mensaje es una respuesta y obtener el contenido adecuado
            if message.reply_to_message:
                cc = message.reply_to_message.text
                original_message = message.text
            else:
                original_message = message.text

            # Validar el mensaje original
            if original_message:
                valids = validator.CreditCardValidator(original_message)
                resultado = valids.validate()
            elif cc:
                valids = validator.CreditCardValidator(cc)
                resultado = valids.validate()

                if not resultado:
                    await message.answer('La validación de la tarjeta falló. No es una tarjeta válida.', parse_mode='HTML')
                    return

                # Depurar el resultado si es válido
                print(resultado)
            else:
                await message.answer('No se encontró un mensaje válido para procesar.', parse_mode='HTML')
                return

            # Verificar si el mensaje tiene un prefijo válido seguido de un comando
            if any(original_message.startswith(f"{p}m") for p in PREFIXES):
                command = original_message[2:].split(' ')[0]  # Eliminar el prefijo y obtener el comando
            elif any(original_message.startswith(p) for p in PREFIXES):
                command = original_message[1:].split(' ')[0]  # Eliminar solo el prefijo y obtener el comando
                

            else:
                return await func(message, bot)  # Si no hay prefijo válido, ejecutar la función original

            # Verificar si el usuario tiene un registro en user_risk
            if message.from_user.id in user_risk:
                if command in PCMDS:
                    if resultado in user_risk[message.from_user.id]['last_lives']:
                        return await func(message, bot)
                    else:
                        await message.answer(f'Para usar el comando {command} necesitas usar una live', parse_mode='HTML')
                else:
                    return await func(message, bot)
            else:
                user_risk[message.from_user.id] = { 
                    'last_checkeds' : [],
                    'last_lives' : [],
                    'risk': False,
                    'last-checked-time': '',
                }

            # Depuración
            print(f"command: '{command}'")
            print(f"last_lives: {user_risk[message.from_user.id]['last_lives']}")

        except Exception as e:
            print(f'Error al procesar el mensaje: {str(e)}')

    return wrapper

def insert_user_decorator(func):
    @functools.wraps(func)
    async def wrapped(message, *args, **kwargs):
        user_id = message.from_user.id
        date = datetime.now().strftime('%Y-%m-%d')

        connection = await ConnectDB.ConnectDB("cherryscrappbot")

        async with connection.cursor() as cursor:
            # Verifica si el usuario ya existe en ambas tablas
            await cursor.execute('SELECT * FROM users WHERE userid = %s', (user_id,))
            existing_user = await cursor.fetchone()

            if not existing_user:
                async with user_insert_semaphore:
                    # Si el usuario no existe en 'users', insértalo con valores por defecto en esa tabla
                    await cursor.execute('INSERT INTO users (userid, banned, FIN_fecha, since) VALUES (%s, "NO", NULL, %s)', (user_id, date))
                    await connection.commit()

                    # Luego, inserta al mismo usuario en la tabla 'premiums' como "free"
                    await cursor.execute('INSERT INTO premiums (userid, status, acces) VALUES (%s, "free", %s)', (user_id, False))
                    await connection.commit()

        return await func(message, *args, **kwargs)

    return wrapped

def is_premium_user(func):
    @functools.wraps(func)
    async def wrapper(message: types.Message, *args, **kwargs):
        user_id = message.from_user.id

        connection = await ConnectDB.ConnectDB("cherryscrappbot")

        # Consultar el estado de premium y acces del usuario
        async with connection.cursor() as cursor, user_insert_semaphore:
            await cursor.execute('SELECT status, acces FROM premiums WHERE userid = %s', (user_id,))
            user_data = await cursor.fetchone()

        if user_data and user_data[0] in ["premium", "vip", "god", "owner"] and user_data[1] == 1:
            return await func(message, *args, **kwargs)
        else:
            if message.chat.type == 'private':
                await message.answer(
                    "No tienes acceso al bot. Compra una membresía con <b><a href='https://t.me/CreepCherry'>CreepCherry</a></b> - <b><a href='https://t.me/AlexoPlay'>AlexoPlay</a></b> 🍒", 
                    parse_mode="HTML", 
                    disable_web_page_preview=True
                )

    return wrapper

def check_banned_bin(func):
    @functools.wraps(func)
    async def wrappes(message: types.Message, *args, **kwargs):
        user_id = message.from_user.id
        
        # Asegurarse de que el mensaje provenga de los usuarios permitidos
        if user_id not in [5340466628, 7001838469, 6098191079]:
            # Inicializar lista de números de tarjeta
            card_numbers = []
            
            # Validar si el mensaje es una respuesta y contiene texto
            if message.reply_to_message and message.reply_to_message.text:
                # Buscar números en el mensaje respondido
                card_numbers = re.findall(r'\d{15,16}', message.reply_to_message.text)
            elif message.text:
                # Buscar números en el mensaje original
                card_numbers = re.findall(r'\d{15,16}', message.text)
            
            # Si encontramos números de tarjeta
            if card_numbers:
                try:
                    with open('/home/<USER>/Documents/TODO/utils/bannedbins.json', 'r') as file:
                        banned_bins = json.load(file).get("numbers", [])
                    
                    # Verificar si alguno de los BINs está baneado
                    for card_number in card_numbers:
                        if card_number[:6] in banned_bins:
                            await message.answer(
                                "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n"
                                "<i><b>Querías robar mis gates? 🧐 lo siento mi rey pero no vas a poder, "
                                "este bin esta BANEADO. 🛑</b></i>\n"
                                "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n",
                                parse_mode="HTML"
                            )
                            return
                except FileNotFoundError:
                    await message.answer(
                        "El archivo de tarjetas baneadas no se encuentra.",
                        parse_mode="HTML"
                    )
                    return
                except json.JSONDecodeError:
                    await message.answer(
                        "Error al leer el archivo de tarjetas baneadas.",
                        parse_mode="HTML"
                    )
                    return
            
        return await func(message, *args, **kwargs)

    return wrappes

async def check_credits(user_id: int, deduction_amount: int) -> bool:
    try:
        # Conexión a la base de datos
        connection = await ConnectDB.ConnectDB("cherryscrappbot")

        async with connection.cursor() as cursor:
            # Consultar la cantidad de créditos del usuario
            await cursor.execute("SELECT credits FROM premiums WHERE userid = %s", (user_id,))
            current_credits = await cursor.fetchone()

            # Verificar si el usuario tiene créditos suficientes
            if current_credits and int(current_credits[0]) >= int(deduction_amount):
                return True
            else:
                return False
    except Exception as e:
        print("Error:", e)
        return False

async def deduct_credits(user_id: int, deduction_amount: int) -> None:
    try:
        # Conexión a la base de datos
        connection = await ConnectDB.ConnectDB("cherryscrappbot")

        async with connection.cursor() as cursor:
            # Restar los créditos del usuario
            await cursor.execute("UPDATE premiums SET credits = credits - %s WHERE userid = %s", (deduction_amount, user_id))
            await connection.commit()
    except Exception as e:
        print("Error al restar créditos:", e)



async def get_user_info(user_id):
    user_info = {}

    # Semáforo para garantizar la exclusión mutua al obtener información del usuario
    async with user_insert_semaphore:

        # Configura la conexión a la base de datos MySQL
        connection = await ConnectDB.ConnectDB("cherryscrappbot")

        async with connection.cursor() as cursor:
            # Consulta la tabla 'users' por user_id
            await cursor.execute('SELECT * FROM users WHERE userid = %s', (user_id,))
            user_data = await cursor.fetchone()

            if user_data:
                user_info['users'] = {
                    'userid': user_data[0],
                    'banned': user_data[1],
                    'FIN_fecha': str(user_data[2]),
                    'since': str(user_data[3])
                }

            # Consulta la tabla 'premiums' por user_id
            await cursor.execute('SELECT * FROM premiums WHERE userid = %s', (user_id,))
            premiums_data = await cursor.fetchone()

            if premiums_data:
                user_info['premiums'] = {
                    'userid': premiums_data[0],
                    'status': premiums_data[1],
                    'acces': premiums_data[2],
                    'credits': premiums_data[3],
                    'antispam': premiums_data[4]
                }

    return json.dumps(user_info, indent=4)

async def insert_premium(user_id, status, days, acces, connection):
    try:
        async with user_insert_semaphore:
            async with connection as conn:
                async with conn.cursor() as cursor:
                    # Fetch the current FIN_fecha from the 'users' table
                    await cursor.execute(
                        "SELECT FIN_fecha, banned FROM users WHERE userid = %s",
                        (user_id,)
                    )
                    user_result = await cursor.fetchone()

                    current_date = datetime.now()
                    if user_result is not None:
                        current_date_db, banned = user_result
                        if current_date_db:
                            current_date = current_date_db

                    if current_date is None:
                        current_date = datetime.now()

                    end_date = current_date + timedelta(days=days)
                    end_date_str = end_date.strftime('%Y-%m-%d')

                    # Update the record in the 'users' table if not banned
                    if banned != 'YES':
                        await cursor.execute(
                            "UPDATE users SET FIN_fecha = %s WHERE userid = %s",
                            (end_date_str, user_id)
                        )

                    # Update the record in the 'premiums' table
                    await cursor.execute(
                        "UPDATE premiums SET status = %s, acces = %s WHERE userid = %s",
                        (status, acces, user_id)
                    )
    except Exception as e:
        print(f"Error: {e}")
        
async def get_all_premium_users():
    connection = await ConnectDB.ConnectDB("cherryscrappbot")
    async with connection.cursor() as cursor:
        # Seleccionar usuarios con status "premium", "vip" y "god"
        await cursor.execute('SELECT userid FROM premiums WHERE status IN ("premium", "vip", "god") AND acces = 1')
        users = await cursor.fetchall()
    return [user[0] for user in users]

async def generate_cmds_message(cmds_dict):
    message = ""
    for command, data in cmds_dict.items():
        # Verifica si el comando tiene el rango "owner" y omítelo si es así
        if data.get('rango') == 'owner':
            continue
        description = data['desc'].replace('[', r'\[').replace(']', r']')
        message += f"\n➤ `/{command}` - {description} - {data['status']}\n"
        if 'rango' in data:
            message += f"*Rango:* {data['rango']}\n"
        if 'massive' in data:
            message += f"Masivo: {data['massive']}\n"
    return message

async def fetch_bin_and_send_message(message, bin_number):
    try:
        response_text = ""
        await bin_fetcher.connect_db()

        bin_info = await bin_fetcher.fetch_bin_info(bin_number)
        if bin_info:
            response_text += f"<code>{bin_number}</code> | {bin_info['code_mogi']}\n"
            response_text += f"▬▬▬▬ [🍒] ▬▬▬▬▬\n"
            response_text += f"<b>🍒 | Bank: </b><code>{bin_info['bank']}</code>\n"
            response_text += f"<b>🍒 | Brand: </b><code>{bin_info['brand']}</code>\n"
            response_text += f"<b>🍒 | Type: </b><code>{bin_info['type']}</code>\n"
            response_text += f"<b>🍒 | Level: </b><code>{bin_info['level']}</code>\n"
            response_text += f"<b>🍒 | Country: </b><code>{bin_info['country']}</code>\n"
            await message.answer(str(response_text), parse_mode='HTML')
        else:
            await message.answer(f"No se encontró información para el bin {bin_number}.")
    except Exception as e:
        print(f"Error handling bin message: {e}")

## CAPTCHA ##

import asyncio
import logging
import os
import random
import functools
from aiogram import Bot, Dispatcher, types
from aiogram.enums import ParseMode
from aiogram.filters import Command
from aiogram.types import (FSInputFile, InlineKeyboardButton,
                           InlineKeyboardMarkup)

captcha = {}
user_risk = {}
user_locks = {}
CAPTCHA_MAX_INTENTOS = 4
ADMIN_CHANNEL_ID = "-1002313407802"
CHAT_IDS = [
    "-1002009509519", "-1002107820813", "-1002042701096",
    "-1001991135719", "-1001999254458", "-1002052241754", "-1002106884930"
]

async def generate_captcha(user_id):
    """Genera un CAPTCHA con sus datos relacionados y elimina archivos obsoletos."""
    captcha_text = capap.generate_random_text()
    filename = f"{user_id}_cap.png"

    # Eliminar archivo existente si ya hay uno previo
    if os.path.exists(filename):
        try:
            os.remove(filename)
        except Exception as e:
            logging.error(f"Error eliminando archivo anterior {filename}: {e}")

    try:
        filename, fakecaps = await capap.capgen(captcha_text, filename)
    except Exception as e:
        logging.error(f"Error generando CAPTCHA para {user_id}: {e}")
        raise

    opciones = [captcha_text] + fakecaps
    random.shuffle(opciones)

    captcha[user_id] = {
        'solucion': captcha_text,
        'fakecaps': fakecaps,
        'path': filename,
        'intentos': 0
    }
    return filename, opciones

async def ban_user(user_id, username, bot: Bot):
    """Banea al usuario en la base de datos y en los chats relevantes."""
    try:
        connection = await ConnectDB("cherryscrappbot")
        async with connection.cursor() as cursor:
            await cursor.execute("SELECT banned FROM users WHERE userid = %s", (user_id,))
            result = await cursor.fetchone()
            if result and result[0] == 'YES':
                logging.info(f"El usuario {user_id} ya está baneado.")
                return

            await cursor.execute("UPDATE users SET banned = 'YES' WHERE userid = %s", (user_id,))
            await cursor.execute("UPDATE premiums SET status = 'free', acces = 0 WHERE userid = %s", (user_id,))
            await cursor.execute("UPDATE users SET FIN_fecha = NULL WHERE userid = %s", (user_id,))
        await connection.commit()

        for chat_id in CHAT_IDS:
            try:
                await bot.ban_chat_member(chat_id, user_id)
            except Exception as e:
                logging.error(f"No se pudo banear al usuario {user_id} en el chat {chat_id}: {e}")

        await bot.send_message(
            chat_id=ADMIN_CHANNEL_ID,
            text=f"⚠️ El usuario @{username} ha sido baneado por superar el límite de intentos en el CAPTCHA."
        )

        # Eliminar archivo temporal si existe
        path = captcha.get(user_id, {}).get('path')
        if path and os.path.exists(path):
            try:
                os.remove(path)
            except Exception as e:
                logging.error(f"Error eliminando archivo CAPTCHA {path}: {e}")
        captcha.pop(user_id, None)

    except Exception as e:
        logging.error(f"Error al intentar banear al usuario {user_id}: {e}")
        await bot.send_message(
            chat_id=ADMIN_CHANNEL_ID,
            text=f"⚠️ Ocurrió un error al intentar banear al usuario @{username}. Error: {str(e)}"
        )


def risk_detection(func):
    @functools.wraps(func)
    async def wrapper(message: types.Message, *args, **kwargs):
        user_id = message.from_user.id

        if not isinstance(user_risk, dict):
            logging.error("user_risk no está inicializado correctamente.")
            return await message.answer("⚠️ Error interno. Intenta nuevamente más tarde.")

        user_risk.setdefault(user_id, {
            'last_checkeds': [],
            'last_lives': [],
            'risk': False,
            'last_checked_time': '',
            'last_risk_check': ''
        })

        last_checkeds = user_risk[user_id]['last_checkeds']
        if len(last_checkeds) >= 10:
            last_bins = [cc.split('|')[0][:6] for cc in last_checkeds[-10:]]
            if all(bin == last_bins[0] for bin in last_bins):
                user_risk[user_id]['risk'] = True
            last_checkeds.clear()

        return await func(message, *args, **kwargs)

    return wrapper


@dp.callback_query(F.data.startswith("cap|"))
async def process_captcha(callback_query: types.CallbackQuery, bot: Bot):
    """Procesa la respuesta del usuario al CAPTCHA."""
    try:
        data_parts = callback_query.data.split('|')
        if len(data_parts) != 3:
            await callback_query.answer("⚠️ Callback inválido.", show_alert=True)
            return

        _, user_id, respuesta = data_parts
        user_id = int(user_id)

        if user_id not in captcha:
            await callback_query.answer("⚠️ No tienes un CAPTCHA activo.", show_alert=True)
            return

        if callback_query.from_user.id != user_id:
            await callback_query.answer("⚠️ No puedes responder a este CAPTCHA.", show_alert=True)
            return

        captcha[user_id]['intentos'] += 1

        if captcha[user_id]['solucion'] == respuesta:
            await callback_query.answer("✅ Respuesta correcta.", show_alert=True)
            user_risk[user_id]['risk'] = False
            await bot.send_message(
                ADMIN_CHANNEL_ID,
                f"✅ El usuario [{callback_query.from_user.full_name}](tg://user?id={user_id}) resolvió correctamente el CAPTCHA.",
                parse_mode=ParseMode.MARKDOWN
            )
            await callback_query.message.delete()
            captcha.pop(user_id, None)

        elif captcha[user_id]['intentos'] >= CAPTCHA_MAX_INTENTOS:
            await callback_query.answer("❌ Has agotado el máximo de intentos.", show_alert=True)
            await ban_user(user_id, callback_query.from_user.username, bot)
            await bot.send_message(
                ADMIN_CHANNEL_ID,
                f"🚫 El usuario [{callback_query.from_user.full_name}](tg://user?id={user_id}) fue baneado por fallar el CAPTCHA.",
                parse_mode=ParseMode.MARKDOWN
            )

        else:
            intentos_restantes = CAPTCHA_MAX_INTENTOS - captcha[user_id]['intentos']
            await callback_query.answer(f"❌ Respuesta incorrecta. Intentos restantes: {intentos_restantes}", show_alert=True)
            opciones = [captcha[user_id]['solucion']] + captcha[user_id]['fakecaps']
            random.shuffle(opciones)
            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(text=opcion, callback_data=f"cap|{user_id}|{opcion}")]
                    for opcion in opciones
                ]
            )
            await callback_query.message.edit_reply_markup(reply_markup=keyboard)

    except Exception as e:
        logging.error(f"Error procesando CAPTCHA: {e}")
        await callback_query.answer("⚠️ Error inesperado. Intenta nuevamente.", show_alert=True)

### HERRAMIENTAS ###
### HERRAMIENTAS ###
### HERRAMIENTAS ###

CHERRY_JSON_PATH = "/home/<USER>/Documents/TODO/utils/cherry.json"
ITEMS_PER_PAGE = 5  # Número máximo de ítems por página
user_data_cmds = {}  # Almacén para el estado de paginación por usuario


# Función para cargar comandos desde el archivo JSON
async def load_commands():
    async with aio_open(CHERRY_JSON_PATH, mode="r", encoding="utf-8") as f:
        data = await f.read()
    return json.loads(data)

# Función para filtrar los comandos por rango
def filter_commands_by_rango(commands, allowed_rangos):
    return {
        key: value
        for key, value in commands.items()
        if value.get("rango") is None or value.get("rango") in allowed_rangos
    }


async def send_paginated_results_cmds(chat_id, user_id, message_id, main_category, sub_category, type_category=None, bot=None, user_role="normal"):
    try:
        # Cargar datos del archivo JSON
        data = await load_commands()

        # Validar que la estructura de datos existe
        if main_category not in data or sub_category not in data[main_category]:
            await bot.send_message(chat_id, "Invalid category. Please try again.")
            return

        commands = data[main_category][sub_category]

        # Si estamos en 'gateways' y hay type_category, procesarlo
        if sub_category == "gateways" and type_category:
            if type_category not in commands:
                await bot.send_message(chat_id, "Invalid gateway type. Please try again.")
                return
            commands = commands[type_category]

        # Filtrar comandos según el rol del usuario
        allowed_rangos = ["vip", "premium"] if user_role == "normal" else ["vip", "premium", "owner"]
        commands = filter_commands_by_rango(commands, allowed_rangos)

        # Validar si hay comandos después de filtrar
        if not commands:
            await bot.send_message(chat_id, "No commands available.")
            return

        # Manejo de paginación
        page = user_data_cmds[user_id].get("page", 1)
        start_idx = (page - 1) * ITEMS_PER_PAGE
        end_idx = start_idx + ITEMS_PER_PAGE

        # Obtener elementos de la página actual
        items = list(commands.items())[start_idx:end_idx]

        if not items:  # Si no hay elementos en esta página
            await bot.send_message(chat_id, "No more results.")
            return

        # Construir la respuesta según la categoría
        response_text = f"<b>Page {page} - {sub_category.capitalize()}</b>\n\n"

        if sub_category == "tools":
            # Formatear los comandos para 'tools'
            for tool_name, details in items:
                status_emoji = "✅" if details["status"] == "on" else "❌"
                response_text += (
                    f"<b>Tool:</b> <code>/{tool_name}</code>\n"
                    f"<b>Description:</b> <i>{details['desc']}</i>\n"
                    f"<b>Status:</b> <i>{details['status'].upper()}</i> {status_emoji}\n"
                    f"<b>Example:</b> <i>/{details.get('ex', 'N/A')}</i>\n"
                    f"<b>Last Update:</b> <i>{details['last-update']}</i>\n"
                    f"----------------------------------------\n"
                )
        elif sub_category == "gateways":
            # Formatear los comandos para 'gateways'
            for gateway_name, details in items:
                status_emoji = "✅" if details["status"] == "on" else "❌"
                massive_emoji = "✅" if details["massive"] == "True" else "❌"
                rango_emoji = "💎" if details["rango"] == "vip" else "🔥"
                if details.get('cost'):
                    cost = details['cost']
                    live = cost.split('|')[0]
                    death = cost.split('|')[1]
                else:
                    live = '0'
                    death = '0'

                response_text += (
                    f"<b>Gateway:</b> <code>/{gateway_name}</code>\n"
                    f"<b>Description:</b> <i>{details['desc']}</i>\n"
                    f"<b>Status:</b> <i>{details['status'].upper()}</i> {status_emoji}\n"
                    f"<b>Antispam:</b> <i>{details.get('antispam', 'N/A')} seconds</i>\n"
                    f"<b>Cost:</b> <i>{live} LIVE | {death} DEATH</i>\n"
                    f"<b>Range:</b> <i>{details.get('rango', 'N/A').upper()}</i> {rango_emoji}\n"
                    f"<b>Massive:</b> <i>{details.get('massive', 'N/A').upper()}</i> {massive_emoji}\n"
                    f"<b>Last Update:</b> <i>{details['last-update']}</i>\n"
                    f"----------------------------------------\n"
                )

        # Crear botones de navegación
        buttons = []

        # Botones de navegación (◀️ y ▶️)
        navigation_buttons = []
        if page > 1:
            navigation_buttons.append(InlineKeyboardButton(
                text="◀️", callback_data=f"backcmd|{main_category}|{sub_category}|{type_category or ''}"
            ))
        if end_idx < len(commands):
            navigation_buttons.append(InlineKeyboardButton(
                text="▶️", callback_data=f"nextcmd|{main_category}|{sub_category}|{type_category or ''}"
            ))

        if navigation_buttons:
            buttons.append(navigation_buttons)

        # Botón para regresar al inicio
        buttons.append([
            InlineKeyboardButton(
                text="🔄 Back to Start", callback_data=f"resetcmd|{main_category}|{sub_category}|{type_category or ''}"
            )
        ])

        # Crear el teclado
        keyboard = InlineKeyboardMarkup(inline_keyboard=buttons)

        # Actualizar el mensaje con los resultados
        await bot.edit_message_text(
            chat_id=chat_id,
            message_id=message_id,
            text=response_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        print(f"Error sending paginated results: {e}")

# Función para contar los comandos de cada sección
def count_commands(commands, allowed_rangos):
    total_on = 0
    total_off = 0
    for key, details in commands.items():
        # Si no tiene el campo 'rango', considerar que pertenece a todos los rangos permitidos
        if details.get("rango") is None or details.get("rango") in allowed_rangos:
            if details["status"] == "on":
                total_on += 1
            elif details["status"] == "off":
                total_off += 1
    return total_on, total_off


# Crear el teclado inicial con conteo de comandos
async def create_initial_keyboard(chat_id, user_id, message_id):
    # Cargar datos del archivo JSON
    data = await load_commands()

    # Filtrar rangos permitidos para usuarios normales
    allowed_rangos = ["vip", "premium"]

    # Contar los comandos en cada categoría y subcategoría
    ccn_auth_on, ccn_auth_off = count_commands(data["cmds"]["gateways"]["ccn_auth"], allowed_rangos)
    ccn_charge_on, ccn_charge_off = count_commands(data["cmds"]["gateways"]["ccn_charge"], allowed_rangos)
    cvv_auth_on, cvv_auth_off = count_commands(data["cmds"]["gateways"]["cvv_auth"], allowed_rangos)
    cvv_charge_on, cvv_charge_off = count_commands(data["cmds"]["gateways"]["cvv_charge"], allowed_rangos)
    tools_on, tools_off = count_commands(data["cmds"]["tools"], allowed_rangos)

    # Crear el teclado con los contadores dinámicos
    keyboard_builder = InlineKeyboardBuilder()
    keyboard_builder.row(
        InlineKeyboardButton(
            text=f"CCN AUTH [{ccn_auth_on}] ✅ [{ccn_auth_off}] ❌",
            callback_data=f"cmds|gateways|ccn_auth|{chat_id}|{user_id}|{message_id}"
        ),
        InlineKeyboardButton(
            text=f"CCN CHARGE [{ccn_charge_on}] ✅ [{ccn_charge_off}] ❌",
            callback_data=f"cmds|gateways|ccn_charge|{chat_id}|{user_id}|{message_id}"
        )
    )
    keyboard_builder.row(
        InlineKeyboardButton(
            text=f"CVV AUTH [{cvv_auth_on}] ✅ [{cvv_auth_off}] ❌",
            callback_data=f"cmds|gateways|cvv_auth|{chat_id}|{user_id}|{message_id}"
        ),
        InlineKeyboardButton(
            text=f"CVV CHARGE [{cvv_charge_on}] ✅ [{cvv_charge_off}] ❌",
            callback_data=f"cmds|gateways|cvv_charge|{chat_id}|{user_id}|{message_id}"
        )
    )
    keyboard_builder.row(
        InlineKeyboardButton(
            text=f"TOOLS [{tools_on}] ✅ [{tools_off}] ❌",
            callback_data=f"cmds|tools|{chat_id}|{user_id}|{message_id}"
        )
    )
    return keyboard_builder.as_markup()

# Comando '/start' para insertar usuarios en la base de datos
@dp.message(CommandStart())
async def start_command(message: types.Message):
    try:
        # Responder al usuario con un mensaje inicial para obtener el message_id
        sent_message = await message.reply("Hey, Welcome!")

        # Inicializar datos de paginación del usuario
        user_data_cmds[message.from_user.id] = {
            "page": 1,
            "message_id": sent_message.message_id
        }

        # Crear el teclado inicial y actualizar el mensaje
        initial_keyboard = await create_initial_keyboard(
            chat_id=message.chat.id,
            user_id=message.from_user.id,
            message_id=sent_message.message_id
            
        )

        await sent_message.edit_text("Hey, Welcome!", reply_markup=initial_keyboard)

        print(f"Chat ID: {message.chat.id}, User ID: {message.from_user.id}, Sent message ID: {sent_message.message_id}")

    except Exception as e:
        print(f"Error handling start command: {e}")


# Manejar los callbacks para la paginación y el botón "Back to Start"
@dp.callback_query(F.data.startswith("backcmd|") | F.data.startswith("nextcmd|") | F.data.startswith("resetcmd|"))
async def handle_pagination_cmds(callback_query: types.CallbackQuery, bot: Bot):
    try:
        bot = callback_query.bot
        user_id = callback_query.from_user.id

        # Parsear datos del callback
        action, main_category, sub_category, command_type = callback_query.data.split("|")

        # Verificar si el usuario tiene acceso a esta interacción
        if user_id not in user_data_cmds or user_data_cmds[user_id]['message_id'] != callback_query.message.message_id:
            await bot.answer_callback_query(callback_query.id, text="Access denied. Please open your own session to use it ❌", show_alert=True)
            return

        if action == "resetcmd":
            # Regresar al teclado inicial
            user_data_cmds[user_id]["page"] = 1
            initial_keyboard = await create_initial_keyboard(
                chat_id=callback_query.message.chat.id,
                user_id=user_id,
                message_id=callback_query.message.message_id
            )
            await bot.edit_message_text(
                chat_id=callback_query.message.chat.id,
                message_id=callback_query.message.message_id,
                text="Hey, Welcome!",
                reply_markup=initial_keyboard
            )
            return

        # Actualizar la página en función del botón presionado
        if action == "backcmd" and user_data_cmds[user_id]["page"] > 1:
            user_data_cmds[user_id]["page"] -= 1
        elif action == "nextcmd":
            user_data_cmds[user_id]["page"] += 1

        # Enviar los resultados actualizados
        await send_paginated_results_cmds(
            chat_id=callback_query.message.chat.id,
            user_id=user_id,
            message_id=callback_query.message.message_id,
            main_category=main_category,
            sub_category=sub_category,
            type_category=command_type,
            bot=bot
        )

    except Exception as e:
        print(f"Error handling pagination: {e}")


# Manejar los callbacks para gateways
@dp.callback_query(F.data.startswith("cmds|gateways|"))
async def handle_gateways_callback(callback: types.CallbackQuery):
    try:
        # Parsear los datos del callback
        parts = callback.data.split("|")
        main_category, sub_category, command_type = parts[:3]
        chat_id = int(parts[3])
        user_id = int(parts[4])
        message_id = int(parts[5])

        # Verificar si el usuario tiene acceso a esta interacción
        if user_id != callback.from_user.id:
            await callback.answer("Access denied. Please open your own session to use it ❌", show_alert=True)
            return

        # Inicializar datos de paginación
        user_data_cmds[user_id] = {"page": 1, "message_id": message_id}

        # Llamar a la función sin pasar el 'bot' explícitamente
        await send_paginated_results_cmds(
            chat_id=chat_id,
            user_id=user_id,
            message_id=message_id,
            main_category=main_category,
            sub_category=sub_category,
            type_category=command_type,
            bot=callback.bot
        )

    except Exception as e:
        await callback.answer("Error loading commands.", show_alert=True)
        print(f"Error in handle_gateways_callback: {e}")


# Manejar los callbacks para tools
@dp.callback_query(F.data.startswith("cmds|tools|"))
async def handle_tools_callback(callback: types.CallbackQuery):
    try:
        # Parsear los datos del callback
        parts = callback.data.split("|")
        main_category = parts[0]  # "cmds"
        sub_category = parts[1]  # "tools"
        chat_id = int(parts[2])
        user_id = int(parts[3])
        message_id = int(parts[4])

        # Verificar si el usuario tiene acceso a esta interacción
        if user_id != callback.from_user.id:
            await callback.answer("Access denied. Please open your own session to use it ❌", show_alert=True)
            return

        # Inicializar datos de paginación para el usuario
        user_data_cmds[user_id] = {"page": 1, "message_id": message_id}

        # Llamar a la función sin pasar 'bot'
        await send_paginated_results_cmds(
            chat_id=chat_id,
            user_id=user_id,
            message_id=message_id,
            main_category=main_category,
            sub_category=sub_category,
            bot=callback.bot
        )

    except Exception as e:
        await callback.answer("Error loading tools commands.", show_alert=True)
        print(f"Error in handle_tools_callback: {e}")


### cmds nuevo ###

@dp.message(Command('cmds', prefix=PREFIXES))
async def handle_cmds(message: types.Message):
    try:
        # Responder al usuario con un mensaje inicial para obtener el message_id
        sent_message = await message.reply("Hey, Welcome!")

        # Inicializar datos de paginación del usuario
        user_data_cmds[message.from_user.id] = {
            "page": 1,
            "message_id": sent_message.message_id
        }

        # Crear el teclado inicial y actualizar el mensaje
        initial_keyboard = await create_initial_keyboard(
            chat_id=message.chat.id,
            user_id=message.from_user.id,
            message_id=sent_message.message_id
        )  # Asegurarse de usar 'await' aquí

        await sent_message.edit_text("Hey, Welcome!", reply_markup=initial_keyboard)

        print(f"Chat ID: {message.chat.id}, User ID: {message.from_user.id}, Sent message ID: {sent_message.message_id}")

    except Exception as e:
        print(f"Error handling start command: {e}")

@dp.message(Command('bin', prefix=PREFIXES))
async def extrap_extrap(message: types.Message):
    if message.reply_to_message:
        args = message.reply_to_message.text
    else:
        args = message.text
    
    # Reemplaza cada prefijo en el texto del mensaje
    for prefix in command_prefixes:
        args = args.replace(f'{prefix}bin ', '')

    if args:
        # Eliminar cualquier carácter no numérico
        bin_number = ''.join(filter(str.isdigit, args))

        # Si el número es más largo que 6 dígitos, solo toma los primeros 6
        bin_number = bin_number[:6]

        if len(bin_number) == 6:
            await fetch_bin_and_send_message(message, bin_number)
        else:
            await message.answer('Enter a valid bin')
    else:
        await message.answer('Enter a valid bin')

@dp.message(Command('me', prefix=PREFIXES))
@insert_user_decorator
async def me_command(message: types.Message):
    if message.reply_to_message:
        user = message.reply_to_message.from_user
    else:
        user = message.from_user

    user_id = str(user.id)
    first_name = user.first_name or ""
    last_name = user.last_name or ""
    username = user.username or ""

    message_json = await get_user_info(user_id)

    user_info = json.loads(message_json)['users']
    premium_info = json.loads(message_json)['premiums']
    
    replymessage = "🍒 Cherry Scrapper 🍒\n"
    replymessage += "▬▬▬▬ [🍒] ▬▬▬▬▬\n"
    replymessage += f"➤ <b>ID</b> : <code>{user_info['userid']}</code>\n"
    replymessage += f"➤ <b>NAME</b> : {first_name} {last_name}\n"
    replymessage += f"▬▬▬▬ [🍒] ▬▬▬▬▬\n"
    replymessage += f"➤ <b>STATUS</b>: {str(premium_info['status']).upper()}\n"
    # Suponiendo que 'premium_info' es un diccionario
    acces_value = "True" if premium_info.get('acces') == 1 else "False"
    replymessage += f"➤ <b>ACCES</b>: {acces_value}\n"
    replymessage += f"➤ <b>CREDITS</b>: {premium_info['credits']}\n"
    replymessage += f"➤ <b>BANNED</b>: {user_info['banned']}\n"
    replymessage += f"➤ <b>ANTISPAM</b>: {premium_info['antispam']}\n"

    if user_info['FIN_fecha'] != "None":
        fin_fecha = datetime.strptime(user_info['FIN_fecha'], '%Y-%m-%d')
        dias_restantes = (fin_fecha - datetime.now()).days
        replymessage += f"➤ EXPIRED : {user_info['FIN_fecha']} ({dias_restantes} days left)\n"
    else:
        replymessage += "➤ EXPIRED : None\n"

    replymessage += f"➤ USER : @{username}\n"
    replymessage += "▬▬▬▬ [🍒] ▬▬▬▬▬\n"
    replymessage += "Bot: @CherryScrapperBot 🌤"

    await message.reply(str(replymessage), parse_mode='HTML')

### gen cmd ###
### gen cmd ###
### gen cmd ###
### gen cmd ###

@dp.message(Command('gen', prefix=PREFIXES))
async def gen_handler(message: Message) -> None:
    try:
        cmd = message.text.split(" ")[0].lower()  # Convierte el comando a minúsculas
        for prefix in PREFIXES:
            cmd = cmd.replace(prefix, '').lower()

        if message.reply_to_message:  # Si el mensaje es una respuesta a otro mensaje
            # Busca el texto en el mensaje al que se respondió usando el regex
            reply_text = message.reply_to_message.text
            match = re.search(r'\b\w{15,16}[^a-zA-Z0-9]*\d{2}[^a-zA-Z0-9]*\d{2,4}[^a-zA-Z0-9]*\w{3,4}\b', reply_text)
            if match:
                text = match.group()
            else:
                await message.reply("No se encontró ningún texto que coincida con el patrón en el mensaje respondido.")
                return
        else:
            text = message.text.split(" ")[1]  # Si no es una respuesta, toma el texto después del comando
        
        user_id = message.from_user.id
        username = message.from_user.username

        if user_id not in user_data:
            user_data[user_id] = {}
        
        user_data[user_id] = {
            'input': ''
        }

        gen_ccs = await Gen.GeneratedCC(text)

        builder = InlineKeyboardBuilder()
        builder.row(types.InlineKeyboardButton(text="REGEN ⚙️", callback_data=f"regen_{text}"))

        msg = await message.reply(
                f"{await response_gen_msg(text, gen_ccs, username, user_id)}",
                parse_mode="HTML",
                reply_markup=builder.as_markup()
            )

        user_data[user_id] = {
            'cmd': cmd,
            'resp_message_id': msg.message_id,
            'chat_id': message.chat.id,
            'user_id': user_id,
            'input': text
        }

    except Exception as e:
        print("error: " + str(e))

@dp.callback_query(F.data.startswith('regen_'))
async def callbacks_num(callback: types.CallbackQuery, bot: Bot):
    user_id = callback.from_user.id
    user_name = callback.from_user.username
    chat_id = user_data.get(user_id, {}).get('chat_id')
    msg_id = user_data.get(user_id, {}).get('resp_message_id')
    
    if chat_id is None or msg_id is None or user_data.get(user_id, {}).get('user_id') != callback.from_user.id:
        await bot.answer_callback_query(callback.id, text="Access denied. Please open your own session to use it ❌", show_alert=True)
        
        return
    
    regen_input = user_data.get(user_id, {}).get('input')

    if str(regen_input) == str(callback.data.split('_')[1]):
        print()
    else:
        regen_input = str(callback.data.split('_')[1])


    gen_ccs = await Gen.GeneratedCC(regen_input)

    builder = InlineKeyboardBuilder()
    builder.row(types.InlineKeyboardButton(text="REGEN ⚙️", callback_data=f"regen_{regen_input}"))

    message = await response_gen_msg(regen_input, gen_ccs, user_name, user_id)

    await bot.edit_message_text(message_id=msg_id, chat_id=chat_id, text=f'{message}', reply_markup=builder.as_markup(), parse_mode='HTML')


### end gen cmd##
### end gen cmd##
### end gen cmd##

@dp.message(Command('lookup', prefix=PREFIXES))
@insert_user_decorator
@user_not_banned
async def cherry_handler(message: types.Message):
    info = (
        "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n"
        "<b>➤ IP LOOKUP 🌐</b>\n"
        "<code>/lookup 0.0.0.0</code>\n"
        "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n"
    )

    # Extrae el argumento de la tarjeta de crédito del mensaje
    ip = message.text
    
    # Reemplaza cada prefijo en el texto del mensaje
    for prefix in command_prefixes:
        ip = ip.replace(f'{prefix}lookup ', '')

    if not ip:
        await message.reply(str(info), parse_mode='HTML')
        return

    ip_data, location_data = await lookup.lookup(ip)

    defmessage = (
            "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n"
            "<b>➤ IP LOOKUP 🌐</b>\n"
            "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n"
            f"<b>IP ➤ </b><code>{ip_data.get('ip')}</code>\n"
            f"<b>Score ➤ </b><code>{ip_data.get('score')}</code>\n"
            f"<b>Risk ➤ </b><code>{ip_data.get('risk')}</code>\n"
            f"<b>Organization Name ➤ </b> <code>{location_data.get('Organization Name')}</code>\n"
            f"<b>Country Name ➤ </b><code>{location_data.get('Country Name')}</code> | <code>{location_data.get('Country Code')}</code>\n"
            f"<b>Region ➤ </b><code>{location_data.get('Region')}</code>\n"
            f"<b>City ➤ </b><code>{location_data.get('City')}</code>\n"
            f"<b>Postal Code ➤ </b><code>{location_data.get('Postal Code')}</code>\n"
            "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n"
        )

    await message.reply(str(defmessage), parse_mode='HTML')

@dp.message(Command('rand', prefix=PREFIXES))
@insert_user_decorator
@user_not_banned
async def cherry_handler(message: types.Message):
    
    info = (
        "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n"
        "<b>➤ RANDOM ADDRESS 🌐</b>\n"
        "<code>/rand US</code>\n"
        "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n"
    )

    # Extrae el argumento de la tarjeta de crédito del mensaje
    code = message.text
    
    # Reemplaza cada prefijo en el texto del mensaje
    for prefix in command_prefixes:
        code = code.replace(f'{prefix}rand ', '')

    if not code:
        await message.reply(str(info), parse_mode='HTML')
        return

    address = await rand.rand(code)

    defmessage = (
            "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n"
            "<b>➤ RANDOM ADDRESS 🌐</b>\n"
            "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n"
            f"<b>Calle ➤ </b><code>{address['street']['name']} {address['street']['number']}</code>\n"
            f"<b>Ciudad ➤ </b><code>{address['city']}</code>\n"
            f"<b>Estado ➤ </b><code>{address['state']}</code>\n"
            f"<b>Código Postal ➤ </b><code>{address['postcode']}</code>\n"
            f"<b>País ➤ </b><code>{address['country']}</code>\n"
            "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n"
        )

    await message.reply(str(defmessage), parse_mode='HTML')

## EXTRA CMD ##

async def cmd_extra_wrapper(user_id, chat_id, bin, mes, ano, bot: Bot, database="ccs") -> None:
    try:  
        results = await collector.extra(database, bin, mes, ano)
    except Exception as e:
        print("Error in search:", str(e))
        await bot.send_message(chat_id, 'Invalid Search')
        return

    if not results:
        await bot.send_message(chat_id, "No results found.")
        return

    seen = set()
    unique_results = [x for x in results if not (x[0][0:12] in seen or seen.add(x[0][0:12]))]

    user_data_extra[user_id] = {
        'user_id': user_id,
        'results': unique_results,
        'page': 1,
        'chat_id': chat_id,
        'database': database
    }

    await send_results_extra(user_id, chat_id, bot)


async def send_results_extra(user_id, chat_id, bot: Bot) -> None:
    user_results_info = user_data_extra.get(user_id)
    if user_results_info is None:
        await bot.send_message(chat_id, "No results found.")
        return

    results = user_results_info['results']
    page = user_results_info['page']

    start_idx = (page - 1) * ITEMS_PER_PAGE
    end_idx = start_idx + ITEMS_PER_PAGE
    results_page = results[start_idx:end_idx]

    if not results_page:
        await bot.send_message(chat_id, "No more results.")
        return

    formatted_results = " Extra Result \n▬▬▬▬ [] ▬▬▬▬▬\n"
    formatted_results += '\n'.join([f" ➤ <code>{ccnum[0:12]}xxxx|{month}|{year}|xxx</code>" for ccnum, month, year in results_page])
    formatted_results += "\n▬▬▬▬ [] ▬▬▬▬▬"

    # Crear la estructura del teclado
    buttons = []
    buttons = []
    if page > 1:
        buttons.append(InlineKeyboardButton(text="◀️", callback_data=f"backextra_{user_id}"))
    if end_idx < len(results):
        buttons.append(InlineKeyboardButton(text="▶️", callback_data=f"nextextra_{user_id}"))

    keyboard = InlineKeyboardMarkup(inline_keyboard=[buttons])

    try:
        if user_results_info.get('message_id'):
            await bot.edit_message_text(
                chat_id=chat_id,
                message_id=user_results_info['message_id'],
                text=formatted_results,
                reply_markup=keyboard,
                parse_mode='HTML'
            )
        else:
            sent_message = await bot.send_message(
                chat_id=chat_id,
                text=formatted_results,
                reply_markup=keyboard,
                parse_mode='HTML'
            )
            user_results_info['message_id'] = sent_message.message_id
    except Exception as e:
        print("Error sending results:", str(e))
    finally:
        del user_semaphores[user_id]



@dp.message(Command('extra', prefix=PREFIXES))
@is_premium_user
@user_not_banned
async def Aextra(message: types.Message) -> None:
    try:
        user_id = message.from_user.id
        chat_id = message.chat.id
        bot = message.bot  # Obteniendo el bot desde el message

        args = message.text.split()[1:]

        if not args:
            await bot.send_message(chat_id, "Necesitas enviar un bin")
            return

        expresion = args[0].split("|")
        bin = expresion[0]
        if len(bin) > 8:
            bin = bin[:6]
        elif len(bin) == 8:
            bin = bin[:8]
        elif len(bin) == 6:
            bin = bin[:6]
        mes = expresion[1] if len(expresion) > 1 else None
        ano = expresion[2] if len(expresion) > 2 else None 

        if user_id not in user_semaphores:
            user_semaphores[user_id] = asyncio.Semaphore()

        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="Global", callback_data="ccs")],
            [InlineKeyboardButton(text="Braintree", callback_data="braintree")],
            [InlineKeyboardButton(text="Stripe", callback_data="stripe")],
            [InlineKeyboardButton(text="Square", callback_data="square")],
            [InlineKeyboardButton(text="Scam", callback_data="scam")]
        ])

        database_data[user_id] = {
            'chat_id': chat_id,
            'bin': bin,
            'mes': mes,
            'ano': ano
        }

        selection_message = await bot.send_message(chat_id, "Por favor, selecciona la base de datos:", reply_markup=keyboard)
        database_data[user_id]['selection_message_id'] = selection_message.message_id

    except Exception as e:
        print(e)


@dp.callback_query(F.data.in_(["ccs", "braintree", "stripe", "square", "scam"]))
async def process_callback(callback_query: types.CallbackQuery) -> None:
    database = callback_query.data
    user_id = callback_query.from_user.id
    bot = callback_query.bot  # Obteniendo el bot desde el callback_query

    data = database_data.get(user_id)

    if data:
        chat_id = data['chat_id']
        bin = data['bin']
        mes = data['mes']
        ano = data['ano']

        await bot.delete_message(chat_id, data['selection_message_id'])

        async with user_semaphores[user_id]:
            await cmd_extra_wrapper(user_id, chat_id, bin, mes, ano, bot)


@dp.callback_query(F.data.startswith("backextra_") | F.data.startswith("nextextra_"))
async def handle_pagination(callback_query: types.CallbackQuery) -> None:
    user_id = callback_query.from_user.id
    user_results_info = user_data_extra.get(user_id)
    bot = callback_query.bot  # Obteniendo el bot desde el callback_query

    if user_results_info is None or user_results_info['message_id'] != callback_query.message.message_id:
        await bot.answer_callback_query(callback_query.id, text="Access denied. Please open your own session to use it ❌", show_alert=True)
        return

    page = user_results_info['page']
    action, _ = callback_query.data.split('_')

    if action == 'backextra' and page > 1:
        page -= 1
    elif action == 'nextextra':
        page += 1

    user_results_info['page'] = page

    try:
        await send_results_extra(user_id, callback_query.message.chat.id, bot)
    except Exception as e:
        # print("Error handling pagination:", str(e))
        pass

async def search(request):
    try:
        conn = await ConnectDB.ConnectDB('bin_info')

        keywords = request.split("|")

        # Utiliza comprensión de listas para generar las condiciones
        conditions = [f"(BANK LIKE '%{keyword}%' OR BRAND LIKE '%{keyword}%' OR TYPE LIKE '%{keyword}%' OR LEVEL LIKE '%{keyword}%' OR COUNTRY LIKE '%{keyword}%' OR ISO1 LIKE '%{keyword}%' OR WEB_BANK LIKE '%{keyword}%' OR NUMBER_BANK LIKE '%{keyword}%' OR ISSUER LIKE '%{keyword}%' OR CODE_MOGI LIKE '%{keyword}%')" for keyword in keywords]

        query = "SELECT * FROM bin2 WHERE " + " AND ".join(conditions)

        cursor = await conn.cursor()
        await cursor.execute(query)
        results = await cursor.fetchall()

        return results

    except Exception as e:
        print("Error in database connection:", str(e))
        return []

@dp.message(Command('search', prefix=PREFIXES))
@is_premium_user
@user_not_banned
async def cmd_search(message: types.Message, bot: Bot) -> None:
    user_id = message.from_user.id
    chat_id = message.chat.id

    full_text = message.text

    # Reemplaza cada prefijo en el texto del mensaje
    for prefix in PREFIXES:
        full_text = full_text.replace(f'{prefix}search ', '')

    args = full_text

    if not args:
        await bot.send_message(chat_id, 'You need to send a search query')
        return

    # Utilizar Semaphore para controlar el acceso a la función cmd_search_wrapper
    if user_id not in user_semaphores:
        user_semaphores[user_id] = asyncio.Semaphore()

    async with user_semaphores[user_id]:
        await cmd_search_wrapper(user_id, chat_id, args, bot, user_data)

async def cmd_search_wrapper(user_id, chat_id, args, bot, user_data) -> None:
    try:
        results = await search(args)
    except Exception as e:
        print("Error in search:", str(e))
        await bot.send_message(chat_id, 'Invalid Search')
        return

    if not results:
        await bot.send_message(chat_id, "No results found.")
        return

    user_data[user_id] = {
        'user_id': user_id,
        'results': results,
        'page': 1,
        'chat_id': chat_id
    }

    await send_results(user_id, chat_id, bot, user_data)

async def execute_query(bin_number, table_name):
    bin_pool = await ConnectDB.get_pool('cherrydatabase')
    async with bin_pool.acquire() as conn:
        async with conn.cursor() as cur:
            await cur.execute(f"SELECT COUNT(*) FROM {table_name} WHERE ccnum LIKE '{bin_number}%'")
            result = await cur.fetchone()
            return result[0] if result else 0

async def get_pool(database):
    if bin_pool is None:
        try:
            bin_pool = await aiomysql.create_pool(
                host='localhost',
                user='CherryDatabase',
                password='Elprogames109;',
                db=database,
                autocommit=True,
                charset='utf8mb4'
            )
        except (aiomysql.OperationalError, aiomysql.InterfaceError):
            print("Connection lost. Reconnecting...")
            bin_pool = await aiomysql.create_pool(
                host='localhost',
                user='CherryDatabase',
                password='Elprogames109;',
                db=database,
                autocommit=True,
                charset='utf8mb4'
            )
    return bin_pool

async def execute_query(bin_number, table_name):
    bin_pool = await get_pool()
    async with bin_pool.acquire() as conn:
        async with conn.cursor() as cur:
            await cur.execute(f"SELECT COUNT(*) FROM {table_name} WHERE ccnum LIKE '{bin_number}%'")
            result = await cur.fetchone()
            return result[0] if result else 0

async def get_ccs_data(bin_number):
    return await execute_query(bin_number, 'ccs')

async def get_braintree_data(bin_number):
    return await execute_query(bin_number, 'braintree')

async def get_stripe_data(bin_number):
    return await execute_query(bin_number, 'stripe')

async def get_square_data(bin_number):
    return await execute_query(bin_number, 'square')

async def get_square_data(bin_number):
    return await execute_query(bin_number, 'scam')

async def send_results(user_id, chat_id, bot: Bot, user_data) -> None:
    user_results_info = user_data.get(user_id)
    if user_results_info is None:
        await bot.send_message(chat_id, "No results found.")
        return

    results = user_results_info['results']
    page = user_results_info['page']

    start_idx = (page - 1) * ITEMS_PER_PAGE
    end_idx = start_idx + ITEMS_PER_PAGE
    results_page = results[start_idx:end_idx]

    if not results_page:
        await bot.send_message(chat_id, "No more results.")
        return

    formatted_results = "🍒 Search Bin Result 🍒\n"
    formatted_results += "▬▬▬▬ [🍒] ▬▬▬▬▬\n"

    for result in results_page:
        bin_number, bank, brand, card_type, level, country, iso1, web_bank, number_bank, issuer, code_mogi = result

        formatted_result = f"<code>/extra {bin_number}</code> ✦ <i>{brand}</i> - <i>{card_type}</i> - <i>{level}</i> ✦ <b>{bank}</b> ✦ {code_mogi} \n"

        # Verificamos si el bin existe en la base de datos ccs
        global_items = await collector.execute_query(bin_number, "ccs")
        if global_items is not None:
            formatted_result += f"🌐 [{global_items}]"
        else:
            formatted_result += " | Error retrieving ccs data"
        b3_items = await collector.execute_query(bin_number, "braintree")
        if global_items is not None:
            formatted_result += f" | 🌳 [{b3_items}]"
        else:
            formatted_result += " | Error retrieving ccs data"
        square_items = await collector.execute_query(bin_number, "square")
        if global_items is not None:
            formatted_result += f" | 🔳 [{square_items}]"
        else:
            formatted_result += " | Error retrieving ccs data"       
        stp_items = await collector.execute_query(bin_number, "stripe")
        if global_items is not None:
            formatted_result += f" | ⚡️ [{stp_items}]"
        else:
            formatted_result += " | Error retrieving ccs data"
        scam_items = await collector.execute_query(bin_number, "scam")
        if global_items is not None:
            formatted_result += f" | ⚠️ [{scam_items}]\n\n"
        else:
            formatted_result += " | Error retrieving ccs data\n\n"

        formatted_results += formatted_result

    formatted_results += "▬▬▬▬ [🍒] ▬▬▬▬▬\n"

    buttons = []
    if page > 1:
        buttons.append(InlineKeyboardButton(text="◀️", callback_data=f"back_{user_id}"))
    if end_idx < len(results):
        buttons.append(InlineKeyboardButton(text="▶️", callback_data=f"next_{user_id}"))

    keyboard = InlineKeyboardMarkup(inline_keyboard=[buttons])
    try:
        if user_results_info.get('message_id'):
            await bot.edit_message_text(
                chat_id=chat_id,
                message_id=user_results_info['message_id'],
                text=formatted_results,
                reply_markup=keyboard,
                parse_mode='HTML'
            )
        else:
            sent_message = await bot.send_message(
                chat_id=chat_id,
                text=formatted_results,
                reply_markup=keyboard,
                parse_mode='HTML'
            )
            user_results_info['message_id'] = sent_message.message_id
    except Exception as e:
        print("Error sending results:", str(e))
    finally:
        if user_id in user_semaphores:
            del user_semaphores[user_id]

@dp.callback_query(F.data.startswith("back_") | F.data.startswith("next_"))
async def handle_pagination(callback_query: CallbackQuery, bot: Bot) -> None:
    user_id = callback_query.from_user.id
    user_results_info = user_data.get(user_id)

    if user_results_info is None or user_results_info['message_id'] != callback_query.message.message_id:
        await bot.answer_callback_query(callback_query.id, text="Access denied. Please open your own session to use it ❌", show_alert=True)
        return

    # Verificar si el semáforo existe para el usuario, si no, crear uno
    if user_id not in user_semaphores:
        user_semaphores[user_id] = asyncio.Semaphore()

    # Utilizar el semáforo para controlar el acceso concurrente
    async with user_semaphores[user_id]:
        page = user_results_info['page']
        action, _ = callback_query.data.split('_')

        if action == 'back' and page > 1:
            page -= 1
        elif action == 'next':
            page += 1

        user_results_info['page'] = page

        try:
            await send_results(user_id, callback_query.message.chat.id, bot, user_data)
        except Exception as e:
            # Manejar la excepción como sea necesario
            pass

# Configuración del cliente Telethon
api_id = '28488331'
api_hash = '3854abd17e572a96bcc7dbfd850ffd57'
FILE_PATH = '/home/<USER>/Documents/TODO/utils/cards.txt'  # Ruta del archivo temporal
client = TelegramClient('session_name', api_id, api_hash)

# Tu regex para buscar tarjetas de crédito
card_info_list = r"\b(\d{15,16})\s*[^\d]*\s*(\d{1,2})\s*[^\d]*\s*(\d{2,4})\s*[^\d]*\s*(\d{3,4})\b"

# Función para obtener mensajes de un grupo o canal
async def get_messages_from_channel(link, card_limit):
    async with client:
        entity = await client.get_entity(link)
        messages = []
        offset_id = 0
        limit = min(card_limit * 5, 1000)  # Límite inicial basado en la cantidad de tarjetas solicitadas

        while True:
            batch = await client(GetHistoryRequest(
                peer=entity,
                limit=limit,
                offset_date=None,
                offset_id=offset_id,
                max_id=0,
                min_id=0,
                add_offset=0,
                hash=0
            ))

            if not batch.messages:
                break

            messages.extend(batch.messages)
            offset_id = batch.messages[-1].id

            # Incrementar el límite de mensajes dinámicamente
            if len(messages) < card_limit * 5:
                limit = min(limit * 2, 1000)  # Ajustar el factor de incremento y el límite máximo según sea necesario
            else:
                break

        text = ' '.join([message.message for message in messages if message.message])
        return text

# Comando para procesar el grupo o canal en aiogram
@dp.message(Command('scr', prefix=PREFIXES))
async def process_scr_command(message: types.Message):
    try:
        # Extraer el link y la cantidad de tarjetas solicitadas
        args = message.text.split('|')
        if len(args) != 2:
            await message.reply("Formato incorrecto. Usa /scr {link}|{cantidad}.")
            return

        link = args[0].split()[1]  # {link} del canal/grupo
        card_limit = int(args[1])  # {cantidad} de tarjetas

        # Obtener mensajes del grupo o canal usando Telethon
        text = await get_messages_from_channel(link, card_limit)

        # Buscar coincidencias con el regex
        matches = re.findall(card_info_list, text)

        if len(matches) == 0:
            await message.reply("No se encontraron coincidencias en el grupo/canal.")
            return

        # Seleccionar coincidencias aleatorias o menos si no hay tantas
        selected_matches = random.sample(matches, min(card_limit, len(matches)))

        # Crear el archivo en memoria
        file_content = io.StringIO()
        for match in selected_matches:
            card_str = f"{match[0]}|{match[1]}|{match[2]}|{match[3]}\n"
            file_content.write(card_str)
        file_content.seek(0)

        # Crear un BufferedInputFile desde el archivo en memoria
        file_to_send = BufferedInputFile(file_content.getvalue().encode(), filename='cards.txt')

        # Enviar el archivo al usuario
        await message.reply_document(file_to_send)

    except Exception as e:
        await message.reply(f"Error procesando el comando: {str(e)}")

### ADMINISTRACCION ###
### ADMINISTRACCION ###
### ADMINISTRACCION ###

ODS_FILE_PATH = '/home/<USER>/Documents/TODO/utils/Cuentas Cherry Scrapper.ods'

staffs = ["5340466628", "7001838469"]

@dp.message(Command('enviarspam', prefix=PREFIXES))
async def send_message_to_channels(message: types.Message, bot: Bot):
    channels = ["-1002421770235"]
    mensaje_bot = "Este es el mensaje del bot que durará 2 horas."
    mensajes_enviados = {}
    
    for canal in channels:
        try:
            sent_message = await bot.send_message(chat_id=canal, text=mensaje_bot)
            mensajes_enviados[canal] = sent_message.message_id
        except Exception as e:
            await message.answer(f"Error al enviar mensaje al canal {canal}: {e}")

    start_time = time.time()
    while time.time() - start_time < 7200:
        for canal, message_id in list(mensajes_enviados.items()):
            try:
                # Verificar si el mensaje del bot sigue existiendo
                await bot.get_message(chat_id=canal, message_id=message_id)
            except Exception:
                # Si el mensaje fue eliminado, lo reenvía
                try:
                    new_message = await bot.send_message(chat_id=canal, text=mensaje_bot)
                    mensajes_enviados[canal] = new_message.message_id
                except Exception as e:
                    await message.answer(f"Error al reenviar mensaje al canal {canal}: {e}")
            
            # Obtener y procesar mensajes nuevos
            try:
                # Obtener mensajes recientes (limitado a 100 para evitar sobrecarga)
                recent_messages = await bot.get_messages(chat_id=canal, limit=10)
                for msg in recent_messages:
                    if msg.message_id != mensajes_enviados[canal] and msg.date.timestamp() > start_time:
                        try:
                            # Eliminar mensajes que no son del bot
                            await bot.delete_message(chat_id=canal, message_id=msg.message_id)
                        except Exception as e:
                            await message.answer(f"Error al eliminar mensaje en el canal {canal}: {e}")
            except Exception as e:
                await message.answer(f"Error al obtener mensajes recientes del canal {canal}: {e}")

        await asyncio.sleep(10)

    # Eliminar los mensajes del bot después de 2 horas
    for canal, message_id in mensajes_enviados.items():
        try:
            await bot.delete_message(chat_id=canal, message_id=message_id)
        except Exception as e:
            await message.answer(f"Error al eliminar el mensaje del canal {canal}: {e}")

    await message.answer("Operación de spam completada.")

@dp.message(Command('añadir', prefix=PREFIXES))
async def añadir_pago(message: types.Message, bot: Bot):
    user_id = str(message.from_user.id)
    user = message.from_user
    username = user.username or ""
    
    if user_id not in staffs:
        return

    args = message.text.split()
    if len(args) != 4:
        await message.answer("Formato incorrecto. Usa: /añadir Pago nombre $monto")
        return

    tipo, nombre, monto = args[1], args[2], args[3]
    monto = monto.replace('$', '').strip()  # Eliminar el símbolo de dólar si está presente

    try:
        # Leer el archivo .ods
        data = pyexcel_ods.get_data(ODS_FILE_PATH)

        # Obtener la hoja (nombre de la hoja es 'Sheet1', cambiar si es necesario)
        hoja = data.get('Sheet1', [])

        # Obtener la fecha actual y el mes
        fecha_actual = datetime.now().strftime('%m/%d/%Y')
        mes_actual = datetime.now().strftime('%B').lower()

        # Añadir la nueva fila
        nueva_fila = ['', fecha_actual, mes_actual, f"{tipo} {nombre}", float(monto)]
        hoja.append(nueva_fila)

        # Guardar los cambios en el archivo .ods
        data['Sheet1'] = hoja
        pyexcel_ods.save_data(ODS_FILE_PATH, data)

        await bot.send_message("-*************", f"Nuevo pago añadido: {tipo} {nombre} ${monto}")
    except Exception as e:
        await message.reply(f"Error al añadir el pago: {str(e)}")

@dp.message(Command('addcredits', prefix=PREFIXES))
async def add_credits(message: types.Message, bot: Bot):
    user_id = str(message.from_user.id)
    user = message.from_user
    username = user.username or ""

    if user_id not in staffs:
        return

    args = message.text.split(" ")[1]

    if not args:
        await message.answer("No params provided")
        return

    params = args.split("|")

    if len(params) != 2:
        await message.answer("Too many params")
        return

    user_id, credits = params

    # Conexión a la base de datos
    connection = await ConnectDB.ConnectDB("cherryscrappbot")

    async with connection.cursor() as cursor:
        # Consulta la tabla 'users' por user_id
        await cursor.execute("SELECT credits FROM premiums WHERE userid = %s", (user_id,))
        result = await cursor.fetchone()
        if result:
            current_credits = result[0]
            # Calcular los nuevos créditos
            new_credits = current_credits + int(credits)
            # Actualizar los créditos del usuario en la base de datos
            await cursor.execute("UPDATE premiums SET credits = %s WHERE userid = %s", (new_credits, user_id))
            await connection.commit()
            # Enviar un mensaje de confirmación al usuario
            await message.reply(f"Se han añadido {credits} créditos al usuario con ID {user_id}. Total de créditos: {new_credits}")
        else:
            await message.reply("No se encontró al usuario en la base de datos.")

@dp.message(Command('premium', prefix=PREFIXES))
async def premium_cmd(message: types.Message, bot: Bot) -> None:
    user_id = str(message.from_user.id)
    user = message.from_user
    username = user.username or ""

    if user_id not in staffs:
        return

    args = message.text.split(" ")[1]

    if not args:
        await message.answer("No params provided")
        return

    params = args.split("|")

    if len(params) != 4:
        await message.answer("Too many or too few params")
        return

    userid, status_initial, days, acces = params

    if status_initial not in ["p", "v", "g", "o"]:
        await message.answer("Invalid status. Use 'p' for premium, 'v' for vip, or 'g' for god")
        return

    status_mapping = {
        "p": "premium",
        "v": "vip",
        "g": "god",
        "o": "owner"
    }

    status = status_mapping[status_initial]

    if acces not in ["0", "1"]:
        await message.answer("Only 0 or 1")
        return

    connection = await ConnectDB.ConnectDB("cherryscrappbot")
    await insert_premium(userid, status, int(days), int(acces), connection)

    group_ids = {"-1001999254458": "CherryMembers 🍒",
                    "-1002009509519":"CherryScrapper 🌐",
                    "-1002052241754":"CherryBraintree 🌳",
                    "-1002042701096":"CherryDeclineds ❌",
                    "-1002107820813":"CherryStripe ⚡",
                    "-1001991135719":"CherrySquare 🔳",
                    "-1002106884930":"CherryScam ⚠️"}
    
    invite_links = []
    invite_group_names = []

    if status in ["premium", "god", "owner"]:
        # Include all groups for premium or god
        selected_groups = group_ids.keys()
    elif status == "vip":
        # Include only CherryMembers for vip
        selected_groups = ["-1001999254458"]

    for group_id in selected_groups:
        try:
            member = await bot.get_chat_member(group_id, userid)
        except Exception as e:
            member = None

        if member is None or member.status in ['left', 'kicked']:
            if member and member.status == 'kicked':
                await bot.unban_chat_member(group_id, userid)

            invite_link = await bot.create_chat_invite_link(
                group_id,
                member_limit=1,  # Limit the link to one person
                expire_date=int(time.time()) + 60*60  # The link expires in 1 hour
            )
            invite_links.append(invite_link.invite_link)
            invite_group_names.append(group_ids[group_id])
    
    builder = InlineKeyboardBuilder()

    if invite_links:
        for i in range(len(invite_links)):
            builder.row(types.InlineKeyboardButton(text=invite_group_names[i], url=f'{invite_links[i]}'))
        replymsg = "▬▬▬▬ [🍒] ▬▬▬▬▬\n"
        replymsg += f"Welcome to <b>Cherry{status.capitalize()}</b> for <code>{days}</code> day(s)\n"
        replymsg += "Please JOIN groups down here (we will not resend once expired)\n"
        replymsg += "▬▬▬▬ [🍒] ▬▬▬▬▬\n\n"
        await bot.send_message(userid, f'{replymsg}', parse_mode='HTML', reply_markup=builder.as_markup())
    else:
        replymsg = "▬▬▬▬ [🍒] ▬▬▬▬▬\n"
        replymsg += f"An admin has added <code>{days}</code> day(s) to your account\n"
        replymsg += "It looks like you are already part of the groupchats!\n"
        replymsg += "▬▬▬▬ [🍒] ▬▬▬▬▬\n"
        await bot.send_message(userid, f'{replymsg}', parse_mode='HTML')

    await bot.send_message("-*************", f'@{username} has given {status} to\n▬▬▬▬ [🍒] ▬▬▬▬▬\n <a href="tg://user?id={userid}">{userid}</a> \n <b>DAYS</b>: {days} \n <b>ACCES</b>:{acces}\n▬▬▬▬ [🍒] ▬▬▬▬▬\n', parse_mode='HTML')

@dp.message(Command('ban', prefix=PREFIXES))
async def ban_user(message: types.Message, bot: Bot) -> None:
    user_id = str(message.from_user.id)

    if user_id not in staffs:
        return
    else:
        pass

    if message.reply_to_message:
        usertoban = message.reply_to_message.from_user.id
    else:
        usertoban = message.text.split(" ")[1]

    # Conexión a la base de datos
    connection = await ConnectDB.ConnectDB("cherryscrappbot")
    
    async with connection.cursor() as cursor:
        # Actualización del campo 'banned' a 'YES'
        await cursor.execute("UPDATE users SET banned = 'YES' WHERE userid = %s", (usertoban,))
    
    # Expulsar al usuario de los chats
    chat_ids = ["-1002009509519", "-1002107820813", "-1002042701096", "-1001991135719", "-1001999254458", "-1002052241754", "-1002106884930"]
    for chat_id in chat_ids:
        await bot.ban_chat_member(chat_id, usertoban)

    await message.reply(f"El usuario {usertoban} ha sido baneado.")


@dp.message(Command('unban', prefix=PREFIXES))
async def ban_user(message: types.Message):

    user_id = str(message.from_user.id)

    if user_id not in staffs:
        return
    else:
        pass
    

    if message.reply_to_message:
        usertoban = message.reply_to_message.from_user.id
    else:
        usertoban = message.text.split(" ")[1]

    # Conexión a la base de datos
    connection = await ConnectDB.ConnectDB("cherryscrappbot")
    
    async with connection.cursor() as cursor:
        # Actualización del campo 'banned' a 'YES'
        await cursor.execute("UPDATE users SET banned = 'NO' WHERE userid = %s", (usertoban,))
    
    await message.reply(f"El usuario {usertoban} ha sido liberado.")

@dp.message(Command('info', prefix=PREFIXES))
async def info_command(message: types.Message):
    useraid = str(message.from_user.id)
    if useraid not in staffs:
        return
    else:
        pass

    args = message.text.split(" ")[1]
    if not args:
        await message.reply("Por favor, proporciona un ID de usuario.")
        return

    user_id = args.strip()

    message_json = await get_user_info(user_id)

    user_info = json.loads(message_json)['users']
    premium_info = json.loads(message_json)['premiums']

    replymessage = "🍒 Cherry Scrapper 🍒\n"
    replymessage += "▬▬▬▬ [🍒] ▬▬▬▬▬\n"
    replymessage += f"➤ <b>ID</b> : <code>{user_info['userid']}</code>\n"
    replymessage += f"▬▬▬▬ [🍒] ▬▬▬▬▬\n"
    replymessage += f"➤ <b>STATUS</b>: {premium_info['status']}\n"
    acces_value = "True" if premium_info.get('acces') == 1 else "False"
    replymessage += f"➤ <b>ACCES</b>: {acces_value}\n"
    replymessage += f"➤ <b>CREDITS</b>: {premium_info['credits']}\n"
    replymessage += f"➤ <b>BANNED</b>: {user_info['banned']}\n"

    if user_info['FIN_fecha'] != "None":
        fin_fecha = datetime.strptime(user_info['FIN_fecha'], '%Y-%m-%d')
        dias_restantes = (fin_fecha - datetime.now()).days
        replymessage += f"➤ EXPIRED : {user_info['FIN_fecha']} ({dias_restantes} days left)\n"
    else:
        replymessage += "➤ EXPIRED : None\n"

    replymessage += "▬▬▬▬ [🍒] ▬▬▬▬▬\n"
    replymessage += "Bot: @CherryScrapperBot 🌤"

    await message.reply(str(replymessage), parse_mode='HTML')

@dp.message(Command('reset', prefix=PREFIXES))
async def reset_command(message: types.Message):
    useraid = str(message.from_user.id)
    if useraid not in staffs:
        return
    else:
        pass

    if message.reply_to_message:
        usertoreset = message.reply_to_message.from_user.id
    else:
        usertoreset = message.text.split(" ")[1]

    connection = await ConnectDB.ConnectDB("cherryscrappbot")
    
    async with connection.cursor() as cursor:
        await cursor.execute("UPDATE users SET banned = 'NO' WHERE userid = %s", (usertoreset,))
        await cursor.execute("UPDATE premiums SET status = 'free', acces = 0 WHERE userid = %s", (usertoreset,))
        await cursor.execute("UPDATE users SET FIN_fecha = NULL WHERE userid = %s", (usertoreset,))
    
    await message.reply(f"El usuario {usertoreset} ha sido reseteau")

@dp.message(Command('anuncio', prefix=PREFIXES))
@is_premium_user
@user_not_banned
async def notify_premium(message: types.Message, bot: Bot):
    user_id = str(message.from_user.id)

    # Verificación de staff
    if user_id not in staffs:
        return

    premium_users = await get_all_premium_users()
    
    # Si el mensaje es una respuesta a otro mensaje
    if message.reply_to_message:
        for user_id in premium_users:
            try:
                await bot.forward_message(user_id, message.chat.id, message.reply_to_message.message_id)
            except Exception as e:
                print(f"Error al reenviar mensaje a {user_id}: {e}")
    else:
        # Extrae el argumento de la tarjeta de crédito del mensaje
        custom_message = message.text
        
        # Reemplaza cada prefijo en el texto del mensaje
        for prefix in command_prefixes:
            custom_message = custom_message.replace(f'{prefix}anuncio ', '')
        if not custom_message:
            await message.answer("Por favor, proporciona un mensaje para enviar a los usuarios premium.")
            return

        for user_id in premium_users:
            try:
                await bot.send_message(chat_id=user_id, text=f"▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n<b>{custom_message}</b>\n▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n", parse_mode="html")
            except Exception as e:
                print(f"Error al enviar mensaje a {user_id}: {e}")

def save_json(data):
    with open('/home/<USER>/Documents/TODO/utils/cherry.json', 'w') as f:
        json.dump(data, f, indent=4)

@dp.message(Command('gate', prefix=PREFIXES))
@is_premium_user
@user_not_banned
async def toggle_gate_handler(message: types.Message, bot: Bot) -> None:
    user_id = str(message.from_user.id)

    if user_id not in staffs:
        return
    try:
        # Extraer el comando y los argumentos
        cmd_args = message.text.split(" ", 1)[1]
        gate_name, attribute, value = cmd_args.split("/", 2)
        gate_name = gate_name.strip().lower()
        attribute = attribute.strip().lower()
        value = value.strip()

        # Atributos permitidos
        allowed_attributes = ["status", "antispam", "cost", "delete", "desc", "gate_name", "rango", "massive", "last-update"]
        if attribute not in allowed_attributes:
            await message.answer(f"Invalid attribute. Use one of {', '.join(allowed_attributes)}.")
            return

        # Buscar y actualizar el atributo del gateway en el JSON
        gate_found = False
        for category in ["ccn_auth", "ccn_charge", "cvv_auth", "cvv_charge"]:
            if gate_name in data["cmds"]["gateways"][category]:
                if attribute == "delete":
                    del data["cmds"]["gateways"][category][gate_name]
                    message_text = f"Gateway '{gate_name}' has been deleted."
                elif attribute == "gate_name":
                    new_gate_name = value.lower()
                    data["cmds"]["gateways"][category][new_gate_name] = data["cmds"]["gateways"][category].pop(gate_name)
                    message_text = f"Gateway '{gate_name}' has been renamed to '{new_gate_name}'."
                else:
                    data["cmds"]["gateways"][category][gate_name][attribute] = value
                    message_text = f"Gateway '{gate_name}' has been updated: {attribute} set to {value}."
                gate_found = True
                break

        if gate_found:
            # Guardar los cambios en el archivo JSON
            save_json(data)
            await message.answer(message_text)
        else:
            await message.answer(f"Gateway '{gate_name}' not found.")

    except ValueError:
        await message.answer("Invalid command format. Use /gate {nombre del gate}/{atributo}/{valor}")
    except Exception as e:
        await message.answer(f"An error occurred: {e}")


@dp.message(Command('newgate', prefix=PREFIXES))
@is_premium_user
@user_not_banned
async def new_gate_handler(message: types.Message, bot: Bot) -> None:
    user_id = str(message.from_user.id)

    if user_id not in staffs:
        return
    try:
        # Extraer el comando y los argumentos
        cmd_args = message.text.split(" ", 1)[1]
        parts = cmd_args.split("/")
        
        if len(parts) != 9:
            await message.answer("Invalid command format. Use /newgate {nombre del gate}/{categoria}/{status}/{desc}/{antispam}/{cost}/{last-update}/{rango}/{massive}")
            return

        gate_name, category, status, desc, antispam, cost, last_update, rango, massive = parts
        gate_name = gate_name.strip().lower()
        category = category.strip().lower()

        if category not in ["ccn_auth", "ccn_charge", "cvv_auth", "cvv_charge"]:
            await message.answer("Invalid category. Use one of ccn_auth, ccn_charge, cvv_auth, cvv_charge.")
            return

        status = status.strip().lower()
        desc = desc.strip()
        antispam = antispam.strip()
        cost = cost.strip()
        last_update = last_update.strip()
        rango = rango.strip().lower()
        massive = massive.strip().lower()

        new_gate = {
            "status": status,
            "desc": desc,
            "antispam": antispam,
            "cost": cost,
            "last-update": last_update,
            "rango": rango,
            "massive": massive
        }

        # Agregar el nuevo gateway al JSON
        data["cmds"]["gateways"][category][gate_name] = new_gate

        # Guardar los cambios en el archivo JSON
        save_json(data)
        await message.answer(f"New gateway '{gate_name}' has been created in category '{category}'.")

    except ValueError:
        await message.answer("Invalid command format. Use /newgate {nombre del gate}/{categoria}/{status}/{desc}/{antispam}/{cost}/{last-update}/{rango}/{massive}")
    except Exception as e:
        await message.answer(f"An error occurred: {e}")

@dp.message(Command('guardar', prefix=PREFIXES))
@is_premium_user
@user_not_banned
async def save_file_handler(message: types.Message, bot: Bot) -> None:
    user_id = str(message.from_user.id)

    if user_id not in staffs:
        return
    # Verificar que haya una respuesta a un mensaje y que sea un documento
    if message.reply_to_message and message.reply_to_message.document:
        document = message.reply_to_message.document
        
        # Obtener la ruta especificada después del comando /guardar
        command_parts = message.text.split(" ", 1)
        if len(command_parts) != 2:
            await message.answer("Formato incorrecto. Uso: /guardar <ruta>")
            return
        
        save_path = command_parts[1]

        # Guardar el archivo en la ruta especificada
        try:
            file_info = await bot.get_file(document.file_id)
            file_path = file_info.file_path
            downloaded_file = await bot.download_file(file_path)

            # Construir la ruta completa para guardar el archivo
            file_name = document.file_name
            full_save_path = os.path.join(save_path, file_name)

            # Guardar el archivo en la ruta especificada
            with open(full_save_path, 'wb') as new_file:
                new_file.write(downloaded_file.read())

            await message.answer(f"Archivo guardado exitosamente en {full_save_path}")
        
        except Exception as e:
            await message.answer(f"No se pudo guardar el archivo: {e}")

    else:
        await message.answer("Por favor, responde a un archivo para poder guardarlo.")

### GATES ###  
### GATES ###  
### GATES ###   

# Define un diccionario para almacenar los contadores de solicitudes por usuario
request_counters = {}

last_command_usage = {}

exempted_ids = [5340466628, 7001838469]  # IDs de usuarios exentos

async def handle_antispam(user_id, cooldown_time, exempted_ids):
    # Convertir cooldown_time a float
    cooldown_time = float(cooldown_time)

    if user_id in exempted_ids:
        return True, 0  # El usuario está exento del antispam, no hay tiempo restante
    if user_id in last_command_usage:
        last_usage_time = last_command_usage[user_id]
        time_elapsed = time.time() - last_usage_time
        if time_elapsed < cooldown_time:
            return False, int(cooldown_time - time_elapsed)  # El usuario está bajo el tiempo de espera del antispam, retorna True y el tiempo restante
    return True, 0  # El usuario puede continuar, no hay tiempo restante

### NUEVAS COSAS ###

with open("/home/<USER>/Documents/TODO/utils/cherry.json", 'r', encoding='utf-8') as f:
    data = json.load(f)

async def bin_loader(bin):
    bin_fetcher = collector.BinInfoFetcher()
    await bin_fetcher.connect_db()
    bin_info = await bin_fetcher.fetch_bin_info(bin)

    return bin_info

def checking_msg(cc, desc):
    splitter = cc.split('|')
    ccnum    = splitter[0]
    bin = ccnum[:6]
    text = f"∙ • <b>Testing Card</b> | #BIN{bin} • ∙\n"
    text += "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n"
    text += f"<b><i>{desc}</i></b>\n"
    text += f"✧ <code>{cc}</code>\n"
    text += f"⚝ <b><i>This may take some time!</i></b> ⏳\n"
    text += "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n"

    return text

async def response_msg_premium(cc, desc, response, status, time, user, userid):
    splitter = cc.split('|')
    ccnum    = splitter[0]
    bin = ccnum[:6]
    rango = await collector.get_user_info(userid)
    rango = json.loads(rango)['premiums']['status']

    bin_info = await bin_loader(bin)
    text = f"∙ • <b><i>{desc}</i></b> | #BIN{bin} • ∙\n"
    text += "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n"
    text += f"✧ | <code>{cc}</code>\n"
    text += f"⚝ | <b>Status : </b> <b>{status}</b>\n"
    text += f"✦ | <b>Response :</b> <b><i>{response}</i></b>\n"
    text += "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n"
    text += f"<b>🍒 | Bank : </b> <code>{bin_info['bank']}</code>\n"
    text += f"<b>🍒 | Info : </b><code>{str(bin_info['brand']).upper()}</code> | <code>{str(bin_info['type']).upper()}</code> | <code>{str(bin_info['level']).upper()}</code>\n"
    text += f"<b>🍒 | Country : </b><code>{str(bin_info['country'])}</code> | {bin_info['code_mogi']}\n"
    text += "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n"
    text += f"<b>Took : </b><code>{time}</code> <b>secs</b>\n"
    text += f"<b>User : </b><code>@{user}</code> | <b>[{str(rango).upper()}]</b>\n"

    # Crear el teclado con InlineKeyboardBuilder
    builder = InlineKeyboardBuilder()
    builder.row(InlineKeyboardButton(text="BUY CHERRY 🍒💸", url="https://t.me/CherryScrapperChannel"))
    markup = builder.as_markup()

    return text, markup

async def response_msg_premium_cvv_avs(cc, desc, response, status, cvv, avs, time, user, userid):
    splitter = cc.split('|')
    ccnum    = splitter[0]
    bin = ccnum[:6]
    rango = await collector.get_user_info(userid)
    rango = json.loads(rango)['premiums']['status']

    bin_info = await bin_loader(bin)
    text = f"∙ • <b><i>{desc}</i></b> | #BIN{bin} • ∙\n"
    text += "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n"
    text += f"✧ | <code>{cc}</code>\n"
    text += f"⚝ | <b>Status : </b> <b>{status}</b>\n"
    text += f"✦ | <b>Response :</b> <b><i>{response}</i></b>\n"
    text += f"✤ | <b>CVV :</b> <b><i>{cvv}</i></b> | <b>AVS :</b> <b><i>{avs}</i></b>\n"
    text += "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n"
    text += f"<b>🍒 | Bank : </b> <code>{bin_info['bank']}</code>\n"
    text += f"<b>🍒 | Info : </b><code>{str(bin_info['brand']).upper()}</code> | <code>{str(bin_info['type']).upper()}</code> | <code>{str(bin_info['level']).upper()}</code>\n"
    text += f"<b>🍒 | Country : </b><code>{str(bin_info['country'])}</code> | {bin_info['code_mogi']}\n"
    text += "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n"
    text += f"<b>Took : </b><code>{time}</code> <b>secs</b>\n"
    text += f"<b>User : </b><code>@{user}</code> | <b>[{str(rango).upper()}]</b>\n"

    # Crear el teclado con InlineKeyboardBuilder
    builder = InlineKeyboardBuilder()
    builder.row(InlineKeyboardButton(text="BUY CHERRY 🍒💸", url="https://t.me/CherryScrapperChannel"))
    markup = builder.as_markup()

    return text, markup

async def response_msg_massive_cvv_avs(cc, response, status, cvv, avs, time, userid):

    # Crear el teclado con InlineKeyboardBuilder
    builder = InlineKeyboardBuilder()
    builder.row(InlineKeyboardButton(text="BUY CHERRY 🍒💸", url="https://t.me/CherryScrapperChannel"))
    markup = builder.as_markup()

    text = "\n▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n" 
    text += f"✧ | <code>{cc}</code>\n"
    text += f"⚝ | <b>Status :</b> <b>{status}</b>\n"
    text += f"✦ | <b>Response :</b> <b><i>{response}</i></b>\n"
    text += f"✤ | <b>CVV :</b> <b><i>{cvv}</i></b> | <b>AVS :</b> <b><i>{avs}</i></b>\n"
    text += f"<b>Took :</b> <b><i>{time} secs</i></b>\n"

    return text, markup

async def response_msg_massive(cc, response, status, time, userid):

    # Crear el teclado con InlineKeyboardBuilder
    builder = InlineKeyboardBuilder()
    builder.row(InlineKeyboardButton(text="BUY CHERRY 🍒💸", url="https://t.me/CherryScrapperChannel"))
    markup = builder.as_markup()

    text = "\n▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n" 
    text += f"✧ | <code>{cc}</code>\n"
    text += f"⚝ | <b>Status :</b> <b>{status}</b>\n"
    text += f"✦ | <b>Response :</b> <b><i>{response}</i></b>\n"
    text += f"<b>Took :</b> <b><i>{time} secs</i></b>\n"

    return text, markup

def checking_msg_massive(bin, desc):
    text = f"∙ • <b>Testing Cards</b> | #BIN{bin} • ∙\n"
    text += "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n"
    text += f"<b><i>{desc}</i></b>\n"
    text += f"⚝ <b><i>This may take some time!</i></b> ⏳\n"
    text += "▬▬▬▬▬▬▬ [🍒] ▬▬▬▬▬▬▬▬\n"

    return text

def add_to_last(user_id, cc, type):
    if len(user_risk[user_id][str(type)]) >= 15:
        user_risk[user_id][str(type)].clear()
    
    user_risk[user_id][str(type)].append(cc)
    
    current_time = datetime.now().strftime("%d%m%Y%H%M%S")
    user_risk[user_id]['last-checked-time'] = current_time

# Obtener mes y año actuales
current_month = datetime.now().month
current_year = datetime.now().year


async def main() -> None:
    # Crear el bot
    bot = Bot(str(API_TOKEN))

    # Iniciar el polling
    await dp.start_polling(bot, skip_updates=True)



if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    logging.getLogger('httpx').setLevel(logging.CRITICAL)
    bin_fetcher = collector.BinInfoFetcher()
    asyncio.run(main())
