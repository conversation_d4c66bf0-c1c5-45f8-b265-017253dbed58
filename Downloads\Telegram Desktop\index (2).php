<?php

/**
 * 该文件负责跟踪访问者并通过 Telegram 通知他们。
 */

// 引入所需的类
require_once 'VisitorTracker.php'; 

// 使用 VisitorTracker 命名空间中的 Visitor 和 TelegramNotifier 类
use VisitorTracker\Visitor;
use VisitorTracker\TelegramNotifier;

/**
 * 创建一个新的 Visitor 对象，并使用访问者的 IP 地址、用户代理和来源 URL。
 * 如果来源 URL 没有提供，则默认为 '直接连接'。
 */
$visitor = new Visitor($_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT'], $_SERVER['HTTP_REFERER'] ?? '直接连接');

/**
 * 创建一个新的 TelegramNotifier 对象，并使用机器人的 API 令牌和聊天 ID。
 * 机器人的 API 令牌和聊天 ID 目前是占位符，需要用实际值代替。
 */
$notifier = new TelegramNotifier('您的_BOT_TOKEN', '您的_CHAT_ID');

/**
 * 使用 TelegramNotifier 对象向 Telegram 聊天发送新访问者的通知。
 * notify 方法向指定的聊天发送一条包含访问者详细信息的消息。
 *
 * @param Visitor $visitor 要通知的访问者对象。
 * @return void
 */
$notifier->notify($visitor);