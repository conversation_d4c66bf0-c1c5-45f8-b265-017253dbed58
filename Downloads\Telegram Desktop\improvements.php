<?php
class MassReqs
{
    public int $wait = 100;
    private string $file;
    private array $results = []; 

    public function __construct(string $file)
    {
        match (file_exists($file)) {
            true => $this->file = $file,
            false => throw new Exception("The file does not exist!"),
        };
    }

    private function runReq(string $data): void
    {
        $proc = proc_open(
            "php $this->file $data",
            pipes: [1 => ["pipe", "w"]],
            process: $procDetails, 
        );

        Fiber::suspend();

        match (proc_get_status($proc)['running']) {
            true => $this->results[] = "Timeout exceeded!",
            false => $this->results[] = stream_get_contents($procDetails['pipes'][1]),
        };
        proc_close($proc);
    }

    public function execute(
        array $data, 
        int $timeoutMs = 1000 
    ): array 
    {
        $fibers = [];

        foreach ($data as $d) {
            $fiber = new Fiber([$this, "runReq"]);
            $fiber->start($d);
            $fibers[] = $fiber;
        }

        usleep($timeoutMs * 1000); 

        foreach ($fibers as $fiber) {
            $fiber->resume();
        }
        return $this->results;
    }
}
