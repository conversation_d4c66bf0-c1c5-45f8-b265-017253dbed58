import requests
import base64
import json
import uuid
import random

headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "en-US,en;q=0.9,es;q=0.8",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}

response1 = requests.get("https://api.zephyr-sim.com/v2/braintree/token", headers=headers)
decoded_response = base64.b64decode(response1.text)
bearer = decoded_response.decode().split('"authorizationFingerprint":"')[1].split('",')[0]

guid = str(uuid.uuid4())

headers = {
    "accept": "*/*",
    "accept-language": "en-US,en;q=0.9,es;q=0.8",
    "Authorization": f"Bearer {bearer}",
    "braintree-version": "2018-05-10",
    "content-type": "application/json",
    "origin": "https://assets.braintreegateway.com",
    "referer": "https://assets.braintreegateway.com/",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}

data = {
    "clientSdkMetadata": {
        "source": "client",
        "integration": "custom",
        "sessionId": guid
    },
    "query": "mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) { tokenizeCreditCard(input: $input) { token creditCard { bin brandCode last4 cardholderName expirationMonth expirationYear binData { prepaid healthcare debit durbinRegulated commercial payroll issuingBank countryOfIssuance productId } } } }",
    "variables": {
        "input": {
            "creditCard": {
                "number": "****************",
                "expirationMonth": "02",
                "expirationYear": "2028",
                "cvv": "828",
                "billingAddress": {
                    "postalCode": "10080"
                }
            },
            "options": {
                "validate": False
            }
        }
    },
    "operationName": "TokenizeCreditCard"
}

response2 = requests.post("https://payments.braintree-api.com/graphql", headers=headers, data=json.dumps(data))
tokencc = json.loads(response2.text)["data"]["tokenizeCreditCard"]["token"]

headers = {
    'accept': 'application/json, text/plain, */*',
    'content-type': 'application/json',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
}

data = {
    "paymentMethodNonce": tokencc,
    "email": f"jsentau{random.randint(100, 999)}@gmail.com",
    "cart": [{"productId": "HOBBYIST-PACK", "quantity": 1, "isUpsell": False, "isDownsell": False}],
    "billingCountry": "US",
    "billingStateProvince": "NY",
    "billingPostalCode": "10080",
    "expedited": False,
    "total": 9.99
}

response3 = requests.post("https://api.zephyr-sim.com/v2/orders/braintree", headers=headers, data=json.dumps(data))
msg = json.loads(response3.text)["message"]

print(msg)
