<?php

interface AntiCaptchaTaskProtocol {
    public function getPostData();
    public function getTaskSolution();
}

class Anticaptcha {
    private string $host = "api.anti-captcha.com";
    private string $scheme = "https";
    private string $clientKey;
    private bool $verboseMode = false;
    public bool $verifySSL = true;
    private ?string $errorMessage = null;
    private ?int $taskId = null;
    public ?stdClass $taskInfo = null;
    private ?int $softId = null;

    public function createTask(): bool {
        $postData = [
            "clientKey" =>  $this->clientKey,
            "task" =>  $this->getPostData(),
            "softId" =>  $this->softId
        ];

        $submitResult = $this->jsonPostRequest("createTask", $postData);

        if ($submitResult === false) {
            $this->debout("API error", "red");
            return false;
        }

        if ($submitResult->errorId === 0) {
            $this->taskId = (int)$submitResult->taskId;

            if ($this->taskId === 0) {
                $this->debout("API Error: incorrect taskId = 0", "red");
                return false;
            }

            $this->debout("created task with ID {$this->taskId}", "yellow");
            return true;
        } else {
            $this->debout("API error {$submitResult->errorCode} : {$submitResult->errorDescription}", "red");
            $this->setErrorMessage($submitResult->errorDescription);
            return false;
        }
    }

    public function waitForResult(int $maxSeconds = 300, int $currentSecond = 0): bool {
        $postData = [
            "clientKey" =>  $this->clientKey,
            "taskId" =>  $this->taskId
        ];

        if ($currentSecond === 0) {
            $this->debout("waiting 5 seconds..");
            sleep(3);
        } else {
            sleep(1);
        }

        $this->debout("requesting task status");
        $postResult = $this->jsonPostRequest("getTaskResult", $postData);

        if ($postResult === false) {
            $this->debout("API error", "red");
            return false;
        }

        $this->taskInfo = $postResult;

        if ($this->taskInfo->errorId === 0) {
            if ($this->taskInfo->status === "processing") {
                $this->debout("task is still processing");
                // Repeating attempt
                return $this->waitForResult($maxSeconds, $currentSecond + 1);
            } elseif ($this->taskInfo->status === "ready") {
                $this->debout("task is complete", "green");
                return true;
            } else {
                $this->setErrorMessage("unknown API status, update your software");
                return false;
            }
        } else {
            $this->debout("API error {$this->taskInfo->errorCode} : {$this->taskInfo->errorDescription}", "red");
            $this->setErrorMessage($this->taskInfo->errorDescription);
            return false;
        }
    }

    public function getBalance(): float|false {
        $postData = [
            "clientKey" =>  $this->clientKey
        ];

        $result = $this->jsonPostRequest("getBalance", $postData);

        if ($result === false) {
            $this->debout("API error", "red");
            return false;
        }

        if ($result->errorId === 0) {
            return $result->balance;
        } else {
            return false;
        }
    }

    public function reportIncorrectImageCaptcha(): bool {
        $result = $this->jsonPostRequest("reportIncorrectImageCaptcha", [
            "clientKey" =>  $this->clientKey,
            "taskId" =>  $this->taskId
        ]);

        if ($result === false) {
            $this->debout("API error", "red");
            return false;
        }

        if ($result->errorId === 0) {
            return true;
        } else {
            return false;
        }
    }

    public function reportIncorrectRecaptcha(): bool {
        $result = $this->jsonPostRequest("reportIncorrectRecaptcha", [
            "clientKey" =>  $this->clientKey,
            "taskId" =>  $this->taskId
        ]);

        if ($result === false) {
            $this->debout("API error", "red");
            return false;
        }

        if ($result->errorId === 0) {
            return true;
        } else {
            return false;
        }
    }

    public function reportIncorrectHcaptcha(): bool {
        $result = $this->jsonPostRequest("reportIncorrectHcaptcha", [
            "clientKey" =>  $this->clientKey,
            "taskId" =>  $this->taskId
        ]);

        if ($result === false) {
            $this->debout("API error", "red");
            return false;
        }

        if ($result->errorId === 0) {
            return true;
        } else {
            return false;
        }
    }

    public function reportCorrectRecaptcha(): bool {
        $result = $this->jsonPostRequest("reportCorrectRecaptcha", [
            "clientKey" =>  $this->clientKey,
            "taskId" =>  $this->taskId
        ]);

        if ($result === false) {
            $this->debout("API error", "red");
            return false;
        }

        if ($result->errorId === 0) {
            return true;
        } else {
            return false;
        }
    }

    public function pushAntiGateVariable(string $name, string $value): bool {
        $result = $this->jsonPostRequest("pushAntiGateVariable", [
            "clientKey" =>  $this->clientKey,
            "taskId" =>  $this->taskId,
            "name" =>  $name,
            "value" =>  $value
        ]);

        if ($result === false) {
            $this->debout("API error", "red");
            return false;
        }

        if ($result->errorId === 0) {
            return true;
        } else {
            return false;
        }
    }

    public function jsonPostRequest(string $methodName, array $postData): false|stdClass {
        if ($this->verboseMode) {
            echo "making request to {$this->scheme}://{$this->host}/$methodName with following payload:\n";
            print_r($postData);
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "{$this->scheme}://{$this->host}/$methodName");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_ENCODING, "gzip,deflate");
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        $postDataEncoded = json_encode($postData);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postDataEncoded);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json; charset=utf-8',
            'Accept: application/json',
            'Content-Length: ' . strlen($postDataEncoded)
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);

        if (!$this->verifySSL) {
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($ch, CURLOPT_SSLVERSION, 1);
        }

        $result = curl_exec($ch);

        if ($this->verboseMode) {
            echo "API response:\n";
            echo $result . "\n";
        }

        $curlError = curl_error($ch);

        if ($curlError !== "") {
            $this->errorMessage = "Network error: $curlError";
            $this->debout("Network error: $curlError");
            return false;
        }

        curl_close($ch);
        return json_decode($result);
    }

    public function setVerboseMode(bool $mode): void {
        $this->verboseMode = $mode;
    }

    public function debout(string $message, string $color = "white"): void {
        if (!$this->verboseMode) return;

        if ($color !== "white" && $color !== "") {
            $CLIcolors = [
                "cyan" => "0;36",
                "green" => "0;32",
                "blue" => "0;34",
                "red" => "0;31",
                "yellow" => "1;33"
            ];

            $CLIMsg  = "\033[" . $CLIcolors[$color] . "m$message\033[0m";
        } else {
            $CLIMsg  = $message;
        }

        echo $CLIMsg . "\n";
    }

    public function setErrorMessage(string $message): void {
        $this->errorMessage = $message;
    }

    public function getErrorMessage(): string {
        return $this->errorMessage;
    }

    public function getTaskId(): int {
        return $this->taskId;
    }

    public function setTaskId(int $taskId): void {
        $this->taskId = $taskId;
    }

    public function setHost(string $host): void {
        $this->host = $host;
    }

    public function setScheme(string $scheme): void {
        $this->scheme = $scheme;
    }

    /**
     * Set client access key, must be 32 bytes long
     * @param string $key
     */
    public function setKey(string $key): void {
        $this->clientKey = $key;
    }

    /**
     * Specify softId to earn 10% commission with your app.
     * Get your softId here: https://anti-captcha.com/clients/tools/devcenter
     *
     * @param int $value
     */
    public function setSoftId(int $value): void {
        $this->softId = $value;
    }
}
