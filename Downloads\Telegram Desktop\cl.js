const https = require('https');
const forge = require('node-forge');
const crypto = require('crypto');

function getPublicKey() {
    return new Promise((resolve, reject) => {
        https.get("https://checkout.clover.com/assets/keys.json", (res) => {
            let data = '';

            res.on('data', (chunk) => { data += chunk; });
            res.on('end', () => {
                const keys = JSON.parse(data);
                const publicKeyBase64 = keys["TA_PUBLIC_KEY_PROD"];
                const keyBuffer = Buffer.from(publicKeyBase64, 'base64');

                const modulusBytes = keyBuffer.slice(0, 256);  
                const exponentBytes = keyBuffer.slice(256);    

                const modulus = new forge.jsbn.BigInteger(modulusBytes.toString('hex'), 16);
                const exponent = new forge.jsbn.BigInteger(exponentBytes.toString('hex'), 16);


                const rsaPublicKey = forge.pki.setRsaPublicKey(modulus, exponent);
                const pemKey = forge.pki.publicKeyToPem(rsaPublicKey); 

                resolve(pemKey);
            });
        }).on('error', (err) => {
            reject(err);
        });
    });
}

async function encryptCardNumber(pan, prefix_id = "00000000") {
    try {
        const publicKeyPem = await getPublicKey(); 
        const inputData = Buffer.from(`${prefix_id}${pan}`, 'utf8');

        const encryptedBuffer = crypto.publicEncrypt(
            {
                key: publicKeyPem,
                padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
                oaepHash: "sha1"
            },
            inputData
        );

        return encryptedBuffer.toString('base64');  
    } catch (error) {
        console.error("Encryption failed:", error);
        return null;
    }
}

// Example Usage
const pan = "****************";  // Your test card number

encryptCardNumber(pan).then(encryptedPan => {
    console.log("Encrypted PAN:", encryptedPan);
});
