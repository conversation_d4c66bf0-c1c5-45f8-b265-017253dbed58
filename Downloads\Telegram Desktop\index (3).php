<?php
/*=====[Made With Love By XiaoTempesT]========*/

$directory = "./";
$files = scandir($directory);
$uploadDirectory = 'done/';
$backup = 'backups/';
$destination = $uploadDirectory . "result.txt";

if (!file_exists($uploadDirectory)) {
    mkdir($uploadDirectory, 0777, true);
}

if (!file_exists($backup)) {
    mkdir($backup, 0777, true);
}

$allCreditCardDetails = [];

foreach ($files as $file) {
    if (pathinfo($file, PATHINFO_EXTENSION) === "html") {
        $file_path = $directory . "/" . $file;
        $html_content = file_get_contents($file_path);
        $creditCardDetails = extractCreditCardDetails($html_content);
        $allCreditCardDetails = array_merge($allCreditCardDetails, $creditCardDetails);
        $backupFilePath = $backup . basename($file);
        copy($file_path, $backupFilePath);
        unlink($file_path);
    }
}

$uniqueCreditCardDetails = array_unique($allCreditCardDetails);
file_put_contents($destination, implode("\n", $uniqueCreditCardDetails) . "\n", FILE_APPEND);

$completedTime = date('Y-m-d H:i:s');
echo "HTML files converted, credit card details extracted, duplicate lines removed, and saved successfully. Completed at: $completedTime";

function extractCreditCardDetails($text) {
    $regexPattern = '/<code>(\d+)\|(\d+)\|(\d+)\|(\d+)<\/code>/';
    preg_match_all($regexPattern, $text, $matches);
    $creditCardDetails = [];
    for ($i = 0; $i < count($matches[0]); $i++) {
        $cardNumber = $matches[1][$i];
        $expirationDate = $matches[2][$i] . '|' . $matches[3][$i];
        $cvv = $matches[4][$i];
        $creditCardDetails[] = "$cardNumber|$expirationDate|$cvv";
    }
    return $creditCardDetails;
}
?>