<?php
	// Code by <PERSON> English - https://t.me/johnnyenglishreal
	function request_to_checkbin($bin){
		$url = 'https://bincheck.org/'.$bin;
		$ch = curl_init($url);
		$header = [
			'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36 Edg/97.0.1072.62'
		]; 
		curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0); 
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		$output = curl_exec($ch);
		$httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
		curl_close($ch);
		return array($httpcode, $output);
	}
	
	function get_value($str){
		$str = trim($str);
		if ($str == "--")
			return "N/A";
		return $str;
	}

	$result = array(
		'bin' => 'N/A',
		'status' => 'fail',
		'message' => 'Error!',
		'brand' => 'N/A',
		'type' => 'N/A',
		'category' => 'N/A',
		'bank' => 'N/A',
		'bank_url' => 'N/A',
		'country' => 'N/A',
		'country_code' => 'N/A',
		'author' => 'Johnny English',
		'author_contact' => 'https://t.me/johnnyenglishreal'
	);
	
	if (isset($_GET['bin']) && (!preg_match('#[^0-9]#', $_GET['bin'])) && (strlen($_GET['bin']) >= 6)){
		$real_bin = substr($_GET['bin'], 0, 6);
		$result['bin'] = $real_bin;
		$result['status'] = 'success';
		$result['message'] = 'Done!';
		$result_check_bin = request_to_checkbin($real_bin);
		$status_code = $result_check_bin[0];
		$str_result = $result_check_bin[1];
		if ($status_code != 200){
			$result['message'] = 'Not found BIN';
		} else {
			$doc = new DOMDocument();
			@$doc->loadHTML($str_result);
			$td = $doc->getElementsByTagName('td');
			$result['brand'] = get_value($td[1]->nodeValue);
			$result['type'] = get_value($td[2]->nodeValue);
			$result['category'] = get_value($td[3]->nodeValue);		
			$result['bank'] = get_value($td[4]->nodeValue);
			$result['bank_url'] = get_value($td[5]->nodeValue);
			$result['country'] = get_value($td[6]->nodeValue);
			$result['country_code'] = get_value($td[7]->nodeValue);
		}	
	}
	echo json_encode($result);
?>