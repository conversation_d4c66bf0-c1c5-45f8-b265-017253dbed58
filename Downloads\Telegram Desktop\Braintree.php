<?php
@unlink('cookie.txt');
function Capture($string, $start, $final)
{
    $explodedStart = explode($start, $string);
    if (isset($explodedStart[1])) {
        $innerString = $explodedStart[1];
        $explodedFinal = explode($final, $innerString);
        if (isset($explodedFinal[0])) {
            return $explodedFinal[0];
        } else {
            return null; // Handle the case where the final delimiter doesn't exist
        }
    } else {
        return null; // Handle the case where the start delimiter doesn't exist
    }
}
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.publicstorage.com/self-storage-search?location=new+york');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
    'accept-language: en-US,en;q=0.6',
    'upgrade-insecure-requests: 1',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
]);

$response = curl_exec($ch);

curl_close($ch);


// $ch = curl_init();
// curl_setopt($ch, CURLOPT_URL, 'https://www.shopaew.com/checkout/cart/add/uenc/' . $uenc . '/product/' . $product . '/');
// curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
// curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
// curl_setopt($ch, CURLOPT_HTTPHEADER, [
//     'accept: application/json, text/javascript, */*; q=0.01',
//     'accept-language: en-US,en;q=0.5',
//     'content-type: application/x-www-form-urlencoded; charset=UTF-8',
//     'origin: https://www.shopaew.com',
//     'referer: https://www.shopaew.com/catalog/product/view/id/44477/s/nyla-rose-skull-face-bandana/category/7152/',
//     'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
//     'x-requested-with: XMLHttpRequest',
// ]);
// curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd() . '/cookie.txt');
// curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd() . '/cookie.txt');
// curl_setopt($ch, CURLOPT_POSTFIELDS, $dates);
// $response = curl_exec($ch);
// curl_close($ch);
// echo $response;
// exit();

// $ch = curl_init();
// curl_setopt($ch, CURLOPT_URL, 'https://www.ebonyline.com/checkout/cart/');
// curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
// curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
// curl_setopt($ch, CURLOPT_HTTPHEADER, [
//     'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0',
//     'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
//     'Accept-Language: en-US,en;q=0.5',
//     // 'cookie: form_key=' . $dates['form_key'],
//     'Referer: https://www.ebonyline.com/magic-collection-deluxe-stocking-wig-cap-expandable-2-pcs-brown.html',
// ]);

// curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd() . '/cookie.txt');
// curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd() . '/cookie.txt');
// $response = curl_exec($ch);
// echo $response;


$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.publicstorage.com/self-storage-ny-long-island-city/1782.html');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
    'accept-language: en-US,en;q=0.6',
    'referer: https://www.publicstorage.com/self-storage-search?location=new+york',
    'upgrade-insecure-requests: 1',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
]);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd() . '/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd() . '/cookie.txt');

$response = curl_exec($ch);

curl_close($ch);


$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.publicstorage.com/on/demandware.store/Sites-publicstorage-Site/default/Reservation-GetHoldForm?masterPropertyID=1782&unitID=V_1488769&factor=1');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: */*',
    'accept-language: en-US,en;q=0.6',
    'referer: https://www.publicstorage.com/self-storage-ny-long-island-city/1782.html',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'x-requested-with: XMLHttpRequest',
]);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd() . '/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd() . '/cookie.txt');

$response = curl_exec($ch);
curl_close($ch);
$csrf = Capture($response,'"token": "','"');

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.publicstorage.com/on/demandware.store/Sites-publicstorage-Site/default/Reservation-HoldUnit');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: application/json, text/javascript, */*; q=0.01',
    'accept-language: en-US,en;q=0.6',
    'content-type: application/x-www-form-urlencoded; charset=UTF-8',
    'origin: https://www.publicstorage.com',
    'referer: https://www.publicstorage.com/self-storage-ny-long-island-city/1782.html',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'x-requested-with: XMLHttpRequest',
]);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd() . '/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd() . '/cookie.txt');
curl_setopt($ch, CURLOPT_POSTFIELDS, 'dwfrm_holdProperty_FullName=John+Smitgh&dwfrm_holdProperty_EmailAddress=aircracker2091%40hotmail.com&dwfrm_holdProperty_MoveInDate=04%2F27%2F2024&dwfrm_holdProperty_PhoneNumber=(941)+526-3365&SiteID=1782&ProductSiteID=1782&unitID=V_1488769&unitPrice=&unitTier=&eRentalEnable=true&moveInDateUTC=2024-04-27T00%3A00%3A00&eRentalPageURLType=a&csrf_token='.urlencode($csrf));

$response = curl_exec($ch);
curl_close($ch);
echo $response;