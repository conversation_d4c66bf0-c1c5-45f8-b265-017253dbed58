<?php
require 'vendor/autoload.php';

use Httpful\Request;

$postFields = [
    "card" => "****************|01|2030|111",
    "adyenKey" => "10001|D2C1C6C11FD8671FBF417F53B033321D33F277D1119382C254F98949D68B7D75244DD792ADE0ABE5B3FBD86E5306DA19DD9505DE9B90874E42308998225EFE4ADDC6D7A32CE97EC92AB83F5CF2461662B7B81F94ABDFE7E16CA98EBEBF2D566CAEFAEA7E12F000F518C3C22829833A83A6BD89819F6E8658308D9351F040D68871076746F40E4CD8ECCEDF2E5018EDFCF9807979C050B58BF35EA368E0A948E82A885C4473A0AAD04525FD74F32857D2F3C93433AED746276F01B698041BE87C468CC5328953A1D51544EEAB498265BC44E5510D4F755AC48FB37BC4D45A24815F1C9866CA279D69AD20FC360F7CFAF52C77A89C2BB10D3865BA50B372315EA1",
    "version" => "18",
    "risk" => "eyJ2ZXJzaW9uIjoiMS4wLjAiLCJkZXZpY2VGaW5nZXJwcmludCI6IkRwcXdVNHpFZE4wMDUwMDAwMDAwMDAwMDAwNUI4R2M5cVhLUzAwMzYwOTgwNjI1V3BZV2lLekJHdnlIcUlFbTFhMFZHZm0zSlFEemcwMDJjRzNCZG5YVm1mMDAwMDBxWmtURTAwMDAwOGJjSXU2RkRIT1ZRQ1U0UnBoa1g6NDAiLCJwZXJzaXN0ZW50Q29va2llIjpbXSwiY29tcG9uZW50cyI6eyJ1c2VyQWdlbnQiOiI4Mjc5NDVlODUxYmMyODhkOTUwNzRlYTc2YTk4YjJjZSIsIndlYmRyaXZlciI6MCwibGFuZ3VhZ2UiOiJlbi1BVSIsImNvbG9yRGVwdGgiOjE5LCJkZXZpY2VNZW1vcnkiOjIxLCJwaXhlbFJhdGlvIjoyLCJoYXJkd2FyZUNvbmN1cnJlbmN5Ijo4LCJzY3JlZW5XaWR0aCI6MTg1Miwic2NyZWVuSGVpZ2h0IjoxMDE1LCJhdmFpbGFibGVTY3JlZW5XaWR0aCI6Mzk0LCJhdmFpbGFibGVTY3JlZW5IZWlnaHQiOjg1NSwidGltZXpvbmVPZmZzZXQiOi01OSwidGltZXpvbmUiOiJBc2lhL1VydW1xaSIsInNlc3Npb25TdG9yYWdlIjoxLCJsb2NhbFN0b3JhZ2UiOjEsImluZGV4ZWREYiI6MSwiYWRkQmVoYXZpb3IiOjAsIm9wZW5EYXRhYmFzZSI6MCwicGxhdGZvcm0iOiJXaW4zMiIsInBsdWdpbnMiOiIyOWNmNzFlM2Q4MWQ3NGQ0M2E1YjBlYjc5NDA1YmE4NyIsImNhbnZhcyI6ImM2ODg1ZTE0MWZmYTVmODAwMTE4NjllMjkxYjcxMWU3IiwiYWRCbG9jayI6MCwiaGFzTGllZExhbmd1YWdlcyI6MCwiaGFzTGllZFJlc29sdXRpb24iOjAsImhhc0xpZWRPcyI6MCwiaGFzTGllZEJyb3dzZXIiOjAsImZvbnRzIjoiYzg3YjFiMjdmNDFmODZkZWM3MmMxZDJlMGRlZjcxMjAiLCJhdWRpbyI6IjU1ZjY3MTI0NDM0YjJkZTcxMzIyMjc5YWVmMTZlMzZiIiwiZW51bWVyYXRlRGV2aWNlcyI6IjVmM2ZkYWY0NzQzZWFhNzA3Y2E2YjdkYTY1NjAzODkyIn19", //opcional
    "ip" => "***********" //opcional
];

$response = Request::post('https://asianprozyy.us/encrypt/adyen')
    ->sendsJson()
    ->body($postFields)
    ->addHeader('User-Agent', 'PostmanRuntime/7.31.1')
    ->addHeader('Content-Type', 'application/json')
    ->send();

$result = json_decode($response->raw_body);

$riskData = $result->riskData;
$encryptedCardNumber = $result->encryptedCardNumber;
$encryptedExpiryMonth = $result->encryptedExpiryMonth;
$encryptedExpiryYear = $result->encryptedExpiryYear;
$encryptedSecurityCode = $result->encryptedSecurityCode;
$encryptedCardDataCVV = $result->encryptedCardDataCVV;
$encryptedCardDataCCN = $result->encryptedCardDataCCN;

echo "<pre>";
echo "Risk Data: " . print_r($riskData, true) . "\n";
echo "Encrypted Card Number: " . $encryptedCardNumber . "\n";
echo "Encrypted Expiry Month: " . $encryptedExpiryMonth . "\n";
echo "Encrypted Expiry Year: " . $encryptedExpiryYear . "\n";
echo "Encrypted Security Code: " . $encryptedSecurityCode . "\n";
echo "Encrypted Card Data CVV: " . $encryptedCardDataCVV . "\n";
echo "Encrypted Card Data CCN: " . $encryptedCardDataCCN . "\n";
echo "</pre>";
