<?php

/*******************************************************
 * *** Developed by: @Xypher_xD *** 
 * *** ReCAPTCHA Bypassing Code ***
 * *** For support, contact me on Telegram ***
 * *** Code crafted with precision and care ***
 *******************************************************/


class RecaptchaBypass
{
    private $anchor_url;
    private $headers;

    public function __construct($anchor_url)
    {
        $this->anchor_url = $anchor_url;
        $this->headers = [
            "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36 Edg/121.0.0.0",
            "Accept: */*",
            "Connection: keep-alive",
            "X-Requested-With: XMLHttpRequest"
        ];
    }

    public function bypass($type)
    {
        $url_vars = $this->parseAnchorUrl();

        if (!$url_vars) {
            return json_encode([
                'Status' => 'false',
                'Error' => 'Invalid anchor URL format',
                'Dev' => '@Xypher_xD'
            ]);
        }

        $anchor_token = $this->getAnchorToken();

        if (!$anchor_token) {
            return json_encode([
                'Status' => 'false',
                'Error' => 'Failed to retrieve anchor token',
                'Dev' => '@Xypher_xD'
            ]);
        }

        if ($type === 'v3') {
            $v3token = $this->getV3Token($url_vars, $anchor_token);

            if (!$v3token) {
                return json_encode([
                    'Status' => 'false',
                    'Error' => 'Failed to retrieve v3 token',
                    'Dev' => '@Xypher_xD'
                ]);
            }

            return json_encode([
                'Status' => 'true',
                'Recaptchav3 Token' => $v3token,
                'Dev' => '@Xypher_xD'
            ]);

        } elseif ($type === 'v2') {
            $v3token = $this->getV3Token($url_vars, $anchor_token);

            if (!$v3token) {
                return json_encode([
                    'Status' => 'false',
                    'Error' => 'Failed to retrieve intermediate v3 token for v2 retrieval',
                    'Dev' => '@Xypher_xD'
                ]);
            }

            $v2token = $this->getV2Token($url_vars, $v3token);

            if (!$v2token) {
                return json_encode([
                    'Status' => 'false',
                    'Error' => 'Failed to retrieve v2 token',
                    'Dev' => '@Xypher_xD'
                ]);
            }

            return json_encode([
                'Status' => 'true',
                'Recaptchav2 Token' => $v2token,
                'Dev' => '@Xypher_xD'
            ]);

        } else {
            return json_encode([
                'Status' => 'false',
                'Error' => 'Invalid type parameter. Use "v2" or "v3".',
                'Dev' => '@Xypher_xD'
            ]);
        }
    }

    private function parseAnchorUrl()
    {
        $url_components = parse_url($this->anchor_url);
        if (!isset($url_components['query'])) {
            return false;
        }

        parse_str($url_components['query'], $url_vars);
        if (!isset($url_vars["v"]) || !isset($url_vars["k"]) || !isset($url_vars["co"])) {
            return false;
        }

        return $url_vars;
    }

    private function getAnchorToken()
    {
        $ch = curl_init($this->anchor_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->headers);
        $response = curl_exec($ch);
        curl_close($ch);

        if (preg_match('/type="hidden" id="recaptcha-token" value="([^"]+)"/', $response, $matches)) {
            return $matches[1];
        }

        file_put_contents('debug_anchor_token.log', "Anchor Token Response:\n$response\n");
        return null;
    }

    private function getV3Token($url_vars, $anchor_token)
    {
        $reload_url = "https://www.google.com/recaptcha/api2/reload?k=" . $url_vars["k"];
        $data = http_build_query([
            'v' => $url_vars["v"],
            'reason' => 'q',
            'c' => $anchor_token,
            'k' => $url_vars["k"],
            'co' => $url_vars["co"],
            'hl' => 'en',
            'size' => 'invisible'
        ]);

        $headers = $this->headers;
        $headers[] = "Referer: {$this->anchor_url}";
        $headers[] = "Content-Type: application/x-www-form-urlencoded";

        $ch = curl_init($reload_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        $response = curl_exec($ch);
        curl_close($ch);

        if (substr($response, 0, 4) === ")]}'") {
            $response = substr($response, 4);
        }
        
        if (preg_match('/"rresp","([^"]+)"/', $response, $matches)) {
            return $matches[1];
        }

        file_put_contents('debug_v3_token.log', "V3 Token Response:\n$response\n");
        return null;
    }

    private function getV2Token($url_vars, $v3token)
    {
        $userverify_url = "https://www.google.com/recaptcha/api2/userverify?k=" . $url_vars["k"];
        $data = http_build_query([
            'v' => $url_vars["v"],
            'c' => $v3token,
            'bg' => '',
            't' => '',
            'ct' => '',
            'response' => 'eyJyZXNwb25zZSI6IiIsInMiOiI3ZjU5IiwiZSI6ImJYVnMifQ..'
        ]);
    
        $ch = curl_init($userverify_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->headers);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        $response = curl_exec($ch);
        curl_close($ch);
    
        if (substr($response, 0, 4) === ")]}'") {
            $response = substr($response, 4);
        }
    
        $patterns = [
            '/\["uvresp","([^"]+)"\]/', 
            '/"uvresp","([^"]+)"/',     
            '/"rresp","([^"]+)"/'       
        ];
    
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $response, $matches)) {
                return $matches[1];
            }
        }
    
        file_put_contents('debug_v2_token.log', "Failed to retrieve v2 token.\nResponse:\n$response\n");
        return null;
    }
}

$anchor_url = $_GET['anchor_url'] ?? '';
$type = $_GET['type'] ?? '';
if (empty($type)) {
    echo json_encode([
        'Status' => 'false',
        'Error' => 'type parameter is missing',
        'Dev' => '@Xypher_xD'
    ]);
    exit;
}
if (empty($anchor_url)) {
    echo json_encode([
        'Status' => 'false',
        'Error' => 'anchor_url parameter is missing',
        'Dev' => '@Xypher_xD'
    ]);
    exit;
}

// $recaptchaBypass = new RecaptchaBypass($anchor_url);
// $response = $recaptchaBypass->bypass($type);
// echo $response;
