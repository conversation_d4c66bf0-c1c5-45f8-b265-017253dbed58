# helpers/mailtm_account_helpers.py

import requests
import random
import string

# Hàm để sinh ra một username ngẫu nhiên
def generate_random_username(length=8):
    letters = string.ascii_lowercase
    return ''.join(random.choice(letters) for i in range(length))

# Hàm để lấy danh sách domain
def get_domains():
    response = requests.get('https://api.mail.tm/domains')
    if response.status_code == 200:
        domains = response.json()['hydra:member']
        return domains
    else:
        return None

# Hàm để tạo địa chỉ email
def create_address(email, domain_id):
    url = 'https://api.mail.tm/accounts'
    data = {
        "address": email,
        "password": "juldeptrai123@@G",  # Thay đổi mật khẩu tại đây
        "domainId": domain_id
    }
    response = requests.post(url, json=data)
    return response.json()
