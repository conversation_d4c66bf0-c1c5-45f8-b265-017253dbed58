[SETTINGS]
{
  "Name": "Discord VM",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-06-16T16:17:11.9240147+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Discord VM",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#RandomUserName FUNCTION RandomString "?l8l?l?l?l?l8952?l" -> VAR "RandomUserName" 

#Api REQUEST POST "https://discord.com/api/v9/auth/register" 
  CONTENT "{\"fingerprint\":\"793580565130641419.LGQ5IVlIkNTEQfpHbXcQLA2ABrM\",\"email\":\"<USER>\",\"username\":\"<RandomUserName>\",\"password\":\"rth21e98!fmPP\",\"invite\":null,\"consent\":true,\"date_of_birth\":\"1993-05-03\",\"gift_code_sku_id\":null,\"captcha_key\":null}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#rqtk PARSE "<SOURCE>" LR "captcha_rqtoken\":\"" "\"}" -> VAR "rqtk" 

#ssid PARSE "<SOURCE>" LR "captcha_session_id\":\"" "\"," -> VAR "ssid" 

SOLVECAPTCHA HCaptcha "a9b5fb07-92ff-493f-86fe-352a2803b3df" "https://discord.com" 

#Api REQUEST POST "https://discord.com/api/v9/auth/register" 
  CONTENT "{\"fingerprint\":\"793580565130641419.LGQ5IVlIkNTEQfpHbXcQLA2ABrM\",\"email\":\"<USER>\",\"username\":\"<RandomUserName>\",\"password\":\"rth21e98!fmPP\",\"invite\":null,\"consent\":true,\"date_of_birth\":\"1993-05-03\",\"gift_code_sku_id\":null,\"promotional_email_opt_in\":true,\"captcha_key\":\"<SOLUTION>\" }" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "X-Captcha-Rqtoken: <rqtk>" 
  HEADER "X-Captcha-Session-Id: <ssid>" 
  HEADER "X-Captcha-Key: <SOLUTION>" 
  HEADER "X-Super-Properties: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" 
  HEADER "Referer: https://discord.com/register?redirect_to=%2Fchannels%2F%40me" 
  HEADER "X-Fingerprint: 793580565130641419.LGQ5IVlIkNTEQfpHbXcQLA2ABrM" 

#KeyCheck KEYCHECK 
  KEYCHAIN Success OR 
    KEY "EMAIL_ALREADY_REGISTERED" 
  KEYCHAIN Failure OR 
    KEY "EMAIL_TYPE_INVALID_EMA" 
    KEY "token" 
    KEY "captcha-required" 

