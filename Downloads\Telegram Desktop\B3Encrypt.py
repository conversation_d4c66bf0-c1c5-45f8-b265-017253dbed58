import base64
import hmac
import hashlib
from Crypto.PublicKey import RSA
from Crypto.Cipher import AES, PKCS1_v1_5
from Crypto.Random import get_random_bytes
from Crypto.Util.Padding import pad

class Braintree:
    version = "1.3.10"
    
    @staticmethod
    def create(public_key):
        return Braintree.EncryptionClient(public_key)

    class EncryptionClient:
        def __init__(self, public_key):
            self.public_key = public_key
            self.version = Braintree.version

        def generate_rsa_key(self):
            try:
                key_bytes = base64.b64decode(self.public_key)
                rsa_key = RSA.importKey(key_bytes)
                cipher = PKCS1_v1_5.new(rsa_key)
                return cipher
            except Exception as e:
                raise ValueError("Invalid encryption key. Please use the key labeled 'Client-Side Encryption Key'")

        @staticmethod
        def generate_aes_key():
            class AesKey:
                def __init__(self):
                    self.key = get_random_bytes(32)

                def encrypt_with_iv(self, plaintext, iv):
                    if isinstance(plaintext, str):
                        plaintext = plaintext.encode()
                    cipher = AES.new(self.key, AES.MODE_CBC, iv)
                    padded = pad(plaintext, AES.block_size)
                    ct_bytes = cipher.encrypt(padded)
                    return base64.b64encode(iv + ct_bytes).decode('utf-8')

                def encrypt(self, plaintext):
                    iv = get_random_bytes(16)
                    return self.encrypt_with_iv(plaintext, iv)
            return AesKey()

        @staticmethod
        def generate_hmac_key():
            class HmacKey:
                def __init__(self):
                    self.key = get_random_bytes(32)

                def sign(self, message):
                    if isinstance(message, str):
                        message = base64.b64decode(message)
                    h = hmac.new(self.key, message, hashlib.sha256)
                    return base64.b64encode(h.digest()).decode('utf-8')
            return HmacKey()

        def encrypt(self, plaintext):
            rsa = self.generate_rsa_key()
            aes = self.generate_aes_key()
            hmac_key = self.generate_hmac_key()
            
            ciphertext = aes.encrypt(plaintext)
            signature = hmac_key.sign(ciphertext)
            
            combined_key = aes.key + hmac_key.key
            encoded_key = base64.b64encode(combined_key).decode('utf-8')
            encrypted_key = base64.b64encode(
                rsa.encrypt(encoded_key.encode())
            ).decode('utf-8')

            prefix = f"$bt4|javascript_{self.version.replace('.', '_')}$"
            
            return f"{prefix}{encrypted_key}${ciphertext}${signature}"