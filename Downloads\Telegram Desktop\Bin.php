<?php

namespace AsyncChkBot\Commands\Cmds\Tools;
require_once 'public/Cmds/Gen.php';

use AsyncChkBot\Db\Connection;
use AsyncChkBot\Db\Users;
use SergiX44\Nutgram\Nutgram;
use SergiX44\Nutgram\Handlers\Type\Command;
use AsyncChkBot\Commands\Cmds\Gates\Apis\Vbv;
use AsyncChkBot\Commands\Cmds\Gates\Apis\HttpX;
use function AsyncChkBot\Commands\Cmds\generateCardDetailss;
use MongoDB\Collection;

class Bin extends Command
{
    protected string $command = 'bin\s+(.+)';
    protected ?string $description = 'Bin Gateway';
    private const BIN_COLLECTION = 'bins';

    public function handle(Nutgram $bot, string $input): void
    {
        $this->registerUser($bot);
        
        $cardSegment = $this->extractCardSegment($input);
        $rawCard = $this->sanitizeCardNumber($cardSegment);
        
        if (!$this->validateCardLength($rawCard, $bot)) {
            return;
        }
        
        $bin = $this->extractBin($rawCard);
        $binInfo = $this->getBinInfo($bin);
        
        if ($binInfo === null) {
            $bot->sendMessage(
                '❌ BIN not found in the database.',
                reply_to_message_id: $bot->messageId()
            );
            return;
        }
        
        $cc = generateCardDetailss($bin,1);
        $all = explode('|', $cc);
        $cc = explode('|', $all[0]);
        $mes = explode('|', $all[1]);
        $ano = explode('|', $all[2]);
        $cvv = explode('|', $all[3]);
        // print_r($cvv);
        $Vbv = new Vbv(new HttpX());
        // print_r($cc);
        $b3auth = $Vbv->f();
        $token = $Vbv->f2($b3auth, $cc[0],$mes[0],$ano[0],$cvv[0]);
        $response = $Vbv->f3($token, $cc[0],$b3auth);
        // print_r($cc);
        // print($cc);
        $this->sendBinResponse($bot, $bin, $binInfo,$response);

        
    }

    private function registerUser(Nutgram $bot): void
    {
        Users::userDetails($bot, $bot->userId());
    }

    private function extractCardSegment(string $input): string
    {
        return trim(explode('|', $input)[0]);
    }

    private function sanitizeCardNumber(string $cardSegment): string
    {
        return preg_replace('/\D/', '', $cardSegment);
    }

    private function validateCardLength(string $rawCard, Nutgram $bot): bool
    {
        if (strlen($rawCard) >= 6) {
            return true;
        }
        
        $bot->sendMessage(
            '❌ Please provide at least the first 6 digits of the card number.',
            reply_to_message_id: $bot->messageId()
        );
        return false;
    }

    private function extractBin(string $rawCard): int
    {
        return (int) substr($rawCard, 0, 6);
    }

    private function getBinInfo(int $bin): ?array
    {
        $collection = $this->getBinCollection();
        $result = $collection->findOne(['_id' => $bin]);

        return $result ? [
            $result['country'],
            $result['emoji'],
            $result['bank'],
            $result['type'],
            $result['level'],
            $result['brand'],
        ] : null;
    }
    
    private function getBinCollection(): Collection
    {
        return Connection::getCollection(self::BIN_COLLECTION);
    }
    
    private function sendBinResponse(Nutgram $bot, string $bin, array $binInfo, array $response): void
    {
        $response = [
            "<b>BIN Lookup:</b> <code>{$bin}</code>",
            "<b>Country:</b> <code>{$binInfo[0]} {$binInfo[1]}</code>",
            "<b>Bank:</b> <code>{$binInfo[2]}</code>",
            "<b>Type:</b> <code>{$binInfo[3]}</code>",
            "<b>Level:</b> <code>{$binInfo[4]}</code>",
            "<b>Brand:</b> <code>{$binInfo[5]}</code>",
            "<b>VBV: </b> <code>{$response[0]} | {$response[1]}</code>",
        ];

        $bot->sendMessage(
            implode("\n", $response),
            reply_to_message_id: $bot->messageId(),
            parse_mode: 'HTML'
        );
    }
}