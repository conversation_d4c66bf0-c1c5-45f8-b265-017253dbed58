<?php



error_reporting(0);

function Capture($str, $starting_word, $ending_word){
$subtring_start  = strpos($str, $starting_word);
$subtring_start += strlen($starting_word);
$size            = strpos($str, $ending_word, $subtring_start) - $subtring_start;
return substr($str, $subtring_start, $size);
};

/* Must fill these details */

// Anchor Details

$anchor_link = 'https://www.google.com/recaptcha/api2/anchor?ar=1&k=6LexhS4gAAAAAA4GP_UDAT-9MEVnOGRG09cRYMWA&co=aHR0cHM6Ly9tZW5nby5jb20uYnI6NDQz&hl=pt-BR&v=mBwkfBPLFWI0ygbsp8eJNMkw&size=invisible&cb=xwgr5p2m31l0'; // It looks like: anchor?ar=1&k=
$anchor_ref  = 'https://mengo.com.br/'; // Open Anchor Headers and see the referer link

//----------------------------------------------------------------------------------//

// Reload Details

$reload_link = 'https://www.google.com/recaptcha/api2/reload?k=6LexhS4gAAAAAA4GP_UDAT-9MEVnOGRG09cRYMWA'; // It looks like: reload?k=
$v   = 'mBwkfBPLFWI0ygbsp8eJNMkw';  // Available in Anchor Query String Parameters
$k   = '6LexhS4gAAAAAA4GP_UDAT-9MEVnOGRG09cRYMWA';  // Available in Anchor Query String Parameters
$co  = 'aHR0cHM6Ly9tZW5nby5jb20uYnI6NDQz';  // Available in Anchor Query String Parameters
$chr = urlencode('70,12,58');  // Available in Reload's Request Payload (Post Field) and looks like: [21,71,92]
$bg  = '!Cw2gDQgKAAQeD-sUbQEHDwLOcHx1-I9EW3pP_YJ45tPg-OYHksquH2jKTqla7S3EZtvk3bcg_VeMnSXf4MlcIm6LNrNtpmu0quo5jqqeXsWBPP6gCP5aQq1cGrJSoc1LQBwYqIf4eUDHg6gEFd72FBiA4G6Mn4tr-7REHIbTy7ePiHpldnaHzjdGayAbBsavVFmVMTL3y3hpN_IMirmxvE1ShzFT2g0MmFXtf8xhIzXPEWtqiRS1nxucJ5ZnK9Dir_AyBZGTRfpDU25rhXv0kHp6mxhjPOKFSM26yTx72cZ7td5zYL1HqLOYtmeWfHDU81bjmybrZBCzGNwH95Re-sLuWcTZt2AenE6CMflpyF_6lO_YOmZ5ZKzRtslfXDGOXOIZiNPmgYfh07Xt4zpUJPkMvet7agZkDKs5ChsfhdRvpcAHxFCiwOuB2tq5Uf3Q14kfIstI_dkI0wwcYhgJvM00OgPCtta0y0kIiU1W8I8OgayEw9REeEggL3MkjC5jIQLTNtXImiuMlpczCq1yWTFmZca-bJ4tlMm1kntEfSrJmpoIXa4H2EqiMTGOHGTS4wcyHbHh-9vXBHCuX2U8W78otuSMSa1EDzVLdKelxIOwzQIPWNykwEsLpsNyknW3ZGzaa9-Un3SXYlW-fshRuuhocr193pmCUibwWMP1Y1VG72IB4QPi-r_mmDcWxLFHjb0jeNBOH0dGv6Ieo3ejjNYADEm5dgxN584Tyul6-3WUH-kBgwDZMhlHP2IrqapqpP3PGPyZ3QSptTGXDdQFZlnIU3Z4QU1bDAZ_B_GtduK8_fkaI05az-KyV7zwOjyKKAIZ6ma48-CgjIyJXRuZebhnZhztAvbSO6LAoPaE7MU_yDHCUrsSWf73msFO8v0gPdq4UanPvIv2PxBUKb86b6V3W1kmWJnTf6OwIbQvg0wt6fTnM4Z9ogg-Jbsf5QvTQ5KtBjjFx-Tj2oC138RDy5wANMRstKnODTrD_WDs5mthTEIaX5so3PIOqR4jMGrBRQd_50qOAc0_s7wSi6JBl8AJMopF9w8*';  // Available in Reload's Request Payload and is after the $chr and it starts from ! and ends with * (Exclude *)
$vh  = '132118568';  // Available in Reload's Request Payload and is after the $bg and select only the number (Sometimes - sign is also included in number, you have to take that - sign also)

//===================================== [ REQ ANCHOR ] =====================================//

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $anchor_link);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
'authority: www.google.com',
'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
'accept-language: en-US,en;q=0.9',
'referer: '.$anchor_ref.'',
'sec-fetch-dest: iframe',
'sec-fetch-mode: navigate',
'sec-fetch-site: cross-site',
'upgrade-insecure-requests: 1',
'user-agent: Mozilla/5.0 (Windows NT '.rand(11,99).'.0; Win64; x64) AppleWebKit/'.rand(111,999).'.'.rand(11,99).' (KHTML, like Gecko) Chrome/'.rand(11,99).'.0.'.rand(1111,9999).'.'.rand(111,999).' Safari/'.rand(111,999).'.'.rand(11,99).''));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

$result = curl_exec($ch);
curl_close($ch);

if (!strpos($result, "recaptcha-token")){
$json = [
  "msg" =>'Pagina Nao recarregada'
 ];

};

$rtk = Capture($result, '<input type="hidden" id="recaptcha-token" value="', '"');

//===================================== [ REQ RELOAD ] =====================================//

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $reload_link);
curl_setopt($ch, CURLOPT_HEADER, 0);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
'authority: www.google.com',
'accept: */*',
'accept-language: en-US,en;q=0.9',
'content-type: application/x-www-form-urlencoded',
'origin: https://www.google.com',
'referer: '.$reload_ref.'',
'user-agent: Mozilla/5.0 (Windows NT '.rand(11,99).'.0; Win64; x64) AppleWebKit/'.rand(111,999).'.'.rand(11,99).' (KHTML, like Gecko) Chrome/'.rand(11,99).'.0.'.rand(1111,9999).'.'.rand(111,999).' Safari/'.rand(111,999).'.'.rand(11,99).''));
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, 'v='.$v.'&reason=q&c='.$rtk.'&k='.$k.'&co='.$co.'&hl=en&size=invisible&chr='.$chr.'&vh='.$vh.'&bg='.$bg.'');
$result = curl_exec($ch);
curl_close($ch);


$captcha = Capture($result, '["rresp","', '"');

if (strpos($result, '"rresp","')){
$json = [
  "msg" =>'Captcha Bypassed Successfully',
  "captchaResponse" => $captcha,
  
];
}else{
	$json = [
  "msg" => 'Error to captcha',
];
};

print_r(json_encode($json, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT));



?>