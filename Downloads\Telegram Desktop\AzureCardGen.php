<?php

class AzureCardGen {
    private string $font;
    private GdImage $bg;
    private int $bgX;
    private array $cardTypes = ['visa', 'amex', 'mastercard', 'discovery'];

    public function __construct(public string $PATH_IMG, public string $PATH_FONTS) { }

    private function loadJpeg(string $file)
    {
        return imagecreatefromjpeg($this->PATH_IMG . '/' . $file);
    }

    private function loadPng(string $file)
    {
        return imagecreatefrompng($this->PATH_IMG. '/' .  $file);
    }

    public function setFontText(string $font): void
    {
        $this->font = $this->PATH_FONTS . '/' . $font;
    }

    public function setBackGround(string $file): void
    {
        $this->bg = $this->loadPng($file);
        $this->bgX = imagesx($this->bg);
    }

    public function setWaterMark(string $logo, int $size, int $dstX, int $dstY): void
    {
        $logo = $this->loadPng($logo);
        $this->resize($this->bg, $logo, $size, $dstX, $dstY);
        $this->destroy($logo);
    }

    private function getCardLogo(string $type)
    {
        return in_array($type, $this->cardTypes) ? $this->loadPng($type . '.png') : false;
    }

    private function resize(\GdImage $source, \GdImage $dest, int $size, int $dstX, int $dstY): void
    {
        $wm_x = imagesx($dest);
        $wm_y = imagesy($dest);
        # Calculate size
        $wm_w = intdiv($this->bgX, $size);
        $wm_h = intval(($wm_y / $wm_x) * $wm_w);
        imagecopyresized($source, $dest, $dstX, $dstY, 0, 0, $wm_w, $wm_h, $wm_x, $wm_y);
    }

    public function generate(array $data): GdImage
    {
        # expected: card data
        # $data['full_name']
        # $data['number']
        # $data['expiration']
        # $data['card_type']

        # Text color white
        $textColor = imagecolorallocate($this->bg, 255, 255, 255);
        # Write the $data['full_name']
        imagettftext($this->bg, 17, 0, 30, 350, $textColor, $this->font, $data['full_name']);
        # Write the card type logo on the image
        if ($cardMark = $this->getCardLogo($data['card_type'])) {
            $this->resize($this->bg, $cardMark, 9, 553, 307);
            $this->destroy($cardMark);
        }
        # Write the credit card number on the image
        imagettftext($this->bg, 18, 0, 30, 279, $textColor, $this->font, $data['number']);
        # Write the expiration date on the image
        imagettftext($this->bg, 6, 0, 30, 300, $textColor, $this->font,  "VALID THRU");
        imagettftext($this->bg, 11, 0, 30, 323, $textColor, $this->font,  $data['expiration']);
        return $this->bg;
    }

    public function debug(): void
    {
        imagepng($this->bg, 'test.png');
    }

    public function destroy(\GdImage $i): void
    {
        imagedestroy($i);
    }
}
