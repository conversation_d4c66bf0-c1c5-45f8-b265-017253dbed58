<?php 

//                                                                      By @Decoder008
//                                                                Now don't copy my style xD.

error_reporting(0);
set_time_limit(0);
date_default_timezone_set('America/Buenos_Aires');

if ($_SERVER['REQUEST_METHOD'] == "POST") {
    extract($_POST);
} elseif ($_SERVER['REQUEST_METHOD'] == "GET") {
    extract($_GET);
}

$bin = $_GET['lista'];

if (strlen($bin) > 6) {
  $bin = substr($bin, 0, 6);
}

function decoder($string, $start, $end)
{
  $str = explode($start, $string);
  $str = explode($end, $str[1]);
  $str = trim(strip_tags($str[0]));
  return $str;
}

# -------------------------------------------------------------------- [PROXY SECTION] --------------------------------------------------------------------- #

$rotate = array(
'qhfrsbmh-rotate:zmbdmkd87ia5',
'mbvysvrz-rotate:610mzp2slqpv',
'jniplplm-rotate:bdu77smhqbt4'
);

$userpwd = $rotate[array_rand($rotate)];

# ------------------------------------------------------------------------- [1 Req] ------------------------------------------------------------------------ #

$ch = curl_init();
curl_setopt($ch, CURLOPT_PROXY, "p.webshare.io:80");
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $userpwd);
curl_setopt($ch, CURLOPT_URL, 'https://binov.net/');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_POST, 1);
$headers = array();
$headers[] = 'authority: binov.net';
$headers[] = 'method: POST';
$headers[] = 'path: /';
$headers[] = 'scheme: https';
$headers[] = 'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9';
$headers[] = 'accept-language: en-US,en;q=0.9';
$headers[] = 'cache-control: max-age=0';
$headers[] = 'content-type: application/x-www-form-urlencoded';
$headers[] = 'cookie: _ym_uid=1629993482419299443; _ym_d=1629993482; _ym_isad=2; _ym_visorc=w';
$headers[] = 'origin: https://binov.net';
$headers[] = 'referer: https://binov.net/';
$headers[] = 'sec-ch-ua: "Chromium";v="92", " Not A;Brand";v="99", "Google Chrome";v="92"';
$headers[] = 'sec-ch-ua-mobile: ?0';
$headers[] = 'sec-fetch-dest: document';
$headers[] = 'sec-fetch-mode: navigate';
$headers[] = 'sec-fetch-site: same-origin';
$headers[] = 'sec-fetch-user: ?1';
$headers[] = 'upgrade-insecure-requests: 1';
$headers[] = 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_POSTFIELDS, 'BIN='.$bin.'&COUNTRY=1&BANK=1');
$result = curl_exec($ch);
$brand = decoder($result, '</tr><tr><td>'.$bin.'</td><td>','</td>');
$bank = decoder($result, '<td>'.$brand.'</td><td>','</td>');
$type = decoder($result, '<td>'.$bank.'</td><td>','</td>');
$level = decoder($result, '<td>'.$type.'</td><td>','</td>');
$country = decoder($result, '<td>'.$level.'</td><td>','</td>');
$time = curl_getinfo($ch)['total_time'];

# ----------------------------------------------------------------------- [RESPONSES] ---------------------------------------------------------------------- #

echo "𝙱𝙸𝙽: $bin<br>𝙱𝚁𝙰𝙽𝙳: $brand<br>𝙻𝙴𝚅𝙴𝙻: $level<br>𝙱𝙰𝙽𝙺: $bank<br>𝚃𝚈𝙿𝙴: $type<br>𝙲𝙾𝚄𝙽𝚃𝚁𝚈: $country<br>𝚃𝙸𝙼𝙴 𝚃𝙰𝙺𝙴𝙽: $time"." seconds";

curl_close($ch);
ob_flush();

//                                                                     Share With Credits

?>