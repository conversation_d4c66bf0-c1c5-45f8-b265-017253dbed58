import os
import time
from telethon.sync import TelegramClient

os.system('cls')
session = input('Ingresa tu sesión: ')
client = TelegramClient(session, 'API_ID', 'API_HASH')

client.start()

txt_file = input('Ingresa el nombre y extensión del archivo a recorrer: ')

if os.path.exists(txt_file):
    entity_input = input('Deseas enviar los mensajes a través de un username (1) o de un chat_id (2)?: ')

    try:
        entity_type = int(entity_input)
        
        if entity_type == 1 or entity_type == 2:
            if entity_type == 1:
                entity = input('Ingresa el username: ')
            else:
                entity = int(input('Ingresa el chat_id: '))   
            
            command_to_use = input('Ingresa el comando que se va a utilizar (en minúsculas): ')
            antispam = int(input('Ingresa el antispam que tendrá entre chequeos (en segundos): '))

            with open(txt_file, 'r') as file_:
                for line in file_:
                    client.send_message(entity, f"/{command_to_use} {line}")
                    time.sleep(antispam + 3)

            client.send_message(entity, 'Finalizó el proceso de chequeo automatico')
        else:
            print('Opción invalida. Por favor, elige entre 1 y 2.')
    except ValueError:
        print('Entrada invalida. Por favor, ingresa un número.')
else:
    print(f'El archivo {txt_file} no existe.')

client.disconnect()