<?php

namespace AsyncChkBot\Commands\Cmds;

use Async<PERSON>hkBot\Db\Users;
use SergiX44\Nutgram\Nutgram;
use AsyncChkBot\Db\Connection;
use SergiX44\Nutgram\Handlers\Type\Command;
use SergiX44\Nutgram\Telegram\Types\Keyboard\InlineKeyboardButton;
use SergiX44\Nutgram\Telegram\Types\Keyboard\InlineKeyboardMarkup;

class Gen extends Command
{
    protected string $command = 'gen ?(.*)?';

    protected ?string $description = 'Generates cards';

    public function handle(Nutgram $bot, string|null $bin)
    {

        if(empty($bin)) {
            return $bot->sendMessage("<b>Format -➤</b> <code>/gen bin</code>", parse_mode: "HTML", reply_to_message_id: $bot->messageId());
        }

        $users = Connection::getCollection("users");
        $user_id = $bot->userId();

        Users::userDetails($bot, $user_id);

        $role = $users->findOne(['_id' => $user_id])['role'];
        $text = $bot->message()->getText();
        $name = $bot->user()->first_name . $bot->user()->last_name;
        $cmdFunc = $this->parseSeparator($text);
        $cards_to_generate = isset($cmdFunc[2]) ? intval($cmdFunc[2]) : 10;
        $cards = generateCardDetails($cmdFunc[1], $cards_to_generate);
        $file_path = 'cards_' . $user_id . '.txt';
        file_put_contents($file_path, $cards);
        $text = '
    [<a href="https://t.me/AsyncCrew">木</a>] <u><i>Card Details »</i></u>
    ────────────────────────
' . $cards . '
    ────────────────────────

    [<a href="https://t.me/AsyncCrew">木</a>] <b>Checked By -»</b> <a href="tg://user?id=' . $user_id . '">' . $name . '</a> <b>[' . $role . ']</b>
    ';

        $keyboard = InlineKeyboardMarkup::make()->addRow(
            InlineKeyboardButton::make("Generate Again !", callback_data: 'gen_' . $cmdFunc[1] . '_' . $cards_to_generate),
            InlineKeyboardButton::make("Download File", callback_data: 'download_' . $user_id)
        );

        return $bot->sendMessage($text, parse_mode: "HTML", reply_to_message_id: $bot->messageId(), reply_markup: $keyboard);
    }

    public function parseSeparator($input)
    {
        $text = str_replace(array("\n", "\r"), ' ', $input);
        $input = explode(' ', $text);
        return $input;
    }

}


function generateRandomDigit()
{
    return random_int(0, 9);
}

function replaceXWithRandomDigits($pattern)
{
    $result = '';
    foreach (str_split($pattern) as $char) {
        if ($char == 'x') {
            $result .= generateRandomDigit();
        } else {
            $result .= $char;
        }
    }
    return $result;
}


function isLuhnValid($card_number)
{
    $reversed = strrev($card_number);
    $sum = 0;
    $shouldDouble = false;

    foreach (str_split($reversed) as $char) {
        $digit = intval($char);
        if ($shouldDouble) {
            $digit *= 2;
            if ($digit > 9) {
                $digit -= 9;
            }
        }
        $sum += $digit;
        $shouldDouble = !$shouldDouble;
    }

    return ($sum % 10) == 0;
}

function generateValidCardNumber($prefix, $length)
{
    $card_number = replaceXWithRandomDigits($prefix);

    while (strlen($card_number) < $length) {
        $card_number .= generateRandomDigit();
    }


    if (isLuhnValid($card_number)) {
        return $card_number;
    } else {

        return generateValidCardNumber($prefix, $length);
    }
}

function generateCvv($card_number, $provided_cvv = null)
{
    if ($provided_cvv !== null) {
        $cvv = replaceXWithRandomDigits($provided_cvv);

        if (strlen($cvv) == 3 || strlen($cvv) == 4) {
            return $cvv;
        }
    }

    $prefix = intval($card_number[0]);
    if ($prefix == 3) {
        return random_int(1000, 9999);
    } else {
        return random_int(100, 999);
    }
}


function generateExpirationMonth($provided_month = null)
{
    $current_month = intval(date('m'));

    if ($provided_month !== null && is_numeric($provided_month) && intval($provided_month) >= 1 && intval($provided_month) <= 12) {
        return str_pad($provided_month, 2, '0', STR_PAD_LEFT);
    }

    return str_pad(random_int($current_month, 12), 2, '0', STR_PAD_LEFT);
}


function generateExpirationYear($provided_year = null)
{
    $current_year = intval(date('Y'));

    if ($provided_year !== null && is_numeric($provided_year)) {
        if (strlen($provided_year) == 2) {
            return intval("20" . $provided_year);
        }

        if (intval($provided_year) >= $current_year) {
            return intval($provided_year);
        }
    }

    return random_int($current_year, $current_year + 5);
}

function generateCardDetails($bin_pattern, $number_of_cards)
{
    $pattern_parts = explode('|', $bin_pattern);
    $card_number_pattern = $pattern_parts[0];
    $provided_month = $pattern_parts[1] ?? null;
    $provided_year = $pattern_parts[2] ?? null;
    $provided_cvv = $pattern_parts[3] ?? null;

    $cards = "";

    for ($i = 0; $i < $number_of_cards; $i++) {
        $card_number = generateValidCardNumber($card_number_pattern, 16);
        $exp_month = generateExpirationMonth($provided_month);
        $exp_year = generateExpirationYear($provided_year);
        $cvv = generateCvv($card_number, $provided_cvv);

        $cards .= "<code>{$card_number}|{$exp_month}|{$exp_year}|{$cvv}</code>\n";
    }

    return $cards;
}

function generateCardDetailss($bin_pattern, $number_of_cards)
{
    $pattern_parts = explode('|', $bin_pattern);
    $card_number_pattern = $pattern_parts[0];
    $provided_month = $pattern_parts[1] ?? null;
    $provided_year = $pattern_parts[2] ?? null;
    $provided_cvv = $pattern_parts[3] ?? null;

    $cards = "";

    for ($i = 0; $i < $number_of_cards; $i++) {
        $card_number = generateValidCardNumber($card_number_pattern, 16);
        $exp_month = generateExpirationMonth($provided_month);
        $exp_year = generateExpirationYear($provided_year);
        $cvv = generateCvv($card_number, $provided_cvv);

        $cards .= "{$card_number}|{$exp_month}|{$exp_year}|{$cvv}\n";
    }

    return $cards;
}



