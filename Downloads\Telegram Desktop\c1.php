<?php

function GetStr($string, $start, $end) {
    $str = explode($start, $string);
    $str = explode($end, $str[1]);  
    return $str[0];
}

$retry = 0;
retry:
if ($retry > 50){
  return;
}
sleep(2);
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api.anycaptcha.com/createTask');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_POST, 1);
$headers = array();
$headers[] = 'Host: api.anycaptcha.com';
$headers[] = 'Content-Type: application/json';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_POSTFIELDS, '{
	"clientKey": "c6b1769c17094b23a2024955e0f8cffc",
	"task": {
		"type": "RecaptchaV2TaskProxyless",
		"websiteURL": "https://agedonations.ageuk.org.uk/",
		"websiteKey": "6Lfx6sgaAAAAAGITjPrkZAbt6_n3M4ZxJRGs1tCy",
		"isInvisible": true
	}');
$r1 = curl_exec($ch);
curl_close($ch);
$taskid = GetStr($r1, '"taskId":','}');

sleep(100);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api.anycaptcha.com/getTaskResult');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_POST, 1);
$headers = array();
$headers[] = 'Host: api.anycaptcha.com';
$headers[] = 'Content-Type: application/json';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_POSTFIELDS, '{
	"clientKey": "c6b1769c17094b23a2024955e0f8cffc",
	"taskId": '.$taskid.'
}');
$r2 = curl_exec($ch);
curl_close($ch);
$gcaptcha = GetStr($r2, '{"gRecaptchaResponse":"','"}');

if(strpos($r2, 'processing')){
    $retry++;
    goto retry;
}
elseif(strpos($r2, 'Maximum execution time of 120 seconds exceeded')){
    $retry++;
    goto retry;

}

// echo $r2;
// echo "<br>";
// echo "<br>";
// echo "<br>";
// echo "<br>";
// echo "<br>";
echo $gcaptcha;

?>