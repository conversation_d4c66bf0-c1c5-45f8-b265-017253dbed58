﻿[WLTYPE]
Name=MailPass
Regex=^*.@.*:.*$
Verify=True
Separator=:
Slices=USER,PASS

[WLTYPE]
Name=SDT
Regex=^[0-9]*$|
Verify=True
Separator=|
Slices=NUM,DATEM,DATEY,CVV

[WLTYPE]
Name=Nghia
Regex=^[0-9]*$
Verify=True
Separator=:
Slices=USER:PASS

[WLTYPE]
Name=UserPass
Regex=^*.:.*$
Verify=True
Separator=:
Slices=USER,PASS

[WLTYPE]
Name=Numeric
Regex=^[0-9]*$
Verify=True
Separator=
Slices=CODE

[WLTYPE]
Name=URLs
Regex=^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$
Verify=True
Separator=
Slices=URL

[CUSTOMKC]
Name=FREE
Color=OrangeRed

[CUSTOMKC]
Name=2FACTOR
Color=Maroon

[CUSTOMKC]
Name=EXPIRED
Color=Crimson

[CUSTOMKC]
Name=CUSTOM
Color=OrangeRed

[EXPFORMAT]
Format=<CAPTURE>

[EXPFORMAT]
Format=<DATA>:<PROXY>:<CAPTURE>