<?php
/**
 * ImageToText - A PHP class that extracts text from a base64-encoded image using Tesseract OCR.
 *
 * This script:
 * - Takes a base64-encoded image as input.
 * - Decodes and saves it as a temporary file.
 * - Detects the image type (JPEG, PNG, GIF) and loads it accordingly.
 * - Applies preprocessing filters to enhance OCR accuracy:
 *   - Converts image to grayscale.
 *   - Adjusts brightness and contrast.
 *   - Applies Gaussian blur for noise reduction.
 * - Saves the preprocessed image as PNG for better OCR results.
 * - Uses Tesseract OCR to extract text from the processed image.
 * - Deletes temporary image files after processing.
 *
 * Requirements:
 * - PHP with GD extension enabled.
 * - Tesseract OCR installed on the system.
 * - Composer installed with `thiagoalessio/tesseract_ocr` package.
 *
 * Coded by: @ILoveTightAss
 */
include __DIR__ . '/vendor/autoload.php';
use thiagoalessio\TesseractOCR\TesseractOCR;

class ImageToText {
    private $base64;
    private $imageFile;
    private $outputFile;
    private $tesseractPath;

    public function __construct($base64, $tesseractPath = 'C:\Program Files\Tesseract-OCR\tesseract.exe') /* use this if tesseract is not available in path */{
        $this->base64 = $base64;
        $this->tesseractPath = $tesseractPath;
        $this->imageFile = __DIR__ . '/input_image';  
        $this->outputFile = __DIR__ . '/processed_image.png';  
    }

    private function decodeImage() {
        file_put_contents($this->imageFile, base64_decode($this->base64));
        $imageInfo = getimagesize($this->imageFile);
        if (!$imageInfo) {
            throw new Exception("Invalid image data.");
        }

        switch ($imageInfo[2]) {
            case IMAGETYPE_JPEG:
                $image = imagecreatefromjpeg($this->imageFile);
                break;
            case IMAGETYPE_PNG:
                $image = imagecreatefrompng($this->imageFile);
                break;
            case IMAGETYPE_GIF:
                $image = imagecreatefromgif($this->imageFile);
                break;
            default:
                throw new Exception("Unsupported image type.");
        }

        if (!$image) {
            throw new Exception("Failed to load image.");
        }

        return $image;
    }

    private function preprocessImage($image) {
        imagefilter($image, IMG_FILTER_GRAYSCALE);
        imagefilter($image, IMG_FILTER_BRIGHTNESS, 20);
        imagefilter($image, IMG_FILTER_CONTRAST, -25);
        for ($i = 0; $i < 2; $i++) {  
            imagefilter($image, IMG_FILTER_GAUSSIAN_BLUR);
        }
        imagepng($image, $this->outputFile);
        imagedestroy($image);
    }

    public function extractText() {
        try {
            $image = $this->decodeImage();
            $this->preprocessImage($image);

            $text = (new TesseractOCR($this->outputFile))
                ->executable($this->tesseractPath)
                ->run();

            $this->cleanup();
            return trim($text); 
        } catch (Exception $e) {
            $this->cleanup();
            return "Error: " . $e->getMessage();
        }
    }

    private function cleanup() {
        if (file_exists($this->imageFile)) unlink($this->imageFile);
        if (file_exists($this->outputFile)) unlink($this->outputFile);
    }
}

// Example Usage
$base64Image = base64_encode(file_get_contents('huu.jpg'));
$ocr = new ImageToText($base64Image);
$text = $ocr->extractText();
echo "Extracted Text: " . $text;
