<?php

class _chung
{
    public function xulythe($lista)
    {
        $randomzip = rand(00000,99999);
        $lista = $lista;
        $lista = trim(str_replace(array("\\\"", "\\'"), array("\"", "'"), $lista));
        $lista = str_replace("\r\r", "\r", $lista);
        $lista = str_replace("\n\n", "\n", $lista);
        $in_fo = $this->info($lista);
        if(empty($in_fo['zip'])) $in_fo['zip'] = ''.$randomzip.''; 
        if(empty($in_fo['cvv'])) $in_fo['cvv'] = 000 ; 
        
        // Kiểm tra nếu các trường thiết yếu thiếu và trả về lỗi
        if(empty($in_fo['num']) || empty($in_fo['mon']) || empty($in_fo['year'])){
            die(json_encode(['status' => 'unk', 'message' => 'Please, Check your card information.']));
        }

        return [
            'n' => $in_fo['num'],
            'm' => $in_fo['mon'],
            'y' => $in_fo['year'],
            'c' => $in_fo['cvv'],
            't' => $in_fo['type'],
            'z' => $in_fo['zip']
        ];
    }

    function info($ccline)
    {
        $xy = array("|", "\\", "/", "-", ";", " ", "//", "||", "|||", ":");
        $sepe = $xy[0];
        foreach ($xy as $v) {
            if (substr_count($ccline, $sepe) < substr_count($ccline, $v)) $sepe = $v;
        }
        $x = explode($sepe, $ccline);
        foreach ($xy as $y) $x = str_replace($y, "", str_replace(" ", "", $x));
        foreach ($x as $xx) {
            $xx = trim($xx);
            if (is_numeric($xx)) {
                $yy = strlen($xx);
                switch ($yy) {
                    case 15:
                        // Amex có số thẻ 15 chữ số
                        if (substr($xx, 0, 1) == 3) {
                            $ccnum['num'] = $xx;
                            $ccnum['type'] = "Amex"; // Nhận diện thẻ Amex
                        }
                        break;
                    case 16:
                        // Các loại thẻ Visa, Mastercard, Discover có 16 chữ số
                        switch (substr($xx, 0, 1)) {
                            case '4':
                                $ccnum['num'] = $xx;
                                $ccnum['type'] = "visa";
                                break;
                            case '5':
                                $ccnum['num'] = $xx;
                                $ccnum['type'] = "mastercard";
                                break;
                            case '6':
                                $ccnum['num'] = $xx;
                                $ccnum['type'] = "discover";
                                break;
                        }
                        break;
                    case 1:
                        // Đảm bảo tháng luôn có 2 chữ số
                        if (($xx >= 1) and ($xx <= 12) and (!isset($ccnum['mon']))) {
                            $ccnum['mon'] = str_pad($xx, 2, "0", STR_PAD_LEFT);
                        }
                    case 2:
                        if (($xx >= 1) and ($xx <= 12) and (!isset($ccnum['mon']))) {
                            $ccnum['mon'] = str_pad($xx, 2, "0", STR_PAD_LEFT);
                        }
                        elseif (($xx >= 9) and ($xx <= 99) and (isset($ccnum['mon'])) and (!isset($ccnum['year']))) {
                            $ccnum['year'] = "20" . $xx;
                        }
                        break;
                    case 4:
                        // Năm đầy đủ (2026, 2027, v.v.)
                        if (($xx >= 2023) and ($xx <= 2099) and (isset($ccnum['mon']))) {
                            $ccnum['year'] = $xx;
                        }
                        elseif ((substr($xx, 0, 2) >= 1) and (substr($xx, 0, 2) <= 12) and (substr($xx, 2, 2) >= 9) and (substr($xx, 2, 2) <= 99) and (!isset($ccnum['mon'])) and (!isset($ccnum['year']))) {
                            $ccnum['mon'] = substr($xx, 0, 2);
                            $ccnum['year'] = "20" . substr($xx, 2, 2);
                        } else $ccv['cv4'] = $xx; // CVV 4 chữ số cho thẻ Amex
                        break;
                    case 6:
                        // Xử lý định dạng yy/mm và mm/yy
                        if ((substr($xx, 0, 2) >= 1) and (substr($xx, 0, 2) <= 12) and (substr($xx, 2, 4) >= 2023) and (substr($xx, 2, 4) <= 2099)) {
                            $ccnum['mon'] = substr($xx, 0, 2);
                            $ccnum['year'] = substr($xx, 2, 4);
                        }
                        elseif ((substr($xx, 0, 2) >= 9) and (substr($xx, 0, 2) <= 99) and (substr($xx, 2, 2) >= 1) and (substr($xx, 2, 2) <= 12)) {
                            // Định dạng yy/mm
                            $ccnum['year'] = "20" . substr($xx, 0, 2);
                            $ccnum['mon'] = substr($xx, 2, 2);
                        }
                        break;
                    case 5:
                        // Mã ZIP (US only)
                        if (intval($xx) >= 1000 && strlen($xx) == 5) {
                            $ccnum['zip'] = $xx;
                        }
                        break;
                    case 3:
                        // CVV 3 chữ số cho Visa, Mastercard, Discover
                        $ccv['cv3'] = $xx;
                        break;
                }
            } else if (strlen($xx) == 10 && strpos($xx, '-') !== false && !isset($ccv['zip'])) {
                list($z1, $z2) = explode('-', $xx);
                if (is_numeric($z1) && is_numeric($z2))
                    $ccnum['zip'] = $xx;
                break;
            } else if (isset($_POST['ccinter'])) {
                if (strlen($xx) == 6) {
                    $ccnum['zip'] = $xx;
                }
            }
        }
        if (isset($ccnum['num']) and isset($ccnum['mon']) and isset($ccnum['year'])) {
            // Nếu thẻ là Amex, CVV sẽ là 4 chữ số
            if ($ccnum['type'] == "Amex") {
                $ccnum['cvv'] = $ccv['cv4'];
            } else {
                $ccnum['cvv'] = $ccv['cv3']; // Các loại thẻ khác có CVV 3 chữ số
            }
            return $ccnum;
        } else {
            return false; // Trả về false nếu thiếu thông tin
        }
    }
}
