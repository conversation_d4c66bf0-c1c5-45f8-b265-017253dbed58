from urllib.parse import urlparse, parse_qs
import re
import requests


def recaptchav3_bypass(anchor_url: str, reload_url: str) -> str:
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36 Edg/121.0.0.0"
    }
    url_var = parse_qs(urlparse(anchor_url).query)

    r = requests.get(anchor_url, headers=headers)
    anchor_token = re.search(
        r'type="hidden" id="recaptcha-token" value="([^"]+)"', r.text
    ).group(1)
    value1 = url_var["v"][0]
    value2 = url_var["k"][0]
    value3 = url_var["co"][0]

    data = f"v={value1}&reason=q&c={anchor_token}&k={value2}&co={value3}&hl=en&size=invisible"
    headers.update(
        {"Referer": r.url, "Content-Type": "application/x-www-form-urlencoded"}
    )
    r = requests.post(reload_url, headers=headers, data=data)
    return r.text.split('["rresp","')[1].split('"')[0]


if __name__ == "__main__":
    anchor_url = ""
    reload_url = ""
    result = recaptchav3_bypass(anchor_url, reload_url)
    print("Bypassed RecaptchaV3:", result)
