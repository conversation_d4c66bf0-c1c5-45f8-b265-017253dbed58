
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title>3mk baqer 😈 🔥♛</title>
    <link rel="icon" type="image/x-icon" href="assets/favicon.png"/>
    <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
    <link href="style.css" rel="stylesheet" type="text/css" />
    <script src="assets/js/loader.js"></script>
    

    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&display=swap" rel="stylesheet">
    <link href="assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    <link href="assets/css/apexcharts.css" rel="stylesheet" type="text/css">
    <link href="assets/css/dash_2.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="assets/css/theme-checkbox-radio.css">
    <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

</head>
<title></title>


<!--تحذف ذا بس -->

<style>
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@800&display=swap');
*{
	padding: 0;
	margin: 0;
	box-sizing: border-box;
}
body{
  font-family: 'Open Sans', sans-serif;
	background-color: #0d1117;
	color: #D5D8DC;
	font-size: 14px;
}
.container{
	margin: 50px auto;
}
.card, .card-body{
	background-color: #4B0082;
	border-radius: 10px;
}
	.form-control {
		background-color: dark gray;
		border: none;
		font-size: 15px;
	}
	.form-control:focus {
		border: none;
		background-color: dark gray;
		color: #fff;
	}
	.btn{
		border-radius: 18px;
	}
  nav {
		color: #4B0082;
		border-radius: 15px;
		background-color: #000;
	}
	a{
		color: #fff;
	}
	.navbar-nav{
		padding: 12px;
	}
	a:hover{
		border-radius: 15px;
	}
 img{
	 width: 50px;
	 height: 50px;
	 float: right;
	 border-radius: 25px;
 }
</style>
</head>
<body>
&nbsp
<!--الى هنا-->


<body class="alt-menu sidebar-noneoverflow"onload="ccgen();">
    <!-- BEGIN LOADER -->
    <div id="load_screen"> <div class="loader"> <div class="loader-content">
        <div class="spinner-grow align-self-center"></div>
    </div></div></div>
    <!--  END LOADER -->

    <!--  BEGIN NAVBAR  -->
    <div class="header-container">
        <header class="header navbar navbar-expand-sm bg-gradient-dark col-lg-12 col-md-12 col-sm-12 col-xl-12">

            <a class="navbar-brand" href="#"></svg><span class="navbar-brand-name" style="font-weight: bold;">3mk baqer 😈 🔥♛</span></a>
            </header>
    </div>
    <!--  END MAINBAR  -->

    <!--  END NAVBAR  -->

    <!--  BEGIN MAIN CONTAINER  -->
    <div class="main-container" id="container">

        <div class="overlay"></div>
        <div class="search-overlay"></div>

        
        <!--  BEGIN CONTENT PART  -->
        <div id="content" class="main-content">
            <div class="layout-px-spacing">

            

<div class="widget-list row">
                
                <!-- /.widget-holder -->
                <div class="widget-holder widget-full-height widget-flex col-lg-6">
                    <div class="md-form">
    <div class="col-md-12">
        
        
<button type="button" class="btn btn-success btn-block" onclick="playClick();" id='hideshow'>BIN CHECKER</button><href="binlist.net"/>
                 <!-- ===== START OF SCREEN ===== -->
<center>
<div class="panel">
<iframe src="BIN-CHECKER/index.php" class="content" style="border:0;display:none" width="440" height="725">
</iframe>
</div>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.1.1/jquery.min.js"></script>
</center>   
                 <!-- ===== END OF SCREEN ===== -->
                 
<button type="button" class="btn btn-primary btn-block" onclick="modalCCGEN();">CC GEN</button>

        
        
        
  <textarea type="text" style="text-align: center;" id="lista" class="md-textarea form-control" rows="10" placeholder="xxxxxxxxxxxxxxxxxxx|xx|xxxx|xxx"></textarea>
  <label for="lista"></label>
  <div name="lines_count" type="hidden" id="lines" style="display:none;" >Loading...</div>
</div>
</div>
<center>
    
    
    
    
    
    
<button type="button" class="btn btn-primary" id="submit" onclick="start()">Start</button>
<button type="button" class="btn btn-danger">Stop</button>
</center>

<br></br>
<select class="form-control h-10" id="gate" onchange="check()">




<option value="null" style="color: red;">3mk baqer CHeckers 😈 🔥♛</option>
<option  value="captest.php">captest</option>
<option  value="solve.php">capsolve</option>
<option  value="AUTH-B3steal.php">AUTH-B3steal</option>
<option  value="b3woo.php">b3woo</option>
<option value="null" style="color: orange;"> ----- 3mk baqer AUTH CHeckers 😈 🔥♛ -----</option>
<option  value="AUTH-ADYEN.php">AUTH-ADYEN</option>
<option  value="AUTHWOO-CYBER.php">AUTHWOO-CYBER</option>
<option  value="CYBERAUTH-PAYMENT.php">CYBERAUTH-PAYMENT</option>
<option  value="AUTH-B3.php">AUTH-BRAINTREE</option>
<option  value="AUTH-B3-2.php">AUTH-BRAINTREE-2</option>
<option  value="AUTH-B3-3.php">AUTH-BRAINTREE-3</option>
<option  value="AUTH-B3-4.php">AUTH-BRAINTREE-4</option>
<option  value="AUTH-B3-5.php">AUTH-BRAINTREE-5</option>
<option  value="AUTH-B3-CCN.php">AUTH-B3-CCN</option>
<option  value="AUTH-SQUAREUP.php">AUTH-SQUAREUP</option>
<option  value="STRIPE-AUTH.php">Stripe-AUTH</option>
<option  value="STRIPE-AUTH2.php">Stripe-AUTH2</option>
<option value="null" style="color: pink;"> ----- 3mk baqer CHARGE CHeckers 😈 🔥♛ -----</option>
<option  value="CHASE-PAYMENT.php">CHASE-PAYMENT</option>
<option  value="CHASE-MAGENTO.php">CHASE-MAGENTO</option>
<option  value="CHASE-MAGENTO2.php">CHASE-MAGENTO2</option>
<option  value="CHASE-CCN.php">CHASE-CCN</option>
<option  value="CHASE-PAYMENT-CCNAUTH.php">CHASE-PAYMENT-CCNAUTH</option>
<option  value="CYBERSURCE-PAYMENT.php">CYBERSURCE-PAYMENT</option>
<option  value="adyen.php">adyen</option>
<option  value="ADYEN-CCN.php">ADYEN-CCN</option>
<option  value="ADYEN-FAMOUSsite.php">ADYEN-FAMOUSsite</option>
<option  value="STRIPE-UNK.php">STRIPE-UNK</option>
<option  value="SHOPIFY.php">SHOPIFY</option>
<option  value="SHOPIFY-2.php">SHOPIFY-2</option>
<option  value="SHOPIFY + B3.php">SHOPIFY + B3</option>
<option  value="BRAINTREE-DONATE.php">BRAINTREE-DONATE</option>
<option  value="B3-CCN-CHARGE.php">B3-CCN-CHARGE</option>
<option  value="B3-CCN-CHARGE 2.php">B3-CCN-CHARGE 2</option>
<option  value="B3-CCN-CHARGE 3.php">B3-CCN-CHARGE 3</option>
<option  value="BRAINTREE-WOO.php">BRAINTREE-WOO</option>
<option  value="BRAINTREE-WOO2.php">BRAINTREE-WOO2</option>
<option  value="BRAINTREE-WOO3.php">BRAINTREE-WOO3</option>
<option  value="BRAINTREE-WOO4.php">BRAINTREE-WOO4</option>
<option  value="BRAINTREE-WOO5.php">BRAINTREE-WOO5</option>
<option  value="B3.php">BRAINTREE</option>
<option  value="B32.php">BRAINTREE2</option>
<option  value="B33.php">BRAINTREE3</option>
<option  value="B34.php">BRAINTREE4</option>
<option  value="MONERIS-WOO.php">MONERIS-WOO</option>
<option  value="MONERIS-WOO 2.php">MONERIS-WOO 2</option>
<option  value="moners.php">moners</option>
<option  value="moners2.php">moners2</option>
<option  value="moners3.php">moners3</option>
<option  value="stripe.php">STRIPE</option>
<option  value="STRIPE-2.php">STRIPE-2</option>
<option  value="STRIPE-CVV.php">Stripe-CVV&CCN</option>
<option  value="PAYFLOW.php">PAYFLOW</option>
<option  value="UNKNOWN-CCN CHARGE.php">UNKNOWN-CCN CHARGE</option>
<option  value="UNKNOWN.php">UNKNOWN</option>
<option  value="UNKNOWN-2.php">UNKNOWN-2</option>
<option  value="UNKNOWN-3.php">UNKNOWN-3</option>
<option  value="STRIPE-WOO.php">STRIPE-WOO</option>
<option  value="STRIPE-WOO2.php">STRIPE-WOO2</option>
<option  value="STRIPE-WOO3.php">STRIPE-WOO3</option>
<option  value="STRIPE-WOO4.php">STRIPE-WOO4</option>
<option  value="STRIPE-WOO5.php">STRIPE-WOO5</option>
<option  value="UNKNOWN-WOO.php">UNKNOWN-WOO</option>
<option  value="UNKNOWN-WOO 2.php">UNKNOWN-WOO2</option>
<option  value="2-3req.php">2-3req</option>
<option  value="2-3req-2.php">2-3req-2</option>
<option  value="2-3req-3.php">2-3req-3</option>
<option  value="2-3req-4.php">2-3req-4</option>
<option  value="2-3req-5.php">2-3req-5</option>
<option  value="2-3req-6.php">2-3req-6</option>
<option  value="v3.php">v3</option>
<option  value="Cybersource.php">Cybersource</option>
<option  value="Cybersource-2.php">Cybersource-2</option>
<option  value="payway.php">PAYWAY</option>
<option  value="sk based.php">sk based</option>
<option  value="ccn charge.php">ccn charge</option>
<option  value="shit.php">shit</option>
<option  value="api.php">api</option>
<option  value="test1.php">test1</option>
<option  value="test2.php">test2</option>
<option  value="test3.php">test3</option>
<option  value="test4.php">test4</option>
<option  value="test5.php">test5</option>
<option  value="test6.php">test6</option>
<option  value="test7.php">test7</option>
<option  value="test8.php">test8</option>
<option  value="test9.php">test9</option>
<option  value="test10.php">test10</option>
<option  value="test11.php">test11</option>
<option value="null" style="color: yellow;"> ----- 3mk baqer TOOLS 😈 🔥♛ -----</option>
<br></br>
<option  value="PROXY LOOKUP WITH ALL INFO.php" style="color: white;">PROXY LOOKUP WITH ALL INFO</option>
<option  value="PROXY CHECKER WITH ALL INFO.php" style="color: white;">PROXY CHECKER WITH ALL INFO</option>
<option  value="PROXY CHECKER.php" style="color: white;">PROXY CHECKER</option>
<option  value="SITE SQL FINDER.php" style="color: white;">SITE SQL FINDER </option>
<option  value="SITES DOMAIN FILTER.php" style="color: white;">SITES DOMAIN FILTER </option>
<option  value="DORK SEARCHER.php" style="color: white;">DORK SEARCHER </option>
<option  value="SITE LOOKUP-SEARCHER.php" style="color: white;">LOOKUP-SEARCHER SITES </option>
<option  value="Magento GATE FINDER.php" style="color: white;">Magento GATE FINDER</option>
<option  value="SITE GATE FINDER.php" style="color: white;">SITE GATE FINDER</option>
<option  value="SITE GATE FINDER-V2.php" style="color: white;">SITE GATE FINDER-V2</option>
<option  value="SITE AUTH FINDER.php" style="color: white;">SITE AUTH FINDER</option>
<option  value="SK CHECKER.php" style="color: white;">SK CHECKER</option>
<option  value="LOGS CC FILTER.php" style="color: white;">LOGS CC FILTER</option>
<option  value="BINS FILTER.php" style="color: white;">BINS FILTER</option>
<option  value="antipublic.php" style="color: white;">antipublic</option>
<option  value="FILTER CC IV.php" style="color: white;">FILTER CC IV</option>
<option  value="VBV&BIN-LOOKUP.php" style="color: white;">VBV&BIN-LOOKUP BEST BUT SLOW</option>
<option  value="VBV&BIN-LOOKUP-2.php" style="color: white;">VBV&BIN-LOOKUP BEST BUT SLOW-2</option>
<option  value="BIN-LOOKUP-V2 BEST.php" style="color: white;">BIN-LOOKUP-V2 BEST</option>
<option  value="BIN-LOOKUP.php" style="color: white;">BIN-LOOKUP</option>
<option  value="VBV-LOOKUP.php" style="color: white;">VBV-LOOKUP</option>
<option  value="billing address maker.php" style="color: white;">Billing address maker</option>
<option  value="SHEIN-CHECKER.php" style="color: white;">SHEIN-CHECKER</option>
<option  value="NOMAD-CHECKOUTER.php" style="color: white;">NOMAD-CHECKOUTER</option>
<option  value="CHEGG-CHECKER.php" style="color: white;">CHEGG-CHECKER</option>
<option  value="SHAHID-VIP-CHECKER.php" style="color: white;">SHAHID-VIP-CHECKER</option>
<option  value="RandomWords-GEN.php" style="color: white;">RandomWords-GEN</option>
<option  value="NETFLIX -CHECKER.php" style="color: white;">NETFLIX -CHECKER</option>
<option  value="SNAPCHAT-CHECKER.php" style="color: white;">SNAPCHAT-CHECKER</option>
<option  value="Activision-CHECKER.php" style="color: white;">Activision-CHECKER</option>
<option  value="mullvad CHECKER.php" style="color: white;">mullvad CHECKER</option>
<option  value="PYPROXY -CHECKER.php" style="color: white;">PYPROXY-CHECKER</option>
<option  value="SOAXPROXY-GEN&CHECKER.php" style="color: white;">SOAXPROXY-GEN&CHECKER</option>
<option  value="WEBSHAREPROXY-GEN&CHECKER.php" style="color: white;">WEBSHAREPROXY-GEN&CHECKER</option>
<option  value="LunaProxy-CHECKER.php" style="color: white;">LunaProxy-CHECKER</option>
<option  value="oxylabs.io -CHECKER.php" style="color: white;">oxylabs.io -CHECKER</option>
<option  value="TIKTOK FOLLOWERS LOOKUP.php" style="color: white;">TIKTOK FOLLOWERS LOOKUP</option>
<option  value="CYBERGHOST CHECKER.php" style="color: white;">CYBERGHOST CHECKER</option>
<option  value="HOSTGATOR-CHECKER.php" style="color: white;">HOSTGATOR-CHECKER</option>
<option  value="BLUE-HOST-CHECKER.php" style="color: white;">BLUE-HOST-CHECKER</option>
<option  value="WHM REAPER.php" style="color: white;">WHM REAPER</option>
<option  value="GAYS REAPER .php" style="color: white;">GAYS REAPER</option>
<option  value="fastcomet REAPER.php" style="color: white;">fastcomet REAPER</option>
<option  value="SA NUMBER LOOKUP NAMES.php" style="color: white;">SA NUMBER LOOKUP NAMES</option>
<option  value="SA NUMBER GRAPER.php" style="color: white;">VALID SA NUMBER GRAPER</option>
<option  value="UAE NUMBER GRAPER.php" style="color: white;">VALID UAE NUMBER GRAPER</option>
<option  value="CANADA NUMBER GRAPER.php" style="color: white;">VALID CANADA NUMBER GRAPER</option>
</select>






                </div>

                <div class="widget-holder widget-full-content widget-full-height col-lg-6">

                        <div class="widget-four">
                            
                            <div class="widget-content">
                                <div class="vistorsBrowser">
                                    <div class="browser-list">
                                        <div class="w-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                                        </div>
                                        <div class="w-browser-details">
                                            <div class="w-browser-info">
                                                <h6>Cvv</h6>
                                                <span class="badge rounded-pill bg-primary" id="cvv_count"></span>
                                            </div>
                                            <div class="w-browser-stats">
                                                <div class="progress">
                                                    <div class="progress-bar bg-gradient-primary" id ="cvv_count_bar" role="progressbar" style="width: 0%" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="browser-list">
                                        <div class="w-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                                        </div>
                                        <div class="w-browser-details">
                                            
                                            <div class="w-browser-info">
                                                <h6>Ccn</h6>
                                                <span class="badge rounded-pill bg-success" id="ccn_count"></span>
                                            </div>

                                            <div class="w-browser-stats">
                                                <div class="progress">
                                                    <div class="progress-bar bg-gradient-success" id ="ccn_count_bar" role="progressbar" style="width: 0%" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                            </div>

                                        </div>

                                    </div>

                                    <div class="browser-list">
                                        <div class="w-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                                        </div>
                                        <div class="w-browser-details">
                                            
                                            <div class="w-browser-info">
                                                <h6>Dead</h6>
                                                <span class="badge rounded-pill bg-danger" id="dead_count"></span>
                                            </div>

                                            <div class="w-browser-stats">
                                                <div class="progress">
                                                    <div class="progress-bar bg-gradient-warning" id ="dead_count_bar" role="progressbar" style="width: 0%" aria-valuenow="15" aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                            </div>

                                        </div>

                                    </div>
                                    
                                    
                                    

                                    <div class="browser-list">
                                        <div class="w-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="4" width="20" height="16" rx="2"/><path d="M7 15h0M2 9.5h20"/></svg>
                                        </div>
                                        <div class="w-browser-details">
                                            
                                            <div class="w-browser-info">
                                                <h6>Ccs to be tested left</h6>
                                                <span class="badge rounded-pill bg-dark" id="total_count"></span>
                                              

                                            </div>
                                            
                                            
                                            
                                     
                                            

                                            <div class="w-browser-stats">
                                                <div class="progress">
                                                    <div class="progress-bar bg-gradient-dark" id ="total_count_bar" role="progressbar" style="width: 0%" aria-valuenow="15" aria-valuemin="0" aria-valuemax="100"></div>
                                                    
                                                    
                                                </div>
                                            </div>

                                        </div>

                                    </div>
                                    
                                </div>
                                


                            </div>
                            
                           
                            
                        </div>
                        
                         
                        
                        
                    </div>
                    
                   

                
</div>


                

                  <div class="row layout-top-spacing">

                    <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12 layout-spacing">
                        <div class="row widget-statistic">
                            <div class="col-xl-4 col-lg-4 col-md-4 col-sm- col-12">
                                
                                
                    <button type="button" class="btn btn-danger btn-block" onclick="clearText33()">Clear CVV 3mk baqer 😈 🔥♛</button>
                                
                                <div class="widget widget-one_hybrid widget-followers" id="widget_followers_id" onclick="Mudaestado();">
                                    <div class="widget-heading">
                                        <div class="w-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                                        </div>
                                        <p class="w-value">Cvv</p>
                                        <h5 class=""></h5>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-4 col-lg-4 col-md-4 col-sm-4 col-12">
                                
                <center>                
            <button type="button" class="btn btn-danger btn-block" onclick="clearText22()">    Clear CCN 3mk baqer 😈 🔥♛</button>   
            </center>
           
                                
                                
                                <div class="widget widget-one_hybrid widget-engagement" id="widget_engagement_id" onclick="Mudaestado_1();">
                                    <div class="widget-heading">
                                        <div class="w-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                                        </div>
                                        <p class="w-value">Ccn</p>
                                        <span class="badge badge-success" id="live2"></span>
                                        <h5 class=""></h5>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-4 col-lg-4 col-md-4 col-sm-4 col-12">
                                
                                
                               
<button type="button" class="btn btn-danger btn-block" onclick="clearText()">Clear Dead 3mk baqer 😈 🔥♛</button>

                               
                               
                               
                                
                                <div class="widget widget-one_hybrid widget-referral" id="widget_referral_id" onclick="Mudaestado_2();">
                                    <div class="widget-heading">
                                        <div class="w-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                                        </div>
                                        <p class="w-value">Dead</p>
                                        <span class="badge badge-success" id="live2"></span>
                                        <h5 class=""></h5>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                        
                    <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12 layout-spacing" id="cvv_lives" style="display: none">
                        <div class="widget widget-one">
                            <div class="widget-heading">
                                <h6 class="cvv-master">Cvv</h6>
                                <div id="bode"><span id="cvv_LIVE" class="cvv-LIVE"></span>
                              </div>
                            </div>
                            
                        </div>
                    </div>

                    <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12 layout-spacing" id="ccn_lives" style="display: none">
                        <div class="widget widget-one">
                            <div class="widget-heading">
                                <h6 class="ccn-master">Ccn</h6>
                                <div id="bode"><span id="ccn_LIVE" class="ccn-LIVE"></span>
                              </div>
                            </div>
                            
                        </div>
                    </div>

                    <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12 layout-spacing" id="dead_cc" style="display: none">
                        <div class="widget widget-one">
                            <div class="widget-heading">
                                <h6 class="dead-master">Dead</h6>
                                <div id="bode"><span id="declined_cc" class="declined-cc"></span>
                              </div>
                            </div>
                            
                        </div>
                    </div>
                                    

                        </div>
                    </div>

                    
                
        
        
        
        <center>  
        
        
        <button type="button" name="clear_dead" id="reload" class="btn btn-warning" data-dismiss="modal" onclick="ReloadPage();">ʀᴇʟᴏᴀᴅ</button>
        
        <button type="button" name="clear_dead" id="reload" class="btn btn-primary" data-dismiss="modal" onclick="myFunction99();">Back</button>
        
        
        
        </center>
        <!--  END CONTENT PART  -->

    </div>
    <!-- END MAIN CONTAINER -->
    
    
        <!-- START OF CCGEN MODAL -->

    <div class="modal fade" id="ccGEN" role"dialog" aria-hidden="true" >
        <div class="modal-dialog modal-dialog-centered"  style="background: transparent;">
            <div class="modal-content" style="background: transparent;">
                <div class="modal-body" style="background: #000000">
                    <center style="margin-bottom: 20px">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        
                        </button>
                        <!---<img class="rounded-circle" src="assets/img/hyno.jpg" width="150" height="150" style="margin-top: 10px;margin-bottom: 20px;" >--->
               
                        

        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true" style="color:#fff">&times;</span>
        </button>       
      </center>
                    <form name="console" id="console" role="form" method="POST">
                        <div>
                            <div class="row">
                                <div class="col-8 col-lg-8">
                                    <div class="form-group">
                                    <center>   <span class="badge  badge-info" style="margin-left: 0px;color: #FFFFFF" for="inputbin">BIN</span>  <center> 
                                        <input id="ccpN" name="ccp" maxlength="19" type="text" id="inputbin" class="form-control" style="border-color: #35c0dc;background: transparent;color: #FFFFFF" placeholder="xxxxxx">
                                    </div>
                                </div>
                                <div class="col-4 col-lg-4">
                                    <div class="form-group">
                                    <center>      <span class="badge  badge-info" style="margin-left: 0px;color: #FFFFFF" for="inputcvv">CVV </span>  <center> 

                                        <input type="text" id="eccv" name="eccv" style="border-color: #35c0dc;background: transparent;color: #FFFFFF" class="form-control" placeholder="rnd" value="rnd">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-4 col-lg-4">
                                    <div class="form-group">
                                        <select type="text" name="ccoudatfmt" class="input_text" style="display:none;">
                                            <option value="CHECKER" selected="selected">CHK</option>
                                            <option value="CSV">CSV</option>
                                            <option value="XML">XML</option>
                                            <option value="JSON">JSON</option>
                                        </select>
                                        <input type="hidden" name="tr" value="1000">
                                        <input type="hidden" name="L" style="width: 20px" value="1L">
                                        <div type="hidden" id="bininfo" align="center"></div>
                                        <center>   <span class="badge  badge-info" style="margin-left: 0px;color: #FFFFFF" for="inputmonth">Month</span>  <center> 
                                        <select type="text" class="form-control" style="border-color: #35c0dc;background: transparent;color: #FFFFFF" name="emeses">
                                            <option style="background: #000000" value="rnd">RND</option>
                                            <option style="background: #000000" value="01">01 - ᴊᴀɴ</option>
                                            <option style="background: #000000" value="02">02 - ꜰᴇʙ</option>
                                            <option style="background: #000000" value="03">03 - ᴍᴀʀ</option>
                                            <option style="background: #000000" value="04">04 - ᴀᴘʀ</option>
                                            <option style="background: #000000" value="05">05 - ᴍᴀʏ</option>
                                            <option style="background: #000000" value="06">06 - ᴊᴜɴ</option>
                                            <option style="background: #000000" value="07">07 - ᴊᴜʟ</option>
                                            <option style="background: #000000" value="08">08 - ᴀᴜɢ</option>
                                            <option style="background: #000000" value="09">09 - ꜱᴇᴘ</option>
                                            <option style="background: #000000" value="10">10 - ᴏᴄᴛ</option>
                                            <option style="background: #000000" value="11">11 - ɴᴏᴠ</option>
                                            <option style="background: #000000" value="12">12 - ᴅᴇᴄ</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-4 col-lg-4">
                                    <div class="form-group">
                                      <center>  <span class="badge  badge-info" style="margin-left: 0px;color: #FFFFFF" for="inputyear">Year</span>  <center> 
                                        <select type="text" class="form-control" style="border-color: #35c0dc;background: transparent;color: #FFFFFF" name="eyear">
                                            <option style="background: #000000; " value="rnd">RND</option>
                                            <option style="background: #000000" value="2024">2024</option>
                                            <option style="background: #000000" value="2025">2025</option>
                                            <option style="background: #000000" value="2026">2026</option>
                                            <option style="background: #000000" value="2027">2027</option>
                                            <option style="background: #000000" value="2028">2028</option>
                                            <option style="background: #000000" value="2029">2029</option>
                                            <option style="background: #000000" value="2030">2030</option>
                                            <option style="background: #000000" value="2031">2031</option>
                                            <option style="background: #000000" value="2032">2032</option>
                                            <option style="background: #000000" value="2033">2033</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-4  col-lg-4">
                                    <div class="form-group">
                                      <center>  <span class="badge  badge-info" style="margin-center: 0px;color: #FFFFFF" for="inputquantity">Quantity</span><center>
                                        <input type="number" name="ccghm" style="border-color: #35c0dc;background: transparent;color: #FFFFFF" maxlength="4" class="form-control" value="12">
                                        <select type="text" name="ccnsp" class="input_text" style="display:none;">
                                            <option selected="selected">None</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">

                                <button type="button" style="margin-right: 20px;margin-left: 20px;" class="btn btn-success btn-block"  name="gerar" id="gerar" onclick="playClick();">GENERATE</button>

                            </div>
                            <div class="row">
                                
                            <button type="button" style="margin-right: 20px;margin-left: 20px;margin-top:5px;" class="btn btn-primary btn-block"  name="gerar" id="gerar"  data-dismiss="modal" onclick="start();">CHECK CARDS</button>
</div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- END OF CCGEN MODAL --> 
    <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
    <script src="assets/js/jquery-3.1.1.min.js"></script>
    <script src="assets/js/popper.min.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
    <script src="assets/js/perfect-scrollbar.min.js"></script>
    <script src="assets/js/app.js"></script>
    <script>
        $(document).ready(function() {
            App.init();
        });
    </script>
    <script src="assets/js/custom.js"></script>
    <!-- END GLOBAL MANDATORY SCRIPTS -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->
    <script src="assets/js/dash_2.js"></script>

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->
<script type="text/javascript">
function Mudaestado() {
        var display = document.getElementById("cvv_lives").style.display;
        if(display == "none"){
            if(document.getElementById("ccn_lives").style.display || document.getElementById("dead_cc").style.display == 'block'){
                document.getElementById("ccn_lives").style.display = 'none';
                document.getElementById("dead_cc").style.display = 'none';
                document.getElementById("cvv_lives").style.display = 'block';
            }
            else {
                document.getElementById("cvv_lives").style.display = 'block';
            }
        }
        else
            document.getElementById("cvv_lives").style.display = 'none';
    }
</script>
<script type="text/javascript">
function Mudaestado_1() {
        var display = document.getElementById("ccn_lives").style.display;
        if(display == "none"){
            if(document.getElementById("cvv_lives").style.display || document.getElementById("dead_cc").style.display == 'block'){
                document.getElementById("cvv_lives").style.display = 'none';
                document.getElementById("dead_cc").style.display = 'none';
                document.getElementById("ccn_lives").style.display = 'block';
            }
            else {
                document.getElementById("ccn_lives").style.display = 'block';
            }
        }
        else
            document.getElementById("ccn_lives").style.display = 'none';
    }
</script>
<script type="text/javascript">
function Mudaestado_2() {
        var display = document.getElementById("dead_cc").style.display;
        if(display == "none"){
            if(document.getElementById("cvv_lives").style.display || document.getElementById("ccn_lives").style.display == 'block'){
                document.getElementById("cvv_lives").style.display = 'none';
                document.getElementById("ccn_lives").style.display = 'none';
                document.getElementById("dead_cc").style.display = 'block';
            }
            else {
                document.getElementById("dead_cc").style.display = 'block';
            }
        }
        else
            document.getElementById("dead_cc").style.display = 'none';
    }
</script>
</script>
    <script type="text/javascript">
      function start() {
        var linha = $("#lista").val();
        var linhaenviar = linha.split("\n");
        var total = linhaenviar.length;
        var tested = total;
        document.getElementById("total_count").innerHTML = total;
        $('#total_count_bar').css('width', ((tested/total)*100)+'%').attr('aria-valuenow', ((tested/total)*100));  
        console.log(total);
        var ap = 0;
        var rp = 0;
        var up = 0;
        document.getElementById('cvv_count').setAttribute('value','')
        document.getElementById('ccn_count').setAttribute('value','')
        document.getElementById('dead_count').setAttribute('value','')
        linhaenviar.forEach(function(value, index) {
            setTimeout(
                function() {
        var req = new XMLHttpRequest();
        console.log("Grabbing Value");
        req.onreadystatechange = function () {
          if (req.readyState == 4 && req.status == 200) {
            if (req.responseText.match("LIVE")) {
            remove();
            ap++;
            tested --;
            LIVE(req.responseText + "");
            }
            else if (req.responseText.match("CCN")) { 
            remove();
            rp++;
            tested --;
            removed(req.responseText + "");
            }
            else { 
            remove();
            up++;
            tested --;
            unknownccs(req.responseText + "");
            }   
            $('#cvv_count').html(ap);
            $('#cvv_count_bar').css('width', ((ap/total)*100)+'%').attr('aria-valuenow', ((ap/total)*100));
            $('#ccn_count').html(rp);
            $('#ccn_count_bar').css('width', ((rp/total)*100)+'%').attr('aria-valuenow', ((rp/total)*100));
            $('#dead_count').html(up);
            $('#dead_count_bar').css('width', ((up/total)*100)+'%').attr('aria-valuenow', ((up/total)*100));
            document.getElementById("total_count").innerHTML = tested;
            $('#total_count_bar').css('width', ((tested/total)*100)+'%').attr('aria-valuenow', ((tested/total)*100));
          }
        }
        var whichgate = document.getElementById('gate').value;
        var gate = whichgate + "?lista="
        req.open("GET", gate + value, true);
        req.send(null);
        }, 0 * index);
        });
      }
    function LIVE(str) {
        $("#cvv_LIVE").append(str + "<br>");
    }
    function removed(str) {
        $("#ccn_LIVE").append(str + "<br>");
    }
    function unknownccs(str) {
        $("#declined_cc").append(str + "<br>");
    }
    function remove() {
        var lines = $("#lista").val().split('\n');
        lines.splice(0, 1);
        $("#lista").val(lines.join("\n"));
    }

    </script>
 
 
 
 
 
    
<script>
function myFunction99() {
location.replace("/#")
}
</script>






    
    <script src="assets/js/Clearprone3.js"></script>
    <script src="assets/js/Clearprone2.js"></script>
    <script src="assets/js/Clearprone.js"></script>
    <script src="assets/js/UDP.js"></script>
    
<!--===== START OF SCREEN JS =====-->
<script>
$(document).ready(function(){
  $('#hideshow').on('click', function(event) {        
     $('.content').toggle('show');
  });
});    
</script>
<!--===== END OF SCREEN JS =====-->

    
 
 
    
    
    
    
    
</body>
</html>