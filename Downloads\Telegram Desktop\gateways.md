# StormAPI - Services

**Host**

| Target                        |
| :---------------------------- |
| `https://api.stormapi.store/` |

**Header Access**

| Name          | Value                 | Description                    |
| :------------ | :-------------------- | :----------------------------- |
| `storm-token` | `STORM-XXXX-XXXX-API` | **Required**. For all requests |

**Routes / Gateways /**

| Routes                | Method | Description                        |
| :-------------------- | :----- | :--------------------------------- |
| `gateways/amazon-us/` | POST   | **Required a public key and data** |
| `gateways/amazon-mx/` | POST   | **Required a public key and data** |

-> You need to put all parameters.

# Website For your mass checking in Amazon

**[Link:](https://api.stormapi.store/checking/amazon/)**

# Example PHP Request:

```php
$ch = curl_init('https://api.stormapi.store/{route here}');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, '{json body here}');


$headers = array(
    'Content-Type: application/json',
    'storm-token: STORM-XXXX-XXXX-API',
);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
echo $response = curl_exec($ch);
```

# Example Python Request

```python
import requests

url = 'https://api.stormapi.store/{route_here}'
json_body = '{json_body_here}'
headers = {
    'Content-Type': 'application/json',
    'storm-token': 'STORM-XXXX-XXXX-API',
}

response = requests.post(url, data=json_body, headers=headers)
print(response.text)
```

# Amazon US, MX Example Body:

**Cookies of this link for Amazon USA: https://www.amazon.com/cpe/yourpayments/wallet?ref_=ya_d_c_pmt_mpo**
**Cookies of this link for Amazon MX: https://www.amazon.com.mx/cpe/yourpayments/wallet?ref_=ya_d_c_pmt_mpo**

**Tutorial: https://t.me/PhantomChecker/6**

```json body with IP
{
  "card": "****************|06|2025|788",
  "cookie": "Cookie in base 64",
  "proxyServer": "pyproxy.io",
  "proxyPort": "16666",
  "proxyUsername": "mark",
  "proxyPassword": "soy_chipi123",
  "proxy_path": "y"
}
```

```json body without IP (Proxy Slow)
{
  "card": "****************|06|2025|788",
  "cookie": "Cookie in base 64",
  "proxy_path": "n"
}
```
