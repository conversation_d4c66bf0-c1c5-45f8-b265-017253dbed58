import base64
import requests
from Crypto.PublicKey import RSA
from Crypto.Cipher import <PERSON><PERSON><PERSON>1_OAEP
from Crypto.Hash import SHA1
from Crypto.Util.number import bytes_to_long

class CloverEncrypt:
    def __init__(self, pan, prefix_id="00000000"):
        self.pan = pan
        self.prefix_id = prefix_id
        self.public_key = self._get_public_key()

    def _get_public_key(self):
        response = requests.get("https://checkout.clover.com/assets/keys.json")
        keys = response.json()
        public_key_base64 = keys["TA_PUBLIC_KEY_PROD"]

        key_bytes = base64.b64decode(public_key_base64)
        modulus = bytes_to_long(key_bytes[:256])
        exponent = bytes_to_long(key_bytes[256:])

        return RSA.construct((modulus, exponent))

    def encrypt(self):
        input_data = f"{self.prefix_id}{self.pan}".encode()
        cipher = PKCS1_OAEP.new(self.public_key, hashAlgo=SHA1)
        cipher_text = cipher.encrypt(input_data)
        return base64.b64encode(cipher_text).decode()

    def get_encrypted_pan(self):
        return self.encrypt()
