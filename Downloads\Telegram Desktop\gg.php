<?php

require_once '../vendor/autoload.php';

use phpseclib3\Crypt\AES;
use phpseclib3\Crypt\PublicKeyLoader;

class CardEncryptor
{
    private const CARD_TYPES = [
        'Visa' => '001',
        'MasterCard' => '002',
        'AmericanExpress' => '003',
        'Discover' => '004',
        'Diners' => '005',
        'JCB' => '007',
        'Maestro' => '042',
        'ChinaUnionPay' => '062',
    ];

    private string $context;

    public function __construct(string $context)
    {
        $this->context = $context;
    }

    private function getCardTypeNumeric(string $cardNumber): string
    {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);

        if (preg_match('/^4[0-9]{12}(?:[0-9]{3})?$/', $cardNumber)) return self::CARD_TYPES['Visa'];
        if (preg_match('/^(5[1-5][0-9]{14}|2(2[2-9][0-9]{2}|[3-6][0-9]{3}|7[0-1][0-9]{2}|720)[0-9]{12})$/', $cardNumber)) return self::CARD_TYPES['MasterCard'];
        if (preg_match('/^3[47][0-9]{13}$/', $cardNumber)) return self::CARD_TYPES['AmericanExpress'];
        if (preg_match('/^6(?:011|4[4-9][0-9]|5[0-9]{2})[0-9]{12}$/', $cardNumber)) return self::CARD_TYPES['Discover'];
        if (preg_match('/^3(?:0[0-5]|[68][0-9])[0-9]{11}$/', $cardNumber)) return self::CARD_TYPES['Diners'];
        if (preg_match('/^35(?:2[8-9][0-9]|[3-8][0-9]{2})[0-9]{12}$/', $cardNumber)) return self::CARD_TYPES['JCB'];
        if (preg_match('/^(5018|5020|5038|6304|6759|676[1-3])[0-9]{8,15}$/', $cardNumber)) return self::CARD_TYPES['Maestro'];
        if (preg_match('/^62[0-9]{14,17}$/', $cardNumber)) return self::CARD_TYPES['ChinaUnionPay'];

        return '000';
    }

    private function decodeJwt(string $jwt): array
    {
        $parts = explode('.', $jwt);
        if (count($parts) !== 3) {
            throw new Exception('Invalid JWT format.');
        }
        $payload = $this->base64UrlDecode($parts[1]);
        return json_decode($payload, true);
    }

    private function base64UrlDecode(string $input): string
    {
        $remainder = strlen($input) % 4;
        if ($remainder) {
            $input .= str_repeat('=', 4 - $remainder);
        }
        return base64_decode(strtr($input, '-_', '+/'));
    }

    private function replace(string $str): string
    {
        return str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($str));
    }

    private function generateKey(): string
    {
        return random_bytes(32);
    }

    private function encryptPayload(array $payload, string $key, array $header, string $iv): array
    {
        $aes = new AES('gcm');
        $aes->setKey($key);
        $aes->setNonce($iv);
        $aes->setAAD($this->replace(json_encode($header)));
        $ciphertext = $aes->encrypt(json_encode($payload));
        $tag = $aes->getTag();

        return [$ciphertext . $tag, $key];
    }

    private function wrapKey(string $key, array $jsonWebKey): string
    {
        $rsaPublicKey = PublicKeyLoader::load([
            'n' => new phpseclib3\Math\BigInteger($this->base64UrlDecode($jsonWebKey['n']), 256),
            'e' => new phpseclib3\Math\BigInteger($this->base64UrlDecode($jsonWebKey['e']), 256)
        ]);

        return $rsaPublicKey->encrypt($key);
    }

    private function build(string $buffer, string $key, string $iv, array $header, array $jsonWebKey): string
    {
        $u = strlen($buffer) - ((128 + 7) >> 3);
        $keyBuffer = $this->wrapKey($key, $jsonWebKey);

        return implode('.', [
            $this->replace(json_encode($header)),
            $this->replace($keyBuffer),
            $this->replace($iv),
            $this->replace(substr($buffer, 0, $u)),
            $this->replace(substr($buffer, $u))
        ]);
    }

    public function encryptCardData(array $data): string
    {
        $keyId = $this->decodeJwt($this->context);

        $header = [
            'kid' => $keyId['flx']['jwk']['kid'],
            'alg' => 'RSA-OAEP',
            'enc' => 'A256GCM'
        ];

        $data['type'] = $this->getCardTypeNumeric($data['number']);
        $payload = [
            'data' => $data,
            'context' => $this->context,
            'index' => 0
        ];

        $iv = random_bytes(12);
        $key = $this->generateKey();
        [$buffer, $key] = $this->encryptPayload($payload, $key, $header, $iv);

        return $this->build($buffer, $key, $iv, $header, $keyId['flx']['jwk']);
    }
}

// Example usage
try {
    $context = '**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
    $encryptor = new CardEncryptor($context);

    $data = [
        'number' => '****************',
        'expirationMonth' => '12',
        'expirationYear' => '2025',
        'securityCode' => '123'
    ];

    $encrypted = $encryptor->encryptCardData($data);
    echo "Encrypted Result: $encrypted\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
