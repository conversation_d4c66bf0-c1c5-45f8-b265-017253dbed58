<?php
/**
 * This script fetches a real address from a website and extracts specific details.
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @uses GuzzleHttp\Client for making HTTP requests
 * @uses DOMDocument and DOMXPath for parsing HTML
 *
 * @return void
 */
require 'vendor/autoload.php';

use GuzzleHttp\Client;

$client = new Client([
    'verify' => false,  
]);

$res = $client->request('GET', 'https://www.fakexy.com/fake-address-generator-ca');

$html = (string) $res->getBody();

$dom = new DOMDocument;
@$dom->loadHTML($html);

$xpath = new DOMXPath($dom);

$street = $xpath->query('//td[text()="Street"]/following-sibling::td')->item(0)->nodeValue;
$city = $xpath->query('//td[text()="City/Town"]/following-sibling::td')->item(0)->nodeValue;
$state = $xpath->query('//td[text()="State/Province/Region"]/following-sibling::td')->item(0)->nodeValue;
$zip = $xpath->query('//td[text()="Zip/Postal Code"]/following-sibling::td')->item(0)->nodeValue;
$phone = $xpath->query('//td[text()="Phone Number"]/following-sibling::td')->item(0)->nodeValue;
$country = $xpath->query('//td[text()="Country"]/following-sibling::td')->item(0)->nodeValue;

echo "Street: $street\n";
echo "City: $city\n";
echo "State: $state\n";
echo "Zip: $zip\n";
echo "Phone: $phone\n";
echo "Country: $country\n";
?>