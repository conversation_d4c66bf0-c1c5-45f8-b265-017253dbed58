<?php
error_reporting(0);
date_default_timezone_set('japan');

// Get input value 
$input = $_POST["input"];

if(!$input) {
  exit; 
}

$limit = $_POST["_limit"]; 
if(!$limit){
$file_lines = file('https://hashshin.github.io/hashphil/words.txt', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
$list = [];
foreach ($file_lines as $line) {
    $list[] = "$input $line";
}

echo implode("\n", $list);
exit;
}else{

$file_lines = file("https://hashshin.github.io/hashphil/words.txt");  
$list = [];

for($i = 0; $i < $limit; $i++) {
  $line = $file_lines[array_rand($file_lines)];  
  $list[] = "$input $line";
}

echo implode("", $list);
exit;
}

?>


