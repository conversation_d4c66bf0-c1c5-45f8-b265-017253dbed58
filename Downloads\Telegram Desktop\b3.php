<?php
require_once 'braintreesdk/lib/Braintree.php';
require_once 'Curlx.php';
$CurlX = new CurlX;

$gateway = new Braintree\Gateway([
    'environment' => 'production', // braintree production keys
    'merchantId' => 'cyhswkwr5gtj796j',
    'publicKey' => 'zgmt56fv2sbx7whj',
    'privateKey' => 'fe396a36e905da8a8fb8804730dbcc97'
]);

//best option list(), you only need change the delimeter.
list($num, $month, $year, $sec) = explode("|", $_GET['lista'], 4);

//random amount min = 0.xx, max = 5;
$amount = mt_rand(1*10, 5*10)/10;
$data = json_decode($CurlX::Get('https://randomuser.me/api/')->body);

$result = $gateway->transaction()->sale([
    'amount' => $amount,
    'creditCard' => [
        'number' => $num,
        'expirationDate' => "$month/$year",
        'cvv' => $sec,
    ],
    'customer' => [
        'firstName' => $data->results[0]->name->first,
        'lastName' => $data->results[0]->name->last
    ],
    'options' => [
      'submitForSettlement' => true
    ],
    'billing' => [
        'streetAddress' => $data->results[0]->location->street->number." ".$data->results[0]->location->street->name,
        'postalCode' => $data->results[0]->location->postcode
    ]
]);

$msg = array(
    'approved' => $result->success, 
    'responsecode' => $result->transaction->networkResponseCode, 
    'responsetext' => $result->transaction->networkResponseText, 
    'cvv' => $result->transaction->cvvResponseCode, 
    'reject' => $result->transaction->gatewayRejectionReason, 
    'amount' => $amount, 
    'processorcode' => $result->transaction->processorResponseCode, 
    'processortext' => $result->transaction->processorResponseText
);

header("Content-type: application/json; charset=utf-8");
echo json_encode($msg);

?>
 