<?php

/* Must fill these details */

// Anchor Details
function GetStr($string, $start, $end) {
    $str = explode($start, $string);
    $str = explode($end, $str[1]);  
    return $str[0];
}
function inStr($string, $start, $end, $value) {
    $str = explode($start, $string);
    $str = explode($end, $str[$value]);
    return $str[0];
}
$separa = explode("|", $lista);
$cc = $separa[0];
$mes = $separa[1];
$ano = $separa[2];
$cvv = $separa[3];


function value($str,$find_start,$find_end)
{
    $start = @strpos($str,$find_start);
    if ($start === false) 
    {
        return "";
    }
    $length = strlen($find_start);
    $end    = strpos(substr($str,$start +$length),$find_end);
    return trim(substr($str,$start +$length,$end));
}

function mod($dividendo,$divisor)
{
    return round($dividendo - (floor($dividendo/$divisor)*$divisor));
}

$anchor_link = 'https://www.google.com/recaptcha/api2/anchor?ar=1&k=6LdkPLQUAAAAAD0VC1UWWpor5SMHgFUufvE-UeSs&co=aHR0cHM6Ly9iaWN5Y2xlcGFydHNkaXJlY3QuY29tOjQ0Mw..&hl=en&v=4rwLQsl5N_ccppoTAwwwMrEN&size=invisible&cb=z9ob5uldyr16'; // It looks like: anchor?ar=1&k=
$anchor_ref  = 'https://bicyclepartsdirect.com/'; // Open Anchor Headers and see the referer link

//----------------------------------------------------------------------------------//

// Reload Details

$reload_link = 'https://www.google.com/recaptcha/api2/reload?k=6LdkPLQUAAAAAD0VC1UWWpor5SMHgFUufvE-UeSs'; // It looks like: reload?k=
$v   = '4rwLQsl5N_ccppoTAwwwMrEN';  // Available in Anchor Query String Parameters
$k   = '6LdkPLQUAAAAAD0VC1UWWpor5SMHgFUufvE-UeSs';  // Available in Anchor Query String Parameters
$co  = 'aHR0cHM6Ly9iaWN5Y2xlcGFydHNkaXJlY3QuY29tOjQ0Mw..';  // Available in Anchor Query String Parameters
$chr = urlencode('[84,90,61]');  // Available in Reload's Request Payload (Post Field) and looks like: [21,71,92]
$bg  = '39mg2dwKAAQeFB9hbQEHnAM6iZnbhQiXhKNxgaZO-1UwPwiLs5SVovE2SGbeGuarpVENlFYDuy0Ufk3veew66weQ6qXrq1OmHt-zS-rH-AMRUia8WOrBtAI80SSnzK60eoV2hoJ-_yof1eCtm1vNniQvuNsvONFRs5TNhwFKjPG5J7UMHayO1cS4tgyXeDl5yYtUWRCR9Ibb8OJCseFPN8TeF_gvNiDc1jWmSnSBISFsp45jbLzsBzK2IIwW7aCSe-N6csLKBAsAFfwHvxKhUGgmSW_kJ09TCxVbDiRESpYrk-28VZGP5V6X4nKJGdAwOAMKXHdRDrLoPa9mclQj49kXYAxQ0OFF6vcrDFsbn9ynwN2c9v0YoJlXWeQs31LtQoINtpv64sFeEir3EKM0IybzHSyJrpuU6_vWU3UCknE-NmqWebtf7EgctI611GgvMb3DMAvOwhULBEZzKKFPiFnMP64kEFj8TnPKpypclu7op-LGUNJDwXhypdhLSVBryIbSbA3wPGxZn52FO094pEho202lCZcTC9504vNzkQ-NaBe0bYbChuU11dldhw5eFHD9rcQLOyx_xboafrfh1dCXGnQZTLztcb6d7Mbl1Cjdxa1lyec8rjy0JfszH3irYhncw_sll6VhcjL5kaM2GTaAK_XWS_54lGGpo8TgoVBVHLhrxbE7GddaBXc70ckkt1e9uhzdSQs6eqHzh50HZgvN9jI2Tgjsxp6kTsB9Rh_TS3_i1QCq5o4nL9sOb3L3_GwTV4Op8XK6u6j7xW4aDV9B1jm2SxmrSVQDzEnVTP8dZSwN6HVJHvbyPCFMs1cxRPQj8QJr4MFG_aITR7zeT6mhcoyk4XMBI3SEwm8q13NiM0SUtln0l2aYMb0gOI3BMHiytFKBKbFKGxatCNhapW9lrnIguTvovx9-YUXp_GHGPUBMQ2mWc0vutm-7EZYmEyS_DznjcuhXmXrpV6Wx3VlXITVD63JsWsK8wN6ng8iRzc6lAW7Ezs_3dNmRtNEAXRnBZegHyNouSFj8KiZdYtBN7y6d_9rpJDrEUNnt3GRbLpcMnN5Q_7urU90DbLKbav9VunpRVLsXXaLMzNsHhulvc77sGQZaAxHCjg';  // Available in Reload's Request Payload and is after the $chr and it starts from ! and ends with * (Exclude *)
$vh  = '2102103885';  // Available in Reload's Request Payload and is after the $bg and select only the number (Sometimes - sign is also included in number, you have to take that - sign also)

//////////////////=================[Captcha][1st Req]=================//////////////////

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $anchor_link);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
'authority: www.google.com',
'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
'accept-language: en-US,en;q=0.9',
'referer: '.$anchor_ref.'',
'sec-fetch-dest: iframe',
'sec-fetch-mode: navigate',
'sec-fetch-site: cross-site',
'upgrade-insecure-requests: 1',
'user-agent: Mozilla/5.0 (Windows NT '.rand(11,99).'.0; Win64; x64) AppleWebKit/'.rand(111,999).'.'.rand(11,99).' (KHTML, like Gecko) Chrome/'.rand(11,99).'.0.'.rand(1111,9999).'.'.rand(111,999).' Safari/'.rand(111,999).'.'.rand(11,99).''));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
$cp1 = curl_exec($ch);
curl_close($ch);

//if (!strpos($cp1, "recaptcha-token")){
//echo '<span class="badge badge-warning">#DEAD</span></br><span class="badge badge-danger">『 ❌ Error In 1st Request Reason: Page Not Loaded !! ❌ 』</span></br><span class="new badge badge-warning">API Made By: [☠️【★Bв™】Bin Bhai]</span></br>';
//return;
//};

$rtk = trim(strip_tags(getStr($cp1,'<input type="hidden" id="recaptcha-token" value="', '"')));
echo $rtk;

//////////////////=================[Captcha][2nd Req]=================//////////////////

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $reload_link);
curl_setopt($ch, CURLOPT_HEADER, 0);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
'authority: www.google.com',
'accept: */*',
'accept-language: en-US,en;q=0.9',
'content-type: application/x-www-form-urlencoded',
'origin: https://www.google.com',
'referer: '.$anchor_link.'',
'user-agent: '.$ua.''));
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, 'v='.$v.'&reason=q&c='.$rtk.'&k='.$k.'&co='.$co.'&hl=en&size=invisible&chr='.$chr.'&vh='.$vh.'&bg='.$bg.'');
$cp2 = curl_exec($ch);
curl_close($ch);

//if (!strpos($cp2, '"rresp","')){
//echo '<span class="badge badge-warning">#DEAD</span></br><span class="badge badge-danger">『 ❌ Error Solving Captcha !! ❌ 』</span></br><span class="new badge badge-warning">API Made By: [☠️【★Bв™】Bin Bhai]</span></br>';
//return;
//};

$captcha = trim(strip_tags(getStr($cp2, '["rresp","', '"')));

echo $captcha;
