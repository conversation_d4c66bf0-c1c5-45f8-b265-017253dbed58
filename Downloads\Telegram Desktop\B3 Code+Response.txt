{"DropinError": "Please fill out the payment form.", "avsPostalCodeResponseCode_N": "Postal code does not match.", "avsStreetAddressResponseCode_N": "Street address does not match.", "cvvResponseCode_N": "Card security code does not match.", "avsPostalCodeResponseCode_U": "Postal code not verified.", "avsStreetAddressResponseCode_U": "Street address not verified.", "cvvResponseCode_U": "Card security code not verified.", "avsPostalCodeResponseCode_I": "Postal code not provided.", "avsStreetAddressResponseCode_I": "Street address not provided.", "cvvResponseCode_I": "Card security code not provided.", "gatewayRejectionReason_three_d_secure": "Your payment method was rejected due to 3D Secure.", "2000": "Do Not Honor", "2001": "Insufficient Funds", "2002": "Limit Exceeded", "2003": "Cardholder's Activity Limit Exceeded", "2004": "Expired Card", "2005": "Invalid Credit Card Number", "2006": "Invalid Expiration Date", "2007": "No Account", "2008": "Card Account Length Error", "2009": "No Such Issuer", "2010": "Card Issuer Declined CVV", "2011": "Voice Authorization Required", "2012": "Processor Declined Possible Lost Card", "2013": "Processor Declined - Possible Stolen Card", "2014": "Processor Declined - <PERSON><PERSON>", "2015": "Transaction Not Allowed", "2016": "Duplicate Transaction", "2017": "Cardholder Stopped Billing", "2018": "Cardholder Stopped All Billing", "2019": "Invalid Transaction", "2020": "Violation", "2021": "Security Violation", "2022": "Declined - Updated Cardholder Available", "2023": "Processor Does Not Support This Feature", "2024": "Card Type Not Enabled", "2025": "Set Up Error - Merchant", "2026": "Invalid Merchant ID", "2027": "Set Up Error - Amount", "2028": "Set Up Error - Hierarchy", "2029": "Set Up Error - Card", "2030": "Set Up Error - Terminal", "2031": "Encryption Error", "2032": "Surcharge Not Permitted", "2033": "Inconsistent Data", "2034": "No Action Taken", "2035": "Partial Approval For Amount In Group III Version", "2036": "Authorization could not be found to reverse", "2037": "Already Reversed", "2038": "Processor Declined", "2039": "Invalid Authorization Code", "2040": "Invalid Store", "2041": "Declined - Call For Approval", "2042": "Invalid Client ID", "2043": "Error - <PERSON> Not Retry, Call Issuer", "2044": "Declined - Call Issuer", "2045": "Invalid Merchant Number", "2046": "Declined", "2047": "Call Issuer. Pick Up Card", "2048": "<PERSON><PERSON><PERSON>", "2049": "Invalid SKU Number", "2050": "Invalid Credit Plan", "2051": "Credit Card Number does not match method of payment", "2053": "Card reported as lost or stolen", "2054": "Reversal amount does not match authorization amount", "2055": "Invalid Transaction Division Number", "2056": "Transaction amount exceeds the transaction division limit", "2057": "Issuer or Cardholder has put a restriction on the card", "2058": "Merchant not Mastercard SecureCode enabled", "2059": "Address Verification Failed", "2060": "Address Verification and Card Security Code Failed", "2061": "Invalid Transaction Data", "2062": "Invalid Tax Amount", "2063": "PayPal Business Account preference resulted in the transaction failing", "2064": "Invalid Currency Code", "2065": "Refund Time Limit Exceeded", "2066": "PayPal Business Account Restricted", "2067": "Authorization Expired", "2068": "PayPal Business Account Locked or Closed", "2069": "PayPal Blocking Duplicate Order IDs", "2070": "PayPal Buyer Revoked Future Payment Authorization", "2071": "PayPal Payee Account Invalid Or Does Not Have a Confirmed Email", "2072": "PayPal Payee Email Incorrectly Formatted", "2073": "PayPal Validation Error", "2074": "Funding Instrument In The PayPal Account Was Declined By The Processor Or Bank, Or It Can't Be Used For This Payment", "2075": "Payer Account Is Locked Or Closed", "2076": "Payer Cannot Pay For This Transaction With PayPal", "2077": "Transaction Refused Due To PayPal Risk Model", "2079": "PayPal Merchant Account Configuration Error", "2081": "PayPal pending payments are not supported", "2082": "PayPal Domestic Transaction Required", "2083": "PayPal Phone Number Required", "2084": "PayPal Tax Info Required", "2085": "PayPal Payee Blocked Transaction", "2086": "PayPal Transaction Limit Exceeded", "2087": "PayPal reference transactions not enabled for your account", "2088": "Currency not enabled for your PayPal seller account", "2089": "PayPal payee email permission denied for this request", "2090": "PayPal account not configured to refund more than settled amount", "2091": "Currency of this transaction must match currency of your PayPal account", "3000": "Processor Network Unavailable - Try Again", "Braintree\\Exception\\NotFound": "Object was not found.", "Braintree\\Exception\\Authentication": "Authentication failed, check your API key configuration.", "Braintree\\Exception\\Authorization": "API Authorization check failed. Ensure you have entered your API keys correctly and the user associated with your API keys has the proper permissions.", "81801": "Addresses must have at least one field filled in.", "81802": "Company is too long.", "81804": "Extended address is too long.", "81805": "First name is too long.", "81806": "Last name is too long.", "81807": "Locality is too long.", "81813": "Postal code can only contain letters, numbers, spaces, and hyphens.", "81808": "Postal code is required.", "81809": "Postal code may contain no more than 9 letter or number characters.", "81810": "Region is too long.", "81811": "Street address is required.", "81812": "Street address is too long.", "81827": "US state codes must be two characters to meet PayPal Seller Protection requirements.", "91803": "Country name is not an accepted country.", "91815": "Provided country information is inconsistent.", "91816": "Country code (alpha3) is not an accepted country.", "91817": "Country code (numeric) is not an accepted country.", "91814": "Country code (alpha2) is not an accepted country.", "91818": "Customer has already reached the maximum of 50 addresses.", "91819": "First name must be a string.", "91820": "Last name must be a string.", "91821": "Company must be a string.", "91822": "Street address must be a string.", "91823": "Extended address must be a string.", "91824": "Locality must be a string.", "91825": "Region must be a string.", "91826": "Postal code must be a string.", "91828": "Address is invalid.", "82602": "Applicant merchant id is too long.", "82603": "Applicant merchant id format is invalid.", "82604": "Applicant merchant id is in use.", "82605": "Applicant merchant id is not allowed.", "82606": "Master merchant account ID is required.", "82607": "Master merchant account ID is invalid.", "82608": "Master merchant account must be active.", "82610": "Terms Of Service needs to be accepted. Applicant tos_accepted required.", "82675": "Merchant account id can not be updated.", "82676": "Master merchant account id can not be updated.", "82674": "Merchant accounts with a status of pending or suspended cannot be updated.", "82609": "Applicant first name is required.", "82637": "Individual first name is required.", "82611": "Applicant last name is required.", "82638": "Individual last name is required.", "82612": "Applicant date of birth is required.", "82639": "Individual date of birth is required.", "82613": "Applicant routing number is required.", "82640": "Funding routing number is required.", "82614": "Applicant account number is required.", "82641": "Funding account number is required.", "82615": "Applicant SSN must be blank, last 4 digits, or full 9 digits.", "82642": "Individual SSN must be blank, last 4 digits, or full 9 digits.", "82616": "Applicant email is invalid.", "82643": "Individual email is invalid.", "82627": "Applicant first name is invalid.", "82644": "Individual first name is invalid.", "82628": "Applicant last name is invalid.", "82645": "Individual last name is invalid.", "82631": "Applicant company name is invalid.", "82632": "Applicant tax ID is invalid.", "82688": "Business params provided in an invalid format.", "82647": "Business tax ID is invalid.", "82633": "Applicant company name is required with tax ID.", "82634": "Applicant tax ID is required with company name.", "82635": "Applicant routing number is invalid.", "82649": "Funding routing number is invalid.", "82650": "An unexpected error occurred trying to save the merchant account; support has been notified and is looking into the issue. You may safely retry this request", "82621": "Applicant declined due to OFAC.", "82622": "Applicant declined due to MasterCard MATCH.", "82623": "Applicant declined due to failed KYC.", "82624": "Applicant declined due to invalid SSN.", "82625": "Applicant declined due to SSN matching that of a deceased person.", "82626": "Applicant declined after review.", "82617": "Applicant street address is required.", "82657": "Individual street address is required.", "82618": "Applicant locality is required.", "82658": "Individual locality is required.", "82619": "Applicant postal code is required.", "82659": "Individual postal code is required.", "82620": "Applicant region is required.", "82660": "Individual region is required.", "82629": "Applicant street address is invalid.", "82661": "Individual street address is invalid.", "82664": "Applicant region is invalid.", "82668": "Individual region is invalid.", "82630": "Applicant postal code is invalid.", "82662": "Individual postal code is invalid.", "82636": "Applicant phone is invalid.", "82656": "Individual phone is invalid.", "82663": "Applicant date of birth is invalid", "82666": "Individual date of birth is invalid.", "82670": "Applicant account number is invalid.", "82671": "Funding account number is invalid.", "82665": "Applicant email is required.", "82667": "Individual email is required.", "82672": "Business tax ID must be blank unless business legal name is present.", "82673": "Applicant tax ID must be blank unless company name present.", "82646": "Business DBA name is invalid.", "82677": "Business legal name is invalid.", "82669": "Business legal name is required with tax ID.", "82648": "Business tax ID is required with business legal name.", "82685": "Business street address is invalid.", "82686": "Business postal code is invalid.", "82684": "Business region is invalid.", "82679": "Funding destination is invalid.", "82678": "Funding destination is required.", "82681": "Funding email is invalid.", "82680": "Funding email is required when destination is email.", "82683": "Funding mobile phone is invalid.", "82682": "Funding mobile phone is required when destination is mobile phone.", "82687": "Individual params provided in an invalid format.", "82689": "Business locality is invalid.", "82690": "Individual locality is invalid.", "82691": "Applicant locality is invalid.", "92801": "Cannot specify make_default without a customer_id", "92802": "Cannot specify verify_card without a customer_id", "92803": "Cannot specify fail_on_duplicate_payment_method without a customer_id", "92804": "Customer specified by customer_id does not exist", "92806": "Unsupported client token version", "92807": "Merchant Account specified by merchant_account_id does not exist", "91602": "Custom field is invalid:", "91609": "Customer ID has already been taken.", "91610": "Customer ID is invalid (use only letters, numbers, '-', and '_').", "91611": "Customer ID is not an allowed ID.", "91612": "Customer ID is too long.", "91613": "Customer ID is required.", "91617": "<PERSON><PERSON> references a vaulted payment instrument - cannot be transferred between customers", "91618": "Customer attribute must be a map of keys and values representing a customer.", "91619": "Ambiguous usage of default payment method token.", "81601": "Company is too long.", "81603": "Custom field is too long:", "81604": "Email is an invalid format.", "81605": "Email is too long.", "81606": "Email is required if sending a receipt.", "81607": "Fax is too long.", "81608": "First name is too long.", "81613": "Last name is too long.", "81614": "Phone is too long.", "81615": "Website is too long.", "81616": "Website is an invalid format.", "93101": "Payment method params are required.", "93102": "<PERSON><PERSON> is invalid.", "93103": "Nonce is required.", "93104": "Customer ID is required.", "93105": "Customer ID is invalid.", "93106": "Cannot forward a payment method of this type.", "93107": "Cannot use a payment_method_nonce more than once.", "93108": "Unknown or expired payment_method_nonce.", "93109": "<PERSON><PERSON> is not vaultable.", "83501": "Apple Pay cards are not accepted by this merchant account.", "83502": "A customer ID is required to vault an Apple Pay Card.", "93503": "Apple Pay token is taken.", "93504": "Cannot use a payment_method_nonce more than once.", "93505": "Unknown or expired payment_method_nonce.", "93506": "Payment method nonce locked.", "83518": "Credit card type is not accepted by this merchant account.", "93507": "Payment method nonces cannot be used to update an existing Apple Pay Card.", "93508": "Number is required for Apple Pay Card", "93509": "Expiration Month is required for Apple Pay Card", "93510": "Expiration Year is required for Apple Pay Card", "93511": "Cryptogram is required for Apple Pay Card", "83512": "Apple Pay payment data decryption failed", "93513": "Apple Pay is disabled for this merchant", "93514": "Apple Pay certificate, private key or merchant ID not configured", "93517": "Certificate provided is not valid", "93519": "Public key used to sign payment data does not match stored certificate", "83520": "Payment data is malformed", "93521": "Private key stored does not match private key used to encrypt payment data", "93522": "Certificate does not match stored key pair", "91701": "Cannot provide both a billing address and a billing address ID.", "91702": "Billing address ID is invalid.", "91704": "Customer ID is required.", "91705": "Customer ID is invalid.", "91708": "Cannot provide expirationdate if you are also providing expiration_month and expiration_year.", "91718": "Token is invalid (use only letters, numbers, \" - \", and '').", "91719": "Credit card token is taken.", "91720": "Credit card token is too long.", "91721": "Token is not an allowed token.", "91722": "Payment Method token is required.", "91744": "Billing address format is invalid.", "81723": "Cardholder name is too long.", "81703": "Credit card type is not accepted by this merchant account.", "81718": "Credit card number cannot be updated to an unsupported card type when it is associated to subscriptions.", "81706": "CVV is required.", "81707": "CVV must be 4 digits for American Express and 3 digits for other card types.", "81709": "Expiration date is required.", "81710": "Expiration date is invalid.", "81711": "Expiration date year is invalid. It must be between 1975 and 2200.", "81712": "Expiration month is invalid.", "81713": "Expiration year is invalid.", "81714": "Credit card number is required.", "81715": "Credit card number is invalid.", "81716": "Credit card number must be 12-19 digits.", "81717": "Credit card number is not an accepted test number.", "91723": "Update Existing To<PERSON> is invalid.", "81724": "Duplicate card exists in the vault.", "81725": "Credit card must include number, payment_method_nonce, or venmo_sdk_payment_method_code.", "91726": "Credit card type is not accepted by this merchant account.", "91727": "Invalid VenmoSDK payment method code", "91728": "Verification Merchant Account ID is invalid.", "91729": "Update Existing Token is not allowed when creating a customer.", "91730": "Verifications are not supported on this merchant account", "91731": "Cannot use a payment_method_nonce more than once.", "91732": "Unknown or expired payment_method_nonce.", "91733": "Payment method nonce locked.", "91734": "Credit card type is not accepted by this merchant account.", "91735": "Payment method nonces cannot be used to update an existing card.", "91738": "Payment method is not a credit card payment method.", "91742": "Verification Merchant Account is suspended.", "91743": "The current user does not have access to the specified verification_merchant_account_id", "81736": "CVV verification failed.", "81737": "Postal code verification failed.", "91739": "Verification amount cannot be negative.", "91740": "Verification amount is invalid.", "91741": "Verification amount not supported by processor.", "91745": "Payment method params supplied are not valid for updating a credit card.", "81750": "Credit card number is prohibited.", "91752": "Verification amount is too large.", "91755": "Verification Merchant Account ID cannot be a sub-merchant account.", "93401": "Industry type is invalid.", "93402": "Lodging data is empty.", "93403": "Folio number is invalid.", "93404": "Check in date is invalid.", "93405": "Check out date is invalid.", "93406": "Check out date must occur during or after the check in date.", "93407": "Data fields are unknown.", "93408": "Travel and Cruise data is empty.", "93409": "Data fields are unknown.", "93410": "Travel Package is invalid.", "93411": "Departure date is invalid.", "93412": "Lodging check in date is invalid.", "93413": "Lodging check out date is invalid.", "82901": "Incomplete PayPal account information.", "82902": "Pre-Approved Payment enabled PayPal account required for vaulting.", "82903": "Invalid PayPal account information.", "82904": "PayPal Accounts are not accepted by this merchant account.", "82905": "A customer ID is required to vault a PayPal Account.", "92906": "PayPal Account token is taken.", "92907": "Cannot use a payment_method_nonce more than once.", "92908": "Unknown or expired payment_method_nonce.", "92909": "Payment method nonce locked.", "92910": "Error communicating with PayPal.", "92911": "PayPal authentication expired.", "92912": "Funding source selection was given without an access token.", "92913": "Funding source object is invalid or missing required fields.", "92914": "Payment method nonces cannot be used to update an existing PayPal account.", "92915": "Payment method params supplied are not valid for updating a PayPal account.", "84101": "Common ID is required.", "84102": "Username is required.", "84103": "Venmo user ID is required.", "84104": "Customer ID is required.", "84105": "Venmo accounts are not accepted by this merchant account.", "84106": "Customer ID is invalid.", "92001": "Quantity is invalid.", "92002": "Amount is invalid.", "92003": "Amount cannot be blank.", "92004": "Quantity cannot be blank.", "92005": "Number of billing cycles is invalid.", "92010": "Quantity must be greater than zero.", "92011": "Existing ID is invalid.", "92012": "Existing ID is required.", "92013": "Inherited From ID is invalid.", "92014": "Inherited From ID is required.", "92015": "Cannot update a removed add-on or discount.", "92016": "Cannot remove add-on or discount if not already associated with subscription.", "92017": "Number of billing cycles cannot be blank.", "92018": "Cannot specify both number of billing cycles and never expires as true.", "92019": "Number of billing cycles must be greater than zero.", "92020": "Existing ID is not of the correct kind.", "92021": "ID to remove is incorrect kind.", "92022": "Cannot edit add-on or discount on a past due subscription.", "92023": "Amount is too large.", "92024": "Cannot pass null modification.", "92025": "ID to remove is invalid.", "81901": "Cannot edit a canceled subscription.", "81902": "ID has already been taken.", "81903": "Price cannot be blank.", "81904": "Price is an invalid format.", "81905": "Subscription has already been canceled.", "81906": "ID is invalid (use only letters, numbers, '-', and '').", "81907": "Trial Duration is an invalid format.", "81908": "Trial Duration is required.", "81909": "Trial Duration Unit is invalid.", "81910": "Cannot edit an expired subscription.", "81923": "Price is too large.", "91901": "Merchant Account ID is invalid.", "91902": "Payment method token payment instrument type is not accepted by this merchant account.", "91903": "Payment method token is invalid.", "91904": "Plan ID is invalid.", "91905": "Payment method token does not belong to the subscription's customer.", "91906": "Number Of Billing Cycles must be numeric.", "91907": "Number Of Billing Cycles must be greater than zero.", "91908": "Cannot specify both number of billing cycles and never expires as true.", "91909": "Number Of Billing Cycles is less than the current billing cycle.", "91911": "Cannot add duplicate add-on or discount.", "91912": "Number Of Billing Cycles cannot be blank if the subscription expires.", "91913": "Billing Day of Month must be numeric.", "91914": "Billing Day of Month must be between 1 and 28, or 31.", "91915": "First Billing Date is invalid.", "91916": "First Billing Date cannot be in the past.", "91917": "Cannot specify more than one type of start date.", "91918": "Billing Day of Month cannot be updated.", "91919": "First Billing Date cannot be updated.", "91920": "Can only edit id, merchant account id, payment method token, and descriptor on a past due subscription.", "91921": "Invalid request format.", "91922": "Cannot update subscription to a plan with a different billing frequency.", "91923": "Subscription Plan currency must be the same as the merchant account's currency.", "91924": "Payment method nonce payment instrument type is not accepted by this merchant account.", "91925": "Payment method nonce is invalid.", "91926": "Payment method nonce does not belong to the subscription's customer.", "91927": "Payment method nonce represents an un-vaulted payment instrument.", "91928": "Payment instrument type is not valid for subscriptions.", "91929": "Payment instrument type is not valid for subscriptions.", "91930": "Merchant Account does not support the given payment instrument type.", "82301": "Settlement Date is required", "82302": "Settlement Date is invalid", "82303": "Group By Custom Field is not a valid custom field", "81501": "Amount cannot be negative.", "81502": "Amount is required.", "81503": "Amount is an invalid format.", "81528": "Amount is too large.", "81509": "Credit card type is not accepted by this merchant account.", "81527": "Custom field is too long:", "91501": "Order ID is too long.", "91530": "Cannot provide a billing address unless also providing a credit card.", "91504": "Transaction can only be voided if status is authorized or submitted_for_settlement.", "91505": "Credit transactions cannot be refunded.", "91506": "Cannot refund a transaction unless it is settled.", "91507": "Cannot submit for settlement unless status is authorized.", "91508": "Cannot determine payment method.", "91526": "Custom field is invalid:", "91510": "Customer ID is invalid.", "91511": "Customer does not have any credit cards.", "91512": "Transaction has already been completely refunded.", "91513": "Merchant account ID is invalid.", "91514": "Merchant account is suspended.", "91515": "Cannot provide both payment_method_token and credit_card attributes.", "91516": "Cannot provide both payment_method_token and customer_id unless the payment_method belongs to the customer.", "91527": "Cannot provide both payment_method_token and subscription_id unless the payment_method belongs to the subscription.", "91517": "Payment instrument type is not accepted by this merchant account.", "91518": "Payment method token is invalid.", "91519": "Processor authorization code cannot be set unless for a voice authorization.", "91521": "Refund amount is too large.", "91538": "Cannot refund a transaction with a suspended merchant account.", "91522": "Settlement amount is too large.", "91529": "Cannot provide both subscription_id and customer_id unless the subscription belongs to the customer.", "91528": "Subscription ID is invalid.", "91523": "Transaction type is invalid.", "91524": "Transaction type is required.", "91525": "<PERSON><PERSON> is disabled.", "91531": "Subscription status must be Past Due in order to retry.", "91547": "Merchant account does not support refunds.", "81531": "Amount must be greater than zero.", "81534": "Tax amount cannot be negative.", "81535": "Tax amount is an invalid format.", "81536": "Tax amount is too large.", "81571": "Failed to authenticate, please try a different form of payment.", "91537": "Purchase order number is too long.", "91539": "Voice Authorization is not allowed for this card type", "91540": "Transaction cannot be cloned if payment method is stored in vault.", "91541": "Cannot clone voice authorization transactions.", "91542": "Unsuccessful transaction cannot be cloned.", "91543": "Credits cannot be cloned.", "91544": "Cannot clone transaction without submit_for_settlement flag.", "91545": "Voice Authorizations are not supported for this processor.", "91546": "Credits are not supported by this processor.", "91548": "Purchase order number is invalid.", "81520": "Processor authorization code must be 6 characters.", "91549": "Cannot provide more than one of payment_method_token, payment_method_nonce, credit_card, and venmo_sdk_payment_method_code attributes.", "91550": "Channel is too long.", "91551": "Settlement amount cannot be less than the service fee amount.", "91552": "Credits not allowed with service fee.", "91553": "Sub-merchant account requires a service fee.", "91554": "Amount cannot be negative.", "91555": "Amount is an invalid format.", "91556": "Service fee amount is larger than transaction amount.", "91557": "Service fee not supported on master merchant account.", "91558": "Merchant account does not support MOTO transactions unless configured by processor.", "91559": "Cannot refund a transaction with a pending merchant account.", "91560": "Transaction could not be held in escrow.", "91561": "Cannot release a transaction that is not escrowed.", "91562": "Release can only be cancelled if the transaction is submitted for release.", "91563": "Escrowed transactions cannot be partially refunded.", "91564": "Cannot use a payment_method_nonce more than once.", "91565": "Unknown or expired payment_method_nonce.", "91567": "Payment instrument type is not accepted by this merchant account.", "91568": "Three D Secure <PERSON> is invalid.", "91569": "payment_method_nonce does not contain a valid payment instrument type.", "91572": "Current payment method does not support use_billing_for_shipping flag.", "91575": "Cannot transition transaction to settled, settlement_confirmed, or settlement_declined", "91576": "PayPal is not enabled for your merchant account.", "91577": "Merchant account does not support payment instrument.", "91570": "Transaction data does not match data from Three D Secure verify call.", "91573": "Transaction cannot be cloned if payment method is a PayPal account.", "91574": "Cannot refund a transaction transaction in settling status on this merchant account. Try again after the transaction has settled.", "91578": "Service fee can not be applied on PayPal transactions.", "91580": "PayPal custom fields must be less than 256 characters in length.", "91581": "Shipping address customer does not match customer in request.", "91582": "PayPal unilateral transactions must also be submitted for settlement.", "91583": "This PayPal account was not vaulted with the required data", "91584": "Merchant account must match the 3D Secure authorization merchant account.", "91585": "Amount must match the 3D Secure authorization amount.", "91586": "Shared billing address ID cannot be used in the same call as a standard billing address ID", "91587": "Shared customer ID cannot be used in the same call as a standard customer ID", "91588": "Shared payment method token cannot be used in the same call as a standard payment method token", "91589": "Shared payment method token cannot be used in the same call as a non-shared identifier param", "91590": "Shared identifier param cannot be used with non-shared payment method token", "91591": "Shared shipping address ID cannot be used in the same call as a standard shipping address ID", "91592": "Shared payment methods cannot be vaulted", "91593": "Shared payment methods cannot be vaulted", "91594": "Shared shipping addresses cannot be vaulted", "91595": "Shared payment methods cannot be updated", "91597": "Cannot provide both shared_payment_method_token and shared_customer_id unless the payment_method belongs to the customer.", "91598": "Payment instrument type is not accepted by this merchant account.", "91599": "Shared Shipping address customer does not match customer in request.", "91596": "Shared payment method token is invalid.", "915100": "Shared Customer ID is invalid.", "915103": "Cannot submit for partial settlement.", "915101": "Payment instrument type is not accepted.", "915102": "Partial settlements are not supported by this processor.", "915104": "Delayed settlements are not supported for this processor. The submit for settlement option is required.", "915105": "Merchant account does not support Amex rewards.", "915106": "Points amount is too large.", "915107": "Updating order_id on submit_for_settlement is not supported by this processor.", "915108": "Updating descriptor on submit_for_settlement is not supported by this processor.", "915109": "PayPal supplementary data fields must be less than 4001 characters in length:", "915110": "Cannot clone facilitated transactions.", "915111": "PayPal supplementary data field count must be less than 101.", "915112": "Shared payment method token originated from another merchant and is not allowed to be shared", "915113": "EciFlag is required.", "915114": "EciFlag is invalid.", "915115": "Xid is required for specified EciFlag.", "915116": "Cavv is required for specified EciFlag.", "915131": "Merchant account does not support 3D Secure transactions for card type.", "915133": "Transaction source must be either 'moto' or 'recurring'.", "915134": "submit_for_settlement is required and must be true.", "915135": "shared_payment_method_nonce does not contain valid payment instrument type.", "915136": "Payment instrument type is not accepted by this merchant.", "915137": "Cannot clone Braintree Marketplace transactions via the API.", "92201": "Company name/DBA section is invalid.", "92202": "Phone number is invalid.", "92203": "Dynamic descriptors have not been enabled for this account. <NAME_EMAIL>.", "92204": "Descriptor format is invalid.", "92205": "International phone number is invalid.", "92206": "URL must be 13 characters or shorter.", "94201": "Verification amount cannot be negative.", "94202": "Verification amount is invalid.", "94203": "Verification amount not supported by processor.", "94204": "Verification Merchant Account ID is invalid.", "94205": "Verification Merchant Account is suspended.", "94206": "The current user does not have access to the specified merchant_account_id", "94207": "Verification amount is too large.", "94208": "Verification Merchant Account ID cannot be a sub-merchant account.", "95817": "Product unit amount is in an invalid format.", "95820": "Unit amount for transaction line item must be greater than zero. If this error continues, please disable the line items option in the plugin settings.", "gateway_rejected:_avs": "Invalid postal code or street address.", "gateway_rejected:_postal_code": "Postal code.", "gateway_rejected:_cvv": "CVV.", "gateway_rejected:_avs_and_cvv": "Invalid postal code and cvv", "gateway_rejected:_three_d_secure": "Your payment method was rejected due to 3D Secure."}