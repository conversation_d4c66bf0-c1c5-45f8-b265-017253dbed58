<?php

class StripeKeyExtractor
{
    private function xorDecode($input)
    {
        $output = "";
        for ($i = 0; $i < strlen($input); $i++) {
            $char = $input[$i];
            $dec = ord($char);
            $dec = $dec ^ 5;
            $output .= chr($dec);
        }
        return $output;
    }

    private function deBase64($data)
    {
        $output = str_replace("%2F", "/", $data);
        $output = str_replace("%2B", "+", $output);
        $output = base64_decode($output);
        $output = utf8_decode($output);
        return $output;
    }

    public function extract($cs)
    {
        $pattern = "/cs_live_[\w]+/";
        preg_match($pattern, $cs, $matches);
        $csId = $matches[0] ?? null;

        if (!$csId) {
            throw new Exception("Invalid CS string provided.");
        }

        $encryptedKey = substr($cs, strpos($cs, "#") + 1);
        $decodedKey = $this->xorDecode($this->deBase64(urlencode($encryptedKey)));
        $pk = json_decode($decodedKey, true);

        if (!$pk) {
            throw new Exception("Failed to decode API key.");
        }

        return [
            'cs' => $csId,
            'pk' => $pk
        ];
    }
}

try {
    $cs = "https://pay.openai.com/c/pay/cs_live_a1mrH1BMQepGR6fcRB7gSQnjZbGRCvGECZqvLGeSCiPr7udsou09C3wlyv#fidpamZkaWAnPydgaycpJ3ZwZ3Zmd2x1cWxqa1BrbHRwYGtgdnZAa2RnaWBhJz9jZGl2YCknZHVsTmB8Jz8ndW5aaWxzYFowNE1Kd1ZyRjNtNGt9QmpMNmlRRGJXb1xTd38xYVA2Y1NKZGd8RmZOVzZ1Z0BPYnBGU0RpdEZ9YX1GUHNqV200XVJyV2RmU2xqc1A2bklOc3Vub20yTHRuUjU1bF1Udm9qNmsnKSdjd2poVmB3c2B3Jz9xd3BgKSdpZHxqcHFRfHVgJz8ndmxrYmlgWmxxYGgnKSdga2RnaWBVaWRmYG1qaWFgd3YnP3F3cGB4JSUl";
    if (empty($cs)) {
        $response = json_encode([
            'success' => false,
            'Error' => 'Requirements Are Empty.'
        ]);
        echo $response;
        exit;
    }

    $extractor = new StripeKeyExtractor();
    $result = $extractor->extract($cs);

    $response = json_encode([
        'success' => true,
        'Pk' => $result['pk']['apiKey'],
        'CS' => $result['cs']
    ]);
    echo $response;
} catch (Throwable $e) {
    $response = json_encode([
        'success' => false,
        'Error' => $e->getMessage()
    ]);
    echo $response;
}
