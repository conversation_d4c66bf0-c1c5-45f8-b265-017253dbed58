import requests
import base64
import json
import hmac
import hashlib
import base64


s = requests.Session()

url = 'https://www.givemn.org/api/v4/checkout/get_payment_attributes.json?story_id=Givemn&amount=5&scheduled=One%20Time&matching_grant_id=undefined'
req1 = s.get(url)
req11 = req1.json()
orderid = req11['payment_processor_order_id']
accid = req11['payment_account_id']
id = req11['story']['id']


#Req2
req2 = s.get(f'https://www.givemn.org/api/v4/checkout/braintree_client_token.json?payment_account_id={accid}&customer_id=undefined')
req22 = req2.json()
clienttoken = req22['client_token']
b3auth = base64.b64decode(clienttoken)
data = json.loads(b3auth)
auth_fingerprint = data['authorizationFingerprint']

#Req3
url = "https://payments.braintree-api.com/graphql"

headers = {
    "authority": "payments.braintree-api.com",
    "Accept": "*/*",
    "Accept-Language": "en-US,en;q=0.9,vi;q=0.8",
    "Authorization": f"Bearer {auth_fingerprint}",  # Add your token here
    "Braintree-Version": "2018-05-10",
    "Content-Type": "application/json",
    "Origin": "https://assets.braintreegateway.com",
    "Referer": "https://assets.braintreegateway.com/",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "cross-site",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36"
}

payload = {
    "clientSdkMetadata": {
        "source": "client",
        "integration": "custom",
        "sessionId": "5b50792b-88fb-4163-9ca2-df0fb3fbb45e"
    },
    "query": "mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }",
    "variables": {
        "input": {
            "creditCard": {
                "number": "****************",
                "expirationMonth": "07",
                "expirationYear": "2025",
                "cvv": "000",
                "cardholderName": "david joe"
            },
            "options": {
                "validate": False
            }
        }
    },
    "operationName": "TokenizeCreditCard"
}

req3 = s.post(url, headers=headers, data=json.dumps(payload))
req33 = req3.json()
token = req33['data']['tokenizeCreditCard']['token']



data = {
  "name": "con cac",
  "email": "<EMAIL>" 
}
secret_key = "mysecretkey"
json_str = json.dumps(data)
base64_str = base64.urlsafe_b64encode(json_str.encode()).decode().replace('=', '')
signature = hmac.new(secret_key.encode(), base64_str.encode(), hashlib.sha1).hexdigest() 
encoded_signature = base64.urlsafe_b64encode(signature.encode()).decode().replace('=', '')
print(encoded_signature)






#Req4
url = "https://www.givemn.org/api/v4/checkout.json"
headers = {
    "authority": "www.givemn.org",
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "en-US,en;q=0.9,vi;q=0.8",
    "Content-Type": "application/json",
    "Origin": "https://www.givemn.org",
    "Referer": "https://www.givemn.org/donate/Givemn",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36",
    "X-Mc-Signature": "PsGQ7sqXnV03A7lNUAI_lLQQNrQ79HJEaQei5bz_BRQ"
}

payload = '{"cart":{"cart_items":[{"amount":5,"identifies_id":'+id+',"identifies_type":"Org","recurring_donation":"One Time","designation":"","dedication":null,"hide_donation_amount":true,"publicly_hidden":true,"anonymous":true}],"number_of_items":0,"allow_fully_anonymous_donations":true,"fees_covered":false,"cached_donation_booster":true,"donation_booster_default_state":true},"email":"<EMAIL>","payment_processor_order_id":"'+orderid+'","credit_card_attributes":{"address1":"new york123","address2":"","card_number":"","card_type":"Visa","last4":"1362","city":"new york","country":"USA","email":"<EMAIL>","expiration_month":"07","expiration_year":"2025","first_name":"david","last_name":"joe","full_name":"david joe","postal_code":"10080","security_code":"","state":"NY","bt_payment_nonce":"'+token+'"},"donation_params":{"form_question_answers":[{"form_question_id":3899,"form_answer_option_id":null,"brand_category_id":null,"search_category_id":null,"form_answer":""}]},"store_card":"","stored_credit_card_id":null,"stored_bank_account_id":null,"giving_fund_id":null,"tip_amount":0,"source":"Desktop Website","bt_device_data":"{\"correlation_id\":\"506d70811c8a61731ca84697a8c860d7\"}"}'

response = requests.post(url, headers=headers, data=json.dumps(payload))

print(response.json())