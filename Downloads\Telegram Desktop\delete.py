from telethon import TelegramClient

# Define tu API ID y API Hash de Telegram
api_id = 'API_ID'
api_hash = 'API_HASH'

# Define el nombre del canal
channel_username = 'Usernamedelcanalogrupo'

# Define el nombre de sesión
session_name = 'session_name'

# Crea el cliente de Telethon
client = TelegramClient(session_name, api_id, api_hash)

async def clear_messages():
    await client.start()

    # Obtiene la entidad del canal
    channel = await client.get_entity(channel_username)

    # Elimina mensajes en bloques de 100
    async for message in client.iter_messages(channel):
        await client.delete_messages(channel, [message.id])

    print("Mensajes eliminados.")

with client:
    client.loop.run_until_complete(clear_messages())