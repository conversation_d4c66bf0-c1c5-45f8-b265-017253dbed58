<?php
/**
 * CheckoutChamp API
 * 
 * Coded by @NezukoChk0wner
 *
 * This script demonstrates an integration with the CheckoutChamp API.
 * It adds a product to the cart, retrieves the cart token, loads the checkout page,
 * and finally submits an order through the API.
 *
 * Note: The CurlX library used in this script can be obtained from:
 *       https://github.com/devblack/curlx
 *
 * Requirements:
 * - CurlX.php (for handling HTTP requests)
 */
require 'CurlX.php';
$curlx = new CurlX();
$cookie = uniqid();

function generateSessionId() {
    $prefix = date('Ymd');
    
    $randomHex = bin2hex(random_bytes(16));
    
    return $prefix . substr($randomHex, 0, 24);
}

function generateUserAgent() {
    $platform = "Windows NT 10.0; Win64; x64";
    $webkit_version = "537.36"; 
    $chrome_major = rand(100, 140);
    $chrome_version = "{$chrome_major}.0.0.0";

    $userAgent = "Mozilla/5.0 ({$platform}) AppleWebKit/{$webkit_version} (KHTML, like Gecko) Chrome/{$chrome_version} Safari/{$webkit_version}";

    return $userAgent;
}


$curlx->post(
    'https://painsafari.com/cart/add',
    http_build_query([
        'quantity' => '1',
        'form_type' => 'product',
        'utf8' => "✓",
        'id' => '46828831539507',
        'product-id' => '8632226218291',
        'section-id' => 'template--22557966827827__main',
        'sections' => 'cart-notification-product,cart-notification-button,cart-icon-bubble',
        'sections_url' => '/products/cia-ice-defender',
    ]),
    null,
    $cookie
);

$a = $curlx->get(
    'https://painsafari.com/cart.json',
    null,
    $cookie
)->getbody();

$cartid = json_decode($a)->token;

$b = $curlx->get(
    'https://checkout.painsafari.com/secure?products=33:1&noparams=1&frm_sesstorage=1&cartId=' . $cartid,
    null,
    $cookie
)->getbody();

$c = $curlx->post(
    'https://live-api.checkoutchamp.com/providersApi/V1/Import/Order',
    [
        'state' => 'FL',
        'country' => 'US',
        'emailAddress' => '<EMAIL>',
        'phoneNumber' => '**********',
        'shipProfileId' => '0',
        'custom1' => 'Shopify Store - PainSafari',
        'custom2' => 'Checkout Page',
        'redirectsTo' => 'https://checkout.painsafari.com/thankyou',
        'browserData' => '{"acceptHeader":"application/json","userAgent":"' . generateUserAgent() . '","language":"en-US","timezone":"-330","colorDepth":24,"screen":{"height":"1080","width":"1920"},"javaScriptEnabled":true,"javaEnabled":false}',
        'billShipSame' => 1,
        'salesUrl' => 'https://checkout.painsafari.com/secure?products=33%3A2&noparams=1&frm_sesstorage=1&cartId=' . $cartid . '&cc_redirected=1',
        'cartId' => $cartid,
        'campaignId' => 4,
        'attributes[buyer_accepts_marketing_attentive]' => 0,
        'custom_order_products' => '33:1',
        'custom_order_noparams' => '1',
        'custom_order_frm_sesstorage' => '1',
        'custom_order_cartId' => $cartid,
        'custom_order_cc_redirected' => '1',
        'shipFirstName' => 'insane',
        'shipLastName' => 'xd',
        'shipAddress1' => '11n lane avenue south',
        'shipCity' => 'jacksonwille',
        'shipState' => 'FL',
        'shipPostalCode' => '32210',
        'shipCountry' => 'US',
        'cardNumber' => '****************',
        'cardMonth' => 11,
        'cardYear' => 27,
        'cardSecurityCode' => '767',
        'paySource' => 'CREDITCARD',
        'product1_id' => '33',
        'product1_qty' => '1',
        'sessionId' => generateSessionId(),
        'insureShipment' => 0,
        'currencyCode' => 'USD'
    ],
    [
        'accept: */*',
        'accept-language: en-US,en;q=0.9',
        'access-control-allow-origin: *',
        'cache-control: no-cache',
        'companytoken: 70421aa0-3545-11ee-9ddf-47eccb9b4f20',
        'content-type: application/json; charset=utf-8',
        'funnelreferenceid: 089f0873-8b63-499e-8013-9bcb9d6ec7b8',
        'origin: https://checkout.painsafari.com',
        'priority: u=1, i',
        'referer: https://checkout.painsafari.com/',
        'sec-ch-ua: "Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
        'sec-ch-ua-mobile: ?0',
        'sec-ch-ua-platform: "Windows"',
        'sec-fetch-dest: empty',
        'sec-fetch-mode: cors',
        'sec-fetch-site: cross-site',
    ],
    $cookie
);
echo $c->getbody();