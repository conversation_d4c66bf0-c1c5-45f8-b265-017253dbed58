# Yakuza-ECO-System
 
**Hosts**

| Target |
| :-------- |
| `api.八座区.online` |
| `api.xn--45qx7by8s.online` |


**Header Access**

| Name | Value     | Description                |
| :-------- | :------- | :------------------------- |
| `yakuza-nonce` | `my-key` | **Required**. For all requests |

**V1**

| Name      | Method     | Description                |
| :-------- | :------- | :------------------------- |
| `/v1/bin/:num` | GET | **Lookup data of an bin number.** |
| `/v1/person` | GET | **Get random person data.** |

**Encrypts**

| Name      | Method     | Description                |
| :-------- | :--------- | :------------------------- |
| `/encrypt/adyen`        | POST | **Required a public key and data**                   |
| `/encrypt/adyen2`       | POST | **Required a public key and data**                   |
| `/encrypt/zoura`        | POST | **Required a public key and data.**                  |
| `/encrypt/braintree`    | POST | **Required a public key and data.**                  |
| `/encrypt/bluesnap`     | POST | **Required `e` and `n` separate by `$` and data.**   |
| `/encrypt/chase`        | POST | **Required a `PIE` dynamic params and object data.** |
| `/encrypt/securepay`    | POST | **Required a `JWK` and data.**                       |
| `/encrypt/cybersource`  | POST | **Required a `JWK` and data.**                       |
| `/encrypt/cybersource2` | POST | **Required a `KEY` and data.**                       |
| `/encrypt/vanti`        | POST | **Required the `exponent`, `modulus` and data.**     |
| `/encrypt/eway`         | POST | **Required the `exponent`, `modulus` and data.**     |
| `/encrypt/paytrace`     | POST | **Required a public key string and data.**           |
| `/encrypt/payeezy`      | POST | **Required a public key string and data.**           |
| `/encrypt/card-connect` | POST | **Required a public key string and data.**           |
| `/encrypt/bolt-encrypt` | POST | **Required a public key string and data.**           |
| `/encrypt/bolt-decrypt` | POST | **Required the `public key`, `secret key` and data.** |


**Examples**

You need to find similars public keys or paramters, depent of the website.

- Adyen Example:
    ```json
    {
        "version": 18,
        "pk": "10001|BCB1F75845907F183EB06FDC0D996DC4ECF25B7AC113A083EE484CEE2F2B9F0337E4AE5C67690CB24C805B2469AABBA00E0E9A6E55CF00D2E1A70F3360762F466352D149F25DF55638FB9CE344A5058AF9956C9BFC29EF7D82EC1F9C0B841B53AC445D8020352811AD106D310673850894CE8427070016D95191C2FE73FBDBE614E6FF179828AE1F5B49BA952385536CACBE2109798B8A1DC49810A568FE16786DFF731EA4D15E6E32FE5F156F0062FCAE2854ABC182F4F4B0DD6EFC4D82122EC293DA5FDD5C72E729172C6B870823D1064CDF034029E360CA7D010BAD4A26FB7B2146D686809CF6463A32B8EB5D5F25DC5DFC7B25B343EECBF6A33DE9505609",
        "data": [{
            "number": "*****************"
        },
        {
            "cvc": "000"
        },
        {
            "expiryMonth": "01"
        },
        {
            "expiryYear": "2021"
        }]
    }
    ```

- Adyen2 Example:
      ```json
      {
          "pk": "10001|BCB1F75845907F183EB06FDC0D996DC4ECF25B7AC113A083EE484CEE2F2B9F0337E4AE5C67690CB24C805B2469AABBA00E0E9A6E55CF00D2E1A70F3360762F466352D149F25DF55638FB9CE344A5058AF9956C9BFC29EF7D82EC1F9C0B841B53AC445D8020352811AD106D310673850894CE8427070016D95191C2FE73FBDBE614E6FF179828AE1F5B49BA952385536CACBE2109798B8A1DC49810A568FE16786DFF731EA4D15E6E32FE5F156F0062FCAE2854ABC182F4F4B0DD6EFC4D82122EC293DA5FDD5C72E729172C6B870823D1064CDF034029E360CA7D010BAD4A26FB7B2146D686809CF6463A32B8EB5D5F25DC5DFC7B25B343EECBF6A33DE9505609",
          "data": [{
              "number": "*****************",
              "expiryMonth": "05",
              "expiryYear": "2010",
              "cvc": "000"
          },
          {
              "cvc": "000"
          },
          {
              "expiryMonth": "05"
          },
          {
              "expiryYear": "2010"
          }]
      }
      ```

- Zoura Example:
    ```json
    {
        "pk": "MIIBCgKCAQEAqwMQwio/BxEAjKpoeW2MLmOBytB0L6K9nvsx3s6SrFF2VTjdf45rO1VOYDRVG82dn2hGeM+F9RkCk/sYORoP1Ozfy+62JBla2ArjtgmyPZrZoo8vFdrTzXSJUglBJJZFnvAkr0YRd2+5CyI8S/EP1xFCB8dP0ZIwPGH/DoxdY6Cvyzh04l7fsE7E3nH9+8W7+tJ2jFk2Rg6g8NCzaOIej/C1EnRXrvdJmgg1TAVbQH44pEn0gWQVnZKBbibFj8TUrAy/ptgaV8rYcbMztNtcLAJrcL8AougyE6bQDyZjdjn6ilNeZx3iKZk3isuQ9NLYj8KorELVE68PM3s9tZCuZwIDAQAB",
        "data": ["*****************", "000"]
    }
    ```

- Braintree Example:
    ```json
    {
        "pk": "MIIBCgKCAQEAqwMQwio/BxEAjKpoeW2MLmOBytB0L6K9nvsx3s6SrFF2VTjdf45rO1VOYDRVG82dn2hGeM+F9RkCk/sYORoP1Ozfy+62JBla2ArjtgmyPZrZoo8vFdrTzXSJUglBJJZFnvAkr0YRd2+5CyI8S/EP1xFCB8dP0ZIwPGH/DoxdY6Cvyzh04l7fsE7E3nH9+8W7+tJ2jFk2Rg6g8NCzaOIej/C1EnRXrvdJmgg1TAVbQH44pEn0gWQVnZKBbibFj8TUrAy/ptgaV8rYcbMztNtcLAJrcL8AougyE6bQDyZjdjn6ilNeZx3iKZk3isuQ9NLYj8KorELVE68PM3s9tZCuZwIDAQAB",
        "pk": "MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAqqKOTfRHO+sVyBO5qDGtMepIKGMehAj7QiMDQzy56wNUFg035hG/gEeyyUh0V/VzqbVfhDNelJS2520jRhim73djPmp4HI1NoExApjnKh70pqKJ8ZowyVFSqhfTRAHF48YRiczRFm3nlhZiXBwJDDk/SoAbkox7rFQmZtMt9MAphZt7UXeORsE+oK9t/4Yv8aAAi6tHHqcdsE/06+2W2M3QlWPmm03BSgcP+SeB+htL6mtmp0o5r3VWfUEIyM9Jn5xtmpHMNrDX7dhxuf8M45X06NB3JqkFvOiIPc0R6faYjISZYx3gOZdo1k4lla2FHIoVMC4F6ctMSNZ6mJnXHGEYV4Kxhvft6nmqIfOpXw91MzV30vIRYhm1K48szhwXiAppAkzXJYMGiCYY19OHUhb5rZvHyVcGwDlEupxuHAjYFdvCmFMgTAA/9o83pgUMHJu1M/JHpgNLZXVkexuOFgSFhsWVukUlMCPg6pPY0ci8NHIUnKioRB4b4QSTWGkycrHjXmq00nAYwxhTGSarm+tyFSSf7HhJkl0xjCw68IJ4ryl+V4y2tTG2YB83cSIsBY6ThT7TqHnv71ASQCdvpWQ7mGjdG3UJUqggK4HDOwv4q0sspp6dZlvnHNj7WkplylNB+tz38v7RFxeRTM6E6/2UveOGVPKuxKq1+N2MtsbcCAwEAAQ=="
        "data": ["*****************", "000"]
    }
    ```

- Bluesnap Example:
    ```json
    {
        "pk": "10001$9ebd8772a8cb5c1c7a1019352e9d1a3a4cfbaa179e1ded40e4d0622f7d4c13b6b284809ad7f2e53fae4da6bfff29101f6c1fb1ff3a5dc6c82a86527cd0c52dde4f693bb7585289b6264365058106b6805878e459cb00e2e1514b91a29d2e711e870119df5ede37db4dbe288c921bb42f4fc25c01878b9e1f38e51fdd997a5ba65a757a0c06660ef7f5d0d4895dd0b18c02edcab173a60780eac622ed1a63373e94893f5de3baf28d3bf327ff36b7b36bd721547d23e4d50a38d1744321512d748f780e740affa2f5c538445fc32aa7b24846728d3edf767be923ce9e25cb95b2c64768f8278c1f2a3bdf7849ff2e29a3e210a66e5c80f6673f82572a4e8a1715",
        "data": ["*****************", "000"]
    }
    ```

- Chase Example:
    ```json
    {
        "data": {
            "cc": "*****************",
            "cvv": "000"
        },
        "pie": {
            "L": 6,
            "E": 4,
            "K": "130BB196BC72D787F6FC4FB3178B5959",
            "key_id": "d88574c2",
            "phase": 0
        }
    }
    ```

- SecurePay Example:
    ```json
    {
        "jwk": {
            "kty": "RSA",
            "n": "24gKlX5n_yeOKYOyAQ4LCiZtzWrhU3SoHfHBVEYufsMvSA_BQ8M985Foj-LWuM3NleRJVTPptfaVS8Oryr5RYlNYxOtUcUw5MeVBbkSRr8k56NY4mN7XTAPHwvol2ZeFUWhPJrzEmvN-eiU1TXJF1lqe0CDoYILjb5oAcGzjiPyfUsxYokCR7AWytdIrqjmqqN9QoBiB1QdpABCEwmBFh5owOhOrm8l_V9KGScd0-hAXYr-uJrGqh12EUhmc5AL5jZPxtYvdTmutVZOwXhfNC1ywIjdsGBnsCKPRlcUunf_J-NbRiPKVepsTGFbu7QrurSmXN_-moBZ_unG4WQSpk0RDoFazf7L0X2bIyL1vj7HT3x-IB0F6nKCLiKeUBncxFgbgit2TGEf5IbscFMVMCscTQBjh4F_zUw1d1u2DKAvXsrylhk2D3X9T4NM6Bypb-zU0mKVXMv-gMaoYEknOm_prohDvY1idfkf0cqhlkEkV7Fe6cV4MxHxuR0ig3yvHjEu5BvO1Slhtc_uumZvHKfhC_4dR4ZN5gl_Zqrj8M157fSQP4juvBx_iKThDTSc9a6tW9B9AesY-imV2zxLNNbFSrA9E6OXqEJweLSOa-ulJi_Tzs9LtYgg5l3WvuiR2FF5dI8c5JQsMEnrsDwp4hzBCevkp7JbUU-b25ZhSa6Uz", 
            "e": "AQAB"
        },
        "data": ["*****************", "000"]
    }
    ```

- CyberSource Example:
    ```json
    {
        "jwk": {
            "kty": "RSA",
            "n": "24gKlX5n_yeOKYOyAQ4LCiZtzWrhU3SoHfHBVEYufsMvSA_BQ8M985Foj-LWuM3NleRJVTPptfaVS8Oryr5RYlNYxOtUcUw5MeVBbkSRr8k56NY4mN7XTAPHwvol2ZeFUWhPJrzEmvN-eiU1TXJF1lqe0CDoYILjb5oAcGzjiPyfUsxYokCR7AWytdIrqjmqqN9QoBiB1QdpABCEwmBFh5owOhOrm8l_V9KGScd0-hAXYr-uJrGqh12EUhmc5AL5jZPxtYvdTmutVZOwXhfNC1ywIjdsGBnsCKPRlcUunf_J-NbRiPKVepsTGFbu7QrurSmXN_-moBZ_unG4WQSpk0RDoFazf7L0X2bIyL1vj7HT3x-IB0F6nKCLiKeUBncxFgbgit2TGEf5IbscFMVMCscTQBjh4F_zUw1d1u2DKAvXsrylhk2D3X9T4NM6Bypb-zU0mKVXMv-gMaoYEknOm_prohDvY1idfkf0cqhlkEkV7Fe6cV4MxHxuR0ig3yvHjEu5BvO1Slhtc_uumZvHKfhC_4dR4ZN5gl_Zqrj8M157fSQP4juvBx_iKThDTSc9a6tW9B9AesY-imV2zxLNNbFSrA9E6OXqEJweLSOa-ulJi_Tzs9LtYgg5l3WvuiR2FF5dI8c5JQsMEnrsDwp4hzBCevkp7JbUU-b25ZhSa6Uz", 
            "e": "AQAB"
        },
        "data": ["*****************", "000"]
    }
    ```

- CyberSource2 Example:
    ```json
    {
        "key": "***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "data": {"number": "*****************", "expirationMonth": "01", "expirationYear": "2021", "securityCode": "000", "type": "001"}
    }
    ```

- Vanti Example:
    ```json
    {
        "key": {
            "e": "10001",
            "n": "b1794db268531a00f487c6cd21920ff04a1a95877f363d7e1354db6bd98fe69f62f8baaa72f6ae5036a0f13f18ff1afc5e33bfb5ece1a6de1deabe53af1f61fe8cb06b28fa0b5b85c7ddda756b1686082d3947e5a4bb12710c1d88bc2751112d663d076f0d064956c878d34e1a8a00fb1b8d6cd9543058761d326125c9ec45556507ed5efc5a7f28c6dffc48ac8c1e0fb6dfd0284b7914b95b30402b1aa0da6b7e21d72a931d4a03f8032cad1bf016d4f70bfb83dc01057246a58c90bdf7dc61110035b708bec68ed92c724e3311d5ac21722101a1d5ffb5421da715967de38254f964b271c95add2e9591be355110a8b14c1d91376713011a8400358be11d79"
        },
        "data": ["*****************", "000"]
    }
    ```

- Eway Example:
    ```json
    {
        "key": {
            "e": "AQAB",
            "n": "wjZQgH7VWeNK7VEFm3LHzT+MgVjaaj7LuYVTWMbHW/YVIfK9hDheeHGffxkbXB6xr0wUI4Ag1/ZQzeFcS3m4BCqBDpKEu+7eCOeLKh3r0FR6j1kdZCRUEH2TEFar0Vh6Va5xjls0ZMSJvQRvKTy9KQdiPrb6cpDYVlD//ROiKnkftcC5Dx5pUkKDxX+YaEqoi6zuSOWRH+qwb/XfDlceasN4c+VqIaPvZAXjd/BHzZOW+S/lKblPQmaVGDNY5BTGy6go7e1aj5UvIUI1cNsmAIynS5cDlqAialJnVdpSIpQlXAb4UQeI0OvkBO5ZhBIoece65sO5PpwA3OAoyEU23w=="
        },
        "data": ["*****************", "000"]
    }
    ```

- PayTrace Examples:
    * A string line public key.
    ```json
    {
        "pk": "-----BEGIN PUBLIC KEY-----MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvUlOB011i+PKcu/fi0mqD5X3EPLWi7glJN/76/77KhQdvIQxRXRVau2NVSWihT4z33SMGYIMmEC+QGqlA3tq/QSwmmy/K3LMR7wNeNbetaiEaOPa8rk8pQHQ7JdyBI3ynlxggsBGZS4N5Xm26xhU1bQzeHdqlxhirk71r2EHQL9IrnkkRogEr+W3axPdF82SWrkKur5F7o+wpMDm2nkSpz2elhg/qXmFDupbeRntl3t7pzIhU+M7nYuVmnJSori2o9E1FuID68p5XxaA8mYUrnRvBfaszKaGkGnOij0bxBjcpHhd1TD8C6eCeho3yumM+dQX1FDZqQbyYHgM+BGXjQIDAQAB-----END PUBLIC KEY-----",
        "data": ["*****************", "000"]
    }
    ```
    * A base64 encode of public key.
    ```json
    {
        "pk": "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS1NSUlCSWpBTkJna3Foa2lHOXcwQkFRRUZBQU9DQVE4QU1JSUJDZ0tDQVFFQXZVbE9CMDExaStQS2N1L2ZpMG1xRDVYM0VQTFdpN2dsSk4vNzYvNzdLaFFkdklReFJYUlZhdTJOVlNXaWhUNHozM1NNR1lJTW1FQytRR3FsQTN0cS9RU3dtbXkvSzNMTVI3d05lTmJldGFpRWFPUGE4cms4cFFIUTdKZHlCSTN5bmx4Z2dzQkdaUzRONVhtMjZ4aFUxYlF6ZUhkcWx4aGlyazcxcjJFSFFMOUlybmtrUm9nRXIrVzNheFBkRjgyU1dya0t1cjVGN28rd3BNRG0ybmtTcHoyZWxoZy9xWG1GRHVwYmVSbnRsM3Q3cHpJaFUrTTduWXVWbW5KU29yaTJvOUUxRnVJRDY4cDVYeGFBOG1ZVXJuUnZCZmFzekthR2tHbk9pajBieEJqY3BIaGQxVEQ4QzZlQ2VobzN5dW1NK2RRWDFGRFpxUWJ5WUhnTStCR1hqUUlEQVFBQi0tLS0tRU5EIFBVQkxJQyBLRVktLS0tLQ==",
        "data": ["*****************", "000"]
    }
    ```

- PayEezy Examples:
    * A string line public key.
    ```json
    {
        "pk": "-----BEGIN PUBLIC KEY-----MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvUlOB011i+PKcu/fi0mqD5X3EPLWi7glJN/76/77KhQdvIQxRXRVau2NVSWihT4z33SMGYIMmEC+QGqlA3tq/QSwmmy/K3LMR7wNeNbetaiEaOPa8rk8pQHQ7JdyBI3ynlxggsBGZS4N5Xm26xhU1bQzeHdqlxhirk71r2EHQL9IrnkkRogEr+W3axPdF82SWrkKur5F7o+wpMDm2nkSpz2elhg/qXmFDupbeRntl3t7pzIhU+M7nYuVmnJSori2o9E1FuID68p5XxaA8mYUrnRvBfaszKaGkGnOij0bxBjcpHhd1TD8C6eCeho3yumM+dQX1FDZqQbyYHgM+BGXjQIDAQAB-----END PUBLIC KEY-----",
        "data": {"card":"*****************","cvv":"000","exp":"0121","name":"Armando Bancos"}
    }
    ```
    * A base64 encode of public key.
    ```json
    {
        "pk": "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS1NSUlCSWpBTkJna3Foa2lHOXcwQkFRRUZBQU9DQVE4QU1JSUJDZ0tDQVFFQXZVbE9CMDExaStQS2N1L2ZpMG1xRDVYM0VQTFdpN2dsSk4vNzYvNzdLaFFkdklReFJYUlZhdTJOVlNXaWhUNHozM1NNR1lJTW1FQytRR3FsQTN0cS9RU3dtbXkvSzNMTVI3d05lTmJldGFpRWFPUGE4cms4cFFIUTdKZHlCSTN5bmx4Z2dzQkdaUzRONVhtMjZ4aFUxYlF6ZUhkcWx4aGlyazcxcjJFSFFMOUlybmtrUm9nRXIrVzNheFBkRjgyU1dya0t1cjVGN28rd3BNRG0ybmtTcHoyZWxoZy9xWG1GRHVwYmVSbnRsM3Q3cHpJaFUrTTduWXVWbW5KU29yaTJvOUUxRnVJRDY4cDVYeGFBOG1ZVXJuUnZCZmFzekthR2tHbk9pajBieEJqY3BIaGQxVEQ4QzZlQ2VobzN5dW1NK2RRWDFGRFpxUWJ5WUhnTStCR1hqUUlEQVFBQi0tLS0tRU5EIFBVQkxJQyBLRVktLS0tLQ==",
        "data": {"card":"*****************","cvv":"000","exp":"0121","name":"Armando Bancos"}
    }
    ```

- Card-Connect Example:
    * Encrypt
    ```json
    {
        "pk": "my public key",
        "data": "*****************4444"
    }
    ```

- Bolt Examples:
    * Encrypt
    ```json
    {
        "pk": "my public key",
        "data": {
            "cc": "***************",
            "cvv": "000"
        }
    }
    ```

    * Decrypt bolt response
    ```json
    {
        "pk": "my public key",
        "sk": "my secret key",
        "data": {
            "payload": "aaaaaaaaaaaaaaaaa=",
            "nonce": "bbbbbbbbbbbbbb="
        }
    }
