import axios from 'axios';

class WebsiteAnalyzer {
  constructor(url, options = {}) {
    this.url = url;
    this.options = {
      timeout: options.timeout || 5000,
      userAgent: options.userAgent || 'WebsiteAnalyzer/1.0',
    };
  }

  async analyze() {
    try {
      const response = await axios.get(this.url, {
        headers: { 'User-Agent': this.options.userAgent },
        timeout: this.options.timeout,
      });

      const html = response.data.toLowerCase();

      return {
        captcha: this.#detectCaptcha(html),
        gateways: this.#detectGateways(html),
      };
    } catch (error) {
      if (error.response) {
        console.error(`Error fetching ${this.url}: ${error.response.status} ${error.response.statusText}`);
        return { error: `HTTP error ${error.response.status}` };
      } else if (error.request) {
        console.error(`Error fetching ${this.url}: No response received`);
        return { error: 'No response received' };
      } else {
        console.error(`Error fetching ${this.url}:`, error.message);
        return { error: error.message };
      }
    }
  }

  #detectCaptcha(html) {
    if (html.includes('recaptcha') || html.includes('captcha')) {
      if (html.includes('g-recaptcha')) {
        return { type: 'reCAPTCHA', version: 'v2' };
      } else if (html.includes('data-sitekey')) {
        return { type: 'reCAPTCHA', version: 'v3' };
      } else if (html.includes('hcaptcha')) {
        return { type: 'hCaptcha' };
      } else {
        return { type: 'Captcha', version: 'unidentified' };
      }
    } else {
      return { type: 'None' };
    }
  }

  #detectGateways(html) {
    const gateways = [
      'braintree', 'stripe', 'shopify', 'payezzi', 'cybersource', 'adyen', 'razorpay', 'payflow', 'recurly'
    ];

    const detectedGateways = gateways.filter(gateway => html.includes(gateway));

    if (detectedGateways.length > 0) {
      return { gateways: detectedGateways };
    } else {
      return { gateways: [] };
    }
  }
}
analyzer.analyze().then(result => console.log(result));/**
 * Classe per analizzare un sito web per la presenza di CAPTCHA e gateway di pagamento.
 */
class WebsiteAnalyzer {
  /**
   * Costruttore della classe WebsiteAnalyzer.
   * @param {string} url - L'URL del sito web da analizzare.
   * @param {object} options - Parametri opzionali.
   * @param {number} options.timeout - Timeout della richiesta in millisecondi. Default è 5000.
   * @param {string} options.userAgent - User-Agent per la richiesta. Default è 'WebsiteAnalyzer/1.0'.
   */
  constructor(url, options = {}) {
    this.url = url;
    this.options = {
      timeout: options.timeout || 5000,
      userAgent: options.userAgent || 'WebsiteAnalyzer/1.0',
    };
  }

  /**
   * Analizza il sito web per la presenza di CAPTCHA e gateway di pagamento.
   * @returns {Promise} - Un Promise che risolve con un oggetto contenente i risultati dell'analisi.
   */
  async analyze() {
    try {
      const response = await axios.get(this.url, {
        headers: { 'User-Agent': this.options.userAgent },
        timeout: this.options.timeout,
      });

      const html = response.data.toLowerCase();

      return {
        captcha: this.#detectCaptcha(html),
        gateways: this.#detectGateways(html),
      };
    } catch (error) {
      if (error.response) {
        console.error(`Errore durante il recupero di ${this.url}: ${error.response.status} ${error.response.statusText}`);
        return { error: `Errore HTTP ${error.response.status}` };
      } else if (error.request) {
        console.error(`Errore durante il recupero di ${this.url}: Nessuna risposta ricevuta`);
        return { error: 'Nessuna risposta ricevuta' };
      } else {
        console.error(`Errore durante il recupero di ${this.url}:`, error.message);
        return { error: error.message };
      }
    }
  }

  /**
   * Rileva il tipo e la versione di CAPTCHA nel codice HTML.
   * @param {string} html - Il codice HTML del sito web.
   * @returns {object} - Un oggetto contenente il tipo e la versione di CAPTCHA rilevati.
   * @private
   */
  #detectCaptcha(html) {
    if (html.includes('recaptcha') || html.includes('captcha')) {
      if (html.includes('g-recaptcha')) {
        return { type: 'reCAPTCHA', version: 'v2' };
      } else if (html.includes('data-sitekey')) {
        return { type: 'reCAPTCHA', version: 'v3' };
      } else if (html.includes('hcaptcha')) {
        return { type: 'hCaptcha' };
      } else {
        return { type: 'Captcha', version: 'unidentified' };
      }
    } else {
      return { type: 'None' };
    }
  }

  /**
   * Rileva i gateway di pagamento nel codice HTML.
   * @param {string} html - Il codice HTML del sito web.
   * @returns {object} - Un oggetto contenente i gateway di pagamento rilevati.
   * @private
   */
  #detectGateways(html) {
    const gateways = [
      'braintree', 'stripe', 'shopify', 'payezzi', 'cybersource', 'adyen', 'razorpay',
    ];

    const detectedGateways = gateways.filter(gateway => html.includes(gateway));

    if (detectedGateways.length > 0) {
      return { gateways: detectedGateways };
    } else {
      return { gateways: [] };
    }
  }
}
// Esempio di utilizzo
const analyzer = new WebsiteAnalyzer('https://store.ie.edu');
analyzer.analyze().then(result => console.log(result));
