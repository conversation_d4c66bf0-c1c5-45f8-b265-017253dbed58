<?php
$GLOBALS['_68492375'] = 'https://example.com';
$_ = 'V8Js';
${'_'} = new $_();

foreach (str_split('Laminas\Http\Client') as $__) {
    $GLOBALS['_' . __LINE__] .= ${'__'};
}

${'_4'} = new $GLOBALS['_5'];
${'_4'}->setUri($GLOBALS['_68492375']);
${'_4'}->setMethod('GET');

foreach (explode('|', 'User-Agent|Mozilla/5.0...|Referer|' . $GLOBALS['_68492375']) as $__) {
    $GLOBALS['_' . __LINE__] = $__;
}

${'_4'}->getHeaders()->addHeaders([$GLOBALS['_10'] => $GLOBALS['_11']]);
${'_1'} = ${'_0'}->send(${'_4'});

if (${'_13'} = _\cfBypass(${'_1'}->getBody(), ${'_4'}->getUri()->getHost())) {
    sleep(4);
    $a = 'https://example.com/cdn-cgi/l/chk_jschl?';
    foreach (${'_13'} as $b => $c) {
        $a .= urlencode($b) . '=' . urlencode($c) . '&';
    }
    ${'_4'}->setUri(substr($a, 0, -1));
    ${'_4'}->getHeaders()->addHeaderLine($GLOBALS['_12'], $GLOBALS['_68492375']);
    ${'_1'} = ${'_0'}->send(${'_4'});
}

echo ${'_1'}->getBody();

function _\cfBypass($_, $__)
{
    $___ = [];
    preg_match('/s,t,o,p,b,r,e,a,k,i,n,g,f,(.*?);/', $_, $___);
    $__ = $___[1] . ';';

    preg_match('/getElementById\(\'challenge-form\'\);(.*?);t.length;/', $_, $___);
    $____ = $___[1] . (strlen($__) ?: strlen(parse_url($_, PHP_URL_HOST))) . ';';

    $_ = new V8Js();
    $_____ = $_->executeString('var ' . $__ . str_replace('a.value', '$', $____));

    preg_match('/jschl_vc" value="(.*?)"/', $_, $___);
    $___['jschl_vc'] = $___[1];

    preg_match('/pass" value="(.*?)"/', $_, $___);
    $___['pass'] = htmlentities($___[1]);

    $___['jschl_answer'] = $_____;
    return $___;
}
