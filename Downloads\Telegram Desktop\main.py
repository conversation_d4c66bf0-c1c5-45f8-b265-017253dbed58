import requests
import socket
from print_color import print
from googlesearch import search
#Coded By @DaddyReturns
print("", tag='Enter Your Dork', tag_color='blue', color='blue')
query = input()

user = ""
passw = ""
ip = ""
port = ""

def find_gateway(html_content):
    gateway_keywords = {
        'Stripe': ['stripe', 'checkout.stripe.com', 'js.stripe.com', 'stripe.com', 'stripe-elements', 'stripe-js-v3',
                   'stripe-button', 'stripe-payment', 'stripe-checkout', 'stripe-form', 'stripe-sdk', 'stripe-pay',
                   'stripe-card', 'stripe-subscription', 'stripe-checkout-button', 'stripe-elements', 'stripe-token'],
        'PayPal': ['paypal', 'paypal.com', 'paypal-button', 'paypal-checkout', 'paypal-payment', 'paypal-form'],
        'Shopify': ['shopify', 'shopify.com', 'shopify-checkout', 'shopify-payment', 'shopify-button']
        # Add more gateway keywords as needed
    }
    for gateway, keywords in gateway_keywords.items():
        for keyword in keywords:
            if keyword.lower() in html_content.lower():
                return gateway
    return None

def print_info(url,  gateway, captcha, cloudflare, tag='success', tag_color='green', color='yellow'):
    print(f"URL: {url} - Gateway: {gateway}, CAPTCHA: {captcha}, Cloudflare: {cloudflare}", tag=tag, tag_color=tag_color, color=color)
    if gateway:
        file_name = f"{gateway}.txt"
        with open(file_name, 'a') as file:
            file.write(f"Url:{url} Gateway:{gateway}, CAPTCHA: {captcha}, Cloudflare: {cloudflare}\n")

def fetch_urls(query):
    urls = list(search(query)) 
    for url in urls:
        try:
            response = requests.get(url)
            response.raise_for_status()
            gateway = find_gateway(response.text)
            captcha = "CAPTCHA found" if "captcha" in response.text.lower() else "No CAPTCHA found"
            cloudflare = "Cloudflare detected in response headers" if 'Server' in response.headers and 'cloudflare' in response.headers['Server'].lower() else "No Cloudflare detected"
            print_info(url, gateway, captcha, cloudflare)
        except requests.HTTPError as err:
            if err.response.status_code == 429: 
                print(f"Error: {err}")
        except requests.ConnectionError as conn_err:
            print(f"Connection Error: {conn_err}")

if __name__ == "__main__":
    fetch_urls(query)
