🔴 ****************|08|2025|961 - Card Not Activated	</div>
</div>
</div>	<form id="add_payment_method" method="post">
		<div id="payment" class="woocommerce-Payment">
			<ul class="woocommerce-PaymentMethods payment_methods methods">
									<li class="woocommerce-PaymentMethod woocommerce-PaymentMethod--braintree_cc payment_method_braintree_cc">
						<input id="payment_method_braintree_cc" type="radio" class="input-radio" name="payment_method" value="braintree_cc"  checked='checked' />
						<label for="payment_method_braintree_cc">Pay with VISA, Discover or Mastercard <span class="wc-braintree-card-icons-container">
	<img decoding="async" alt="Discover" class="wc-braintree-card-icon closed discover" src="https://www.lumenflex.com/wp-content/plugins/woo-payment-gateway/assets/img/payment-methods/closed/discover.svg" />	<img decoding="async" alt="MasterCard" class="wc-braintree-card-icon closed master_card" src="https://www.lumenflex.com/wp-content/plugins/woo-payment-gateway/assets/img/payment-methods/closed/master_card.svg" />	<img decoding="async" alt="Visa" class="wc-braintree-card-icon closed visa" src="https://www.lumenflex.com/wp-content/plugins/woo-payment-gateway/assets/img/payment-methods/closed/visa.svg" /></span></label>
						<div class="woocommerce-PaymentBox woocommerce-PaymentBox--braintree_cc payment_box payment_method_braintree_cc" style="display: none;"><input type="hidden" class="woocommerce_braintree_cc_data" data-gateway="{&quot;currency&quot;:&quot;USD&quot;,&quot;price_label&quot;:&quot;Total&quot;,&quot;merchant_account&quot;:&quot;LumenflexInc_instant&quot;,&quot;shipping_options&quot;:[],&quot;total&quot;:0,&quot;order_total&quot;:0,&quot;needs_shipping&quot;:false,&quot;items&quot;:[],&quot;_3ds&quot;:{&quot;enabled&quot;:false,&quot;active&quot;:false}}"/><div class="wc-braintree-payment-gateway ">
	<input type="hidden" id="braintree_cc_nonce_key" name="braintree_cc_nonce_key" class="wc-braintree-payment-nonce"/><input type="hidden" id="braintree_cc_device_data" name="braintree_cc_device_data" class="wc-braintree-device-data"/>		    <div class="wc-braintree-new-payment-method-container" style="">
		<input type="hidden" id="braintree_cc_3ds_nonce_key" name="braintree_cc_3ds_nonce_key" class="wc-braintree-3ds-vaulted-nonce"/><input type="hidden" id="braintree_cc_config_data" name="braintree_cc_config_data" class=""/><div class="wc-braintree-cc-container closed-icons">
			<div class="wc-braintree-payment-loader" style="display: none">
			<div class="load">
	<div class="gear one">
		<svg id="blue" viewbox="0 0 100 100" fill="#94DDFF">
      <path
				d="M97.6,55.7V44.3l-13.6-2.9c-0.8-3.3-2.1-6.4-3.9-9.3l7.6-11.7l-8-8L67.9,20c-2.9-1.7-6-3.1-9.3-3.9L55.7,2.4H44.3l-2.9,13.6      c-3.3,0.8-6.4,2.1-9.3,3.9l-11.7-7.6l-8,8L20,32.1c-1.7,2.9-3.1,6-3.9,9.3L2.4,44.3v11.4l13.6,2.9c0.8,3.3,2.1,6.4,3.9,9.3      l-7.6,11.7l8,8L32.1,80c2.9,1.7,6,3.1,9.3,3.9l2.9,13.6h11.4l2.9-13.6c3.3-0.8,6.4-2.1,9.3-3.9l11.7,7.6l8-8L80,67.9      c1.7-2.9,3.1-6,3.9-9.3L97.6,55.7z M50,65.6c-8.7,0-15.6-7-15.6-15.6s7-15.6,15.6-15.6s15.6,7,15.6,15.6S58.7,65.6,50,65.6z"></path>
    </svg>
	</div>
	<div class="gear two">
		<svg id="pink" viewbox="0 0 100 100" fill="#FB8BB9">
      <path
				d="M97.6,55.7V44.3l-13.6-2.9c-0.8-3.3-2.1-6.4-3.9-9.3l7.6-11.7l-8-8L67.9,20c-2.9-1.7-6-3.1-9.3-3.9L55.7,2.4H44.3l-2.9,13.6      c-3.3,0.8-6.4,2.1-9.3,3.9l-11.7-7.6l-8,8L20,32.1c-1.7,2.9-3.1,6-3.9,9.3L2.4,44.3v11.4l13.6,2.9c0.8,3.3,2.1,6.4,3.9,9.3      l-7.6,11.7l8,8L32.1,80c2.9,1.7,6,3.1,9.3,3.9l2.9,13.6h11.4l2.9-13.6c3.3-0.8,6.4-2.1,9.3-3.9l11.7,7.6l8-8L80,67.9      c1.7-2.9,3.1-6,3.9-9.3L97.6,55.7z M50,65.6c-8.7,0-15.6-7-15.6-15.6s7-15.6,15.6-15.6s15.6,7,15.6,15.6S58.7,65.6,50,65.6z"></path>
    </svg>
	</div>
	<div class="gear three">
		<svg id="yellow" viewbox="0 0 100 100" fill="#FFCD5C">
      <path
				d="M97.6,55.7V44.3l-13.6-2.9c-0.8-3.3-2.1-6.4-3.9-9.3l7.6-11.7l-8-8L67.9,20c-2.9-1.7-6-3.1-9.3-3.9L55.7,2.4H44.3l-2.9,13.6      c-3.3,0.8-6.4,2.1-9.3,3.9l-11.7-7.6l-8,8L20,32.1c-1.7,2.9-3.1,6-3.9,9.3L2.4,44.3v11.4l13.6,2.9c0.8,3.3,2.1,6.4,3.9,9.3      l-7.6,11.7l8,8L32.1,80c2.9,1.7,6,3.1,9.3,3.9l2.9,13.6h11.4l2.9-13.6c3.3-0.8,6.4-2.1,9.3-3.9l11.7,7.6l8-8L80,67.9      c1.7-2.9,3.1-6,3.9-9.3L97.6,55.7z M50,65.6c-8.7,0-15.6-7-15.6-15.6s7-15.6,15.6-15.6s15.6,7,15.6,15.6S58.7,65.6,50,65.6z"></path>
    </svg>
	</div>
	<div class="lil-circle"></div>
	<svg class="blur-circle">
    <filter id="blur">
      <fegaussianblur in="SourceGraphic" stddeviation="13"></fegaussianblur>
    </filter>
    <circle cx="70" cy="70" r="66" fill="transparent" stroke="white"
			stroke-width="40" filter="url(#blur)"></circle>
  </svg>
</div>
<div class="text">Processing...</div>

<style>
@import url(https://fonts.googleapis.com/css?family=Open+Sans);
body.wc-braintree-body .wc-braintree-new-payment-method-container .wc-braintree-payment-loader{
	background: rgba(255,255,255,.85);
    display: flex;
    display: -webkit-flex;
    justify-content: center;
    align-items: center;
    flex-direction: column
}
body.wc-braintree-body .wc-braintree-new-payment-method-container .load {
    position: relative;
    width: 100px;
    height: 80px;
}

body.wc-braintree-body .wc-braintree-new-payment-method-container .gear {
  position: absolute;
  z-index: -10;
  width: 40px;
  height: 40px;
  -webkit-animation: spin 5s infinite;
          animation: spin 5s infinite;
}

body.wc-braintree-body .wc-braintree-new-payment-method-container .two {
  left: 40px;
  width: 80px;
  height: 80px;
  -webkit-animation: spin-reverse 5s infinite;
          animation: spin-reverse 5s infinite;
}

body.wc-braintree-body .wc-braintree-new-payment-method-container .three {
  top: 45px;
  left: -10px;
  width: 60px;
  height: 60px;
}

@-webkit-keyframes spin {
  50% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}

@keyframes spin {
  50% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
@-webkit-keyframes spin-reverse {
  50% {
    -webkit-transform: rotate(-360deg);
            transform: rotate(-360deg);
  }
}
@keyframes spin-reverse {
  50% {
    -webkit-transform: rotate(-360deg);
            transform: rotate(-360deg);
  }
}
body.wc-braintree-body .wc-braintree-new-payment-method-container .lil-circle {
  position: absolute;
  border-radius: 50%;
  box-shadow: inset 0 0 10px 2px gray, 0 0 50px white;
  width: 100px;
  height: 100px;
  opacity: .65;
}

body.wc-braintree-body .wc-braintree-new-payment-method-container .blur-circle {
  position: absolute;
  top: -19px;
  left: -19px;
}

body.wc-braintree-body .wc-braintree-new-payment-method-container .text {
    color: #8e8e8e;
    font-size: 18px;
    font-family: 'Open Sans', sans-serif;
    margin-top: 40px;
}
</style>		</div>
	
<div class="wc-braintree-card-form">
	<!-- <h3 class="wc-braintree-card-title">
		Payment Details	</h3>-->
	<div class="wc-braintree-form-wrapper">
		<div class="wc-braintree-field-container card-field-container">
			<label>Card Number</label>
			<div id="wc-braintree-card-number" class="hosted-field">
				<span class="wc-braintree-card-type"></span>
			</div>
		</div>
	</div>
	<div class="wc-braintree-form-wrapper">
		<div class="wc-braintree-field-container field-exp-month">
			<label>Exp Date</label>
			<div id="wc-braintree-expiration-date" class="hosted-field"></div>
		</div>
		<div class="wc-braintree-field-container field-cvv cvv-container">
			<label>CVV</label>
			<div id="wc-braintree-cvv" class="hosted-field"></div>
		</div>
	</div>
			<div class="wc-braintree-form-wrapper">
						<div class="wc-braintree-field-container field-postal postalCode-container">
				<label>Postal Code</label>
				<div id="wc-braintree-postal-code" class="hosted-field"></div>
			</div>
								</div>
		</div></div>    </div>
	</div></div>					</li>
								</ul>

			
			<div class="form-row">
				<input type="hidden" id="woocommerce-add-payment-method-nonce" name="woocommerce-add-payment-method-nonce" value="575021767a" /><input type="hidden" name="_wp_http_referer" value="/my-account/add-payment-method/" />				<button type="submit" class="woocommerce-Button woocommerce-Button--alt button alt" id="place_order" value="Add payment method">Add payment method</button>
				<input type="hidden" name="woocommerce_add_payment_method" id="woocommerce_add_payment_method" value="1" />
			</div>
		</div>
	</form>
</div>
</div>
</div></div></div></div></div>
							</div>
																																							</div>
	</section>
						
					</div>  <!-- fusion-row -->
				</main>  <!-- #main -->
				
				
								
					
		<div class="fusion-footer">
					
	<footer class="fusion-footer-widget-area fusion-widget-area">
		<div class="fusion-row">
			<div class="fusion-columns fusion-columns-4 fusion-widget-area">
				
																									<div class="fusion-column col-lg-3 col-md-3 col-sm-3">
							<section id="text-2" class="fusion-footer-widget-column widget widget_text" style="border-style: solid;border-color:transparent;border-width:0px;">			<div class="textwidget"><img src="https://www.lumenflex.com/wp-content/uploads/2016/10/Logo-Lumenflex-1X.png" />

<span style="color:#bfbfbf;"><br/>

Lumenflex Inc.<br/>

<EMAIL><br/>

+1 512-773-7067<br/>

8:30 AM - 6:00 PM EST Mon-Fri</span></div>
		<div style="clear:both;"></div></section>																					</div>
																										<div class="fusion-column col-lg-3 col-md-3 col-sm-3">
							<section id="nav_menu-4" class="fusion-footer-widget-column widget widget_nav_menu"><h4 class="widget-title">Company</h4><div class="menu-company-container"><ul id="menu-company" class="menu"><li id="menu-item-153" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-153"><a href="https://www.lumenflex.com/about/">About</a></li>
<li id="menu-item-155" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-155"><a href="https://www.lumenflex.com/engineering/">Engineering</a></li>
</ul></div><div style="clear:both;"></div></section><section id="nav_menu-6" class="fusion-footer-widget-column widget widget_nav_menu"><h4 class="widget-title">Products</h4><div class="menu-products-container"><ul id="menu-products" class="menu"><li id="menu-item-1704" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1704"><a href="https://www.lumenflex.com/motor-lightrider/">Motor</a></li>
<li id="menu-item-1703" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1703"><a href="https://www.lumenflex.com/bike-brightcycle/">Bike</a></li>
<li id="menu-item-1702" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1702"><a href="https://www.lumenflex.com/explore-brimbeam/">Explore</a></li>
<li id="menu-item-1701" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1701"><a href="https://www.lumenflex.com/build/">Build</a></li>
<li id="menu-item-1700" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1700"><a href="https://www.lumenflex.com/walk-streetseen/">Walk</a></li>
<li id="menu-item-1699" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1699"><a href="https://www.lumenflex.com/accessories/">Accessories</a></li>
</ul></div><div style="clear:both;"></div></section>																					</div>
																										<div class="fusion-column col-lg-3 col-md-3 col-sm-3">
							<section id="nav_menu-3" class="fusion-footer-widget-column widget widget_nav_menu"><h4 class="widget-title">Customer Support</h4><div class="menu-support-container"><ul id="menu-support" class="menu"><li id="menu-item-2054" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2054"><a href="https://www.lumenflex.com/contact/">Contact</a></li>
<li id="menu-item-50" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-50"><a href="https://www.lumenflex.com/shipping/">Shipping</a></li>
<li id="menu-item-49" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-49"><a href="https://www.lumenflex.com/returns/">Returns</a></li>
<li id="menu-item-334" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-334"><a href="https://www.lumenflex.com/exchanges/">Exchanges</a></li>
<li id="menu-item-333" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-333"><a href="https://www.lumenflex.com/conditions-of-sale/">Conditions of Sale</a></li>
<li id="menu-item-1089" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1089"><a href="https://www.lumenflex.com/warranty-and-liability/">Warranty and Liability</a></li>
<li id="menu-item-1698" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1698"><a href="https://www.lumenflex.com/volume-orders/">Volume Orders</a></li>
</ul></div><div style="clear:both;"></div></section><section id="nav_menu-5" class="fusion-footer-widget-column widget widget_nav_menu"><h4 class="widget-title">Service and Use</h4><div class="menu-service-and-use-container"><ul id="menu-service-and-use" class="menu"><li id="menu-item-336" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-336"><a href="https://www.lumenflex.com/terms-and-conditions/">Terms and Conditions</a></li>
<li id="menu-item-335" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-335"><a href="https://www.lumenflex.com/privacy-policy/">Privacy Policy</a></li>
</ul></div><div style="clear:both;"></div></section>																					</div>
																										<div class="fusion-column fusion-column-last col-lg-3 col-md-3 col-sm-3">
							<section id="text-5" class="fusion-footer-widget-column widget widget_text">			<div class="textwidget"><div ><a class="fusion-button button-flat fusion-button-default-size button-default fusion-button-default button-1 fusion-button-span-yes fusion-button-default-type np-button" target="_self" href="#ninja-popup-1609" id="popBtn"><span class="fusion-button-text"><span style="font-size: 22px;">SUBSCRIBE</span></span></a></div>

</div>
		<div style="clear:both;"></div></section><section id="fbw_id-2" class="fusion-footer-widget-column widget widget_fbw_id"><div class="fb_loader" style="text-align: center !important;"><img src="https://www.lumenflex.com/wp-content/plugins/facebook-pagelike-widget/loader.gif" alt="Facebook Pagelike Widget" /></div><div id="fb-root"></div>
        <div class="fb-page" data-href="http://facebook.com/lumenflex" data-width="250" data-height="350" data-small-header="false" data-adapt-container-width="false" data-hide-cover="false" data-show-facepile="true" hide_cta="false" data-tabs="timeline" data-lazy="false"></div><div style="clear:both;"></div></section>        <!-- A WordPress plugin developed by Milap Patel -->
    <section id="social_links-widget-2" class="fusion-footer-widget-column widget social_links">
		<div class="fusion-social-networks boxed-icons">

			<div class="fusion-social-networks-wrapper">
																												
						
																																			<a class="fusion-social-network-icon fusion-tooltip fusion-facebook awb-icon-facebook" href="https://www.facebook.com/lumenflex/"   title="Facebook" aria-label="Facebook" rel="noopener noreferrer" target="_self" style="border-radius:30px;padding:14px;font-size:21px;color:#ffffff;background-color:#3b5998;border-color:#3b5998;"></a>
											
										
																				
						
																																			<a class="fusion-social-network-icon fusion-tooltip fusion-twitter awb-icon-twitter" href="https://twitter.com/Lumenflex"   title="Twitter" aria-label="Twitter" rel="noopener noreferrer" target="_self" style="border-radius:30px;padding:14px;font-size:21px;color:#ffffff;background-color:#000000;border-color:#000000;"></a>
											
										
																				
						
																																			<a class="fusion-social-network-icon fusion-tooltip fusion-linkedin awb-icon-linkedin" href="https://www.linkedin.com/company/lumenflex/"   title="LinkedIn" aria-label="LinkedIn" rel="noopener noreferrer" target="_self" style="border-radius:30px;padding:14px;font-size:21px;color:#ffffff;background-color:#0077b5;border-color:#0077b5;"></a>
											
										
																				
						
																																			<a class="fusion-social-network-icon fusion-tooltip fusion-youtube awb-icon-youtube" href="https://www.youtube.com/channel/UCjvcC2U0UQgABcVUIIjfPSQ"   title="YouTube" aria-label="YouTube" rel="noopener noreferrer" target="_self" style="border-radius:30px;padding:14px;font-size:21px;color:#ffffff;background-color:#cd201f;border-color:#cd201f;"></a>
											
										
				
			</div>
		</div>

		<div style="clear:both;"></div></section>																					</div>
																											
				<div class="fusion-clearfix"></div>
			</div> <!-- fusion-columns -->
		</div> <!-- fusion-row -->
	</footer> <!-- fusion-footer-widget-area -->

	
	<footer id="footer" class="fusion-footer-copyright-area">
		<div class="fusion-row">
			<div class="fusion-copyright-content">

				<div class="fusion-copyright-notice">
		<div>
		<div class="base-footer">

<div>

Lumenflex © 2022

</div>

<div>

<img src="https://www.lumenflex.com/wp-content/uploads/2016/11/encrypt-logo-brighter.png" align="right">

</div>

</div>	</div>
</div>

			</div> <!-- fusion-fusion-copyright-content -->
		</div> <!-- fusion-row -->
	</footer> <!-- #footer -->
		</div> <!-- fusion-footer -->

		
																</div> <!-- wrapper -->
		</div> <!-- #boxed-wrapper -->
				<a class="fusion-one-page-text-link fusion-page-load-link" tabindex="-1" href="#" aria-hidden="true">Page load link</a>

		<div class="avada-footer-scripts">
			<script type="text/javascript">var fusionNavIsCollapsed=function(e){var t,n;window.innerWidth<=e.getAttribute("data-breakpoint")?(e.classList.add("collapse-enabled"),e.classList.remove("awb-menu_desktop"),e.classList.contains("expanded")||window.dispatchEvent(new CustomEvent("fusion-mobile-menu-collapsed",{detail:{nav:e}})),(n=e.querySelectorAll(".menu-item-has-children.expanded")).length&&n.forEach(function(e){e.querySelector(".awb-menu__open-nav-submenu_mobile").setAttribute("aria-expanded","false")})):(null!==e.querySelector(".menu-item-has-children.expanded .awb-menu__open-nav-submenu_click")&&e.querySelector(".menu-item-has-children.expanded .awb-menu__open-nav-submenu_click").click(),e.classList.remove("collapse-enabled"),e.classList.add("awb-menu_desktop"),null!==e.querySelector(".awb-menu__main-ul")&&e.querySelector(".awb-menu__main-ul").removeAttribute("style")),e.classList.add("no-wrapper-transition"),clearTimeout(t),t=setTimeout(()=>{e.classList.remove("no-wrapper-transition")},400),e.classList.remove("loading")},fusionRunNavIsCollapsed=function(){var e,t=document.querySelectorAll(".awb-menu");for(e=0;e<t.length;e++)fusionNavIsCollapsed(t[e])};function avadaGetScrollBarWidth(){var e,t,n,l=document.createElement("p");return l.style.width="100%",l.style.height="200px",(e=document.createElement("div")).style.position="absolute",e.style.top="0px",e.style.left="0px",e.style.visibility="hidden",e.style.width="200px",e.style.height="150px",e.style.overflow="hidden",e.appendChild(l),document.body.appendChild(e),t=l.offsetWidth,e.style.overflow="scroll",t==(n=l.offsetWidth)&&(n=e.clientWidth),document.body.removeChild(e),jQuery("html").hasClass("awb-scroll")&&10<t-n?10:t-n}fusionRunNavIsCollapsed(),window.addEventListener("fusion-resize-horizontal",fusionRunNavIsCollapsed);</script>
		<script>
			window.RS_MODULES = window.RS_MODULES || {};
			window.RS_MODULES.modules = window.RS_MODULES.modules || {};
			window.RS_MODULES.waiting = window.RS_MODULES.waiting || [];
			window.RS_MODULES.defered = false;
			window.RS_MODULES.moduleWaiting = window.RS_MODULES.moduleWaiting || {};
			window.RS_MODULES.type = 'compiled';
		</script>
		<script>var ajaxRevslider;function rsCustomAjaxContentLoadingFunction(){ajaxRevslider=function(obj){var content='',data={action:'revslider_ajax_call_front',client_action:'get_slider_html',token:'06db862a03',type:obj.type,id:obj.id,aspectratio:obj.aspectratio};jQuery.ajax({type:'post',url:'https://www.lumenflex.com/wp-admin/admin-ajax.php',dataType:'json',data:data,async:false,success:function(ret,textStatus,XMLHttpRequest){if(ret.success==true)content=ret.data;},error:function(e){console.log(e);}});return content;};var ajaxRemoveRevslider=function(obj){return jQuery(obj.selector+' .rev_slider').revkill();};if(jQuery.fn.tpessential!==undefined)if(typeof(jQuery.fn.tpessential.defaults)!=='undefined')jQuery.fn.tpessential.defaults.ajaxTypes.push({type:'revslider',func:ajaxRevslider,killfunc:ajaxRemoveRevslider,openAnimationSpeed:0.3});}var rsCustomAjaxContent_Once=false;if(document.readyState==="loading")document.addEventListener('readystatechange',function(){if((document.readyState==="interactive"||document.readyState==="complete")&&!rsCustomAjaxContent_Once){rsCustomAjaxContent_Once=true;rsCustomAjaxContentLoadingFunction();}});else{rsCustomAjaxContent_Once=true;rsCustomAjaxContentLoadingFunction();}</script>	<script type='text/javascript'>
		(function () {
			var c = document.body.className;
			c = c.replace(/woocommerce-no-js/, 'woocommerce-js');
			document.body.className = c;
		})();
	</script>
	    <script>

        var snp_f = [];

        var snp_hostname = new RegExp(location.host);

        var snp_http = new RegExp("^(http|https)://", "i");

        var snp_cookie_prefix = '';

        var snp_separate_cookies = true;

        var snp_ajax_url = 'https://www.lumenflex.com/wp-admin/admin-ajax.php';

        var snp_domain_url = 'https://www.lumenflex.com';

		var snp_ajax_nonce = '431a485921';

		var snp_ajax_ping_time = 1000;

        var snp_ignore_cookies = false;

        var snp_enable_analytics_events = true;

        var snp_is_mobile = false;

        var snp_enable_mobile = false;

        var snp_use_in_all = false;

        var snp_excluded_urls = [];

        var snp_close_on_esc_key = false;

        snp_excluded_urls.push('');    </script>

    <div class="snp-root">

        <input type="hidden" id="snp_popup" value="" />

        <input type="hidden" id="snp_popup_id" value="" />

        <input type="hidden" id="snp_popup_theme" value="" />

        <input type="hidden" id="snp_exithref" value="" />

        <input type="hidden" id="snp_exittarget" value="" />

                    <input type="hidden" id="snp_woocommerce_cart_contents" value="0" />

                	<div id="snppopup-content-1609" class="snp-pop-1609 snppopup"><input type="hidden" class="snp_open" value="inactivity" /><input type="hidden" class="snp_close" value="close_manual" /><input type="hidden" class="snp_show_on_exit" value="2" /><input type="hidden" class="snp_exit_js_alert_text" value="" /><input type="hidden" class="snp_exit_scroll_down" value="" /><input type="hidden" class="snp_exit_scroll_up" value="" /><input type="hidden" class="snp_open_inactivity" value="10" /><input type="hidden" class="snp_open_scroll" value="80" /><input type="hidden" class="snp_optin_redirect_url" value="" /><input type="hidden" class="snp_optin_form_submit" value="single" /><input type="hidden" class="snp_show_cb_button" value="yes" /><input type="hidden" class="snp_popup_id" value="1609" /><input type="hidden" class="snp_popup_theme" value="theme_html" /><input type="hidden" class="snp_overlay" value="default" /><input type="hidden" class="snp_cookie_conversion" value="-2" /><input type="hidden" class="snp_cookie_close" value="10" /><div class="snp-fb snp-theme-html">

	<div class="snp-content">

		<div class="snp-content-inner">

			<p><span style="color: #fcb621; font-family: lato; font-size: 28pt;">Brighten up your inbox</span><br />
&nbsp;</p>
<p style="margin-down: 28px;"><span style="color: white; font-family: lato; font-size: 16pt;">Subscribe to receive updates, insider news, and exclusive Lumenflex deals.</span></p>
<p>&nbsp;</p>
<style type="text/css">
	#mc_embed_signup{background:transparent; clear:left; font:17px lato; font-color:white; }<br /></style>
<div id="mc_embed_signup">
<form id="mc-embedded-subscribe-form" class="validate mcform" action="//lumenflex.us3.list-manage.com/subscribe/post?u=66d2106a0c035fe6a2d10c1d3&amp;id=c776a61c59" method="post" name="mc-embedded-subscribe-form" target="_blank">
<div id="mc_embed_signup_scroll">
<div class="mc-field-group"><label for="mce-EMAIL">Email Address </label><input id="mce-EMAIL" class="required email" name="EMAIL" required="" type="email" value="" /></div>
<p>&nbsp;</p>
<div class="mc-field-group"><label for="mce-FNAME">First Name </label><input id="mce-FNAME" class="required" name="FNAME" required="" type="text" value="" /></div>
<p>&nbsp;</p>
<div class="mc-field-group"><label for="mce-LNAME">Last Name </label><input id="mce-LNAME" class="required" name="LNAME" required="" type="text" value="" /></div>
<p>&nbsp;</p>
<div class="mc-field-group input-group">
&nbsp;</p>
<p><strong>Which products fit <em>your</em> lifestyle? </strong></p>
<ul>
<li><input id="mce-group[18921]-18921-0" name="group[18921][1]" type="checkbox" value="1" /><label for="mce-group[18921]-18921-0">LightRider | Motorcycle Helmet Light</label></li>
<li><input id="mce-group[18921]-18921-1" name="group[18921][2]" type="checkbox" value="2" /><label for="mce-group[18921]-18921-1">BrightCycle | Bike Helmet Light</label></li>
<li><input id="mce-group[18921]-18921-2" name="group[18921][4]" type="checkbox" value="4" /><label for="mce-group[18921]-18921-2">BrimBeam | Cap Light</label></li>
<li><input id="mce-group[18921]-18921-3" name="group[18921][8]" type="checkbox" value="8" /><label for="mce-group[18921]-18921-3">OrbitLight &#038; OrbitSunXT | Hardhat Lights</label></li>
<li><input id="mce-group[18921]-18921-4" name="group[18921][16]" type="checkbox" value="16" /><label for="mce-group[18921]-18921-4">StreetSeen | Cross-Body LED Band</label></li>
</ul>
</div>
<div id="mce-responses" class="clear"></div>
<p><!-- real people should not fill this in and expect good things - do not remove this or risk form bot signups--></p>
<div style="position: absolute; left: -5000px;"><input tabindex="-1" name="b_66d2106a0c035fe6a2d10c1d3_c776a61c59" type="text" value="" /></div>
<p>&nbsp;</p>
<div class="clear"><input id="mc-embedded-subscribe" class="button" name="subscribe" type="submit" value="SUBMIT" /></div>
</div>
</form>
</div>
        </div>

	</div>

	</div>

<style>.snp-pop-1609 .snp-theme-html { max-width: 900px;}.snp-pop-1609 .snp-theme-html { height: 500px;}.snp-pop-1609 .snp-theme-html .snp-no-thx, .snp-pop-1609 .snp-theme-html .snp-powered , .snp-pop-1609 .snp-theme-html .snp-privacy { color: #979797;}</style><style>/* set background of popup to be this image */

.snp-theme-html {

background-image: url(https://www.lumenflex.com/wp-content/uploads/2016/10/flash-original-e1477896776881.jpg);

}



.snp-theme-html label, .snp-theme-html .input-group {

color: white;

}



.snp-theme-html li input {

    margin-right: 0.56rem !important;

}



.snp-theme-html .button {

background-color: #00a3a0 !important;

}</style><script>jQuery('.mcform').on('submit', function(evt) {

	var checkboxFields = jQuery(this).find('[name^="group"]');



	if( checkboxFields.length ) {

		

		// check if at least one field is required

		for(var i=0; i<checkboxFields.length; i++) {

			if( checkboxFields[i].checked ) {

				return true;

			}

		}

		

		alert("Let us know which products you're interested in! Please check at least 1.");

		evt.preventDefault();

		return false;

	}



	return true;

});</script></div>            </div>

    <link rel='stylesheet' id='wc-braintree-blocks-style-css' href='https://www.lumenflex.com/wp-content/plugins/woo-payment-gateway/packages/blocks/build/style.css?ver=3.2.59' type='text/css' media='all' />
<link rel='stylesheet' id='wc-braintree-styles-css' href='https://www.lumenflex.com/wp-content/plugins/woo-payment-gateway/assets/css/braintree.min.css?ver=3.2.59' type='text/css' media='all' />
<link rel='stylesheet' id='wc-blocks-style-css' href='https://www.lumenflex.com/wp-content/plugins/woocommerce/assets/client/blocks/wc-blocks.css?ver=wc-9.5.1' type='text/css' media='all' />
<link rel='stylesheet' id='wc-braintree-card_shape_form-css' href='https://www.lumenflex.com/wp-content/plugins/woo-payment-gateway/assets/css/custom-forms/card-shape-form.css?ver=3.2.59' type='text/css' media='all' />
<link rel='stylesheet' id='plugin_name-admin-ui-css-css' href='https://ajax.googleapis.com/ajax/libs/jqueryui/1.13.3/themes/smoothness/jquery-ui.css?ver=4.7.8' type='text/css' media='' />
<link rel='stylesheet' id='tooltipster-css-css' href='https://www.lumenflex.com/wp-content/plugins/arscode-ninja-popups/tooltipster/tooltipster.bundle.min.css?ver=********************************' type='text/css' media='all' />
<link rel='stylesheet' id='tooltipster-css-theme-css' href='https://www.lumenflex.com/wp-content/plugins/arscode-ninja-popups/tooltipster/plugins/tooltipster/sideTip/themes/tooltipster-sideTip-light.min.css?ver=********************************' type='text/css' media='all' />
<link rel='stylesheet' id='material-design-css-css' href='https://www.lumenflex.com/wp-content/plugins/arscode-ninja-popups/themes/jquery.material.form.css?ver=********************************' type='text/css' media='all' />
<link rel='stylesheet' id='jquery-intl-phone-input-css-css' href='https://www.lumenflex.com/wp-content/plugins/arscode-ninja-popups/assets/vendor/intl-tel-input/css/intlTelInput.min.css?ver=********************************' type='text/css' media='all' />
<link rel='stylesheet' id='snp_styles_reset-css' href='https://www.lumenflex.com/wp-content/plugins/arscode-ninja-popups/themes/reset.min.css?ver=********************************' type='text/css' media='all' />
<link rel='stylesheet' id='fancybox2-css' href='https://www.lumenflex.com/wp-content/plugins/arscode-ninja-popups/fancybox2/jquery.fancybox.min.css?ver=********************************' type='text/css' media='all' />
<link rel='stylesheet' id='snp_styles_theme_html-css' href='https://www.lumenflex.com/wp-content/plugins/arscode-ninja-popups/themes/theme_html/style.css?ver=********************************' type='text/css' media='all' />
<style id='global-styles-inline-css' type='text/css'>
:root{--wp--preset--aspect-ratio--square: 1;--wp--preset--aspect-ratio--4-3: 4/3;--wp--preset--aspect-ratio--3-4: 3/4;--wp--preset--aspect-ratio--3-2: 3/2;--wp--preset--aspect-ratio--2-3: 2/3;--wp--preset--aspect-ratio--16-9: 16/9;--wp--preset--aspect-ratio--9-16: 9/16;--wp--preset--color--black: #000000;--wp--preset--color--cyan-bluish-gray: #abb8c3;--wp--preset--color--white: #ffffff;--wp--preset--color--pale-pink: #f78da7;--wp--preset--color--vivid-red: #cf2e2e;--wp--preset--color--luminous-vivid-orange: #ff6900;--wp--preset--color--luminous-vivid-amber: #fcb900;--wp--preset--color--light-green-cyan: #7bdcb5;--wp--preset--color--vivid-green-cyan: #00d084;--wp--preset--color--pale-cyan-blue: #8ed1fc;--wp--preset--color--vivid-cyan-blue: #0693e3;--wp--preset--color--vivid-purple: #9b51e0;--wp--preset--color--awb-color-1: rgba(255,255,255,1);--wp--preset--color--awb-color-2: rgba(246,246,246,1);--wp--preset--color--awb-color-3: rgba(242,242,242,1);--wp--preset--color--awb-color-4: rgba(224,222,222,1);--wp--preset--color--awb-color-5: rgba(160,206,78,1);--wp--preset--color--awb-color-6: rgba(0,163,160,1);--wp--preset--color--awb-color-7: rgba(51,51,51,1);--wp--preset--color--awb-color-8: rgba(23,23,21,1);--wp--preset--color--awb-color-custom-10: rgba(116,116,116,1);--wp--preset--color--awb-color-custom-11: rgba(251,251,254,1);--wp--preset--color--awb-color-custom-12: rgba(235,234,234,1);--wp--preset--color--awb-color-custom-13: rgba(0,0,0,1);--wp--preset--color--awb-color-custom-14: rgba(191,191,191,1);--wp--preset--color--awb-color-custom-15: rgba(235,234,234,0.8);--wp--preset--color--awb-color-custom-16: rgba(252,183,33,1);--wp--preset--color--awb-color-custom-17: rgba(249,249,249,1);--wp--preset--color--awb-color-custom-18: rgba(248,248,248,1);--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg,rgba(6,147,227,1) 0%,rgb(155,81,224) 100%);--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg,rgb(122,220,180) 0%,rgb(0,208,130) 100%);--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg,rgba(252,185,0,1) 0%,rgba(255,105,0,1) 100%);--wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg,rgba(255,105,0,1) 0%,rgb(207,46,46) 100%);--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg,rgb(238,238,238) 0%,rgb(169,184,195) 100%);--wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg,rgb(74,234,220) 0%,rgb(151,120,209) 20%,rgb(207,42,186) 40%,rgb(238,44,130) 60%,rgb(251,105,98) 80%,rgb(254,248,76) 100%);--wp--preset--gradient--blush-light-purple: linear-gradient(135deg,rgb(255,206,236) 0%,rgb(152,150,240) 100%);--wp--preset--gradient--blush-bordeaux: linear-gradient(135deg,rgb(254,205,165) 0%,rgb(254,45,45) 50%,rgb(107,0,62) 100%);--wp--preset--gradient--luminous-dusk: linear-gradient(135deg,rgb(255,203,112) 0%,rgb(199,81,192) 50%,rgb(65,88,208) 100%);--wp--preset--gradient--pale-ocean: linear-gradient(135deg,rgb(255,245,203) 0%,rgb(182,227,212) 50%,rgb(51,167,181) 100%);--wp--preset--gradient--electric-grass: linear-gradient(135deg,rgb(202,248,128) 0%,rgb(113,206,126) 100%);--wp--preset--gradient--midnight: linear-gradient(135deg,rgb(2,3,129) 0%,rgb(40,116,252) 100%);--wp--preset--font-size--small: 15px;--wp--preset--font-size--medium: 20px;--wp--preset--font-size--large: 30px;--wp--preset--font-size--x-large: 42px;--wp--preset--font-size--normal: 20px;--wp--preset--font-size--xlarge: 40px;--wp--preset--font-size--huge: 60px;--wp--preset--font-family--inter: "Inter", sans-serif;--wp--preset--font-family--cardo: Cardo;--wp--preset--spacing--20: 0.44rem;--wp--preset--spacing--30: 0.67rem;--wp--preset--spacing--40: 1rem;--wp--preset--spacing--50: 1.5rem;--wp--preset--spacing--60: 2.25rem;--wp--preset--spacing--70: 3.38rem;--wp--preset--spacing--80: 5.06rem;--wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);--wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);--wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);--wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);--wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);}:where(.is-layout-flex){gap: 0.5em;}:where(.is-layout-grid){gap: 0.5em;}body .is-layout-flex{display: flex;}.is-layout-flex{flex-wrap: wrap;align-items: center;}.is-layout-flex > :is(*, div){margin: 0;}body .is-layout-grid{display: grid;}.is-layout-grid > :is(*, div){margin: 0;}:where(.wp-block-columns.is-layout-flex){gap: 2em;}:where(.wp-block-columns.is-layout-grid){gap: 2em;}:where(.wp-block-post-template.is-layout-flex){gap: 1.25em;}:where(.wp-block-post-template.is-layout-grid){gap: 1.25em;}.has-black-color{color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-color{color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-color{color: var(--wp--preset--color--white) !important;}.has-pale-pink-color{color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-color{color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-color{color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-color{color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-color{color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-color{color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-color{color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-color{color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-color{color: var(--wp--preset--color--vivid-purple) !important;}.has-black-background-color{background-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-background-color{background-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-background-color{background-color: var(--wp--preset--color--white) !important;}.has-pale-pink-background-color{background-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-background-color{background-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-background-color{background-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-background-color{background-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-background-color{background-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-background-color{background-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-background-color{background-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-background-color{background-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-background-color{background-color: var(--wp--preset--color--vivid-purple) !important;}.has-black-border-color{border-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-border-color{border-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-border-color{border-color: var(--wp--preset--color--white) !important;}.has-pale-pink-border-color{border-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-border-color{border-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-border-color{border-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-border-color{border-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-border-color{border-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-border-color{border-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-border-color{border-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-border-color{border-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-border-color{border-color: var(--wp--preset--color--vivid-purple) !important;}.has-vivid-cyan-blue-to-vivid-purple-gradient-background{background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;}.has-light-green-cyan-to-vivid-green-cyan-gradient-background{background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;}.has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;}.has-luminous-vivid-orange-to-vivid-red-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;}.has-very-light-gray-to-cyan-bluish-gray-gradient-background{background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;}.has-cool-to-warm-spectrum-gradient-background{background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;}.has-blush-light-purple-gradient-background{background: var(--wp--preset--gradient--blush-light-purple) !important;}.has-blush-bordeaux-gradient-background{background: var(--wp--preset--gradient--blush-bordeaux) !important;}.has-luminous-dusk-gradient-background{background: var(--wp--preset--gradient--luminous-dusk) !important;}.has-pale-ocean-gradient-background{background: var(--wp--preset--gradient--pale-ocean) !important;}.has-electric-grass-gradient-background{background: var(--wp--preset--gradient--electric-grass) !important;}.has-midnight-gradient-background{background: var(--wp--preset--gradient--midnight) !important;}.has-small-font-size{font-size: var(--wp--preset--font-size--small) !important;}.has-medium-font-size{font-size: var(--wp--preset--font-size--medium) !important;}.has-large-font-size{font-size: var(--wp--preset--font-size--large) !important;}.has-x-large-font-size{font-size: var(--wp--preset--font-size--x-large) !important;}
:where(.wp-block-post-template.is-layout-flex){gap: 1.25em;}:where(.wp-block-post-template.is-layout-grid){gap: 1.25em;}
:where(.wp-block-columns.is-layout-flex){gap: 2em;}:where(.wp-block-columns.is-layout-grid){gap: 2em;}
:root :where(.wp-block-pullquote){font-size: 1.5em;line-height: 1.6;}
</style>
<link rel='stylesheet' id='wp-block-library-css' href='https://www.lumenflex.com/wp-includes/css/dist/block-library/style.min.css?ver=********************************' type='text/css' media='all' />
<style id='wp-block-library-theme-inline-css' type='text/css'>
.wp-block-audio :where(figcaption){color:#555;font-size:13px;text-align:center}.is-dark-theme .wp-block-audio :where(figcaption){color:#ffffffa6}.wp-block-audio{margin:0 0 1em}.wp-block-code{border:1px solid #ccc;border-radius:4px;font-family:Menlo,Consolas,monaco,monospace;padding:.8em 1em}.wp-block-embed :where(figcaption){color:#555;font-size:13px;text-align:center}.is-dark-theme .wp-block-embed :where(figcaption){color:#ffffffa6}.wp-block-embed{margin:0 0 1em}.blocks-gallery-caption{color:#555;font-size:13px;text-align:center}.is-dark-theme .blocks-gallery-caption{color:#ffffffa6}:root :where(.wp-block-image figcaption){color:#555;font-size:13px;text-align:center}.is-dark-theme :root :where(.wp-block-image figcaption){color:#ffffffa6}.wp-block-image{margin:0 0 1em}.wp-block-pullquote{border-bottom:4px solid;border-top:4px solid;color:currentColor;margin-bottom:1.75em}.wp-block-pullquote cite,.wp-block-pullquote footer,.wp-block-pullquote__citation{color:currentColor;font-size:.8125em;font-style:normal;text-transform:uppercase}.wp-block-quote{border-left:.25em solid;margin:0 0 1.75em;padding-left:1em}.wp-block-quote cite,.wp-block-quote footer{color:currentColor;font-size:.8125em;font-style:normal;position:relative}.wp-block-quote:where(.has-text-align-right){border-left:none;border-right:.25em solid;padding-left:0;padding-right:1em}.wp-block-quote:where(.has-text-align-center){border:none;padding-left:0}.wp-block-quote.is-large,.wp-block-quote.is-style-large,.wp-block-quote:where(.is-style-plain){border:none}.wp-block-search .wp-block-search__label{font-weight:700}.wp-block-search__button{border:1px solid #ccc;padding:.375em .625em}:where(.wp-block-group.has-background){padding:1.25em 2.375em}.wp-block-separator.has-css-opacity{opacity:.4}.wp-block-separator{border:none;border-bottom:2px solid;margin-left:auto;margin-right:auto}.wp-block-separator.has-alpha-channel-opacity{opacity:1}.wp-block-separator:not(.is-style-wide):not(.is-style-dots){width:100px}.wp-block-separator.has-background:not(.is-style-dots){border-bottom:none;height:1px}.wp-block-separator.has-background:not(.is-style-wide):not(.is-style-dots){height:2px}.wp-block-table{margin:0 0 1em}.wp-block-table td,.wp-block-table th{word-break:normal}.wp-block-table :where(figcaption){color:#555;font-size:13px;text-align:center}.is-dark-theme .wp-block-table :where(figcaption){color:#ffffffa6}.wp-block-video :where(figcaption){color:#555;font-size:13px;text-align:center}.is-dark-theme .wp-block-video :where(figcaption){color:#ffffffa6}.wp-block-video{margin:0 0 1em}:root :where(.wp-block-template-part.has-background){margin-bottom:0;margin-top:0;padding:1.25em 2.375em}
</style>
<style id='classic-theme-styles-inline-css' type='text/css'>
/*! This file is auto-generated */
.wp-block-button__link{color:#fff;background-color:#32373c;border-radius:9999px;box-shadow:none;text-decoration:none;padding:calc(.667em + 2px) calc(1.333em + 2px);font-size:1.125em}.wp-block-file__button{background:#32373c;color:#fff;text-decoration:none}
</style>
<link rel='stylesheet' id='rs-plugin-settings-css' href='//www.lumenflex.com/wp-content/plugins/revslider/sr6/assets/css/rs6.css?ver=6.7.25' type='text/css' media='all' />
<style id='rs-plugin-settings-inline-css' type='text/css'>
#rs-demo-id {}
</style>
<script type="text/javascript" src="https://www.lumenflex.com/wp-includes/js/dist/hooks.min.js?ver=4d63a3d491d11ffd8ac6" id="wp-hooks-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-includes/js/dist/i18n.min.js?ver=5e580eb46a90c2b997e6" id="wp-i18n-js"></script>
<script type="text/javascript" id="wp-i18n-js-after">
/* <![CDATA[ */
wp.i18n.setLocaleData( { 'text direction\u0004ltr': [ 'ltr' ] } );
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/contact-form-7/includes/swv/js/index.js?ver=6.0.2" id="swv-js"></script>
<script type="text/javascript" id="contact-form-7-js-before">
/* <![CDATA[ */
var wpcf7 = {
    "api": {
        "root": "https:\/\/www.lumenflex.com\/wp-json\/",
        "namespace": "contact-form-7\/v1"
    }
};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/contact-form-7/includes/js/index.js?ver=6.0.2" id="contact-form-7-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/woocommerce/assets/js/sourcebuster/sourcebuster.min.js?ver=9.5.1" id="sourcebuster-js-js"></script>
<script type="text/javascript" id="wc-order-attribution-js-extra">
/* <![CDATA[ */
var wc_order_attribution = {"params":{"lifetime":1.0e-5,"session":30,"base64":false,"ajaxurl":"https:\/\/www.lumenflex.com\/wp-admin\/admin-ajax.php","prefix":"wc_order_attribution_","allowTracking":true},"fields":{"source_type":"current.typ","referrer":"current_add.rf","utm_campaign":"current.cmp","utm_source":"current.src","utm_medium":"current.mdm","utm_content":"current.cnt","utm_id":"current.id","utm_term":"current.trm","utm_source_platform":"current.plt","utm_creative_format":"current.fmt","utm_marketing_tactic":"current.tct","session_entry":"current_add.ep","session_start_time":"current_add.fd","session_pages":"session.pgs","session_count":"udata.vst","user_agent":"udata.uag"}};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/woocommerce/assets/js/frontend/order-attribution.min.js?ver=9.5.1" id="wc-order-attribution-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-includes/js/imagesloaded.min.js?ver=5.0.0" id="imagesloaded-js"></script>
<script type="text/javascript" id="wc-braintree-payment-method-icons-js-extra">
/* <![CDATA[ */
var wc_braintree_payment_method_icons_params = {"tokens":[],"icons":{"Visa":"<img class=\"wc-braintree-method-icon closed visa\" src=\"https:\/\/www.lumenflex.com\/wp-content\/plugins\/woo-payment-gateway\/assets\/img\/payment-methods\/closed\/visa.svg\"\/>","MasterCard":"<img class=\"wc-braintree-method-icon closed mastercard\" src=\"https:\/\/www.lumenflex.com\/wp-content\/plugins\/woo-payment-gateway\/assets\/img\/payment-methods\/closed\/master_card.svg\"\/>","AmericanExpress":"<img class=\"wc-braintree-method-icon closed amex\" src=\"https:\/\/www.lumenflex.com\/wp-content\/plugins\/woo-payment-gateway\/assets\/img\/payment-methods\/closed\/amex.svg\"\/>","Discover":"<img class=\"wc-braintree-method-icon closed discover\" src=\"https:\/\/www.lumenflex.com\/wp-content\/plugins\/woo-payment-gateway\/assets\/img\/payment-methods\/closed\/discover.svg\"\/>","DinersClub":"<img class=\"wc-braintree-method-icon closed diners\" src=\"https:\/\/www.lumenflex.com\/wp-content\/plugins\/woo-payment-gateway\/assets\/img\/payment-methods\/closed\/diners_club_international.svg\"\/>","JCB":"<img class=\"wc-braintree-method-icon closed jcb\" src=\"https:\/\/www.lumenflex.com\/wp-content\/plugins\/woo-payment-gateway\/assets\/img\/payment-methods\/closed\/jcb.svg\"\/>","Maestro":"<img class=\"wc-braintree-method-icon closed maestro\" src=\"https:\/\/www.lumenflex.com\/wp-content\/plugins\/woo-payment-gateway\/assets\/img\/payment-methods\/closed\/maestro.svg\"\/>","PayPal":"<img class=\"wc-braintree-method-icon paypal\" src=\"https:\/\/www.lumenflex.com\/wp-content\/plugins\/woo-payment-gateway\/assets\/img\/payment-methods\/paypal.svg\"\/>","GooglePay":"<img class=\"wc-braintree-method-icon googlepay\" src=\"https:\/\/www.lumenflex.com\/wp-content\/plugins\/woo-payment-gateway\/assets\/img\/googlepay\/google_pay_standard.svg\"\/>","UnionPay":"<img class=\"wc-braintree-method-icon closed unionpay\" src=\"https:\/\/www.lumenflex.com\/wp-content\/plugins\/woo-payment-gateway\/assets\/img\/payment-methods\/closed\/china_union_pay.svg\"\/>","ApplePay":"<img class=\"wc-braintree-method-icon applepay\" src=\"https:\/\/www.lumenflex.com\/wp-content\/plugins\/woo-payment-gateway\/assets\/img\/applepay\/apple_pay_mark.svg\"\/>","Venmo":"<img class=\"wc-braintree-method-icon venmo\" src=\"https:\/\/www.lumenflex.com\/wp-content\/plugins\/woo-payment-gateway\/assets\/img\/payment-methods\/venmo.svg\"\/>"}};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/woo-payment-gateway/assets/js/frontend/payment-method-icons.js?ver=3.2.59" id="wc-braintree-payment-method-icons-js"></script>
<script type="text/javascript" src="https://js.braintreegateway.com/web/3.106.0/js/client.min.js" id="wc-braintree-client-v3-js"></script>
<script type="text/javascript" id="wc-braintree-message-handler-js-extra">
/* <![CDATA[ */
var wc_braintree_message_handler_params = {"messages":{"INSTANTIATION_OPTION_REQUIRED":"Error initiating Braintree client. This error occurs when you haven't finished configuring your API keys.","INVALID_CLIENT_TOKEN":"Invalid client token supplied. Please check your API keys.","HOSTED_FIELDS_FIELDS_EMPTY":"Please fill out the payment form.","HOSTED_FIELDS_FIELDS_INVALID":"Some of your payment fields are invalid.","HOSTED_FIELDS_FAILED_TOKENIZATION":"Please check that your credit card is valid.","HOSTED_FIELDS_TOKENIZATION_NETWORK_ERROR":"There was a network error. Please try again.","VALIDATION":"Please check your payment method fields.","THREEDS_LOOKUP_VALIDATION_ERROR":"The address data passed to the 3D Secure provider was in an invalid format.","THREEDS_HTTPS_REQUIRED":"3D Secure requires HTTPS.","THREEDS_NOT_ENABLED":"3D Secure is not enabled for this merchant.","PAYPAL_INVALID_ADDRESS":"Provided shipping address failed PayPal validation. Please check your entries.","PAYPAL_INVALID_CLIENT":"Check your PayPal client ID and client secret in your Braintree Control Panel settings.","PAYPAL_TOKENIZATION_REQUEST_ACTIVE":"There is already a PayPal window open.","PAYPAL_FLOW_OPTION_REQUIRED":"PayPal vault flow is required.","PAYPAL_INVALID_PAYMENT_OPTION_ADDRESS":"PayPal validation of address failed. Please verify your shipping address.","PAYPAL_INVALID_PAYMENT_OPTION_CITY":"Invalid city entered for your shipping address.","PAYPAL_INVALID_PAYMENT_OPTION_STATE":"Invalid state entered for your shipping address.","PAYPAL_INVALID_PAYMENT_OPTION_ZIP":"Invalid zip code entered for shipping address.","PAYPAL_LINKED_ACCOUNT_PRODUCTION":"Please link your PayPal account in the Braintree control panel in order to use PayPal.","PAYPAL_LINKED_ACCOUNT_SANDBOX":"Please link your PayPal account in the Braintree control panel in order to use PayPal. <a href=\"https:\/\/developers.braintreepayments.com\/guides\/paypal\/testing-go-live\/php#linked-paypal-testing\" target=\"_blank\">How to link a sandbox PayPal account.<\/a>","DATA_COLLECTOR_KOUNT_NOT_ENABLED":"Please enable fraud tools within your Braintree Control Panel if you have enabled Advanced Fraud in the plugin settings. This setting can take several minutes to take affect.","LOCAL_GATEWAY_INVALID_MERCHANT_ACCOUNT":"No merchant account found for the requested currency.","NO_MERCHANT_ACCOUNT":"No merchant account found for the requested currency.","GOOGLE_PAYMENT_NOT_ENABLED":"Google Pay is not enabled for this merchant. Please enable Google Pay within your Braintree control panel.","DEVELOPER_ERROR_WHITELIST":"You have not been whitelisted by Google to use the paymentDataRequest.callbackIntent. Please contact Google's API team to request that your merchant ID be whitelisted. You can disable the plugin's dynamc price setting which will allow you to accept payments while you wait for Google to whitelist you.","PAYPAL_INVALID_LOCALE":"Please enter a locale that PayPal supports. You can change the locale in your Wordpress settings.","PAYPAL_MERCHANT_ACCOUNT_EMPTY":"PayPal requires that you configure your merchant accounts. Navigate to the Braintree Plugin's Advanced Settings &#62; <a target=\"_blank\" href=\"https:\/\/www.lumenflex.com\/wp-admin\/admin.php?page=wc-settings&tab=checkout&section=braintree_advanced\">Merchant Accounts<\/a> page and click <strong>Import Accounts<\/strong>.","LOCAL_PAYMENT_WINDOW_OPEN_FAILED":"LocalPayment window failed to open; please allow pop-ups for this site.","DropinError":"Please fill out the payment form.","avsPostalCodeResponseCode_N":"Postal code does not match.","avsStreetAddressResponseCode_N":"Street address does not match.","cvvResponseCode_N":"Card security code does not match.","avsPostalCodeResponseCode_U":"Postal code not verified.","avsStreetAddressResponseCode_U":"Street address not verified.","cvvResponseCode_U":"Card security code not verified.","avsPostalCodeResponseCode_I":"Postal code not provided.","avsStreetAddressResponseCode_I":"Street address not provided.","cvvResponseCode_I":"Card security code not provided.","gatewayRejectionReason_three_d_secure":"Your payment method was rejected due to 3D Secure.","2000":"Do Not Honor","2001":"Insufficient Funds","2002":"Limit Exceeded","2003":"Cardholder's Activity Limit Exceeded","2004":"Expired Card","2005":"Invalid Credit Card Number","2006":"Invalid Expiration Date","2007":"No Account","2008":"Card Account Length Error","2009":"No Such Issuer","2010":"Card Issuer Declined CVV","2011":"Voice Authorization Required","2012":"Processor Declined Possible Lost Card","2013":"Processor Declined - Possible Stolen Card","2014":"Processor Declined - Fraud Suspected","2015":"Transaction Not Allowed","2016":"Duplicate Transaction","2017":"Cardholder Stopped Billing","2018":"Cardholder Stopped All Billing","2019":"Invalid Transaction","2020":"Violation","2021":"Security Violation","2022":"Declined - Updated Cardholder Available","2023":"Processor Does Not Support This Feature","2024":"Card Type Not Enabled","2025":"Set Up Error - Merchant","2026":"Invalid Merchant ID","2027":"Set Up Error - Amount","2028":"Set Up Error - Hierarchy","2029":"Set Up Error - Card","2030":"Set Up Error - Terminal","2031":"Encryption Error","2032":"Surcharge Not Permitted","2033":"Inconsistent Data","2034":"No Action Taken","2035":"Partial Approval For Amount In Group III Version","2036":"Authorization could not be found to reverse","2037":"Already Reversed","2038":"Processor Declined","2039":"Invalid Authorization Code","2040":"Invalid Store","2041":"Declined - Call For Approval","2042":"Invalid Client ID","2043":"Error - Do Not Retry, Call Issuer","2044":"Declined - Call Issuer","2045":"Invalid Merchant Number","2046":"Declined","2047":"Call Issuer. Pick Up Card","2048":"Invalid Amount","2049":"Invalid SKU Number","2050":"Invalid Credit Plan","2051":"Credit Card Number does not match method of payment","2053":"Card reported as lost or stolen","2054":"Reversal amount does not match authorization amount","2055":"Invalid Transaction Division Number","2056":"Transaction amount exceeds the transaction division limit","2057":"Issuer or Cardholder has put a restriction on the card","2058":"Merchant not Mastercard SecureCode enabled","2059":"Address Verification Failed","2060":"Address Verification and Card Security Code Failed","2061":"Invalid Transaction Data","2062":"Invalid Tax Amount","2063":"PayPal Business Account preference resulted in the transaction failing","2064":"Invalid Currency Code","2065":"Refund Time Limit Exceeded","2066":"PayPal Business Account Restricted","2067":"Authorization Expired","2068":"PayPal Business Account Locked or Closed","2069":"PayPal Blocking Duplicate Order IDs","2070":"PayPal Buyer Revoked Future Payment Authorization","2071":"PayPal Payee Account Invalid Or Does Not Have a Confirmed Email","2072":"PayPal Payee Email Incorrectly Formatted","2073":"PayPal Validation Error","2074":"Funding Instrument In The PayPal Account Was Declined By The Processor Or Bank, Or It Can't Be Used For This Payment","2075":"Payer Account Is Locked Or Closed","2076":"Payer Cannot Pay For This Transaction With PayPal","2077":"Transaction Refused Due To PayPal Risk Model","2079":"PayPal Merchant Account Configuration Error","2081":"PayPal pending payments are not supported","2082":"PayPal Domestic Transaction Required","2083":"PayPal Phone Number Required","2084":"PayPal Tax Info Required","2085":"PayPal Payee Blocked Transaction","2086":"PayPal Transaction Limit Exceeded","2087":"PayPal reference transactions not enabled for your account","2088":"Currency not enabled for your PayPal seller account","2089":"PayPal payee email permission denied for this request","2090":"PayPal account not configured to refund more than settled amount","2091":"Currency of this transaction must match currency of your PayPal account","3000":"Processor Network Unavailable - Try Again","Braintree\\Exception\\NotFound":"Object was not found.","Braintree\\Exception\\Authentication":"Authentication failed, check your API key configuration.","Braintree\\Exception\\Authorization":"API Authorization check failed. Ensure you have entered your API keys correctly and the user associated with your API keys has the proper permissions.","81801":"Addresses must have at least one field filled in.","81802":"Company is too long.","81804":"Extended address is too long.","81805":"First name is too long.","81806":"Last name is too long.","81807":"Locality is too long.","81813":"Postal code can only contain letters, numbers, spaces, and hyphens.","81808":"Postal code is required.","81809":"Postal code may contain no more than 9 letter or number characters.","81810":"Region is too long.","81811":"Street address is required.","81812":"Street address is too long.","81827":"US state codes must be two characters to meet PayPal Seller Protection requirements.","91803":"Country name is not an accepted country.","91815":"Provided country information is inconsistent.","91816":"Country code (alpha3) is not an accepted country.","91817":"Country code (numeric) is not an accepted country.","91814":"Country code (alpha2) is not an accepted country.","91818":"Customer has already reached the maximum of 50 addresses.","91819":"First name must be a string.","91820":"Last name must be a string.","91821":"Company must be a string.","91822":"Street address must be a string.","91823":"Extended address must be a string.","91824":"Locality must be a string.","91825":"Region must be a string.","91826":"Postal code must be a string.","91828":"Address is invalid.","82602":"Applicant merchant id is too long.","82603":"Applicant merchant id format is invalid.","82604":"Applicant merchant id is in use.","82605":"Applicant merchant id is not allowed.","82606":"Master merchant account ID is required.","82607":"Master merchant account ID is invalid.","82608":"Master merchant account must be active.","82610":"Terms Of Service needs to be accepted. Applicant tos_accepted required.","82675":"Merchant account id can not be updated.","82676":"Master merchant account id can not be updated.","82674":"Merchant accounts with a status of pending or suspended cannot be updated.","82609":"Applicant first name is required.","82637":"Individual first name is required.","82611":"Applicant last name is required.","82638":"Individual last name is required.","82612":"Applicant date of birth is required.","82639":"Individual date of birth is required.","82613":"Applicant routing number is required.","82640":"Funding routing number is required.","82614":"Applicant account number is required.","82641":"Funding account number is required.","82615":"Applicant SSN must be blank, last 4 digits, or full 9 digits.","82642":"Individual SSN must be blank, last 4 digits, or full 9 digits.","82616":"Applicant email is invalid.","82643":"Individual email is invalid.","82627":"Applicant first name is invalid.","82644":"Individual first name is invalid.","82628":"Applicant last name is invalid.","82645":"Individual last name is invalid.","82631":"Applicant company name is invalid.","82632":"Applicant tax ID is invalid.","82688":"Business params provided in an invalid format.","82647":"Business tax ID is invalid.","82633":"Applicant company name is required with tax ID.","82634":"Applicant tax ID is required with company name.","82635":"Applicant routing number is invalid.","82649":"Funding routing number is invalid.","82650":"An unexpected error occurred trying to save the merchant account; support has been notified and is looking into the issue. You may safely retry this request","82621":"Applicant declined due to OFAC.","82622":"Applicant declined due to MasterCard MATCH.","82623":"Applicant declined due to failed KYC.","82624":"Applicant declined due to invalid SSN.","82625":"Applicant declined due to SSN matching that of a deceased person.","82626":"Applicant declined after review.","82617":"Applicant street address is required.","82657":"Individual street address is required.","82618":"Applicant locality is required.","82658":"Individual locality is required.","82619":"Applicant postal code is required.","82659":"Individual postal code is required.","82620":"Applicant region is required.","82660":"Individual region is required.","82629":"Applicant street address is invalid.","82661":"Individual street address is invalid.","82664":"Applicant region is invalid.","82668":"Individual region is invalid.","82630":"Applicant postal code is invalid.","82662":"Individual postal code is invalid.","82636":"Applicant phone is invalid.","82656":"Individual phone is invalid.","82663":"Applicant date of birth is invalid","82666":"Individual date of birth is invalid.","82670":"Applicant account number is invalid.","82671":"Funding account number is invalid.","82665":"Applicant email is required.","82667":"Individual email is required.","82672":"Business tax ID must be blank unless business legal name is present.","82673":"Applicant tax ID must be blank unless company name present.","82646":"Business DBA name is invalid.","82677":"Business legal name is invalid.","82669":"Business legal name is required with tax ID.","82648":"Business tax ID is required with business legal name.","82685":"Business street address is invalid.","82686":"Business postal code is invalid.","82684":"Business region is invalid.","82679":"Funding destination is invalid.","82678":"Funding destination is required.","82681":"Funding email is invalid.","82680":"Funding email is required when destination is email.","82683":"Funding mobile phone is invalid.","82682":"Funding mobile phone is required when destination is mobile phone.","82687":"Individual params provided in an invalid format.","82689":"Business locality is invalid.","82690":"Individual locality is invalid.","82691":"Applicant locality is invalid.","92801":"Cannot specify make_default without a customer_id","92802":"Cannot specify verify_card without a customer_id","92803":"Cannot specify fail_on_duplicate_payment_method without a customer_id","92804":"Customer specified by customer_id does not exist","92806":"Unsupported client token version","92807":"Merchant Account specified by merchant_account_id does not exist","91602":"Custom field is invalid:","91609":"Customer ID has already been taken.","91610":"Customer ID is invalid (use only letters, numbers, '-', and '_').","91611":"Customer ID is not an allowed ID.","91612":"Customer ID is too long.","91613":"Customer ID is required.","91617":"Nonce references a vaulted payment instrument - cannot be transferred between customers","91618":"Customer attribute must be a map of keys and values representing a customer.","91619":"Ambiguous usage of default payment method token.","81601":"Company is too long.","81603":"Custom field is too long:","81604":"Email is an invalid format.","81605":"Email is too long.","81606":"Email is required if sending a receipt.","81607":"Fax is too long.","81608":"First name is too long.","81613":"Last name is too long.","81614":"Phone is too long.","81615":"Website is too long.","81616":"Website is an invalid format.","93101":"Payment method params are required.","93102":"Nonce is invalid.","93103":"Nonce is required.","93104":"Customer ID is required.","93105":"Customer ID is invalid.","93106":"Cannot forward a payment method of this type.","93107":"Cannot use a payment_method_nonce more than once.","93108":"Unknown or expired payment_method_nonce.","93109":"Nonce is not vaultable.","83501":"Apple Pay cards are not accepted by this merchant account.","83502":"A customer ID is required to vault an Apple Pay Card.","93503":"Apple Pay token is taken.","93504":"Cannot use a payment_method_nonce more than once.","93505":"Unknown or expired payment_method_nonce.","93506":"Payment method nonce locked.","83518":"Credit card type is not accepted by this merchant account.","93507":"Payment method nonces cannot be used to update an existing Apple Pay Card.","93508":"Number is required for Apple Pay Card","93509":"Expiration Month is required for Apple Pay Card","93510":"Expiration Year is required for Apple Pay Card","93511":"Cryptogram is required for Apple Pay Card","83512":"Apple Pay payment data decryption failed","93513":"Apple Pay is disabled for this merchant","93514":"Apple Pay certificate, private key or merchant ID not configured","93517":"Certificate provided is not valid","93519":"Public key used to sign payment data does not match stored certificate","83520":"Payment data is malformed","93521":"Private key stored does not match private key used to encrypt payment data","93522":"Certificate does not match stored key pair","91701":"Cannot provide both a billing address and a billing address ID.","91702":"Billing address ID is invalid.","91704":"Customer ID is required.","91705":"Customer ID is invalid.","91708":"Cannot provide expirationdate if you are also providing expiration_month and expiration_year.","91718":"Token is invalid (use only letters, numbers, \" - \", and '').","91719":"Credit card token is taken.","91720":"Credit card token is too long.","91721":"Token is not an allowed token.","91722":"Payment Method token is required.","91744":"Billing address format is invalid.","81723":"Cardholder name is too long.","81703":"Credit card type is not accepted by this merchant account.","81718":"Credit card number cannot be updated to an unsupported card type when it is associated to subscriptions.","81706":"CVV is required.","81707":"CVV must be 4 digits for American Express and 3 digits for other card types.","81709":"Expiration date is required.","81710":"Expiration date is invalid.","81711":"Expiration date year is invalid. It must be between 1975 and 2200.","81712":"Expiration month is invalid.","81713":"Expiration year is invalid.","81714":"Credit card number is required.","81715":"Credit card number is invalid.","81716":"Credit card number must be 12-19 digits.","81717":"Credit card number is not an accepted test number.","91723":"Update Existing Token is invalid.","81724":"Duplicate card exists in the vault.","81725":"Credit card must include number, payment_method_nonce, or venmo_sdk_payment_method_code.","91726":"Credit card type is not accepted by this merchant account.","91727":"Invalid VenmoSDK payment method code","91728":"Verification Merchant Account ID is invalid.","91729":"Update Existing Token is not allowed when creating a customer.","91730":"Verifications are not supported on this merchant account","91731":"Cannot use a payment_method_nonce more than once.","91732":"Unknown or expired payment_method_nonce.","91733":"Payment method nonce locked.","91734":"Credit card type is not accepted by this merchant account.","91735":"Payment method nonces cannot be used to update an existing card.","91738":"Payment method is not a credit card payment method.","91742":"Verification Merchant Account is suspended.","91743":"The current user does not have access to the specified verification_merchant_account_id","81736":"CVV verification failed.","81737":"Postal code verification failed.","91739":"Verification amount cannot be negative.","91740":"Verification amount is invalid.","91741":"Verification amount not supported by processor.","91745":"Payment method params supplied are not valid for updating a credit card.","81750":"Credit card number is prohibited.","91752":"Verification amount is too large.","91755":"Verification Merchant Account ID cannot be a sub-merchant account.","93401":"Industry type is invalid.","93402":"Lodging data is empty.","93403":"Folio number is invalid.","93404":"Check in date is invalid.","93405":"Check out date is invalid.","93406":"Check out date must occur during or after the check in date.","93407":"Data fields are unknown.","93408":"Travel and Cruise data is empty.","93409":"Data fields are unknown.","93410":"Travel Package is invalid.","93411":"Departure date is invalid.","93412":"Lodging check in date is invalid.","93413":"Lodging check out date is invalid.","82901":"Incomplete PayPal account information.","82902":"Pre-Approved Payment enabled PayPal account required for vaulting.","82903":"Invalid PayPal account information.","82904":"PayPal Accounts are not accepted by this merchant account.","82905":"A customer ID is required to vault a PayPal Account.","92906":"PayPal Account token is taken.","92907":"Cannot use a payment_method_nonce more than once.","92908":"Unknown or expired payment_method_nonce.","92909":"Payment method nonce locked.","92910":"Error communicating with PayPal.","92911":"PayPal authentication expired.","92912":"Funding source selection was given without an access token.","92913":"Funding source object is invalid or missing required fields.","92914":"Payment method nonces cannot be used to update an existing PayPal account.","92915":"Payment method params supplied are not valid for updating a PayPal account.","84101":"Common ID is required.","84102":"Username is required.","84103":"Venmo user ID is required.","84104":"Customer ID is required.","84105":"Venmo accounts are not accepted by this merchant account.","84106":"Customer ID is invalid.","92001":"Quantity is invalid.","92002":"Amount is invalid.","92003":"Amount cannot be blank.","92004":"Quantity cannot be blank.","92005":"Number of billing cycles is invalid.","92010":"Quantity must be greater than zero.","92011":"Existing ID is invalid.","92012":"Existing ID is required.","92013":"Inherited From ID is invalid.","92014":"Inherited From ID is required.","92015":"Cannot update a removed add-on or discount.","92016":"Cannot remove add-on or discount if not already associated with subscription.","92017":"Number of billing cycles cannot be blank.","92018":"Cannot specify both number of billing cycles and never expires as true.","92019":"Number of billing cycles must be greater than zero.","92020":"Existing ID is not of the correct kind.","92021":"ID to remove is incorrect kind.","92022":"Cannot edit add-on or discount on a past due subscription.","92023":"Amount is too large.","92024":"Cannot pass null modification.","92025":"ID to remove is invalid.","81901":"Cannot edit a canceled subscription.","81902":"ID has already been taken.","81903":"Price cannot be blank.","81904":"Price is an invalid format.","81905":"Subscription has already been canceled.","81906":"ID is invalid (use only letters, numbers, '-', and '').","81907":"Trial Duration is an invalid format.","81908":"Trial Duration is required.","81909":"Trial Duration Unit is invalid.","81910":"Cannot edit an expired subscription.","81923":"Price is too large.","91901":"Merchant Account ID is invalid.","91902":"Payment method token payment instrument type is not accepted by this merchant account.","91903":"Payment method token is invalid.","91904":"Plan ID is invalid.","91905":"Payment method token does not belong to the subscription's customer.","91906":"Number Of Billing Cycles must be numeric.","91907":"Number Of Billing Cycles must be greater than zero.","91908":"Cannot specify both number of billing cycles and never expires as true.","91909":"Number Of Billing Cycles is less than the current billing cycle.","91911":"Cannot add duplicate add-on or discount.","91912":"Number Of Billing Cycles cannot be blank if the subscription expires.","91913":"Billing Day of Month must be numeric.","91914":"Billing Day of Month must be between 1 and 28, or 31.","91915":"First Billing Date is invalid.","91916":"First Billing Date cannot be in the past.","91917":"Cannot specify more than one type of start date.","91918":"Billing Day of Month cannot be updated.","91919":"First Billing Date cannot be updated.","91920":"Can only edit id, merchant account id, payment method token, and descriptor on a past due subscription.","91921":"Invalid request format.","91922":"Cannot update subscription to a plan with a different billing frequency.","91923":"Subscription Plan currency must be the same as the merchant account's currency.","91924":"Payment method nonce payment instrument type is not accepted by this merchant account.","91925":"Payment method nonce is invalid.","91926":"Payment method nonce does not belong to the subscription's customer.","91927":"Payment method nonce represents an un-vaulted payment instrument.","91928":"Payment instrument type is not valid for subscriptions.","91929":"Payment instrument type is not valid for subscriptions.","91930":"Merchant Account does not support the given payment instrument type.","82301":"Settlement Date is required","82302":"Settlement Date is invalid","82303":"Group By Custom Field is not a valid custom field","81501":"Amount cannot be negative.","81502":"Amount is required.","81503":"Amount is an invalid format.","81528":"Amount is too large.","81509":"Credit card type is not accepted by this merchant account.","81527":"Custom field is too long:","91501":"Order ID is too long.","91530":"Cannot provide a billing address unless also providing a credit card.","91504":"Transaction can only be voided if status is authorized or submitted_for_settlement.","91505":"Credit transactions cannot be refunded.","91506":"Cannot refund a transaction unless it is settled.","91507":"Cannot submit for settlement unless status is authorized.","91508":"Cannot determine payment method.","91526":"Custom field is invalid:","91510":"Customer ID is invalid.","91511":"Customer does not have any credit cards.","91512":"Transaction has already been completely refunded.","91513":"Merchant account ID is invalid.","91514":"Merchant account is suspended.","91515":"Cannot provide both payment_method_token and credit_card attributes.","91516":"Cannot provide both payment_method_token and customer_id unless the payment_method belongs to the customer.","91527":"Cannot provide both payment_method_token and subscription_id unless the payment_method belongs to the subscription.","91517":"Payment instrument type is not accepted by this merchant account.","91518":"Payment method token is invalid.","91519":"Processor authorization code cannot be set unless for a voice authorization.","91521":"Refund amount is too large.","91538":"Cannot refund a transaction with a suspended merchant account.","91522":"Settlement amount is too large.","91529":"Cannot provide both subscription_id and customer_id unless the subscription belongs to the customer.","91528":"Subscription ID is invalid.","91523":"Transaction type is invalid.","91524":"Transaction type is required.","91525":"Vault is disabled.","91531":"Subscription status must be Past Due in order to retry.","91547":"Merchant account does not support refunds.","81531":"Amount must be greater than zero.","81534":"Tax amount cannot be negative.","81535":"Tax amount is an invalid format.","81536":"Tax amount is too large.","81571":"Failed to authenticate, please try a different form of payment.","91537":"Purchase order number is too long.","91539":"Voice Authorization is not allowed for this card type","91540":"Transaction cannot be cloned if payment method is stored in vault.","91541":"Cannot clone voice authorization transactions.","91542":"Unsuccessful transaction cannot be cloned.","91543":"Credits cannot be cloned.","91544":"Cannot clone transaction without submit_for_settlement flag.","91545":"Voice Authorizations are not supported for this processor.","91546":"Credits are not supported by this processor.","91548":"Purchase order number is invalid.","81520":"Processor authorization code must be 6 characters.","91549":"Cannot provide more than one of payment_method_token, payment_method_nonce, credit_card, and venmo_sdk_payment_method_code attributes.","91550":"Channel is too long.","91551":"Settlement amount cannot be less than the service fee amount.","91552":"Credits not allowed with service fee.","91553":"Sub-merchant account requires a service fee.","91554":"Amount cannot be negative.","91555":"Amount is an invalid format.","91556":"Service fee amount is larger than transaction amount.","91557":"Service fee not supported on master merchant account.","91558":"Merchant account does not support MOTO transactions unless configured by processor.","91559":"Cannot refund a transaction with a pending merchant account.","91560":"Transaction could not be held in escrow.","91561":"Cannot release a transaction that is not escrowed.","91562":"Release can only be cancelled if the transaction is submitted for release.","91563":"Escrowed transactions cannot be partially refunded.","91564":"Cannot use a payment_method_nonce more than once.","91565":"Unknown or expired payment_method_nonce.","91567":"Payment instrument type is not accepted by this merchant account.","91568":"Three D Secure Token is invalid.","91569":"payment_method_nonce does not contain a valid payment instrument type.","91572":"Current payment method does not support use_billing_for_shipping flag.","91575":"Cannot transition transaction to settled, settlement_confirmed, or settlement_declined","91576":"PayPal is not enabled for your merchant account.","91577":"Merchant account does not support payment instrument.","91570":"Transaction data does not match data from Three D Secure verify call.","91573":"Transaction cannot be cloned if payment method is a PayPal account.","91574":"Cannot refund a transaction transaction in settling status on this merchant account. Try again after the transaction has settled.","91578":"Service fee can not be applied on PayPal transactions.","91580":"PayPal custom fields must be less than 256 characters in length.","91581":"Shipping address customer does not match customer in request.","91582":"PayPal unilateral transactions must also be submitted for settlement.","91583":"This PayPal account was not vaulted with the required data","91584":"Merchant account must match the 3D Secure authorization merchant account.","91585":"Amount must match the 3D Secure authorization amount.","91586":"Shared billing address ID cannot be used in the same call as a standard billing address ID","91587":"Shared customer ID cannot be used in the same call as a standard customer ID","91588":"Shared payment method token cannot be used in the same call as a standard payment method token","91589":"Shared payment method token cannot be used in the same call as a non-shared identifier param","91590":"Shared identifier param cannot be used with non-shared payment method token","91591":"Shared shipping address ID cannot be used in the same call as a standard shipping address ID","91592":"Shared payment methods cannot be vaulted","91593":"Shared payment methods cannot be vaulted","91594":"Shared shipping addresses cannot be vaulted","91595":"Shared payment methods cannot be updated","91597":"Cannot provide both shared_payment_method_token and shared_customer_id unless the payment_method belongs to the customer.","91598":"Payment instrument type is not accepted by this merchant account.","91599":"Shared Shipping address customer does not match customer in request.","91596":"Shared payment method token is invalid.","915100":"Shared Customer ID is invalid.","915103":"Cannot submit for partial settlement.","915101":"Payment instrument type is not accepted.","915102":"Partial settlements are not supported by this processor.","915104":"Delayed settlements are not supported for this processor. The submit for settlement option is required.","915105":"Merchant account does not support Amex rewards.","915106":"Points amount is too large.","915107":"Updating order_id on submit_for_settlement is not supported by this processor.","915108":"Updating descriptor on submit_for_settlement is not supported by this processor.","915109":"PayPal supplementary data fields must be less than 4001 characters in length:","915110":"Cannot clone facilitated transactions.","915111":"PayPal supplementary data field count must be less than 101.","915112":"Shared payment method token originated from another merchant and is not allowed to be shared","915113":"EciFlag is required.","915114":"EciFlag is invalid.","915115":"Xid is required for specified EciFlag.","915116":"Cavv is required for specified EciFlag.","915131":"Merchant account does not support 3D Secure transactions for card type.","915133":"Transaction source must be either 'moto' or 'recurring'.","915134":"submit_for_settlement is required and must be true.","915135":"shared_payment_method_nonce does not contain valid payment instrument type.","915136":"Payment instrument type is not accepted by this merchant.","915137":"Cannot clone Braintree Marketplace transactions via the API.","92201":"Company name\/DBA section is invalid.","92202":"Phone number is invalid.","92203":"Dynamic descriptors have not been enabled for this account. <NAME_EMAIL>.","92204":"Descriptor format is invalid.","92205":"International phone number is invalid.","92206":"URL must be 13 characters or shorter.","94201":"Verification amount cannot be negative.","94202":"Verification amount is invalid.","94203":"Verification amount not supported by processor.","94204":"Verification Merchant Account ID is invalid.","94205":"Verification Merchant Account is suspended.","94206":"The current user does not have access to the specified merchant_account_id","94207":"Verification amount is too large.","94208":"Verification Merchant Account ID cannot be a sub-merchant account.","95817":"Product unit amount is in an invalid format.","95820":"Unit amount for transaction line item must be greater than zero. If this error continues, please disable the line items option in the plugin settings.","gateway_rejected:_avs":"Invalid postal code or street address.","gateway_rejected:_postal_code":"Postal code.","gateway_rejected:_cvv":"CVV.","gateway_rejected:_avs_and_cvv":"Invalid postal code and cvv","gateway_rejected:_three_d_secure":"Your payment method was rejected due to 3D Secure."}};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/woo-payment-gateway/assets/js/frontend/message-handler.min.js?ver=3.2.59" id="wc-braintree-message-handler-js"></script>
<script type="text/javascript" id="wc-braintree-payment-methods-js-extra">
/* <![CDATA[ */
var wc_braintree_payment_methods_params = {"cards":["Visa","MasterCard","AmericanExpress","Discover","DinersClub","JCB","Maestro","PayPal","GooglePay","UnionPay","ApplePay","Venmo"],"no_results":"Not matches found"};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/woo-payment-gateway/assets/js/frontend/payment-methods.min.js?ver=3.2.59" id="wc-braintree-payment-methods-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/woo-payment-gateway/assets/js/frontend/form-handler.min.js?ver=3.2.59" id="wc-braintree-form-handler-js"></script>
<script type="text/javascript" id="wc-braintree-global-js-extra">
/* <![CDATA[ */
var wc_braintree_global_params = {"page":"add_payment_method"};
var wc_braintree_checkout_fields = {"billing_first_name":{"label":"First name","required":true,"class":["form-row-first"],"autocomplete":"given-name","priority":10,"value":"davod"},"billing_last_name":{"label":"Last name","required":true,"class":["form-row-last"],"autocomplete":"family-name","priority":20,"value":"joe"},"billing_company":{"label":"Company name","class":["form-row-wide"],"autocomplete":"organization","priority":30,"required":false,"value":null},"billing_country":{"type":"country","label":"Country \/ Region","required":true,"class":["form-row-wide","address-field","update_totals_on_change"],"autocomplete":"country","priority":40,"value":"US"},"billing_address_1":{"label":"Street address","placeholder":"House number and street name","required":true,"class":["form-row-wide","address-field"],"autocomplete":"address-line1","priority":50,"value":"new york123"},"billing_address_2":{"label":"Apartment, suite, unit, etc.","label_class":["screen-reader-text"],"placeholder":"Apartment, suite, unit, etc. (optional)","class":["form-row-wide","address-field"],"autocomplete":"address-line2","priority":60,"required":false,"value":null},"billing_city":{"label":"Town \/ City","required":true,"class":["form-row-wide","address-field"],"autocomplete":"address-level2","priority":70,"value":"new york"},"billing_state":{"type":"state","label":"State","required":true,"class":["form-row-wide","address-field"],"validate":["state"],"autocomplete":"address-level1","priority":80,"country_field":"billing_country","country":"US","value":"NY"},"billing_postcode":{"label":"ZIP Code","required":true,"class":["form-row-wide","address-field"],"validate":["postcode"],"autocomplete":"postal-code","priority":90,"value":"10080"},"billing_phone":{"label":"Phone","required":true,"type":"tel","class":["form-row-wide"],"validate":["phone"],"autocomplete":"tel","priority":100,"value":"************"},"billing_email":{"label":"Email address","required":true,"type":"email","class":["form-row-wide"],"validate":["email"],"autocomplete":"email username","priority":110,"value":"<EMAIL>"},"shipping_first_name":{"label":"First name","required":true,"class":["form-row-first"],"autocomplete":"given-name","priority":10,"value":null},"shipping_last_name":{"label":"Last name","required":true,"class":["form-row-last"],"autocomplete":"family-name","priority":20,"value":null},"shipping_company":{"label":"Company name","class":["form-row-wide"],"autocomplete":"organization","priority":30,"required":false,"value":null},"shipping_country":{"type":"country","label":"Country \/ Region","required":true,"class":["form-row-wide","address-field","update_totals_on_change"],"autocomplete":"country","priority":40,"value":"US"},"shipping_address_1":{"label":"Street address","placeholder":"House number and street name","required":true,"class":["form-row-wide","address-field"],"autocomplete":"address-line1","priority":50,"value":null},"shipping_address_2":{"label":"Apartment, suite, unit, etc.","label_class":["screen-reader-text"],"placeholder":"Apartment, suite, unit, etc. (optional)","class":["form-row-wide","address-field"],"autocomplete":"address-line2","priority":60,"required":false,"value":null},"shipping_city":{"label":"Town \/ City","required":true,"class":["form-row-wide","address-field"],"autocomplete":"address-level2","priority":70,"value":null},"shipping_state":{"type":"state","label":"State","required":true,"class":["form-row-wide","address-field"],"validate":["state"],"autocomplete":"address-level1","priority":80,"country_field":"shipping_country","country":"US","value":"NY"},"shipping_postcode":{"label":"ZIP Code","required":true,"class":["form-row-wide","address-field"],"validate":["postcode"],"autocomplete":"postal-code","priority":90,"value":null}};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/woo-payment-gateway/assets/js/frontend/wc-braintree.min.js?ver=3.2.59" id="wc-braintree-global-js"></script>
<script type="text/javascript" id="wc-braintree-client-manager-js-extra">
/* <![CDATA[ */
var wc_braintree_client_manager_params = {"url":"\/?wc-ajax=wc_braintree_frontend_request&path=\/wc-braintree\/v1\/client-token\/create","_wpnonce":"4828bf390d","page_id":"add_payment_method","currency":"USD","merchant_account":"LumenflexInc_instant","version":"3.2.59"};
var wc_braintree_client_token = ["************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"];
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/woo-payment-gateway/assets/js/frontend/client-manager.min.js?ver=3.2.59" id="wc-braintree-client-manager-js"></script>
<script type="text/javascript" src="https://js.braintreegateway.com/web/3.106.0/js/hosted-fields.min.js" id="wc-braintree-hosted-fields-v3-js"></script>
<script type="text/javascript" src="https://js.braintreegateway.com/web/3.106.0/js/data-collector.min.js" id="wc-braintree-data-collector-v3-js"></script>
<script type="text/javascript" src="https://js.braintreegateway.com/web/3.106.0/js/three-d-secure.min.js" id="wc-braintree-3ds-v3-js"></script>
<script type="text/javascript" id="wc-braintree-hosted-fields-js-extra">
/* <![CDATA[ */
var wc_braintree_hosted_fields_params = {"gateway":"braintree_cc","environment":"production","advanced_fraud":{"enabled":false},"token_selector":"braintree_cc_token_key","nonce_selector":"braintree_cc_nonce_key","device_data_selector":"braintree_cc_device_data","payment_type_selector":"braintree_cc_payment_type","tokenized_response_selector":"braintree_cc_tokenized_response","ipAddress":"*************","roles":{"admin":false},"_wp_rest_nonce":"4828bf390d","user_id":"229892","messages":{"terms":"Please read and accept the terms and conditions to proceed with your order.","required_field":"Please fill out all required fields."},"locale":"en_US","routes":{"checkout":"\/?wc-ajax=wc_braintree_frontend_request&path=\/wc-braintree\/v1\/checkout","add_to_cart":"\/?wc-ajax=wc_braintree_frontend_request&path=\/wc-braintree\/v1\/cart","shipping":"\/?wc-ajax=wc_braintree_frontend_request&path=\/wc-braintree\/v1\/cart\/shipping","shipping_address":"\/?wc-ajax=wc_braintree_frontend_request&path=\/wc-braintree\/v1\/cart\/shipping-address","shipping_method":"\/?wc-ajax=wc_braintree_frontend_request&path=\/wc-braintree\/v1\/cart\/shipping-method"},"_3ds":{"verify_vault":false,"challengeRequested":false},"custom_fields":{"number":{"label":"Card Number","placeholder":"Card Number","id":"wc-braintree-card-number","type":"number"},"exp_date":{"label":"Exp Date","placeholder":"MM \/ YY","id":"wc-braintree-expiration-date","type":"expirationDate"},"exp_month":{"label":"Exp Month","placeholder":"MM","id":"wc-braintree-expiration-month","type":"expirationMonth"},"exp_year":{"label":"Exp Year","placeholder":"YY","id":"wc-braintree-expiration-year","type":"expirationYear"},"cvv":{"label":"CVV","placeholder":"CVV","id":"wc-braintree-cvv","type":"cvv"},"postal_code":{"label":"Postal Code","placeholder":"Postal Code","id":"wc-braintree-postal-code","type":"postalCode"},"save":{"label":"Save Card"},"street":{"label":"Street Address"}},"dynamic_card_display":{"enabled":true},"form_styles":{"input":{"font-size":"16px","font-family":"courier, monospace","font-weight":"500"}},"loader":{"enabled":true},"icon_style":"closed","html":{"cards":{"visa":"<img src=\"https:\/\/www.lumenflex.com\/wp-content\/plugins\/woo-payment-gateway\/assets\/img\/payment-methods\/closed\/visa.svg\"\/>","master-card":"<img src=\"https:\/\/www.lumenflex.com\/wp-content\/plugins\/woo-payment-gateway\/assets\/img\/payment-methods\/closed\/master_card.svg\"\/>","american-express":"<img src=\"https:\/\/www.lumenflex.com\/wp-content\/plugins\/woo-payment-gateway\/assets\/img\/payment-methods\/closed\/amex.svg\"\/>","discover":"<img src=\"https:\/\/www.lumenflex.com\/wp-content\/plugins\/woo-payment-gateway\/assets\/img\/payment-methods\/closed\/discover.svg\"\/>","diners-club":"<img src=\"https:\/\/www.lumenflex.com\/wp-content\/plugins\/woo-payment-gateway\/assets\/img\/payment-methods\/closed\/diners_club_international.svg\"\/>","jcb":"<img src=\"https:\/\/www.lumenflex.com\/wp-content\/plugins\/woo-payment-gateway\/assets\/img\/payment-methods\/closed\/jcb.svg\"\/>","maestro":"<img src=\"https:\/\/www.lumenflex.com\/wp-content\/plugins\/woo-payment-gateway\/assets\/img\/payment-methods\/closed\/maestro.svg\"\/>","unionpay":"<img src=\"https:\/\/www.lumenflex.com\/wp-content\/plugins\/woo-payment-gateway\/assets\/img\/payment-methods\/closed\/china_union_pay.svg\"\/>"}},"config_selector":"braintree_cc_config_data","_3ds_vaulted_nonce_selector":"braintree_cc_3ds_nonce_key","urls":{"_3ds":{"vaulted_nonce":"https:\/\/www.lumenflex.com\/wp-json\/wc-braintree\/v1\/3ds\/vaulted_nonce","client_token":"https:\/\/www.lumenflex.com\/wp-json\/wc-braintree\/v1\/3ds\/client_token"}}};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/woo-payment-gateway/assets/js/frontend/credit-cards.js?ver=3.2.59" id="wc-braintree-hosted-fields-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/woo-payment-gateway/assets/js/frontend/custom-forms/card-shape-form.js?ver=3.2.59" id="wc-braintree-card_shape_form-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-includes/js/jquery/ui/core.min.js?ver=1.13.3" id="jquery-ui-core-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-includes/js/jquery/ui/datepicker.min.js?ver=1.13.3" id="jquery-ui-datepicker-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/arscode-ninja-popups/assets/js/tooltipster.bundle.min.js?ver=********************************" id="jquery-np-tooltipster-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/arscode-ninja-popups/assets/js/jquery.material.form.min.js?ver=********************************" id="material-design-js-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/arscode-ninja-popups/assets/vendor/intl-tel-input/js/intlTelInput-jquery.min.js?ver=********************************" id="jquery-intl-phone-input-js-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/arscode-ninja-popups/assets/js/dialog_trigger.js?ver=********************************" id="js-dialog_trigger-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/arscode-ninja-popups/assets/js/ninjapopups.min.js?ver=********************************" id="js-ninjapopups-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/arscode-ninja-popups/fancybox2/jquery.fancybox.min.js?ver=********************************" id="fancybox2-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/facebook-pagelike-widget/fb.js?ver=1.0" id="scfbwidgetscript-js"></script>
<script type="text/javascript" src="https://connect.facebook.net/en_US/sdk.js?ver=2.0#xfbml=1&amp;version=v18.0" id="scfbexternalscript-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/library/cssua.js?ver=2.1.28" id="cssua-js"></script>
<script type="text/javascript" id="fusion-animations-js-extra">
/* <![CDATA[ */
var fusionAnimationsVars = {"status_css_animations":"desktop"};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/fusion-builder/assets/js/min/general/fusion-animations.js?ver=3.11.13" id="fusion-animations-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/general/awb-tabs-widget.js?ver=3.11.13" id="awb-tabs-widget-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/general/awb-vertical-menu-widget.js?ver=3.11.13" id="awb-vertical-menu-widget-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/library/modernizr.js?ver=3.3.1" id="modernizr-js"></script>
<script type="text/javascript" id="fusion-js-extra">
/* <![CDATA[ */
var fusionJSVars = {"visibility_small":"640","visibility_medium":"1024"};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/general/fusion.js?ver=3.11.13" id="fusion-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/library/swiper.js?ver=11.1.0" id="swiper-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/library/bootstrap.transition.js?ver=3.3.6" id="bootstrap-transition-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/library/bootstrap.tooltip.js?ver=3.3.5" id="bootstrap-tooltip-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/library/jquery.requestAnimationFrame.js?ver=1" id="jquery-request-animation-frame-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/library/jquery.easing.js?ver=1.3" id="jquery-easing-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/library/jquery.fitvids.js?ver=1.1" id="jquery-fitvids-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/library/jquery.flexslider.js?ver=2.7.2" id="jquery-flexslider-js"></script>
<script type="text/javascript" id="jquery-lightbox-js-extra">
/* <![CDATA[ */
var fusionLightboxVideoVars = {"lightbox_video_width":"1280","lightbox_video_height":"720"};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/library/jquery.ilightbox.js?ver=2.2.3" id="jquery-lightbox-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/library/jquery.mousewheel.js?ver=3.0.6" id="jquery-mousewheel-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/library/jquery.fade.js?ver=1" id="jquery-fade-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/library/imagesLoaded.js?ver=3.1.8" id="images-loaded-js"></script>
<script type="text/javascript" id="fusion-equal-heights-js-extra">
/* <![CDATA[ */
var fusionEqualHeightVars = {"content_break_point":"800"};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/general/fusion-equal-heights.js?ver=1" id="fusion-equal-heights-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/library/fusion-parallax.js?ver=1" id="fusion-parallax-js"></script>
<script type="text/javascript" id="fusion-video-general-js-extra">
/* <![CDATA[ */
var fusionVideoGeneralVars = {"status_vimeo":"1","status_yt":"1"};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/library/fusion-video-general.js?ver=1" id="fusion-video-general-js"></script>
<script type="text/javascript" id="fusion-video-bg-js-extra">
/* <![CDATA[ */
var fusionVideoBgVars = {"status_vimeo":"1","status_yt":"1"};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/library/fusion-video-bg.js?ver=1" id="fusion-video-bg-js"></script>
<script type="text/javascript" id="fusion-lightbox-js-extra">
/* <![CDATA[ */
var fusionLightboxVars = {"status_lightbox":"","lightbox_gallery":"1","lightbox_skin":"metro-white","lightbox_title":"1","lightbox_arrows":"1","lightbox_slideshow_speed":"5000","lightbox_loop":"0","lightbox_autoplay":"","lightbox_opacity":"0.90","lightbox_desc":"1","lightbox_social":"1","lightbox_social_links":{"facebook":{"source":"https:\/\/www.facebook.com\/sharer.php?u={URL}","text":"Share on Facebook"},"twitter":{"source":"https:\/\/x.com\/intent\/post?url={URL}","text":"Share on X"},"reddit":{"source":"https:\/\/reddit.com\/submit?url={URL}","text":"Share on Reddit"},"linkedin":{"source":"https:\/\/www.linkedin.com\/shareArticle?mini=true&url={URL}","text":"Share on LinkedIn"},"tumblr":{"source":"https:\/\/www.tumblr.com\/share\/link?url={URL}","text":"Share on Tumblr"},"pinterest":{"source":"https:\/\/pinterest.com\/pin\/create\/button\/?url={URL}","text":"Share on Pinterest"},"vk":{"source":"https:\/\/vk.com\/share.php?url={URL}","text":"Share on Vk"},"mail":{"source":"mailto:?body={URL}","text":"Share by Email"}},"lightbox_deeplinking":"1","lightbox_path":"vertical","lightbox_post_images":"1","lightbox_animation_speed":"normal","l10n":{"close":"Press Esc to close","enterFullscreen":"Enter Fullscreen (Shift+Enter)","exitFullscreen":"Exit Fullscreen (Shift+Enter)","slideShow":"Slideshow","next":"Next","previous":"Previous"}};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/general/fusion-lightbox.js?ver=1" id="fusion-lightbox-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/general/fusion-tooltip.js?ver=1" id="fusion-tooltip-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/general/fusion-sharing-box.js?ver=1" id="fusion-sharing-box-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/library/jquery.sticky-kit.js?ver=1.1.2" id="jquery-sticky-kit-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/library/fusion-youtube.js?ver=2.2.1" id="fusion-youtube-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/library/vimeoPlayer.js?ver=2.2.1" id="vimeo-player-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/general/fusion-general-global.js?ver=3.11.13" id="fusion-general-global-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/assets/min/js/general/avada-woo-variations.js?ver=7.11.13" id="avada-woo-product-variations-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/assets/min/js/general/avada-general-footer.js?ver=7.11.13" id="avada-general-footer-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/assets/min/js/general/avada-quantity.js?ver=7.11.13" id="avada-quantity-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/assets/min/js/general/avada-crossfade-images.js?ver=7.11.13" id="avada-crossfade-images-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/assets/min/js/general/avada-select.js?ver=7.11.13" id="avada-select-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/assets/min/js/general/avada-contact-form-7.js?ver=7.11.13" id="avada-contact-form-7-js"></script>
<script type="text/javascript" id="avada-live-search-js-extra">
/* <![CDATA[ */
var avadaLiveSearchVars = {"live_search":"1","ajaxurl":"https:\/\/www.lumenflex.com\/wp-admin\/admin-ajax.php","no_search_results":"No search results match your query. Please try again","min_char_count":"4","per_page":"100","show_feat_img":"1","display_post_type":"1"};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/assets/min/js/general/avada-live-search.js?ver=7.11.13" id="avada-live-search-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/general/fusion-alert.js?ver=********************************" id="fusion-alert-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/fusion-builder/assets/js/min/general/awb-off-canvas.js?ver=3.11.13" id="awb-off-canvas-js"></script>
<script type="text/javascript" id="fusion-flexslider-js-extra">
/* <![CDATA[ */
var fusionFlexSliderVars = {"status_vimeo":"1","slideshow_autoplay":"1","slideshow_speed":"7000","pagination_video_slide":"","status_yt":"1","flex_smoothHeight":"false"};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/general/fusion-flexslider.js?ver=********************************" id="fusion-flexslider-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/fusion-builder/assets/js/min/general/awb-background-slider.js?ver=********************************" id="awb-background-slider-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/general/fusion-button.js?ver=********************************" id="fusion-button-js"></script>
<script type="text/javascript" id="fusion-container-js-extra">
/* <![CDATA[ */
var fusionContainerVars = {"content_break_point":"800","container_hundred_percent_height_mobile":"0","is_sticky_header_transparent":"0","hundred_percent_scroll_sensitivity":"450"};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/fusion-builder/assets/js/min/general/fusion-container.js?ver=3.11.13" id="fusion-container-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/assets/min/js/library/jquery.elasticslider.js?ver=7.11.13" id="jquery-elastic-slider-js"></script>
<script type="text/javascript" id="avada-elastic-slider-js-extra">
/* <![CDATA[ */
var avadaElasticSliderVars = {"tfes_autoplay":"1","tfes_animation":"sides","tfes_interval":"3000","tfes_speed":"800","tfes_width":"150"};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/assets/min/js/general/avada-elastic-slider.js?ver=7.11.13" id="avada-elastic-slider-js"></script>
<script type="text/javascript" id="avada-drop-down-js-extra">
/* <![CDATA[ */
var avadaSelectVars = {"avada_drop_down":"1"};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/assets/min/js/general/avada-drop-down.js?ver=7.11.13" id="avada-drop-down-js"></script>
<script type="text/javascript" id="avada-to-top-js-extra">
/* <![CDATA[ */
var avadaToTopVars = {"status_totop":"desktop","totop_position":"right","totop_scroll_down_only":"0"};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/assets/min/js/general/avada-to-top.js?ver=7.11.13" id="avada-to-top-js"></script>
<script type="text/javascript" id="avada-header-js-extra">
/* <![CDATA[ */
var avadaHeaderVars = {"header_position":"top","header_sticky":"1","header_sticky_type2_layout":"menu_only","header_sticky_shadow":"1","side_header_break_point":"800","header_sticky_mobile":"","header_sticky_tablet":"","mobile_menu_design":"modern","sticky_header_shrinkage":"1","nav_height":"83","nav_highlight_border":"0","nav_highlight_style":"bar","logo_margin_top":"18px","logo_margin_bottom":"0px","layout_mode":"wide","header_padding_top":"0px","header_padding_bottom":"0px","scroll_offset":"full"};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/assets/min/js/general/avada-header.js?ver=7.11.13" id="avada-header-js"></script>
<script type="text/javascript" id="avada-menu-js-extra">
/* <![CDATA[ */
var avadaMenuVars = {"site_layout":"wide","header_position":"top","logo_alignment":"left","header_sticky":"1","header_sticky_mobile":"","header_sticky_tablet":"","side_header_break_point":"800","megamenu_base_width":"site_width","mobile_menu_design":"modern","dropdown_goto":"Go to...","mobile_nav_cart":"Shopping Cart","mobile_submenu_open":"Open submenu of %s","mobile_submenu_close":"Close submenu of %s","submenu_slideout":"1"};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/assets/min/js/general/avada-menu.js?ver=7.11.13" id="avada-menu-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/assets/min/js/library/bootstrap.scrollspy.js?ver=7.11.13" id="bootstrap-scrollspy-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/assets/min/js/general/avada-scrollspy.js?ver=7.11.13" id="avada-scrollspy-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/assets/min/js/general/avada-woo-products.js?ver=7.11.13" id="avada-woo-products-js"></script>
<script type="text/javascript" id="avada-woocommerce-js-extra">
/* <![CDATA[ */
var avadaWooCommerceVars = {"order_actions":"Details","title_style_type":"double solid","woocommerce_shop_page_columns":"4","woocommerce_checkout_error":"Not all fields have been filled in correctly.","related_products_heading_size":"2","ajaxurl":"https:\/\/www.lumenflex.com\/wp-admin\/admin-ajax.php","shop_page_bg_color":"#ffffff","shop_page_bg_color_lightness":"100","post_title_font_size":"18"};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/assets/min/js/general/avada-woocommerce.js?ver=7.11.13" id="avada-woocommerce-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/assets/min/js/general/avada-woo-product-images.js?ver=7.11.13" id="avada-woo-product-images-js"></script>
<script type="text/javascript" id="fusion-responsive-typography-js-extra">
/* <![CDATA[ */
var fusionTypographyVars = {"site_width":"1100px","typography_sensitivity":"1","typography_factor":"1.50","elements":"h1, h2, h3, h4, h5, h6"};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/general/fusion-responsive-typography.js?ver=3.11.13" id="fusion-responsive-typography-js"></script>
<script type="text/javascript" id="fusion-scroll-to-anchor-js-extra">
/* <![CDATA[ */
var fusionScrollToAnchorVars = {"content_break_point":"800","container_hundred_percent_height_mobile":"0","hundred_percent_scroll_sensitivity":"450"};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/themes/Avada/includes/lib/assets/min/js/general/fusion-scroll-to-anchor.js?ver=3.11.13" id="fusion-scroll-to-anchor-js"></script>
<script type="text/javascript" id="fusion-video-js-extra">
/* <![CDATA[ */
var fusionVideoVars = {"status_vimeo":"1"};
/* ]]> */
</script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/fusion-builder/assets/js/min/general/fusion-video.js?ver=3.11.13" id="fusion-video-js"></script>
<script type="text/javascript" src="https://www.lumenflex.com/wp-content/plugins/fusion-builder/assets/js/min/general/fusion-column.js?ver=3.11.13" id="fusion-column-js"></script>
				<script type="text/javascript">
				jQuery( document ).ready( function() {
					var ajaxurl = 'https://www.lumenflex.com/wp-admin/admin-ajax.php';
					if ( 0 < jQuery( '.fusion-login-nonce' ).length ) {
						jQuery.get( ajaxurl, { 'action': 'fusion_login_nonce' }, function( response ) {
							jQuery( '.fusion-login-nonce' ).html( response );
						});
					}
				});
				</script>
				<script>

  jQuery(document).ready(function () {

      

    jQuery("#popBtn").click(function() {

        jQuery.cookie('snp_snppopup-welcome1609', 1);

    });



    var productAr = {

      'motor': [

      {'productName': "lightrider", "label": "Order Now"}

      ],

      'bike': [

      {'productName': "brightcycle", "label": "Order Now"}

      ],

      'build': [

      {'productName': "orbitbeam", "label": "Order Now"}

      ],

      'explore': [

      {'productName': "brimbeam", "label": "Order Now"}

      ],

      'walk': [

      {'productName': "streetseen", "label": "Order Now"}

      ],

    };



    jQuery.each(productAr, function (key, products) {

      if (window.location.href.indexOf(key) > -1 && !(window.location.href.indexOf('product') > -1)) {{



          var href = "/product/" + products[0].productName + "/"

          var label = products[0].label;



          var buttonHtml = "" +

          "<li id=\"menu-item-1004\" class=\"shop-main-menu menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-1003 fusion-megamenu-menu fusion-menu-item-button fusion-last-menu-item\">" +

          "<a href=\"" + href + "\" aria-haspopup=\"true\" style=\"line-height: 83px; height: 83px;\">" +

          "<span class=\"menu-text fusion-button button-default button-medium\">" +

          "" + label + "" +

          "</span>" +

          "</a>" +

          "</li>";

        }



        jQuery("ul#menu-main-menu li").removeClass("fusion-last-menu-item");

        jQuery("ul#menu-main-menu").append(buttonHtml);



      }

    });





  });

</script>



<script>

  // makes the dropdown sticky on hover and removes it on scrolling

  jQuery(document).ready(function () {



    /*

    On hovering over the products button, when the mouse is moved away,

    then make the dropdown stick by resetting the attributes to do that

    per Avada setup.

    */

    jQuery('.shop-main-menu').hover(null, function() {



      // display the menu again after mouse moves away

      jQuery('.fusion-megamenu-wrapper').css({

        'visibility': 'visible',

        'opacity': 1

      });



      // not sure why this timeout is needed, but without the attribute does not get changed

      setTimeout(function() {

        jQuery('.fusion-main-menu').css('overflow', 'visible');

      }, 0);

    });



    // on scrolling anywhere on the page, hide the dropdown

    jQuery(window).scroll(function() {

      jQuery('.fusion-megamenu-wrapper').css({

        'visibility': 'hidden',

        'opacity': 0

      });

      jQuery('.fusion-main-menu').css('overflow', 'hidden');

    })

  });

</script>		</div>

			<section class="to-top-container to-top-right" aria-labelledby="awb-to-top-label">
		<a href="#" id="toTop" class="fusion-top-top-link">
			<span id="awb-to-top-label" class="screen-reader-text">Go to Top</span>

					</a>
	</section>
		</body>
</html>
