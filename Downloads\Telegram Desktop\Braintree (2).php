<?php

include "CurlHandlerV2.php";
include "AddressRandom/AddressRandom.php";

$CurlHandlerV2 = new CurlHandlerV2();
$CurlHandlerV2->SetCookiesHandler(); 

$CurlHandlerV2->ProxyHandler([ 
    "server" => "", #The server is necessary. 
]);

$req1 = $CurlHandlerV2->Get("https://api.zephyr-sim.com/v2/braintree/token", [
    "accept: application/json, text/plain, */*",
    "accept-language: en-US,en;q=0.9,es;q=0.8",
    "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
]);
$bearer = $CurlHandlerV2->capture(base64_decode($req1->getResult()), '"authorizationFingerprint":"','",');

$Guid = sprintf(
    '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
    mt_rand(0, 0xffff), mt_rand(0, 0xffff),
    mt_rand(0, 0xffff),
    mt_rand(0, 0x0fff) | 0x4000,
    mt_rand(0, 0x3fff) | 0x8000,
    mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
);

$req2 = $CurlHandlerV2->Post("https://payments.braintree-api.com/graphql", [
    "accept: */*",
    "accept-language: en-US,en;q=0.9,es;q=0.8",
    "Authorization: Bearer $bearer",
    "braintree-version: 2018-05-10",
    "content-type: application/json",
    "origin: https://assets.braintreegateway.com",
    "referer: https://assets.braintreegateway.com/",
    "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
], '{"clientSdkMetadata":{"source":"client","integration":"custom","sessionId":"'.$Guid.'"},"query":"mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }","variables":{"input":{"creditCard":{"number":"****************","expirationMonth":"02","expirationYear":"2028","cvv":"828","billingAddress":{"postalCode":"10080"}},"options":{"validate":false}}},"operationName":"TokenizeCreditCard"}');
$tokencc = $CurlHandlerV2->capture($req2->getResult(), '"token":"','"');

$req3 = $CurlHandlerV2->Post("https://api.zephyr-sim.com/v2/orders/braintree", [
    'accept: application/json, text/plain, */*',
    'content-type: application/json',
    'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
], '{"paymentMethodNonce":"'.$tokencc.'","email":"jsentau'.rand(100, 999).'@gmail.com","cart":[{"productId":"HOBBYIST-PACK","quantity":1,"isUpsell":false,"isDownsell":false}],"billingCountry":"US","billingStateProvince":"NY","billingPostalCode":"10080","expedited":false,"total":9.99}');
$msg = json_decode($req3->getResult(), 1)["message"];
echo $msg;