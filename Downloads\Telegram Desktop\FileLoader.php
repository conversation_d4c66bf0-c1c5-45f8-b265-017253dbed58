<?php
/**
 * This class is responsible for loading PHP files and ensuring they are not loaded more than once.
 */
class FileLoader {
    /**
     * @var array List of loaded files.
     */
    private static $loadedFiles = [];

    /**
     * Loads a PHP file if it has not been loaded yet.
     *
     * @param string $file The path to the PHP file to be loaded.
     * @return void
     * @throws Exception If the file does not exist.
     */
    public static function load(string $file): void {
        if (static::isLoaded($file)) return;
        if (!file_exists($file)) throw new Exception("File $file doesnt exist");
        include $file;
        static::$loadedFiles[] = $file;
    }

    /**
     * Checks if a file has been loaded.
     *
     * @param string $file The path to the PHP file to be checked.
     * @return bool True if the file has been loaded, false otherwise.
     */
    private static function isLoaded(string $file): bool {
        return in_array($file, static::$loadedFiles);
    }
}