<?php 

error_reporting(0);
ini_set('log_errors', 1);
ini_set('error_log', 'php-error.log');

if (session_status() == PHP_SESSION_NONE) {
    session_start();
    session_write_close();
}

// <PERSON><PERSON><PERSON> thư viện Dotenv và Telegram
require 'vendor/autoload.php';
require_once '../Auth/config.php';
require_once '../Auth/CreditManager.php';
require_once 'lib' . DIRECTORY_SEPARATOR . 'fake_us.php';
require_once 'lib' . DIRECTORY_SEPARATOR . 'Utility.php';
require_once 'lib' . DIRECTORY_SEPARATOR . 'card_class.php';
require_once 'lib' . DIRECTORY_SEPARATOR . 'telegram.php';
require_once 'lib' . DIRECTORY_SEPARATOR . 'user_agent.php';
require_once 'Luu_tru_BABA'.DIRECTORY_SEPARATOR.'main.php';
require_once 'solve_captcha'.DIRECTORY_SEPARATOR.'call_nextcaptcha_v2_nonenterprise.php';
require_once 'PaymentGateway.php'; 

// CHECK USER LOGGED + CREDITS
$creditManager = new CreditManager($conn, $_SESSION['user_id']);

if (!$creditManager->isAuthenticated()) {
    die(json_encode(['status' => 'unk', 'message' => 'User not logged in']));
    exit;
}

if (!$creditManager->hasSufficientCredits(CreditManager::COST_CCV)) {
    die(json_encode(['status' => 'unk', 'message' => 'Insufficient credits']));
    exit;
}

use GuzzleHttp\Client;
use GuzzleHttp\RequestOptions;
use GuzzleHttp\Cookie\CookieJar;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Exception\ConnectException;
use Dotenv\Dotenv;

// Nạp file .env
$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->load();

#POST / GET
if ($_POST) {
    $_GET = $_POST;
}

$xuly = new _chung();
$lista = $_GET['body'];
$cc = $xuly->xulythe($lista);

// Ktra thẻ EXP
$currentYear = date('Y');
$currentMonth = date('m');

if ($cc['y'] < $currentYear || ($cc['y'] == $currentYear && $cc['m'] < $currentMonth)) {
    die(json_encode(['status' => 'unk', 'message' => 'card_expired']));
}

// CardType
$firstDigit = substr($cc['n'], 0, 1);
switch ($firstDigit) {
    case '4':
        $cardType = 'Visa';
        break;
    case '5':
        $cardType = 'MasterCard';
        break;
    case '6':
        $cardType = 'Discover';
        break;
    default:
        $cardType = 'Không xác định';
}

if($cardType == "Không xác định"){
    die(json_encode(['status' => 'unk', 'message' => 'error_type_card']));
}

// Tạo một CookieJar trong bộ nhớ
$cookieJar = new CookieJar();

// Lấy thông tin random user
$randomUser = new RandomUser();
$userData = $randomUser->getUserFromUS();

if ($userData) {
    $name = $userData['firstname'];
    $last = $userData['lastname'];
    $email = $userData['email'];
    $street = $userData['street'];
    $city = $userData['city'];
    $postcode = $userData['postcode'];
    $state_full = $userData['state_full'];
    $state_abbreviation = $userData['state_abbreviation'];
    $phone = $userData['phone'];
}

// Utility
$utility = new Utility();

// Telegram
$telegram = new Telegram();

// UserAgent
$agent = new userAgent();
$ua = $agent->generate('chrome');

// Cấu hình proxy (lấy từ file .env)
$PRX = $_ENV['LUNAPROXY'] ?? NULL;

$retry = 0;
$isRetry = false;

start:

if ($isRetry) {
    $retry++;
}

if ($retry > 2) {
    die(json_encode(['status' => 'unk', 'message' => 'max_retry_2', 'request' => 'retry']));
}

try {
    $gateway = new PaymentGateway($PRX); 

    $result = $gateway->b3avscvv($cc['n'], $cc['m'], $cc['y'], $cc['c']);

    $status = $result[0];
    $processor_code = $result[1];
    $processor_text = $result[2];
    $cvv_code = $result[3];
    $avs_postal = $result[4];
    $avs_street = $result[5];
    $network_code = $result[6];
    $network_text = $result[7]; 

    if ($status == "Approved ✅") {
        die(json_encode(['status' => 'success', 'message' => 'Approved or completed successfully']));
    } else {
        $message = "Declined ❌ - Processor Code: $processor_code - Processor Text: $processor_text";
        die(json_encode(['status' => 'error', 'message' => $message]));
    }

} catch (ConnectException $e) {
    $telegram->send_err("ConnectException: " . $e->getMessage() . " in " . $e->getFile() . ":" . $e->getLine());
    die(json_encode(['status' => 'unk', 'message' => 'proxy_bad', 'report' => 'admin']));
} catch (RequestException $e) {
    $telegram->send_err("RequestException: " . $e->getMessage() . " in " . $e->getFile() . ":" . $e->getLine());
    die(json_encode(['status' => 'unk', 'message' => 'request_error', 'report' => 'admin']));
} catch (\Exception $e) {
    $telegram->send_err("General Exception: " . $e->getMessage() . " in " . $e->getFile() . ":" . $e->getLine());
    die(json_encode(['status' => 'unk', 'message' => 'something went wrong.', 'report' => 'admin']));
}

end: