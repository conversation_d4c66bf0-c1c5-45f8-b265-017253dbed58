<?php
use <PERSON><PERSON>\Http\Client as HttpClient;
use <PERSON><PERSON>\Http\Request as HttpRequest;
use <PERSON><PERSON>\Json\Json;

function cfBypass(string $cloudflarePage, string $domain = null): ?array
{
    if (!preg_match('/s,t,o,p,b,r,e,a,k,i,n,g,f, /', $cloudflarePage)) {
        return null;
    }

    preg_match('/s,t,o,p,b,r,e,a,k,i,n,g,f,(.*?);/', $cloudflarePage, $matches);
    $line1 = $matches[1] . ';';

    preg_match('/getElementById\(\'challenge-form\'\);(.*?);t.length;/', $cloudflarePage, $matches);
    $domainLength = $domain ? strlen($domain) : strlen(parse_url($cloudflarePage, PHP_URL_HOST));
    $line2 = $matches[1] . $domainLength . ';';

    $v8 = new V8Js();
    $jschlAnswer = $v8->executeString('var ' . $line1 . str_replace('a.value', '$', $line2));

    preg_match('/jschl_vc" value="(.*?)"/', $cloudflarePage, $matches);
    $jschlVc = $matches[1];

    preg_match('/pass" value="(.*?)"/', $cloudflarePage, $matches);
    $pass = htmlentities($matches[1]);

    return [
        'jschl_vc' => $jschlVc,
        'pass' => $pass,
        'jschl_answer' => $jschlAnswer,
    ];
}

#example Usage with Laminas HTTP Client
$client = new HttpClient();

$request = new HttpRequest();
$request->setUri('https://example.com');
$request->setMethod('GET');
$request->getHeaders()->addHeaders([
    'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
]);

$response = $client->send($request);

if ($cloudflareData = cfBypass($response->getBody(), $request->getUri()->getHost())) {
    sleep(4);

    $jschlUrl = 'https://example.com/cdn-cgi/l/chk_jschl?' . http_build_query($cloudflareData); 
    $request->setUri($jschlUrl);
    $request->getHeaders()->addHeaderLine('Referer', 'https://example.com'); #Set Referer
    $response = $client->send($request);
}

echo $response->getBody();

/*
Gen G. Team Script
*/