import random
import json

# Your given brands and models
brands_and_models = {
    "samsung": ["SM-G950F", "SM-G960F", "SM-G970F", "Galaxy S21", "Galaxy Note 20", "SM-A315F", "SM-G950U", "SM-G975F", "SM-A135F", "Galaxy M21", "Galaxy A12", "SM-G985F", "SM-G988B"],
    "google": ["Pixel 5", "Pixel 6", "Pixel 7", "Pixel 4a", "Pixel 3", "Pixel 3 XL", "Pixel 4 XL", "Pixel 2", "Pixel C", "Pixel 5a", "Pixel 2 XL"],
    "xiaomi": ["Mi 11", "Redmi Note 10", "Poco F3", "Mi 10", "Redmi K30", "Redmi 9", "Redmi Note 9", "Redmi Note 8", "Redmi Note 7", "Poco X3", "Mi Mix 2", "Mi 9", "Redmi Note 9 Pro"],
    "huawei": ["P30", "Mate 40", "Nova 8", "P40", "Honor 9X", "P20", "Mate 30", "Y9 Prime", "Nova 7i", "Y7a", "Honor 10", "P Smart", "Y6p", "Y5p"],
    "oppo": ["Find X2", "Reno 6", "A94", "A53", "F19", "Reno 4", "A74", "Reno 5", "Find X3", "A92", "A15", "A9 2020", "F11 Pro", "F7"],
    "vivo": ["V21", "X70 Pro", "Y73", "Y51", "V19", "V20", "X60 Pro", "Y20", "Y50", "V17 Pro", "Y12", "Y91C", "Y30"],
    "oneplus": ["OnePlus 8", "OnePlus 9", "OnePlus 10", "OnePlus 7T", "OnePlus Nord", "OnePlus 6T", "OnePlus 7 Pro", "OnePlus 6", "OnePlus 5T", "OnePlus 3T", "OnePlus X", "OnePlus 3"],
    "lg": ["G8 ThinQ", "V60 ThinQ", "Wing", "Velvet", "K92", "G7 ThinQ", "V50 ThinQ", "G6", "Q60", "K40", "Q7", "K30", "Q Stylo 4"],
    "sony": ["Xperia 1 II", "Xperia 5 II", "Xperia 10 II", "Xperia L4", "Xperia XZ3", "Xperia XZ2", "Xperia XZ1", "Xperia XZ Premium", "Xperia XA1", "Xperia Z5", "Xperia Z3+", "Xperia Z Ultra"],
    "motorola": ["Moto G9", "Moto G Power", "Edge 20", "Moto G Stylus", "Moto Z4", "Moto E7", "Moto G6", "Moto G8", "Edge Plus", "Moto One Action", "Moto G7 Power", "Moto G7 Plus"],
    "nokia": ["Nokia 7.2", "Nokia 5.3", "Nokia 8.3 5G", "Nokia 2.3", "Nokia 6.2", "Nokia 1.3", "Nokia 3.4", "Nokia 1.4", "Nokia 9 PureView", "Nokia 7.1", "Nokia 6.1", "Nokia 8 Sirocco"],
    "asus": ["ASUS_X01BDA", "Zenfone 7", "Zenfone 6", "ROG Phone 3", "Zenfone Max Pro", "Zenfone 5", "Zenfone 5Z", "Zenfone AR", "Zenfone 8", "ROG Phone 2", "ROG Phone 5", "Zenfone 4"],
    "tecno": ["TECNO KF6p", "Spark 5", "Camon 16", "Pop 4", "Pouvoir 4", "Camon 15", "Spark 7", "Pop 2F", "Spark 4", "Pouvoir 3", "Pop 3"],
    "realme": ["RMX3263", "RMX3269", "Realme 7", "Realme 8", "Realme C25", "Realme X3", "Realme X2", "Realme 6", "Realme 6 Pro", "Realme Narzo 20", "Realme C11", "Realme C3"],
    "tcl": ["T767W", "TCL 10 Pro", "TCL 10L", "TCL 20 SE", "TCL 20 Pro", "TCL 10 5G", "TCL 10 Plus", "TCL Plex", "TCL 10 Tab Max", "TCL 20R 5G", "TCL 20L", "TCL 20S"],
    "alcatel": ["5041C", "1X", "1V", "3L", "3X", "1SE", "1B", "5V", "3", "5", "3V", "7"],
    "infinix": ["Infinix X682B", "Hot 10", "Note 8", "Zero 8", "Smart 5", "Hot 9", "S5", "Note 7", "Smart 4", "Hot 8", "Note 6", "Hot 7"],
    "huawei": ["STK-L21", "ANE-LX1", "POT-LX1", "DUB-LX1", "Mate 30 Pro", "Mate 40 Pro", "Nova 5T", "P Smart 2021", "Y9a", "Y8p", "Y6s", "Y5 Lite"],
    "honor": ["NTH-NX9", "CMA-LX1", "TFY-LX1", "Honor 30 Pro", "Honor 9X Lite", "Honor 20", "Honor 9C", "Honor View 20", "Honor Play", "Honor 10 Lite", "Honor 8X", "Honor 8A"],
    "vivo": ["V21", "X70 Pro", "Y73", "Y51", "V19", "V20", "X60 Pro", "Y20", "Y50", "V17 Pro", "Y12", "Y91C", "Y30"],
    "zte": ["Blade V2020", "Axon 11", "Blade A7 Prime", "Blade V10", "Blade 20", "Blade A5", "Axon 10 Pro", "Blade V9", "Blade A3", "Blade A7", "Axon 7", "Axon M"],
    "blackberry": ["Key2", "KeyOne", "DTEK50", "DTEK60", "Motion", "Passport", "Classic", "Priv", "Q10", "Z10", "Leap", "Bold 9900", "Curve 9320"],
    "lenovo": ["Vibe K5", "K6 Note", "P2", "A6000", "A7000", "ZUK Z1", "ZUK Z2", "Vibe P1", "K8 Note", "Phab 2", "Phab Plus", "Tab M10", "A10", "Z5 Pro", "S5 Pro", "Z6", "K10", "K9", "A6", "Tab M8"],
    "meizu": ["M6 Note", "M5s", "Pro 7", "MX6", "M3 Note", "M2", "M1", "MX5", "M5", "Pro 6", "M6", "16", "16th Plus", "17 Pro", "17", "18", "18s", "MX4", "MX3", "MX2"],
    "zte": ["Axon 7", "Axon 10 Pro", "Axon 11 SE", "Axon 20 5G", "Blade A7", "Blade A5", "Blade A3", "Blade A510", "Blade A7 2020", "Blade V2020", "Blade V9", "Blade V8", "Blade S6", "Nubia Z17", "Nubia Z18", "Nubia Z20", "Nubia Red Magic 3", "Nubia Red Magic 5G", "Axon M", "Nubia X"],
    "realme": ["Realme 2", "Realme 2 Pro", "Realme C1", "Realme C2", "Realme 3", "Realme 3 Pro", "Realme 5", "Realme 5 Pro", "Realme 6", "Realme 6 Pro", "Realme 7", "Realme 7 Pro", "Realme 8", "Realme 8 Pro", "Realme X", "Realme X2", "Realme X3", "Realme X50", "Realme X50 Pro", "Realme Narzo 30"],
    "htc": ["One M9", "One A9", "One X9", "10", "Desire 10 Pro", "Desire 10 Lifestyle", "U Ultra", "U Play", "Desire 12", "Desire 12 Plus", "U11", "U11 Life", "U12 Plus", "U19e", "Desire 19s", "Exodus 1", "Exodus 1s", "Desire 20 Pro", "U20 5G", "Wildfire R70"],
    "lava": ["Z1", "Z2", "Z4", "Z6", "V2s", "A97", "A89", "A88", "A77", "Iris 870", "Iris 8S", "Iris Fuel 50", "X41", "Pixel V1", "Iris 6S", "X38", "A44", "A51", "Pixel 2", "X19"],
    "micromax": ["Canvas 6", "Canvas Nitro 2", "Canvas Knight 2", "Bharat 5", "Bharat 4", "Bharat 3", "Bolt A1", "Bolt A5", "Canvas Spark 4G", "Canvas 2", "Unite 4", "Unite 3", "Selfie 2", "Canvas Mega", "Canvas Juice 4G", "Vdeo 3", "Canvas Infinity", "Dual 4", "Yu Yureka", "Yu Ace"],
    "nokia": ["Nokia 7.1", "Nokia 8.1", "Nokia 6.1", "Nokia 5.1", "Nokia 3.1", "Nokia 2.1", "Nokia 1", "Nokia 6", "Nokia 8", "Nokia 9 PureView", "Nokia X5", "Nokia X6", "Nokia 5.4", "Nokia 3.4", "Nokia 2.4", "Nokia 1.4", "Nokia C1", "Nokia C3", "Nokia 105", "Nokia 150"],
    "infinix": ["Infinix Note 7", "Infinix Hot 8", "Infinix Smart 4", "Infinix S5", "Infinix Zero 8", "Infinix Hot 9", "Infinix Hot 7 Pro", "Infinix S4", "Infinix Hot 6X", "Infinix Hot 5", "Infinix Zero 5", "Infinix Note 4", "Infinix Hot 4", "Infinix Zero 3", "Infinix Note 3", "Infinix Smart 3", "Infinix Hot 6", "Infinix Hot 10", "Infinix S3", "Infinix Hot 9 Play"],
    "tecno": ["Camon 12", "Camon 15", "Camon iAir", "Spark 3", "Spark 4", "Pouvoir 3", "Pouvoir 4", "Pop 2", "Pop 3", "Pop 4", "Camon iClick", "Spark Go", "Camon i4", "Camon i5", "Camon i2", "Camon X", "Camon X Pro", "Camon 11", "Camon 16", "Pouvoir 5"],
    "coolpad": ["Coolpad Cool 1", "Coolpad Note 5", "Coolpad Mega", "Coolpad Note 3", "Coolpad Cool Play 6", "Coolpad Dazen 1", "Coolpad Mega 2.5D", "Coolpad Mega 3", "Coolpad Cool S1", "Coolpad Cool Changer 1C", "Coolpad Cool 3", "Coolpad Cool M7", "Coolpad Note 3 Lite", "Coolpad Note 3 Plus", "Coolpad Note 6", "Coolpad Cool 5", "Coolpad Cool 7", "Coolpad Cool 8", "Coolpad Note 8", "Coolpad Cool 9"],
    "sony": ["Xperia Z5", "Xperia Z5 Compact", "Xperia Z5 Premium", "Xperia X", "Xperia XA", "Xperia XA Ultra", "Xperia X Compact", "Xperia XZ", "Xperia XZ Premium", "Xperia XZ1", "Xperia XZ2", "Xperia XZ2 Premium", "Xperia XZ3", "Xperia 1", "Xperia 5", "Xperia 10", "Xperia 10 Plus", "Xperia L1", "Xperia L2", "Xperia L3"],
    "acer": ["Liquid Z6", "Liquid Z6 Plus", "Liquid Jade Primo", "Liquid Zest Plus", "Liquid Z530", "Liquid Z630", "Liquid X2", "Liquid Z500", "Liquid Z330", "Liquid Z320", "Liquid Z200", "Liquid Z410", "Liquid Z520", "Liquid E700", "Liquid E600", "Liquid Z4", "Liquid E3", "Liquid S2", "Liquid Z5", "Liquid Z3"],
    "gionee": ["Gionee A1", "Gionee A1 Plus", "Gionee A1 Lite", "Gionee S10", "Gionee S10B", "Gionee S10C", "Gionee S11", "Gionee S11 Lite", "Gionee S6 Pro", "Gionee Marathon M5", "Gionee P7", "Gionee P7 Max", "Gionee M7", "Gionee M7 Power", "Gionee M6S Plus", "Gionee F205", "Gionee F6", "Gionee X1", "Gionee X1s", "Gionee Steel 2"],
    "panasonic": ["Eluga I9", "Eluga I7", "Eluga A3", "Eluga A4", "Eluga Ray", "Eluga Ray Max", "Eluga Ray X", "Eluga Ray 500", "Eluga Ray 700", "Eluga Ray 800", "Eluga Ray 810", "Eluga X1", "Eluga X1 Pro", "P100", "P90", "P85", "P55 Max", "P99", "P77", "P55"],
    "itel": ["itel S12", "itel S15", "itel P32", "itel P33", "itel P33 Plus", "itel A11", "itel A14", "itel A33", "itel A44", "itel A46", "itel A23", "itel A25", "itel Vision 1", "itel Vision 2", "itel A48", "itel S21", "itel S31", "itel P36", "itel P36 Pro", "itel S42"],
    "blackberry": ["Blackberry Priv", "Blackberry DTEK60", "Blackberry DTEK50", "Blackberry KeyOne", "Blackberry Key2", "Blackberry Motion", "Blackberry Classic", "Blackberry Passport", "Blackberry Q10", "Blackberry Z10", "Blackberry Leap", "Blackberry Bold 9900", "Blackberry Bold 9790", "Blackberry Curve 9320", "Blackberry Curve 8520", "Blackberry Torch 9810", "Blackberry Torch 9860", "Blackberry PlayBook", "Blackberry Storm", "Blackberry Pearl 3G"]

}

def generate_sensor_data():
    sensor_data = []
    num_readings = random.randint(250, 350)  # Randomized number of sensor data points

    for _ in range(num_readings):
        # Randomized delay range depending on the sensor type
        accelerometer_data = {
            "delay": random.randint(20000, 30000),
            "accuracy": random.choice([2, 3]),
            "type": 1,
            "x": random.uniform(-2.0, 2.0),
            "y": random.uniform(-2.0, 2.0),
            "z": random.uniform(8.0, 10.5)
        }

        gyroscope_data = {
            "delay": random.randint(20000, 30000),
            "accuracy": random.choice([2, 3]),
            "type": 4,
            "x": random.uniform(-0.05, 0.05),
            "y": random.uniform(-0.05, 0.05),
            "z": random.uniform(-0.05, 0.05)
        }

        magnetometer_data = {
            "delay": random.randint(20000, 30000),
            "accuracy": random.choice([2, 3]),
            "type": 2,
            "x": random.uniform(-60.0, 60.0),
            "y": random.uniform(-60.0, 60.0),
            "z": random.uniform(-60.0, 60.0)
        }

        gravity_data = {
            "delay": random.randint(20000, 30000),
            "accuracy": random.choice([2, 3]),
            "type": 9,
            "x": random.uniform(-1.0, 1.0),
            "y": random.uniform(-1.0, 1.0),
            "z": random.uniform(9.0, 10.5)
        }

        # Optional: Randomly include the rotation vector data
        if random.random() > 0.5:
            rotation_vector_data = {
                "delay": random.randint(20000, 30000),
                "accuracy": random.choice([2, 3]),
                "type": 11,
                "x": random.uniform(-1.0, 1.0),
                "y": random.uniform(-1.0, 1.0),
                "z": random.uniform(-1.0, 1.0),
                "scalar": random.uniform(0.0, 1.0)
            }
            sensor_data.append(rotation_vector_data)

        sensor_data.append(accelerometer_data)
        sensor_data.append(gyroscope_data)
        sensor_data.append(magnetometer_data)
        sensor_data.append(gravity_data)

    return sensor_data


def generate_devices_for_models(brands_and_models):
    all_devices = []

    for brand, models in brands_and_models.items():
        for model in models:
            device_data = {
                "_id": {
                    "$oid": str(random.randint(100000000000000000000000, 999999999999999999999999))
                },
                "BUILD": {
                    "MANUFACTURER": brand,
                    "MODEL": model,
                    "HARDWARE": brand + random.choice(["chipset1", "chipset2", "chipset3"]),
                    "BOOTLOADER": "BL" + str(random.randint(1000, 9999)),
                    "VERSION": {
                        "RELEASE": str(random.randint(9, 13)),
                        "CODENAME": "REL",
                        "INCREMENTAL": "IN" + str(random.randint(1000, 9999)),
                        "SDK_INT": random.randint(28, 33)
                    },
                    "PRODUCT": brand + model,
                    "TAGS": "release-keys",
                    "TYPE": "user",
                    "USER": "dpi",
                    "DISPLAY": "DP" + str(random.randint(1000, 9999)),
                    "BOARD": brand + "board",
                    "BRAND": brand,
                    "DEVICE": model,
                    "FINGERPRINT": brand + "/" + model + ":release-keys",
                    "HOST": "SW" + str(random.randint(1000, 9999)),
                    "ID": "ID" + str(random.randint(1000, 9999))
                },
                "SCREEN": {
                    "heightPixels": random.randint(1920, 3200),
                    "widthPixels": random.randint(1080, 1440),
                    "density": random.randint(2, 4),
                    "xdpi": round(random.uniform(300, 500), 2),
                    "ydpi": round(random.uniform(300, 500), 2),
                    "densityDpi": random.randint(320, 640),
                    "scaledDensity": random.randint(2, 4)
                },
                "SENSOR": generate_sensor_data(),
                "SENSOR_STATUS": {
                    "TYPE_GRAVITY": True,
                    "TYPE_ACCELEROMETER": True,
                    "TYPE_MAGNETIC_FIELD": True,
                    "TYPE_GYROSCOPE": True
                },
                "PERF_BENCH": [
                    f"{random.randint(10, 20)},{random.randint(100, 500)},{random.randint(50, 100)},{random.randint(200, 1000)},{random.randint(10000, 20000)},{random.randint(100, 300)},{random.randint(3000, 8000)},{random.randint(20, 50)},{random.randint(1000, 5000)}"
                    for _ in range(10)
                ],
                "CRYPTO_PERF_BENCH": f";{round(random.uniform(0.1, 1.0), 12)};{random.randint(100, 500)},..."
            }

            all_devices.append(device_data)

    return all_devices

# Generate device data for the brands and models listed
device_data = generate_devices_for_models(brands_and_models)

# Save the generated data to a JSON file
with open('generated_devices.json', 'w') as json_file:
    json.dump(device_data, json_file, indent=4)

print("Device data generation complete.")
