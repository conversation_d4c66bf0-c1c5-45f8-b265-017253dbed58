import re

ilist=iter(open("cclist.txt", 'r').read().splitlines())

while True:
	try:
		msg = next(ilist)
		t = re.findall("[4-6][0-9]{15}", msg)
		countert=0
		for i in t:
			countert = countert + 1
		if countert == 1:
			msg = msg.replace(t[0], "")
			t2 = re.findall("202[1-9]", msg)
			countert2=0
			for i in t2:
				countert2 = countert2 + 1
			if countert2 >= 1:
				msg = msg.replace(t2[0], "")
			t4 = re.findall("[0-9]{3}", msg)
			countert4=0
			for i in t4:
				countert4 = countert4 + 1
			if countert4 >= 1:
				msg = msg.replace(t4[0], "")
		else:
			t = re.findall("[3][0-9]{14}", msg)
			countert=0
			for i in t:
				countert = countert + 1
			if countert == 1:
				msg = msg.replace(t[0], "")
			msg = msg.replace(t[0], "")
			t2 = re.findall("202[1-9]", msg)
			countert2=0
			for i in t2:
				countert2 = countert2 + 1
			if countert2 >= 1:
				msg = msg.replace(t2[0], "")
			t4 = re.findall("[0-9]{4}", msg)
			countert4=0
			for i in t4:
				countert4 = countert4 + 1
			if countert4 >= 1:
				msg = msg.replace(t4[0], "")
		t1 = re.findall("[0-1][0-9]", msg)
		countert1=0
		for i in t1:
			countert1 = countert1 + 1
		if countert1 >= 1:
			msg = msg.replace(t1[0], "")
		t3 = re.findall("2[1-9]", msg)
		countert3=0
		for i in t3:
			countert3 = countert3 + 1
		if countert3 >= 1:
			msg = msg.replace(t3[0], "")
		if countert == 1:
			if countert1 >= 1:
				if countert2 >= 1:
					countert3 = 1
					try:
						t3[0] = t2[0][2:]
					except:
						t3.append(t2[0][2:])
				if countert3 >= 1:
					if countert4 >= 1:
						print(t[0] + "|" + t1[0] + "|" + t3[0] + "|" + t4[0])
	except:
		break

input()